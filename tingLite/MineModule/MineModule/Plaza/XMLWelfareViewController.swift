//
//  XMLWelfareViewController.swift
//  MineModule
//
//  Created by xiaodong2.zhang on 2025/7/1.
//  Copyright © 2025 ximalaya. All rights reserved.
//

import UIKit
import BaseModule
import RouterModule
import XMConfigModule
import XMRNManager

public final class XMLWelfareViewController: XMLPageController {

    // MARK: - Public Properties

    // MARK: - Private Properties
    private let pageTitles: [String] = ["福利中心", "金币中心"]

    private lazy var welfareVC: XMLPlazaViewController = {
        let vc = XMRNRouter.createRNViewController(
            withUrl: "xmly://rntest?bundle=rn_credit_center_lite2",
            props: nil,
            backHandler: {
                // go back handler，返回true表示拦截返回，false表示继续返回
                return false
            },
            loadWhenAppear: false
        )
        vc.persistentIdentifier = self.persistentIdentifier
        return vc
    }()

    /// 金币中心页面
    private lazy var coinVC: XMLPlazaViewController = {
        let vc = XMLPlazaViewController()
        vc.persistentIdentifier = self.persistentIdentifier
        return vc
    }()

    // MARK: - Initialization
    public init() {
        super.init(nibName: nil, bundle: nil)
        setupPageController()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Lifecycle
    public override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }

    // MARK: - Private Methods
    private func setupPageController() {
        self.dataSource = self
        self.delegate = self
        self.pageAnimatable = true
        self.menuHeight = 44
        self.menuTitleWhenSmallerItems = true  // 启用自动分布来实现居中
        self.selectedIndex = 0  // 默认选中福利中心
        self.menuViewStyle = .default
        self.cachePolicy = .never
    }

    private func setupUI() {
        self.view.backgroundColor = .white
        // 设置页面框架
        self.viewFrame = CGRect(x: 0, y: XMSAFEEREA_TOP, width: self.view.frame.width, height: self.view.frame.height - XMTABBAR_HEIGHT - XMSAFEEREA_TOP)
    }

    // MARK: - Override Methods
    public override func menuView(_ menuView: XMMenuView, titleAtIndex index: Int) -> String? {
        return pageTitles[index]
    }

    public override func menuView(_ menuView: XMMenuView, widthForItemAtIndex index: Int) -> CGFloat {
        return 84.0
    }
}

// MARK: - XMLPageControllerDataSource & XMLPageControllerDelegate
extension XMLWelfareViewController: XMLPageControllerDataSource, XMLPageControllerDelegate {

    public func numberOfControllersInPageController(_ pageController: XMLPageController) -> Int {
        return pageTitles.count
    }

    public func pageController(_ pageController: XMLPageController, viewControllerAtIndex index: Int) -> UIViewController {
        switch index {
        case 0:
            return welfareVC  // 福利中心
        case 1:
            return coinVC     // 金币中心
        default:
            return UIViewController()
        }
    }

    public func pageController(_ pageController: XMLPageController, titleAtIndex index: Int) -> String? {
        return pageTitles[index]
    }

    public func pageController(_ pageController: XMLPageController, willEnterViewController viewController: UIViewController, withInfo info: NSDictionary) {
        let currentIndex = info["index"] as? Int ?? 0

        if currentIndex == 0 {
            // 进入福利中心
            self.view.backgroundColor = colorFromRGBA(0xFC6661, 0.5, 0xFC6661, 0.5)
        } else if currentIndex == 1 {
            // 进入金币中心
            self.view.backgroundColor = .white
        }
    }
}

// MARK: - Menu Style Customization
extension XMLWelfareViewController {

    public func menuView(_ menuView: XMMenuView, titleFontForState state: XMMenuItemState, atIndex index: Int) -> UIFont {
        if state == .normal {
            return kPingFangFont(15)
        }
        return kPingFangFont(16, .semiBold)
    }

    public func menuView(_ menuView: XMMenuView, titleColorForState state: XMMenuItemState, atIndex index: Int) -> UIColor {
        return colorFromRGB(0x240000, 0xFFFFFF)
    }
}
