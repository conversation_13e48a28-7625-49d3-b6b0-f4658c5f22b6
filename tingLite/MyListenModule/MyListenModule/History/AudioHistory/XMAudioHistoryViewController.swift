//
//  XMAudioHistoryViewController.swift
//  MyListenModule
//
//  Created by yangle<PERSON> on 2020/8/10.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import UIKit
import BaseModule
import XMHistoryModule
import RouterModule
import XMUBT
import XMConfigModule
import XMUtilModule
import XMNetworkModule
import XMBase
import RxSwift

// MARK: -历史播放
public class XMAudioHistoryViewController: XMBaseViewController {
    
    var hideClearButtonAction: ((Bool) -> Void)?
    // 专辑找相似数据模型
    public var viewModel: XMLHistoryViewModel              = XMLHistoryViewModel()
    public var hasTitle: Bool = true
    fileprivate lazy var disposeBag = { return DisposeBag() }()
    
    public override func viewDidLoad() {
        super.viewDidLoad()
        // 自定义可视化埋点数据
        self.customLoggerPageInfo()
        // 加载基础控件布局
        self.setupBaseUI()
        // 绑定通知响应逻辑
        self.bindNotificationActionHandle()
        // 从网络获取历史播放信息并更新本地
        self.updateHistoryListFromServer()
    }
    
    public override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 从获取播放历史信息
        self.loadHistoryListInfo()
    }
    
    // 当前网络是否在请求
    private var curHistoryUid: UInt = 0
    private var isNetworking: Bool                         = false
    // 当前视图是否在滚动
    private var isScrolling : Bool                         = false
    
    // 基础布局Layout
    private lazy var flowLayout: UICollectionViewFlowLayout = {
        let layout: UICollectionViewFlowLayout = UICollectionViewFlowLayout()
        layout.scrollDirection         = .vertical
        layout.minimumLineSpacing      = 0
        layout.minimumInteritemSpacing = 0
        return layout
    }()
    
    // 滑动容器
    private lazy var displayCollectionView: UICollectionView = {
        let collectionView = UICollectionView.init(frame: .zero, collectionViewLayout: flowLayout)
        collectionView.delegate                     = self
        collectionView.dataSource                   = self
        collectionView.backgroundColor              = .white
        collectionView.alwaysBounceVertical         = true
        collectionView.showsVerticalScrollIndicator = false
        //        collectionView.contentInset                 = UIEdgeInsets(top: 0, left: 0, bottom: UIScreen.tabBarHeight, right: 0)
        collectionView.layer.masksToBounds          = true
        collectionView.register(XMLHistoryCell.self      , forCellWithReuseIdentifier: XMLHistoryCell.identifier)
        collectionView.register(XMLHistoryAdCell.self    , forCellWithReuseIdentifier: XMLHistoryAdCell.identifier)
        collectionView.register(XMLHistoryHeaderView.self, forSupplementaryViewOfKind: UICollectionView.elementKindSectionHeader,
                                withReuseIdentifier: XMLHistoryHeaderView.identifier)
        collectionView.registerEmptyReusableView()
        if #available(iOS 11.0, *) {
            collectionView.contentInsetAdjustmentBehavior = .never
        } else {
            self.automaticallyAdjustsScrollViewInsets = false
        }
        return collectionView
    }()
    
    // 导航栏
    fileprivate lazy var naviBar: XMLNavigationBar = {
        let naviBar = XMLNavigationBar()
        naviBar.title = "最近播放"
        naviBar.hasLine = true
        naviBar.shouldStatusBar = true
        naviBar.hasBackButton = true
        naviBar.backgroundColor = UIColor.white
        naviBar.backAction = { [weak self] in self?.disMiss() }
        naviBar.rightItem = self.emptyButton
        return naviBar
    }()
    
    // 清空
    fileprivate lazy var emptyButton: UIButton = {
        let button = UIButton(type: .custom)
        button.frame = CGRect(x: 0, y: 0, width: 50, height: 50)
        button.setTitle("清空", for: .normal)
        button.setTitleColor(UIColor(rgbHex: 0x333333), for: .normal)
        button.titleLabel?.font = kPingFangFont(14)
        button.addTarget(self, action: #selector(clearHistoryAction), for: .touchUpInside)
        button.isHidden = true
        return button
    }()
    
    deinit {
        // 重置历史请求相关
        XMPhcManager.sharedManager.config(size: 100)
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - UICollectionViewDataSource Method
extension XMAudioHistoryViewController: UICollectionViewDataSource {
    public func numberOfSections(in collectionView: UICollectionView) -> Int {
        return self.viewModel.sectionItems.count
    }
    
    public func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        guard let section = self.viewModel.sectionItems[section, true] else { return 0 }
        return section.datas.count
    }
    
    public func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let item = self.viewModel.sectionItems[indexPath.section, true]?.datas[indexPath.row, true] else {
            // 如果没有数据，返回默认的 history cell
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: XMLHistoryCell.identifier, for: indexPath) as! XMLHistoryCell
            return cell
        }

        if case .history(let model) = item {
            //swiftlint:disable:next force_cast
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: XMLHistoryCell.identifier, for: indexPath) as! XMLHistoryCell
            cell.deleteTapHandleCallback  = { [weak self] in self?.removeHistory(at: indexPath) }
            cell.deleteButton.xmubt_model = ["albumId": model.itemId?.int64Value ?? 0, "albumTitle": model.itemTitle ]
            cell.similarTapHandleCallback = { [weak self] in self?.similarAlbumPage(at: indexPath) }
            return cell
        } else if case .ad = item {
            //swiftlint:disable:next force_cast
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: XMLHistoryAdCell.identifier, for: indexPath) as! XMLHistoryAdCell
            return cell
        }

        // 默认情况下返回 history cell
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: XMLHistoryCell.identifier, for: indexPath) as! XMLHistoryCell
        return cell
    }
    
    func preloadNextAdInfoIfNeed(at indexPath: IndexPath) {
        // 1 如果当前广告失败，首先请求当前广告
        guard case .ad(let model) = self.viewModel.sectionItems[indexPath.section, true]?.datas[indexPath.row, true] else { return }
        if !model.hasAd, !model.isStopLoad {
            model.isPreloadN = true
            DispatchQueue.global(qos: .default).asyncAfter(deadline: .now() + 1) { [weak self] in
                self?.loadHistoryAdInfo(model.historyKey, isPreLoadNext: true)
            }
        } else if model.hasAd, model.isPreloadN == false {
            model.isPreloadN = true
            DispatchQueue.global(qos: .default).asyncAfter(deadline: .now() + 1) { [weak self] in
                self?.loadHistoryAdInfo(model.historyKey + 1)
            }
        }
    }
    
    public func collectionView(_ collectionView: UICollectionView, willDisplay cell: UICollectionViewCell, forItemAt indexPath: IndexPath) {
        guard let item = self.viewModel.sectionItems[indexPath.section, true]?.datas[indexPath.row, true] else { return }
        if case .history(let model) = item, let displayCell = cell as? XMLHistoryCell {
            displayCell.updateWidget(with: model)
            // 青少年模式下不显示找相似按钮
            let childrenMode = mineRouterBridge(self.persistentIdentifier)?.isChildrenMode ?? false
            if childrenMode {
                displayCell.hiddenSimilaerButton()
            }
        } else if case .ad(let model) = item, let displayCell = cell as? XMLHistoryAdCell {
            displayCell.closeEventHandle = { [weak collectionView] in
                model.cleanAd()
                collectionView?.reloadData()
            }
            displayCell.updateWidget(with: model, presentVC: self)
            self.preloadNextAdInfoIfNeed(at: indexPath)
        }
    }
    
    public func collectionView(_ collectionView: UICollectionView, viewForSupplementaryElementOfKind kind: String, at indexPath: IndexPath) -> UICollectionReusableView {
        guard let section = self.viewModel.sectionItems[indexPath.section, true] else { return collectionView.emptyReusableView(kind, at: indexPath) }
        if kind == UICollectionView.elementKindSectionHeader {
            guard let header = collectionView.dequeueReusableSupplementaryView(ofKind: kind, withReuseIdentifier: XMLHistoryHeaderView.identifier, for: indexPath) as? XMLHistoryHeaderView else { return collectionView.emptyReusableView(kind, at: indexPath) }
            header.updateWidgetInfo(section.type.rawValue)
            return header
        }
        return collectionView.emptyReusableView(kind, at: indexPath)
    }
}

// MARK: - UICollectionViewDelegate Method
extension XMAudioHistoryViewController: UICollectionViewDelegate {
    public func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        guard let item = self.viewModel.sectionItems[indexPath.section, true]?.datas[indexPath.row, true] else { return }
        // 声音转化
        guard case .history(let track) = item, let trackId = track.childId?.int64Value else { return }
        
        // 青少模式下添加判断ageLevel字段
        let childrenMode = mineRouterBridge("")?.isChildrenMode ?? false
        if !childrenMode {
            guard track.type == .sound || track.type == .hideAlbum else {
                UIView.showInfoMessage("极速版无法播放该节目，可使用喜马拉雅完整版播放哦", hideAfterDelay: 1)
                return
            }
            let albumId: UInt = track.itemId?.uintValue ?? 0
            self.viewModel.evaluateAlbumIsSoldOut(albumId: albumId, trackId: trackId) { (isValid, tracks, index) in
                guard isValid else { UIView.showInfoMessage("该内容已被下架", hideAfterDelay: 1);return }
                let isAsc = (CacheManager.shared.object(forKey: ascKey(withAlbumId: albumId)) as? NSNumber)?.boolValue
                if tracks.count > 0 {
                    RouterBridge(nil).playWithPlayables(tracks, start: index, autoPlay: true, isAsc: isAsc)
                } else {
                    RouterBridge(nil).playWithPlayables([trackId], start: 0, autoPlay: true, isAsc: isAsc)
                }
                RouterBridge(nil).toPlayPage(fromNavel: false)
            }
        } else {
            if case .history(let track) = item {
                self.view.showXMLLoading()
                self.viewModel.getAgeLevel(trackId: track.childId?.int64Value ?? 0, albumId: track.itemId?.uintValue ?? 0) { [weak self] (ageLevel) in
                    self?.view.hideXMLLoading()
                    if ageLevel == 1 {
                        mineRouterBridge(nil)?.showChildrenAlert(withType: 1, in: nil, closeHandle: nil, existLiveHandle: nil)
                    } else {
                        guard track.type == .sound || track.type == .hideAlbum else {
                            UIView.showInfoMessage("极速版无法播放该节目，可使用喜马拉雅完整版播放哦", hideAfterDelay: 1)
                            return
                        }
                        let albumId: UInt = track.itemId?.uintValue ?? 0
                        self?.viewModel.evaluateAlbumIsSoldOut(albumId: albumId, trackId: trackId) { (isValid, tracks, index) in
                            guard isValid else { UIView.showInfoMessage("该内容已被下架", hideAfterDelay: 1);return }
                            let isAsc = (CacheManager.shared.object(forKey: ascKey(withAlbumId: albumId)) as? NSNumber)?.boolValue
                            if tracks.count > 0 {
                                RouterBridge(nil).playWithPlayables(tracks, start: index, autoPlay: true, isAsc: isAsc)
                            } else {
                                RouterBridge(nil).playWithPlayables([trackId], start: 0, autoPlay: true, isAsc: isAsc)
                            }
                            RouterBridge(nil).toPlayPage(fromNavel: false)
                        }
                    }
                }
            }
        }
    }
}

// MARK: - UICollectionViewDelegateFlowLayout Method
extension XMAudioHistoryViewController: UICollectionViewDelegateFlowLayout {
    
    public func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        guard let item = self.viewModel.sectionItems[indexPath.section, true]?.datas[indexPath.row, true] else { return .zero }
        if case .history(let model) = item {
            var cellHeight: CGFloat = 0.0
            let heightMapKey: String = "\(model.childId?.int64Value ?? 0)" + model.itemTitle
            if let height = self.viewModel.cellHeightMap[heightMapKey] {
                cellHeight = height
            } else {
                cellHeight = XMLHistoryCell.heightFor(model.itemTitle)
                self.viewModel.cellHeightMap[heightMapKey] = cellHeight
            }
            if model.type == .hideAlbum {
                cellHeight = 101
            }
            return CGSize(width: UIScreen.main.bounds.width, height: cellHeight)
        } else if case .ad(let model) = item {
            return CGSize(width: UIScreen.main.bounds.width, height: model.hasAd ? XMLHistoryAdCell.height(collectionView.width) : 0.01)
        }
        return CGSize.zero
    }
    
    public func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, referenceSizeForHeaderInSection section: Int) -> CGSize {
        guard self.viewModel.sectionItems[section, true] != nil else { return .zero }
        return CGSize(width: UIScreen.main.bounds.width, height: XMLHistoryHeaderView.height)
    }
}

// MARK: - 网络 Method
extension XMAudioHistoryViewController {
    // 获取播放记录信息
    @objc fileprivate func loadHistoryListInfo() {
        let currUid: UInt = XMSettings.shared().currentUid ?? 0
        if self.curHistoryUid != currUid {
            displayCollectionView.contentOffset = .zero
        }
        self.isNetworking = true
        self.viewModel.loadHistoryInfo { [weak self] (_) in
            guard let wself = self else { return }
            wself.isNetworking = false
            wself.reloadWidgetIfNeed()
            wself.loadHistoryAdInfo(isPreLoadNext: true)
        }
        self.curHistoryUid = currUid
    }
    
    // 清空现有广告配置
    @objc fileprivate func clearAllHistoryAD() {
        self.viewModel.historyAd.removeAll()
    }
    
    // 获取播放记录广告信息
    @objc fileprivate func loadHistoryAdInfo(_ key: Int = 1, isPreLoadNext: Bool = false) {
        self.isNetworking = true
        self.viewModel.loadHistoryAdInfo(key) { [weak self] (result) in
            guard let wself = self else { return }
            wself.isNetworking = false
            guard result else { return } // 请求广告成功
            // 1 界面进行逻辑刷新
            DispatchQueue.main.async { [weak wself] in wself?.reloadADWidgetIfNeed(key) }
            // 2 根据逻辑预加载下一条
            if isPreLoadNext { wself.loadHistoryAdInfo(key + 1, isPreLoadNext: false) }
        }
    }
    
    // 从网络获取历史播放信息并更新本地
    fileprivate func updateHistoryListFromServer() {
        XMPhcManager.sharedManager.config(size: 100)
        XMPhcManager.sharedManager.requestCloudHistoryList()
    }
}

// MARK: - 异常界面处理 Method
extension XMAudioHistoryViewController {
    public override var isShouldShowEmptyView: Bool {
        let isEmpty: Bool = (self.viewModel.sectionItems.count == 0)
        return isEmpty
    }
    
    public override var isShouldShowNoNetView: Bool {
        return false
    }
    
    public override func emptyViewReload() {
        self.loadHistoryListInfo()
    }
    
    public override func updateEmptyView() {
        self.emptyView?.isHidden = !(isShouldShowNoNetView || isShouldShowEmptyView)
        self.emptyView?.anchorPoint = CGPoint(x: 0.5, y: 0.35)
        self.emptyView?.imageView.image = (self.isNetworking ? XMBaseEmptyType.loading : XMBaseEmptyType.default).getImage()
        self.emptyView?.textLabel.text = (self.isNetworking ? "努力加载中..." : "没有收听过节目")
        super.updateEmptyView()
    }
}

// MARK: - 辅助 Method
extension XMAudioHistoryViewController {
    // 加载基础控件布局
    fileprivate func setupBaseUI() {
        self.view.backgroundColor = .white
        var topMargin: CGFloat = 0
        if hasTitle {
            topMargin = UIScreen.naviBarHeight
            self.view.addSubview(self.naviBar)
            self.naviBar.autoLayout(shouldStatusBar: true)
        }
        self.view.addSubview(self.displayCollectionView)
        self.displayCollectionView.snp.remakeConstraints {
            $0.left.right.bottom.equalToSuperview()
            $0.top.equalToSuperview().offset(topMargin)
        }
    }
    
    // 重新加载控件
    fileprivate func reloadWidgetIfNeed() {
        // 顶部请求按钮显示逻辑
        if hasTitle {
            self.emptyButton.isHidden = self.viewModel.sectionItems.count == 0
        }
        // 刷新界面
        self.updateEmptyViewAndNoNetView()
        self.displayCollectionView.reloadData()
        // 隐藏或显示清空全部按钮
        hideClearButtonAction?(self.viewModel.sectionItems.isEmpty)
    }
    
    // 重新加载控件
    fileprivate func reloadADWidgetIfNeed(_ key: Int) {
        //        // 保证待刷新的广告不滚动，否则不刷新
        //        guard self.isScrolling == false else { return }
        //        let isVisible: Bool = self.displayCollectionView.visibleCells.contains {
        //            if let cell = $0 as? XMLHistoryAdCell, cell.cellKey == key { return true } else { return false }
        //        }
        //        // 保证待刷新的广告在界面内，否则不刷新
        //        guard isVisible else { return }
        
        self.reloadWidgetIfNeed()
    }
    
    // 绑定通知响应逻辑
    fileprivate func bindNotificationActionHandle() {
        // 注册历史更新通知
        NotificationCenter.default.addObserver(self, selector: #selector(loadHistoryListInfo), name: kXMPhcUpdateFinishedNotification, object: nil)
        // 登录成功更新通知
        NotificationCenter.default.addObserver(self, selector: #selector(clearAllHistoryAD), name: .XMLoginSuccessNotification, object: nil)
        // 注销成功更新通知
        NotificationCenter.default.addObserver(self, selector: #selector(clearAllHistoryAD), name: .XMLogoutSuccessNotification, object: nil)
    }
    
    // 自定义可视化埋点数据
    fileprivate func customLoggerPageInfo() {
        self.prePage  = XMUBTManualLogger.shareInstance().currentPageName ?? ""
        self.pageName = "historyPage"
    }
}

// MARK: - 业务 Method
extension XMAudioHistoryViewController {
    // 清空
    @objc func clearHistoryAction() {
        let alert = XMRemoveBookAlert(title: "确认清空播放历史？", content: nil, confirmText: "确定", cancelText: "取消")
        alert.showIn(view: nil, animation: true, complection: nil)
        XMEventLog.logEventWithId(39439, serviceId: "dialogView", properties: ["currPage": "historyPage"])
        alert.cancelAction = {
            XMEventLog.logEventWithId(39441, serviceId: "dialogClick", properties: ["currPage": "historyPage"])
        }
        alert.confirmAction = { [weak self] in
            guard let self = self else {return}
            XMEventLog.logEventWithId(39440, serviceId: "dialogClick", properties: ["currPage": "historyPage"])
            self.viewModel.removeAllHistory { [weak self] (result) in
                guard let self = self else { return }
                if result {
                    self.reloadWidgetIfNeed()
                } else {
                    UIView.showInfoMessage("清空失败")
                }
            }
            alert.hidden(animation: true, complection: nil)
        }
    }
    
    // 移除指定记录
    fileprivate func removeHistory(at indexPath: IndexPath) {
        let alert = XMRemoveBookAlert(title: "确认删除该条播放记录？", content: nil, confirmText: "确定", cancelText: "取消")
        alert.showIn(view: nil, animation: true, complection: nil)
        alert.confirmAction = { [weak self] in
            guard let self = self else {return}
            self.viewModel.removeHistory(indexPath) { [weak self] (result) in
                guard let self = self else { return }
                if result {
                    self.reloadWidgetIfNeed()
                } else {
                    UIView.showInfoMessage("删除失败")
                }
            }
            alert.hidden(animation: true, complection: nil)
        }
    }
    
    // 推荐页跳转
    fileprivate func similarAlbumPage(at indexPath: IndexPath) {
        guard let item = self.viewModel.sectionItems[indexPath.section, true]?.datas[indexPath.row, true] else { return }
        // 声音转化
        guard case .history(let track) = item, let trackId = track.childId?.uintValue else { return }
        
        // 收费处理
        guard track.paid == false || track.isFree || track.isVipFree || track.vipFreeType.intValue == 1 else {
            UIView.showInfoMessage("极速版无法为该节目查找相似节目", hideAfterDelay: 1)
            return
        }
        // 推荐
        RouterBridge(nil).getRecommend(withTrackId: trackId) { [weak self] (viewController) in
            guard let wself = self, let recomVC = viewController else { return }
            wself.push(recomVC)
        }
    }
}
