---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Documents/tingliteproject/tingLite/DerivedData/tingLite/Build/Intermediates.noindex/ArchiveIntermediates/tingLite/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/ObjcExceptionBridging.framework/ObjcExceptionBridging'
relocations:
  - { offset: 0x33, size: 0x8, addend: 0x0, symName: _ObjcExceptionBridgingVersionString, symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x0 }
  - { offset: 0x67, size: 0x8, addend: 0x0, symName: _ObjcExceptionBridgingVersionNumber, symObjAddr: 0x38, symBinAddr: 0x4038, symSize: 0x0 }
...
