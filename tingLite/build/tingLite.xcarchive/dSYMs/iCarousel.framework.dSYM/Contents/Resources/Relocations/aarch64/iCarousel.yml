---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Documents/tingliteproject/tingLite/DerivedData/tingLite/Build/Intermediates.noindex/ArchiveIntermediates/tingLite/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/iCarousel.framework/iCarousel'
relocations:
  - { offset: 0x58FA6, size: 0x8, addend: 0x0, symName: _iCarouselVersionString, symObjAddr: 0x0, symBinAddr: 0xB3E0, symSize: 0x0 }
  - { offset: 0x58FDB, size: 0x8, addend: 0x0, symName: _iCarouselVersionNumber, symObjAddr: 0x28, symBinAddr: 0xB408, symSize: 0x0 }
  - { offset: 0x59018, size: 0x8, addend: 0x0, symName: '-[NSObject(iCarousel) numberOfPlaceholdersInCarousel:]', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x8 }
  - { offset: 0x59680, size: 0x8, addend: 0x0, symName: '-[NSObject(iCarousel) numberOfPlaceholdersInCarousel:]', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x8 }
  - { offset: 0x596BF, size: 0x8, addend: 0x0, symName: '-[NSObject(iCarousel) carouselWillBeginScrollingAnimation:]', symObjAddr: 0x8, symBinAddr: 0x4008, symSize: 0x4 }
  - { offset: 0x596FA, size: 0x8, addend: 0x0, symName: '-[NSObject(iCarousel) carouselDidEndScrollingAnimation:]', symObjAddr: 0xC, symBinAddr: 0x400C, symSize: 0x4 }
  - { offset: 0x59735, size: 0x8, addend: 0x0, symName: '-[NSObject(iCarousel) carouselDidScroll:]', symObjAddr: 0x10, symBinAddr: 0x4010, symSize: 0x4 }
  - { offset: 0x59770, size: 0x8, addend: 0x0, symName: '-[NSObject(iCarousel) carouselCurrentItemIndexDidChange:]', symObjAddr: 0x14, symBinAddr: 0x4014, symSize: 0x4 }
  - { offset: 0x597AB, size: 0x8, addend: 0x0, symName: '-[NSObject(iCarousel) carouselWillBeginDragging:]', symObjAddr: 0x18, symBinAddr: 0x4018, symSize: 0x4 }
  - { offset: 0x597E6, size: 0x8, addend: 0x0, symName: '-[NSObject(iCarousel) carouselDidEndDragging:willDecelerate:]', symObjAddr: 0x1C, symBinAddr: 0x401C, symSize: 0x4 }
  - { offset: 0x5982D, size: 0x8, addend: 0x0, symName: '-[NSObject(iCarousel) carouselWillBeginDecelerating:]', symObjAddr: 0x20, symBinAddr: 0x4020, symSize: 0x4 }
  - { offset: 0x59868, size: 0x8, addend: 0x0, symName: '-[NSObject(iCarousel) carouselDidEndDecelerating:]', symObjAddr: 0x24, symBinAddr: 0x4024, symSize: 0x4 }
  - { offset: 0x598A3, size: 0x8, addend: 0x0, symName: '-[NSObject(iCarousel) carousel:shouldSelectItemAtIndex:]', symObjAddr: 0x28, symBinAddr: 0x4028, symSize: 0x8 }
  - { offset: 0x598EE, size: 0x8, addend: 0x0, symName: '-[NSObject(iCarousel) carousel:didSelectItemAtIndex:]', symObjAddr: 0x30, symBinAddr: 0x4030, symSize: 0x4 }
  - { offset: 0x59935, size: 0x8, addend: 0x0, symName: '-[NSObject(iCarousel) carouselItemWidth:]', symObjAddr: 0x34, symBinAddr: 0x4034, symSize: 0x8 }
  - { offset: 0x59974, size: 0x8, addend: 0x0, symName: '-[NSObject(iCarousel) carousel:itemTransformForOffset:baseTransform:]', symObjAddr: 0x3C, symBinAddr: 0x403C, symSize: 0x24 }
  - { offset: 0x599CE, size: 0x8, addend: 0x0, symName: '-[NSObject(iCarousel) carousel:valueForOption:withDefault:]', symObjAddr: 0x60, symBinAddr: 0x4060, symSize: 0x4 }
  - { offset: 0x59A28, size: 0x8, addend: 0x0, symName: '-[iCarousel setUp]', symObjAddr: 0x64, symBinAddr: 0x4064, symSize: 0x1F4 }
  - { offset: 0x59A7B, size: 0x8, addend: 0x0, symName: '-[iCarousel initWithCoder:]', symObjAddr: 0x258, symBinAddr: 0x4258, symSize: 0x74 }
  - { offset: 0x59AC2, size: 0x8, addend: 0x0, symName: '-[iCarousel initWithFrame:]', symObjAddr: 0x2CC, symBinAddr: 0x42CC, symSize: 0x50 }
  - { offset: 0x59B05, size: 0x8, addend: 0x0, symName: '-[iCarousel dealloc]', symObjAddr: 0x31C, symBinAddr: 0x431C, symSize: 0x44 }
  - { offset: 0x59B38, size: 0x8, addend: 0x0, symName: '-[iCarousel setDataSource:]', symObjAddr: 0x360, symBinAddr: 0x4360, symSize: 0x80 }
  - { offset: 0x59B7B, size: 0x8, addend: 0x0, symName: '-[iCarousel setDelegate:]', symObjAddr: 0x3E0, symBinAddr: 0x43E0, symSize: 0x9C }
  - { offset: 0x59BBE, size: 0x8, addend: 0x0, symName: '-[iCarousel setType:]', symObjAddr: 0x47C, symBinAddr: 0x447C, symSize: 0x20 }
  - { offset: 0x59BFD, size: 0x8, addend: 0x0, symName: '-[iCarousel setVertical:]', symObjAddr: 0x49C, symBinAddr: 0x449C, symSize: 0x20 }
  - { offset: 0x59C40, size: 0x8, addend: 0x0, symName: '-[iCarousel setScrollOffset:]', symObjAddr: 0x4BC, symBinAddr: 0x44BC, symSize: 0x80 }
  - { offset: 0x59C85, size: 0x8, addend: 0x0, symName: '-[iCarousel setCurrentItemIndex:]', symObjAddr: 0x53C, symBinAddr: 0x453C, symSize: 0x8 }
  - { offset: 0x59CC6, size: 0x8, addend: 0x0, symName: '-[iCarousel setPerspective:]', symObjAddr: 0x544, symBinAddr: 0x4544, symSize: 0x10 }
  - { offset: 0x59D08, size: 0x8, addend: 0x0, symName: '-[iCarousel setViewpointOffset:]', symObjAddr: 0x554, symBinAddr: 0x4554, symSize: 0x28 }
  - { offset: 0x59D4B, size: 0x8, addend: 0x0, symName: '-[iCarousel setContentOffset:]', symObjAddr: 0x57C, symBinAddr: 0x457C, symSize: 0x28 }
  - { offset: 0x59D8E, size: 0x8, addend: 0x0, symName: '-[iCarousel setAutoscroll:]', symObjAddr: 0x5A4, symBinAddr: 0x45A4, symSize: 0x1C }
  - { offset: 0x59DD0, size: 0x8, addend: 0x0, symName: '-[iCarousel pushAnimationState:]', symObjAddr: 0x5C0, symBinAddr: 0x45C0, symSize: 0x30 }
  - { offset: 0x59E11, size: 0x8, addend: 0x0, symName: '-[iCarousel popAnimationState]', symObjAddr: 0x5F0, symBinAddr: 0x45F0, symSize: 0xC }
  - { offset: 0x59E41, size: 0x8, addend: 0x0, symName: '-[iCarousel indexesForVisibleItems]', symObjAddr: 0x5FC, symBinAddr: 0x45FC, symSize: 0x58 }
  - { offset: 0x59E79, size: 0x8, addend: 0x0, symName: '-[iCarousel visibleItemViews]', symObjAddr: 0x654, symBinAddr: 0x4654, symSize: 0x88 }
  - { offset: 0x59EC2, size: 0x8, addend: 0x0, symName: '-[iCarousel itemViewAtIndex:]', symObjAddr: 0x6DC, symBinAddr: 0x46DC, symSize: 0x60 }
  - { offset: 0x59F0B, size: 0x8, addend: 0x0, symName: '-[iCarousel currentItemView]', symObjAddr: 0x73C, symBinAddr: 0x473C, symSize: 0x28 }
  - { offset: 0x59F43, size: 0x8, addend: 0x0, symName: '-[iCarousel indexOfItemView:]', symObjAddr: 0x764, symBinAddr: 0x4764, symSize: 0xCC }
  - { offset: 0x59F9D, size: 0x8, addend: 0x0, symName: '-[iCarousel indexOfItemViewOrSubview:]', symObjAddr: 0x830, symBinAddr: 0x4830, symSize: 0xB8 }
  - { offset: 0x59FF7, size: 0x8, addend: 0x0, symName: '-[iCarousel itemViewAtPoint:]', symObjAddr: 0x8E8, symBinAddr: 0x48E8, symSize: 0x1C4 }
  - { offset: 0x5A074, size: 0x8, addend: 0x0, symName: _compareViewDepth, symObjAddr: 0xAAC, symBinAddr: 0x4AAC, symSize: 0x2A4 }
  - { offset: 0x5A1BF, size: 0x8, addend: 0x0, symName: '-[iCarousel setItemView:forIndex:]', symObjAddr: 0xD50, symBinAddr: 0x4D50, symSize: 0x78 }
  - { offset: 0x5A215, size: 0x8, addend: 0x0, symName: '-[iCarousel removeViewAtIndex:]', symObjAddr: 0xDC8, symBinAddr: 0x4DC8, symSize: 0x1E4 }
  - { offset: 0x5A2A3, size: 0x8, addend: 0x0, symName: '-[iCarousel insertView:atIndex:]', symObjAddr: 0xFAC, symBinAddr: 0x4FAC, symSize: 0x1F4 }
  - { offset: 0x5A342, size: 0x8, addend: 0x0, symName: '-[iCarousel alphaForItemWithOffset:]', symObjAddr: 0x11A0, symBinAddr: 0x51A0, symSize: 0xFC }
  - { offset: 0x5A40F, size: 0x8, addend: 0x0, symName: '-[iCarousel valueForOption:withDefault:]', symObjAddr: 0x129C, symBinAddr: 0x529C, symSize: 0x84 }
  - { offset: 0x5A4A9, size: 0x8, addend: 0x0, symName: '-[iCarousel transformForItemViewWithOffset:]', symObjAddr: 0x1320, symBinAddr: 0x5320, symSize: 0xBC4 }
  - { offset: 0x5A9CA, size: 0x8, addend: 0x0, symName: '-[iCarousel depthSortViews]', symObjAddr: 0x1EE4, symBinAddr: 0x5EE4, symSize: 0x164 }
  - { offset: 0x5AA16, size: 0x8, addend: 0x0, symName: '-[iCarousel offsetForItemAtIndex:]', symObjAddr: 0x2048, symBinAddr: 0x6048, symSize: 0x68 }
  - { offset: 0x5AA6C, size: 0x8, addend: 0x0, symName: '-[iCarousel containView:]', symObjAddr: 0x20B0, symBinAddr: 0x60B0, symSize: 0x14C }
  - { offset: 0x5AAD7, size: 0x8, addend: 0x0, symName: '-[iCarousel transformItemView:atIndex:]', symObjAddr: 0x21FC, symBinAddr: 0x61FC, symSize: 0x3D8 }
  - { offset: 0x5ABD9, size: 0x8, addend: 0x0, symName: '-[iCarousel layoutSubviews]', symObjAddr: 0x25D4, symBinAddr: 0x65D4, symSize: 0x60 }
  - { offset: 0x5AC0D, size: 0x8, addend: 0x0, symName: '-[iCarousel transformItemViews]', symObjAddr: 0x2634, symBinAddr: 0x6634, symSize: 0x140 }
  - { offset: 0x5AC82, size: 0x8, addend: 0x0, symName: '-[iCarousel updateItemWidth]', symObjAddr: 0x2774, symBinAddr: 0x6774, symSize: 0xC4 }
  - { offset: 0x5ACD6, size: 0x8, addend: 0x0, symName: '-[iCarousel updateNumberOfVisibleItems]', symObjAddr: 0x2838, symBinAddr: 0x6838, symSize: 0x210 }
  - { offset: 0x5AEAC, size: 0x8, addend: 0x0, symName: '-[iCarousel circularCarouselItemCount]', symObjAddr: 0x2A48, symBinAddr: 0x6A48, symSize: 0x114 }
  - { offset: 0x5AFD4, size: 0x8, addend: 0x0, symName: '-[iCarousel layOutItemViews]', symObjAddr: 0x2B5C, symBinAddr: 0x6B5C, symSize: 0x218 }
  - { offset: 0x5B008, size: 0x8, addend: 0x0, symName: '-[iCarousel queueItemView:]', symObjAddr: 0x2D74, symBinAddr: 0x6D74, symSize: 0x18 }
  - { offset: 0x5B04B, size: 0x8, addend: 0x0, symName: '-[iCarousel queuePlaceholderView:]', symObjAddr: 0x2D8C, symBinAddr: 0x6D8C, symSize: 0x18 }
  - { offset: 0x5B08E, size: 0x8, addend: 0x0, symName: '-[iCarousel dequeueItemView]', symObjAddr: 0x2DA4, symBinAddr: 0x6DA4, symSize: 0x54 }
  - { offset: 0x5B0D7, size: 0x8, addend: 0x0, symName: '-[iCarousel dequeuePlaceholderView]', symObjAddr: 0x2DF8, symBinAddr: 0x6DF8, symSize: 0x54 }
  - { offset: 0x5B120, size: 0x8, addend: 0x0, symName: '-[iCarousel loadViewAtIndex:withContainerView:]', symObjAddr: 0x2E4C, symBinAddr: 0x6E4C, symSize: 0x36C }
  - { offset: 0x5B216, size: 0x8, addend: 0x0, symName: '-[iCarousel loadViewAtIndex:]', symObjAddr: 0x31B8, symBinAddr: 0x71B8, symSize: 0x8 }
  - { offset: 0x5B25B, size: 0x8, addend: 0x0, symName: '-[iCarousel loadUnloadViews]', symObjAddr: 0x31C0, symBinAddr: 0x71C0, symSize: 0x3A8 }
  - { offset: 0x5B3EE, size: 0x8, addend: 0x0, symName: '-[iCarousel reloadData]', symObjAddr: 0x3568, symBinAddr: 0x7568, symSize: 0x26C }
  - { offset: 0x5B45A, size: 0x8, addend: 0x0, symName: '-[iCarousel clampedIndex:]', symObjAddr: 0x37D4, symBinAddr: 0x77D4, symSize: 0x60 }
  - { offset: 0x5B54C, size: 0x8, addend: 0x0, symName: '-[iCarousel clampedOffset:]', symObjAddr: 0x3834, symBinAddr: 0x7834, symSize: 0x60 }
  - { offset: 0x5B63B, size: 0x8, addend: 0x0, symName: '-[iCarousel currentItemIndex]', symObjAddr: 0x3894, symBinAddr: 0x7894, symSize: 0x14 }
  - { offset: 0x5B671, size: 0x8, addend: 0x0, symName: '-[iCarousel minScrollDistanceFromIndex:toIndex:]', symObjAddr: 0x38A8, symBinAddr: 0x78A8, symSize: 0x5C }
  - { offset: 0x5B786, size: 0x8, addend: 0x0, symName: '-[iCarousel minScrollDistanceFromOffset:toOffset:]', symObjAddr: 0x3904, symBinAddr: 0x7904, symSize: 0x5C }
  - { offset: 0x5B8A5, size: 0x8, addend: 0x0, symName: '-[iCarousel scrollByOffset:duration:]', symObjAddr: 0x3960, symBinAddr: 0x7960, symSize: 0xFC }
  - { offset: 0x5B915, size: 0x8, addend: 0x0, symName: '-[iCarousel scrollToOffset:duration:]', symObjAddr: 0x3A5C, symBinAddr: 0x7A5C, symSize: 0x44 }
  - { offset: 0x5B98B, size: 0x8, addend: 0x0, symName: '-[iCarousel scrollByNumberOfItems:duration:]', symObjAddr: 0x3AA0, symBinAddr: 0x7AA0, symSize: 0xA8 }
  - { offset: 0x5BA59, size: 0x8, addend: 0x0, symName: '-[iCarousel scrollToItemAtIndex:duration:]', symObjAddr: 0x3B48, symBinAddr: 0x7B48, symSize: 0xC }
  - { offset: 0x5BAAB, size: 0x8, addend: 0x0, symName: '-[iCarousel scrollToItemAtIndex:animated:]', symObjAddr: 0x3B54, symBinAddr: 0x7B54, symSize: 0x18 }
  - { offset: 0x5BB00, size: 0x8, addend: 0x0, symName: '-[iCarousel removeItemAtIndex:animated:]', symObjAddr: 0x3B6C, symBinAddr: 0x7B6C, symSize: 0x2AC }
  - { offset: 0x5BB67, size: 0x8, addend: 0x0, symName: '-[iCarousel insertItemAtIndex:animated:]', symObjAddr: 0x3E18, symBinAddr: 0x7E18, symSize: 0x1A0 }
  - { offset: 0x5BBDE, size: 0x8, addend: 0x0, symName: '-[iCarousel reloadItemAtIndex:animated:]', symObjAddr: 0x3FB8, symBinAddr: 0x7FB8, symSize: 0x120 }
  - { offset: 0x5BC65, size: 0x8, addend: 0x0, symName: '-[iCarousel startAnimation]', symObjAddr: 0x40D8, symBinAddr: 0x80D8, symSize: 0xEC }
  - { offset: 0x5BC99, size: 0x8, addend: 0x0, symName: '-[iCarousel stopAnimation]', symObjAddr: 0x41C4, symBinAddr: 0x81C4, symSize: 0x34 }
  - { offset: 0x5BCCD, size: 0x8, addend: 0x0, symName: '-[iCarousel decelerationDistance]', symObjAddr: 0x41F8, symBinAddr: 0x81F8, symSize: 0x3C }
  - { offset: 0x5BD10, size: 0x8, addend: 0x0, symName: '-[iCarousel shouldDecelerate]', symObjAddr: 0x4234, symBinAddr: 0x8234, symSize: 0x4C }
  - { offset: 0x5BD8A, size: 0x8, addend: 0x0, symName: '-[iCarousel shouldScroll]', symObjAddr: 0x4280, symBinAddr: 0x8280, symSize: 0x64 }
  - { offset: 0x5BDF9, size: 0x8, addend: 0x0, symName: '-[iCarousel startDecelerating]', symObjAddr: 0x42E4, symBinAddr: 0x82E4, symSize: 0x174 }
  - { offset: 0x5BF03, size: 0x8, addend: 0x0, symName: '-[iCarousel easeInOut:]', symObjAddr: 0x4458, symBinAddr: 0x8458, symSize: 0x54 }
  - { offset: 0x5BFA7, size: 0x8, addend: 0x0, symName: '-[iCarousel step]', symObjAddr: 0x44AC, symBinAddr: 0x84AC, symSize: 0x438 }
  - { offset: 0x5C302, size: 0x8, addend: 0x0, symName: '-[iCarousel didMoveToSuperview]', symObjAddr: 0x48E4, symBinAddr: 0x88E4, symSize: 0x44 }
  - { offset: 0x5C336, size: 0x8, addend: 0x0, symName: '-[iCarousel didScroll]', symObjAddr: 0x4928, symBinAddr: 0x8928, symSize: 0x20C }
  - { offset: 0x5C45B, size: 0x8, addend: 0x0, symName: '-[iCarousel viewOrSuperviewIndex:]', symObjAddr: 0x4B34, symBinAddr: 0x8B34, symSize: 0xA4 }
  - { offset: 0x5C4B5, size: 0x8, addend: 0x0, symName: '-[iCarousel viewOrSuperview:implementsSelector:]', symObjAddr: 0x4BD8, symBinAddr: 0x8BD8, symSize: 0x144 }
  - { offset: 0x5C61E, size: 0x8, addend: 0x0, symName: '-[iCarousel viewOrSuperview:ofClass:]', symObjAddr: 0x4D1C, symBinAddr: 0x8D1C, symSize: 0xC4 }
  - { offset: 0x5C678, size: 0x8, addend: 0x0, symName: '-[iCarousel gestureRecognizer:shouldReceiveTouch:]', symObjAddr: 0x4DE0, symBinAddr: 0x8DE0, symSize: 0x378 }
  - { offset: 0x5C712, size: 0x8, addend: 0x0, symName: '-[iCarousel gestureRecognizerShouldBegin:]', symObjAddr: 0x5158, symBinAddr: 0x9158, symSize: 0x9C }
  - { offset: 0x5C79C, size: 0x8, addend: 0x0, symName: '-[iCarousel didTap:]', symObjAddr: 0x51F4, symBinAddr: 0x91F4, symSize: 0x194 }
  - { offset: 0x5C803, size: 0x8, addend: 0x0, symName: '-[iCarousel didPan:]', symObjAddr: 0x5388, symBinAddr: 0x9388, symSize: 0x3D4 }
  - { offset: 0x5C939, size: 0x8, addend: 0x0, symName: '-[iCarousel dataSource]', symObjAddr: 0x575C, symBinAddr: 0x975C, symSize: 0x20 }
  - { offset: 0x5C970, size: 0x8, addend: 0x0, symName: '-[iCarousel delegate]', symObjAddr: 0x577C, symBinAddr: 0x977C, symSize: 0x20 }
  - { offset: 0x5C9A7, size: 0x8, addend: 0x0, symName: '-[iCarousel type]', symObjAddr: 0x579C, symBinAddr: 0x979C, symSize: 0x10 }
  - { offset: 0x5C9DE, size: 0x8, addend: 0x0, symName: '-[iCarousel perspective]', symObjAddr: 0x57AC, symBinAddr: 0x97AC, symSize: 0x10 }
  - { offset: 0x5CA13, size: 0x8, addend: 0x0, symName: '-[iCarousel decelerationRate]', symObjAddr: 0x57BC, symBinAddr: 0x97BC, symSize: 0x10 }
  - { offset: 0x5CA48, size: 0x8, addend: 0x0, symName: '-[iCarousel setDecelerationRate:]', symObjAddr: 0x57CC, symBinAddr: 0x97CC, symSize: 0x10 }
  - { offset: 0x5CA86, size: 0x8, addend: 0x0, symName: '-[iCarousel scrollSpeed]', symObjAddr: 0x57DC, symBinAddr: 0x97DC, symSize: 0x10 }
  - { offset: 0x5CABB, size: 0x8, addend: 0x0, symName: '-[iCarousel setScrollSpeed:]', symObjAddr: 0x57EC, symBinAddr: 0x97EC, symSize: 0x10 }
  - { offset: 0x5CAF9, size: 0x8, addend: 0x0, symName: '-[iCarousel bounceDistance]', symObjAddr: 0x57FC, symBinAddr: 0x97FC, symSize: 0x10 }
  - { offset: 0x5CB2E, size: 0x8, addend: 0x0, symName: '-[iCarousel setBounceDistance:]', symObjAddr: 0x580C, symBinAddr: 0x980C, symSize: 0x10 }
  - { offset: 0x5CB6C, size: 0x8, addend: 0x0, symName: '-[iCarousel isScrollEnabled]', symObjAddr: 0x581C, symBinAddr: 0x981C, symSize: 0x10 }
  - { offset: 0x5CBA3, size: 0x8, addend: 0x0, symName: '-[iCarousel setScrollEnabled:]', symObjAddr: 0x582C, symBinAddr: 0x982C, symSize: 0x10 }
  - { offset: 0x5CBDE, size: 0x8, addend: 0x0, symName: '-[iCarousel isPagingEnabled]', symObjAddr: 0x583C, symBinAddr: 0x983C, symSize: 0x10 }
  - { offset: 0x5CC15, size: 0x8, addend: 0x0, symName: '-[iCarousel setPagingEnabled:]', symObjAddr: 0x584C, symBinAddr: 0x984C, symSize: 0x10 }
  - { offset: 0x5CC50, size: 0x8, addend: 0x0, symName: '-[iCarousel isVertical]', symObjAddr: 0x585C, symBinAddr: 0x985C, symSize: 0x10 }
  - { offset: 0x5CC87, size: 0x8, addend: 0x0, symName: '-[iCarousel isWrapEnabled]', symObjAddr: 0x586C, symBinAddr: 0x986C, symSize: 0x10 }
  - { offset: 0x5CCBE, size: 0x8, addend: 0x0, symName: '-[iCarousel setWrapEnabled:]', symObjAddr: 0x587C, symBinAddr: 0x987C, symSize: 0x10 }
  - { offset: 0x5CCF9, size: 0x8, addend: 0x0, symName: '-[iCarousel bounces]', symObjAddr: 0x588C, symBinAddr: 0x988C, symSize: 0x10 }
  - { offset: 0x5CD30, size: 0x8, addend: 0x0, symName: '-[iCarousel setBounces:]', symObjAddr: 0x589C, symBinAddr: 0x989C, symSize: 0x10 }
  - { offset: 0x5CD6B, size: 0x8, addend: 0x0, symName: '-[iCarousel scrollOffset]', symObjAddr: 0x58AC, symBinAddr: 0x98AC, symSize: 0x10 }
  - { offset: 0x5CDA0, size: 0x8, addend: 0x0, symName: '-[iCarousel offsetMultiplier]', symObjAddr: 0x58BC, symBinAddr: 0x98BC, symSize: 0x10 }
  - { offset: 0x5CDD5, size: 0x8, addend: 0x0, symName: '-[iCarousel setOffsetMultiplier:]', symObjAddr: 0x58CC, symBinAddr: 0x98CC, symSize: 0x10 }
  - { offset: 0x5CE13, size: 0x8, addend: 0x0, symName: '-[iCarousel contentOffset]', symObjAddr: 0x58DC, symBinAddr: 0x98DC, symSize: 0x14 }
  - { offset: 0x5CE48, size: 0x8, addend: 0x0, symName: '-[iCarousel viewpointOffset]', symObjAddr: 0x58F0, symBinAddr: 0x98F0, symSize: 0x14 }
  - { offset: 0x5CE7D, size: 0x8, addend: 0x0, symName: '-[iCarousel numberOfItems]', symObjAddr: 0x5904, symBinAddr: 0x9904, symSize: 0x10 }
  - { offset: 0x5CEB4, size: 0x8, addend: 0x0, symName: '-[iCarousel numberOfPlaceholders]', symObjAddr: 0x5914, symBinAddr: 0x9914, symSize: 0x10 }
  - { offset: 0x5CEEB, size: 0x8, addend: 0x0, symName: '-[iCarousel numberOfVisibleItems]', symObjAddr: 0x5924, symBinAddr: 0x9924, symSize: 0x10 }
  - { offset: 0x5CF22, size: 0x8, addend: 0x0, symName: '-[iCarousel setNumberOfVisibleItems:]', symObjAddr: 0x5934, symBinAddr: 0x9934, symSize: 0x10 }
  - { offset: 0x5CF5F, size: 0x8, addend: 0x0, symName: '-[iCarousel itemWidth]', symObjAddr: 0x5944, symBinAddr: 0x9944, symSize: 0x10 }
  - { offset: 0x5CF94, size: 0x8, addend: 0x0, symName: '-[iCarousel setItemWidth:]', symObjAddr: 0x5954, symBinAddr: 0x9954, symSize: 0x10 }
  - { offset: 0x5CFD2, size: 0x8, addend: 0x0, symName: '-[iCarousel contentView]', symObjAddr: 0x5964, symBinAddr: 0x9964, symSize: 0x10 }
  - { offset: 0x5D009, size: 0x8, addend: 0x0, symName: '-[iCarousel setContentView:]', symObjAddr: 0x5974, symBinAddr: 0x9974, symSize: 0x14 }
  - { offset: 0x5D04A, size: 0x8, addend: 0x0, symName: '-[iCarousel toggle]', symObjAddr: 0x5988, symBinAddr: 0x9988, symSize: 0x10 }
  - { offset: 0x5D07F, size: 0x8, addend: 0x0, symName: '-[iCarousel autoscroll]', symObjAddr: 0x5998, symBinAddr: 0x9998, symSize: 0x10 }
  - { offset: 0x5D0B4, size: 0x8, addend: 0x0, symName: '-[iCarousel stopAtItemBoundary]', symObjAddr: 0x59A8, symBinAddr: 0x99A8, symSize: 0x10 }
  - { offset: 0x5D0EB, size: 0x8, addend: 0x0, symName: '-[iCarousel setStopAtItemBoundary:]', symObjAddr: 0x59B8, symBinAddr: 0x99B8, symSize: 0x10 }
  - { offset: 0x5D126, size: 0x8, addend: 0x0, symName: '-[iCarousel scrollToItemBoundary]', symObjAddr: 0x59C8, symBinAddr: 0x99C8, symSize: 0x10 }
  - { offset: 0x5D15D, size: 0x8, addend: 0x0, symName: '-[iCarousel setScrollToItemBoundary:]', symObjAddr: 0x59D8, symBinAddr: 0x99D8, symSize: 0x10 }
  - { offset: 0x5D198, size: 0x8, addend: 0x0, symName: '-[iCarousel ignorePerpendicularSwipes]', symObjAddr: 0x59E8, symBinAddr: 0x99E8, symSize: 0x10 }
  - { offset: 0x5D1CF, size: 0x8, addend: 0x0, symName: '-[iCarousel setIgnorePerpendicularSwipes:]', symObjAddr: 0x59F8, symBinAddr: 0x99F8, symSize: 0x10 }
  - { offset: 0x5D20A, size: 0x8, addend: 0x0, symName: '-[iCarousel centerItemWhenSelected]', symObjAddr: 0x5A08, symBinAddr: 0x9A08, symSize: 0x10 }
  - { offset: 0x5D241, size: 0x8, addend: 0x0, symName: '-[iCarousel setCenterItemWhenSelected:]', symObjAddr: 0x5A18, symBinAddr: 0x9A18, symSize: 0x10 }
  - { offset: 0x5D27C, size: 0x8, addend: 0x0, symName: '-[iCarousel isDragging]', symObjAddr: 0x5A28, symBinAddr: 0x9A28, symSize: 0x10 }
  - { offset: 0x5D2B3, size: 0x8, addend: 0x0, symName: '-[iCarousel setDragging:]', symObjAddr: 0x5A38, symBinAddr: 0x9A38, symSize: 0x10 }
  - { offset: 0x5D2EE, size: 0x8, addend: 0x0, symName: '-[iCarousel isDecelerating]', symObjAddr: 0x5A48, symBinAddr: 0x9A48, symSize: 0x10 }
  - { offset: 0x5D325, size: 0x8, addend: 0x0, symName: '-[iCarousel setDecelerating:]', symObjAddr: 0x5A58, symBinAddr: 0x9A58, symSize: 0x10 }
  - { offset: 0x5D360, size: 0x8, addend: 0x0, symName: '-[iCarousel isScrolling]', symObjAddr: 0x5A68, symBinAddr: 0x9A68, symSize: 0x10 }
  - { offset: 0x5D397, size: 0x8, addend: 0x0, symName: '-[iCarousel setScrolling:]', symObjAddr: 0x5A78, symBinAddr: 0x9A78, symSize: 0x10 }
  - { offset: 0x5D3D2, size: 0x8, addend: 0x0, symName: '-[iCarousel itemViews]', symObjAddr: 0x5A88, symBinAddr: 0x9A88, symSize: 0x10 }
  - { offset: 0x5D409, size: 0x8, addend: 0x0, symName: '-[iCarousel setItemViews:]', symObjAddr: 0x5A98, symBinAddr: 0x9A98, symSize: 0x14 }
  - { offset: 0x5D44A, size: 0x8, addend: 0x0, symName: '-[iCarousel itemViewPool]', symObjAddr: 0x5AAC, symBinAddr: 0x9AAC, symSize: 0x10 }
  - { offset: 0x5D481, size: 0x8, addend: 0x0, symName: '-[iCarousel setItemViewPool:]', symObjAddr: 0x5ABC, symBinAddr: 0x9ABC, symSize: 0x14 }
  - { offset: 0x5D4C2, size: 0x8, addend: 0x0, symName: '-[iCarousel placeholderViewPool]', symObjAddr: 0x5AD0, symBinAddr: 0x9AD0, symSize: 0x10 }
  - { offset: 0x5D4F9, size: 0x8, addend: 0x0, symName: '-[iCarousel setPlaceholderViewPool:]', symObjAddr: 0x5AE0, symBinAddr: 0x9AE0, symSize: 0x14 }
  - { offset: 0x5D53A, size: 0x8, addend: 0x0, symName: '-[iCarousel previousScrollOffset]', symObjAddr: 0x5AF4, symBinAddr: 0x9AF4, symSize: 0x10 }
  - { offset: 0x5D56F, size: 0x8, addend: 0x0, symName: '-[iCarousel setPreviousScrollOffset:]', symObjAddr: 0x5B04, symBinAddr: 0x9B04, symSize: 0x10 }
  - { offset: 0x5D5AD, size: 0x8, addend: 0x0, symName: '-[iCarousel previousItemIndex]', symObjAddr: 0x5B14, symBinAddr: 0x9B14, symSize: 0x10 }
  - { offset: 0x5D5E4, size: 0x8, addend: 0x0, symName: '-[iCarousel setPreviousItemIndex:]', symObjAddr: 0x5B24, symBinAddr: 0x9B24, symSize: 0x10 }
  - { offset: 0x5D621, size: 0x8, addend: 0x0, symName: '-[iCarousel numberOfPlaceholdersToShow]', symObjAddr: 0x5B34, symBinAddr: 0x9B34, symSize: 0x10 }
  - { offset: 0x5D658, size: 0x8, addend: 0x0, symName: '-[iCarousel setNumberOfPlaceholdersToShow:]', symObjAddr: 0x5B44, symBinAddr: 0x9B44, symSize: 0x10 }
  - { offset: 0x5D695, size: 0x8, addend: 0x0, symName: '-[iCarousel startOffset]', symObjAddr: 0x5B54, symBinAddr: 0x9B54, symSize: 0x10 }
  - { offset: 0x5D6CA, size: 0x8, addend: 0x0, symName: '-[iCarousel setStartOffset:]', symObjAddr: 0x5B64, symBinAddr: 0x9B64, symSize: 0x10 }
  - { offset: 0x5D708, size: 0x8, addend: 0x0, symName: '-[iCarousel endOffset]', symObjAddr: 0x5B74, symBinAddr: 0x9B74, symSize: 0x10 }
  - { offset: 0x5D73D, size: 0x8, addend: 0x0, symName: '-[iCarousel setEndOffset:]', symObjAddr: 0x5B84, symBinAddr: 0x9B84, symSize: 0x10 }
  - { offset: 0x5D77B, size: 0x8, addend: 0x0, symName: '-[iCarousel scrollDuration]', symObjAddr: 0x5B94, symBinAddr: 0x9B94, symSize: 0x10 }
  - { offset: 0x5D7B0, size: 0x8, addend: 0x0, symName: '-[iCarousel setScrollDuration:]', symObjAddr: 0x5BA4, symBinAddr: 0x9BA4, symSize: 0x10 }
  - { offset: 0x5D7EE, size: 0x8, addend: 0x0, symName: '-[iCarousel startTime]', symObjAddr: 0x5BB4, symBinAddr: 0x9BB4, symSize: 0x10 }
  - { offset: 0x5D823, size: 0x8, addend: 0x0, symName: '-[iCarousel setStartTime:]', symObjAddr: 0x5BC4, symBinAddr: 0x9BC4, symSize: 0x10 }
  - { offset: 0x5D861, size: 0x8, addend: 0x0, symName: '-[iCarousel lastTime]', symObjAddr: 0x5BD4, symBinAddr: 0x9BD4, symSize: 0x10 }
  - { offset: 0x5D896, size: 0x8, addend: 0x0, symName: '-[iCarousel setLastTime:]', symObjAddr: 0x5BE4, symBinAddr: 0x9BE4, symSize: 0x10 }
  - { offset: 0x5D8D4, size: 0x8, addend: 0x0, symName: '-[iCarousel startVelocity]', symObjAddr: 0x5BF4, symBinAddr: 0x9BF4, symSize: 0x10 }
  - { offset: 0x5D909, size: 0x8, addend: 0x0, symName: '-[iCarousel setStartVelocity:]', symObjAddr: 0x5C04, symBinAddr: 0x9C04, symSize: 0x10 }
  - { offset: 0x5D947, size: 0x8, addend: 0x0, symName: '-[iCarousel timer]', symObjAddr: 0x5C14, symBinAddr: 0x9C14, symSize: 0x10 }
  - { offset: 0x5D97E, size: 0x8, addend: 0x0, symName: '-[iCarousel setTimer:]', symObjAddr: 0x5C24, symBinAddr: 0x9C24, symSize: 0x14 }
  - { offset: 0x5D9BF, size: 0x8, addend: 0x0, symName: '-[iCarousel previousTranslation]', symObjAddr: 0x5C38, symBinAddr: 0x9C38, symSize: 0x10 }
  - { offset: 0x5D9F4, size: 0x8, addend: 0x0, symName: '-[iCarousel setPreviousTranslation:]', symObjAddr: 0x5C48, symBinAddr: 0x9C48, symSize: 0x10 }
  - { offset: 0x5DA32, size: 0x8, addend: 0x0, symName: '-[iCarousel didDrag]', symObjAddr: 0x5C58, symBinAddr: 0x9C58, symSize: 0x10 }
  - { offset: 0x5DA69, size: 0x8, addend: 0x0, symName: '-[iCarousel setDidDrag:]', symObjAddr: 0x5C68, symBinAddr: 0x9C68, symSize: 0x10 }
  - { offset: 0x5DAA4, size: 0x8, addend: 0x0, symName: '-[iCarousel toggleTime]', symObjAddr: 0x5C78, symBinAddr: 0x9C78, symSize: 0x10 }
  - { offset: 0x5DAD9, size: 0x8, addend: 0x0, symName: '-[iCarousel setToggleTime:]', symObjAddr: 0x5C88, symBinAddr: 0x9C88, symSize: 0x10 }
  - { offset: 0x5DB17, size: 0x8, addend: 0x0, symName: '-[iCarousel .cxx_destruct]', symObjAddr: 0x5C98, symBinAddr: 0x9C98, symSize: 0x9C }
...
