---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Documents/tingliteproject/tingLite/DerivedData/tingLite/Build/Intermediates.noindex/ArchiveIntermediates/tingLite/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/MJRefresh.framework/MJRefresh'
relocations:
  - { offset: 0x58E54, size: 0x8, addend: 0x0, symName: _MJRefreshVersionString, symObjAddr: 0x0, symBinAddr: 0x14E60, symSize: 0x0 }
  - { offset: 0x58E89, size: 0x8, addend: 0x0, symName: _MJRefreshVersionNumber, symObjAddr: 0x28, symBinAddr: 0x14E88, symSize: 0x0 }
  - { offset: 0x58EC6, size: 0x8, addend: 0x0, symName: '-[MJRefresh<PERSON>utoFooter willMoveToSuperview:]', symObjAddr: 0x0, symBinAddr: 0x8000, symSize: 0x100 }
  - { offset: 0x59199, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter willMoveToSuperview:]', symObjAddr: 0x0, symBinAddr: 0x8000, symSize: 0x100 }
  - { offset: 0x591DC, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter setAppearencePercentTriggerAutoRefresh:]', symObjAddr: 0x100, symBinAddr: 0x8100, symSize: 0x4 }
  - { offset: 0x5921C, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter appearencePercentTriggerAutoRefresh]', symObjAddr: 0x104, symBinAddr: 0x8104, symSize: 0x4 }
  - { offset: 0x59251, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter prepare]', symObjAddr: 0x108, symBinAddr: 0x8108, symSize: 0x64 }
  - { offset: 0x59284, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter scrollViewContentSizeDidChange:]', symObjAddr: 0x16C, symBinAddr: 0x816C, symSize: 0x80 }
  - { offset: 0x592C7, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter scrollViewContentOffsetDidChange:]', symObjAddr: 0x1EC, symBinAddr: 0x81EC, symSize: 0x248 }
  - { offset: 0x59339, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter scrollViewPanStateDidChange:]', symObjAddr: 0x434, symBinAddr: 0x8434, symSize: 0x1C4 }
  - { offset: 0x5938C, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter unlimitedTrigger]', symObjAddr: 0x5F8, symBinAddr: 0x85F8, symSize: 0x1C }
  - { offset: 0x593C3, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter beginRefreshing]', symObjAddr: 0x614, symBinAddr: 0x8614, symSize: 0x64 }
  - { offset: 0x593F6, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter setState:]', symObjAddr: 0x678, symBinAddr: 0x8678, symSize: 0x1F0 }
  - { offset: 0x59474, size: 0x8, addend: 0x0, symName: '___32-[MJRefreshAutoFooter setState:]_block_invoke', symObjAddr: 0x868, symBinAddr: 0x8868, symSize: 0x9C }
  - { offset: 0x594CF, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s, symObjAddr: 0x904, symBinAddr: 0x8904, symSize: 0x8 }
  - { offset: 0x594F6, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s, symObjAddr: 0x90C, symBinAddr: 0x890C, symSize: 0x8 }
  - { offset: 0x59515, size: 0x8, addend: 0x0, symName: '___32-[MJRefreshAutoFooter setState:]_block_invoke.16', symObjAddr: 0x914, symBinAddr: 0x8914, symSize: 0x64 }
  - { offset: 0x5956C, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter resetTriggerTimes]', symObjAddr: 0x978, symBinAddr: 0x8978, symSize: 0x28 }
  - { offset: 0x5959F, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter setHidden:]', symObjAddr: 0x9A0, symBinAddr: 0x89A0, symSize: 0x11C }
  - { offset: 0x595F2, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter setAutoTriggerTimes:]', symObjAddr: 0xABC, symBinAddr: 0x8ABC, symSize: 0x10 }
  - { offset: 0x59631, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter isAutomaticallyRefresh]', symObjAddr: 0xACC, symBinAddr: 0x8ACC, symSize: 0x10 }
  - { offset: 0x59668, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter setAutomaticallyRefresh:]', symObjAddr: 0xADC, symBinAddr: 0x8ADC, symSize: 0x10 }
  - { offset: 0x596A3, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter triggerAutomaticallyRefreshPercent]', symObjAddr: 0xAEC, symBinAddr: 0x8AEC, symSize: 0x10 }
  - { offset: 0x596D8, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter setTriggerAutomaticallyRefreshPercent:]', symObjAddr: 0xAFC, symBinAddr: 0x8AFC, symSize: 0x10 }
  - { offset: 0x59716, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter autoTriggerTimes]', symObjAddr: 0xB0C, symBinAddr: 0x8B0C, symSize: 0x10 }
  - { offset: 0x5974D, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter triggerByDrag]', symObjAddr: 0xB1C, symBinAddr: 0x8B1C, symSize: 0x10 }
  - { offset: 0x59784, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter setTriggerByDrag:]', symObjAddr: 0xB2C, symBinAddr: 0x8B2C, symSize: 0x10 }
  - { offset: 0x597BF, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter leftTriggerTimes]', symObjAddr: 0xB3C, symBinAddr: 0x8B3C, symSize: 0x10 }
  - { offset: 0x597F6, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoFooter setLeftTriggerTimes:]', symObjAddr: 0xB4C, symBinAddr: 0x8B4C, symSize: 0x10 }
  - { offset: 0x599F6, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoGifFooter gifView]', symObjAddr: 0x0, symBinAddr: 0x8B5C, symSize: 0x64 }
  - { offset: 0x59D16, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoGifFooter gifView]', symObjAddr: 0x0, symBinAddr: 0x8B5C, symSize: 0x64 }
  - { offset: 0x59D6C, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoGifFooter stateImages]', symObjAddr: 0x64, symBinAddr: 0x8BC0, symSize: 0x64 }
  - { offset: 0x59DA3, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoGifFooter stateDurations]', symObjAddr: 0xC8, symBinAddr: 0x8C24, symSize: 0x64 }
  - { offset: 0x59DDA, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoGifFooter setImages:duration:forState:]', symObjAddr: 0x12C, symBinAddr: 0x8C88, symSize: 0x15C }
  - { offset: 0x59E4D, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoGifFooter setImages:forState:]', symObjAddr: 0x288, symBinAddr: 0x8DE4, symSize: 0x5C }
  - { offset: 0x59EA0, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoGifFooter prepare]', symObjAddr: 0x2E4, symBinAddr: 0x8E40, symSize: 0x4C }
  - { offset: 0x59ED3, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoGifFooter placeSubviews]', symObjAddr: 0x330, symBinAddr: 0x8E8C, symSize: 0x184 }
  - { offset: 0x59F06, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoGifFooter setState:]', symObjAddr: 0x4B4, symBinAddr: 0x9010, symSize: 0x288 }
  - { offset: 0x59F70, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoGifFooter setStateImages:]', symObjAddr: 0x73C, symBinAddr: 0x9298, symSize: 0x14 }
  - { offset: 0x59FB1, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoGifFooter setStateDurations:]', symObjAddr: 0x750, symBinAddr: 0x92AC, symSize: 0x14 }
  - { offset: 0x59FF2, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoGifFooter .cxx_destruct]', symObjAddr: 0x764, symBinAddr: 0x92C0, symSize: 0x40 }
  - { offset: 0x5A0D2, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoNormalFooter loadingView]', symObjAddr: 0x0, symBinAddr: 0x9300, symSize: 0x94 }
  - { offset: 0x5A3D3, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoNormalFooter loadingView]', symObjAddr: 0x0, symBinAddr: 0x9300, symSize: 0x94 }
  - { offset: 0x5A429, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoNormalFooter setActivityIndicatorViewStyle:]', symObjAddr: 0x94, symBinAddr: 0x9394, symSize: 0x54 }
  - { offset: 0x5A46C, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoNormalFooter prepare]', symObjAddr: 0xE8, symBinAddr: 0x93E8, symSize: 0x70 }
  - { offset: 0x5A49F, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoNormalFooter placeSubviews]', symObjAddr: 0x158, symBinAddr: 0x9458, symSize: 0x11C }
  - { offset: 0x5A4F2, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoNormalFooter setState:]', symObjAddr: 0x274, symBinAddr: 0x9574, symSize: 0xA4 }
  - { offset: 0x5A545, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoNormalFooter setLoadingView:]', symObjAddr: 0x318, symBinAddr: 0x9618, symSize: 0x14 }
  - { offset: 0x5A586, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoNormalFooter activityIndicatorViewStyle]', symObjAddr: 0x32C, symBinAddr: 0x962C, symSize: 0x10 }
  - { offset: 0x5A5BD, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoNormalFooter .cxx_destruct]', symObjAddr: 0x33C, symBinAddr: 0x963C, symSize: 0x10 }
  - { offset: 0x5A667, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoStateFooter stateTitles]', symObjAddr: 0x0, symBinAddr: 0x964C, symSize: 0x64 }
  - { offset: 0x5A965, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoStateFooter stateTitles]', symObjAddr: 0x0, symBinAddr: 0x964C, symSize: 0x64 }
  - { offset: 0x5A99C, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoStateFooter stateLabel]', symObjAddr: 0x64, symBinAddr: 0x96B0, symSize: 0x68 }
  - { offset: 0x5A9D3, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoStateFooter setTitle:forState:]', symObjAddr: 0xCC, symBinAddr: 0x9718, symSize: 0x124 }
  - { offset: 0x5AA26, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoStateFooter stateLabelClick]', symObjAddr: 0x1F0, symBinAddr: 0x983C, symSize: 0x38 }
  - { offset: 0x5AA59, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoStateFooter prepare]', symObjAddr: 0x228, symBinAddr: 0x9874, symSize: 0x17C }
  - { offset: 0x5AA8C, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoStateFooter placeSubviews]', symObjAddr: 0x3A4, symBinAddr: 0x99F0, symSize: 0xE0 }
  - { offset: 0x5AABF, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoStateFooter setState:]', symObjAddr: 0x484, symBinAddr: 0x9AD0, symSize: 0x114 }
  - { offset: 0x5AB12, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoStateFooter labelLeftInset]', symObjAddr: 0x598, symBinAddr: 0x9BE4, symSize: 0x10 }
  - { offset: 0x5AB47, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoStateFooter setLabelLeftInset:]', symObjAddr: 0x5A8, symBinAddr: 0x9BF4, symSize: 0x10 }
  - { offset: 0x5AB85, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoStateFooter isRefreshingTitleHidden]', symObjAddr: 0x5B8, symBinAddr: 0x9C04, symSize: 0x10 }
  - { offset: 0x5ABBC, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoStateFooter setRefreshingTitleHidden:]', symObjAddr: 0x5C8, symBinAddr: 0x9C14, symSize: 0x10 }
  - { offset: 0x5ABF7, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoStateFooter setStateTitles:]', symObjAddr: 0x5D8, symBinAddr: 0x9C24, symSize: 0x14 }
  - { offset: 0x5AC38, size: 0x8, addend: 0x0, symName: '-[MJRefreshAutoStateFooter .cxx_destruct]', symObjAddr: 0x5EC, symBinAddr: 0x9C38, symSize: 0x14 }
  - { offset: 0x5ACE7, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackFooter willMoveToSuperview:]', symObjAddr: 0x0, symBinAddr: 0x9C4C, symSize: 0x4C }
  - { offset: 0x5AF5B, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackFooter willMoveToSuperview:]', symObjAddr: 0x0, symBinAddr: 0x9C4C, symSize: 0x4C }
  - { offset: 0x5AF9E, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackFooter scrollViewContentOffsetDidChange:]', symObjAddr: 0x4C, symBinAddr: 0x9C98, symSize: 0x1B8 }
  - { offset: 0x5B028, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackFooter scrollViewContentSizeDidChange:]', symObjAddr: 0x204, symBinAddr: 0x9E50, symSize: 0xD0 }
  - { offset: 0x5B0B8, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackFooter setState:]', symObjAddr: 0x2D4, symBinAddr: 0x9F20, symSize: 0x230 }
  - { offset: 0x5B122, size: 0x8, addend: 0x0, symName: '___32-[MJRefreshBackFooter setState:]_block_invoke', symObjAddr: 0x504, symBinAddr: 0xA150, symSize: 0xC4 }
  - { offset: 0x5B16D, size: 0x8, addend: 0x0, symName: '___32-[MJRefreshBackFooter setState:]_block_invoke.7', symObjAddr: 0x5D8, symBinAddr: 0xA214, symSize: 0x70 }
  - { offset: 0x5B1C4, size: 0x8, addend: 0x0, symName: '___32-[MJRefreshBackFooter setState:]_block_invoke.9', symObjAddr: 0x648, symBinAddr: 0xA284, symSize: 0xD8 }
  - { offset: 0x5B232, size: 0x8, addend: 0x0, symName: '___32-[MJRefreshBackFooter setState:]_block_invoke_2', symObjAddr: 0x720, symBinAddr: 0xA35C, symSize: 0x8 }
  - { offset: 0x5B27D, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackFooter heightForContentBreakView]', symObjAddr: 0x728, symBinAddr: 0xA364, symSize: 0x84 }
  - { offset: 0x5B2C4, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackFooter happenOffsetY]', symObjAddr: 0x7AC, symBinAddr: 0xA3E8, symSize: 0x44 }
  - { offset: 0x5B30B, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackFooter lastRefreshCount]', symObjAddr: 0x7F0, symBinAddr: 0xA42C, symSize: 0x10 }
  - { offset: 0x5B342, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackFooter setLastRefreshCount:]', symObjAddr: 0x800, symBinAddr: 0xA43C, symSize: 0x10 }
  - { offset: 0x5B37F, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackFooter lastBottomDelta]', symObjAddr: 0x810, symBinAddr: 0xA44C, symSize: 0x10 }
  - { offset: 0x5B3B4, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackFooter setLastBottomDelta:]', symObjAddr: 0x820, symBinAddr: 0xA45C, symSize: 0x10 }
  - { offset: 0x5B620, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackGifFooter gifView]', symObjAddr: 0x0, symBinAddr: 0xA46C, symSize: 0x64 }
  - { offset: 0x5B8F7, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackGifFooter gifView]', symObjAddr: 0x0, symBinAddr: 0xA46C, symSize: 0x64 }
  - { offset: 0x5B94D, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackGifFooter stateImages]', symObjAddr: 0x64, symBinAddr: 0xA4D0, symSize: 0x64 }
  - { offset: 0x5B984, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackGifFooter stateDurations]', symObjAddr: 0xC8, symBinAddr: 0xA534, symSize: 0x64 }
  - { offset: 0x5B9BB, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackGifFooter setImages:duration:forState:]', symObjAddr: 0x12C, symBinAddr: 0xA598, symSize: 0x15C }
  - { offset: 0x5BA2E, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackGifFooter setImages:forState:]', symObjAddr: 0x288, symBinAddr: 0xA6F4, symSize: 0x5C }
  - { offset: 0x5BA81, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackGifFooter prepare]', symObjAddr: 0x2E4, symBinAddr: 0xA750, symSize: 0x4C }
  - { offset: 0x5BAB4, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackGifFooter setPullingPercent:]', symObjAddr: 0x330, symBinAddr: 0xA79C, symSize: 0x164 }
  - { offset: 0x5BB17, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackGifFooter placeSubviews]', symObjAddr: 0x494, symBinAddr: 0xA900, symSize: 0x19C }
  - { offset: 0x5BB4A, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackGifFooter setState:]', symObjAddr: 0x630, symBinAddr: 0xAA9C, symSize: 0x28C }
  - { offset: 0x5BBB4, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackGifFooter setStateImages:]', symObjAddr: 0x8BC, symBinAddr: 0xAD28, symSize: 0x14 }
  - { offset: 0x5BBF5, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackGifFooter setStateDurations:]', symObjAddr: 0x8D0, symBinAddr: 0xAD3C, symSize: 0x14 }
  - { offset: 0x5BC36, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackGifFooter .cxx_destruct]', symObjAddr: 0x8E4, symBinAddr: 0xAD50, symSize: 0x40 }
  - { offset: 0x5BD02, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackNormalFooter arrowView]', symObjAddr: 0x0, symBinAddr: 0xAD90, symSize: 0x90 }
  - { offset: 0x5BFD9, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackNormalFooter arrowView]', symObjAddr: 0x0, symBinAddr: 0xAD90, symSize: 0x90 }
  - { offset: 0x5C02F, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackNormalFooter loadingView]', symObjAddr: 0x90, symBinAddr: 0xAE20, symSize: 0x94 }
  - { offset: 0x5C085, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackNormalFooter setActivityIndicatorViewStyle:]', symObjAddr: 0x124, symBinAddr: 0xAEB4, symSize: 0x54 }
  - { offset: 0x5C0C8, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackNormalFooter prepare]', symObjAddr: 0x178, symBinAddr: 0xAF08, symSize: 0x70 }
  - { offset: 0x5C0FB, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackNormalFooter placeSubviews]', symObjAddr: 0x1E8, symBinAddr: 0xAF78, symSize: 0x260 }
  - { offset: 0x5C15E, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackNormalFooter setState:]', symObjAddr: 0x448, symBinAddr: 0xB1D8, symSize: 0x2E8 }
  - { offset: 0x5C1DA, size: 0x8, addend: 0x0, symName: '___38-[MJRefreshBackNormalFooter setState:]_block_invoke', symObjAddr: 0x730, symBinAddr: 0xB4C0, symSize: 0x38 }
  - { offset: 0x5C219, size: 0x8, addend: 0x0, symName: '___38-[MJRefreshBackNormalFooter setState:]_block_invoke.8', symObjAddr: 0x778, symBinAddr: 0xB4F8, symSize: 0x9C }
  - { offset: 0x5C264, size: 0x8, addend: 0x0, symName: '___38-[MJRefreshBackNormalFooter setState:]_block_invoke.10', symObjAddr: 0x814, symBinAddr: 0xB594, symSize: 0x68 }
  - { offset: 0x5C2B9, size: 0x8, addend: 0x0, symName: '___38-[MJRefreshBackNormalFooter setState:]_block_invoke_2', symObjAddr: 0x87C, symBinAddr: 0xB5FC, symSize: 0x5C }
  - { offset: 0x5C2F8, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackNormalFooter setLoadingView:]', symObjAddr: 0x8D8, symBinAddr: 0xB658, symSize: 0x14 }
  - { offset: 0x5C339, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackNormalFooter activityIndicatorViewStyle]', symObjAddr: 0x8EC, symBinAddr: 0xB66C, symSize: 0x10 }
  - { offset: 0x5C370, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackNormalFooter .cxx_destruct]', symObjAddr: 0x8FC, symBinAddr: 0xB67C, symSize: 0x10 }
  - { offset: 0x5C5DC, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackStateFooter stateTitles]', symObjAddr: 0x0, symBinAddr: 0xB68C, symSize: 0x64 }
  - { offset: 0x5C884, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackStateFooter stateTitles]', symObjAddr: 0x0, symBinAddr: 0xB68C, symSize: 0x64 }
  - { offset: 0x5C8BB, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackStateFooter stateLabel]', symObjAddr: 0x64, symBinAddr: 0xB6F0, symSize: 0x68 }
  - { offset: 0x5C8F2, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackStateFooter setTitle:forState:]', symObjAddr: 0xCC, symBinAddr: 0xB758, symSize: 0x124 }
  - { offset: 0x5C945, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackStateFooter titleForState:]', symObjAddr: 0x1F0, symBinAddr: 0xB87C, symSize: 0x7C }
  - { offset: 0x5C98C, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackStateFooter prepare]', symObjAddr: 0x26C, symBinAddr: 0xB8F8, symSize: 0x140 }
  - { offset: 0x5C9BF, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackStateFooter placeSubviews]', symObjAddr: 0x3AC, symBinAddr: 0xBA38, symSize: 0xE0 }
  - { offset: 0x5C9F2, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackStateFooter setState:]', symObjAddr: 0x48C, symBinAddr: 0xBB18, symSize: 0xE0 }
  - { offset: 0x5CA45, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackStateFooter labelLeftInset]', symObjAddr: 0x56C, symBinAddr: 0xBBF8, symSize: 0x10 }
  - { offset: 0x5CA7A, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackStateFooter setLabelLeftInset:]', symObjAddr: 0x57C, symBinAddr: 0xBC08, symSize: 0x10 }
  - { offset: 0x5CAB8, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackStateFooter setStateTitles:]', symObjAddr: 0x58C, symBinAddr: 0xBC18, symSize: 0x14 }
  - { offset: 0x5CAF9, size: 0x8, addend: 0x0, symName: '-[MJRefreshBackStateFooter .cxx_destruct]', symObjAddr: 0x5A0, symBinAddr: 0xBC2C, symSize: 0x14 }
  - { offset: 0x5CBA8, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent initWithFrame:]', symObjAddr: 0x0, symBinAddr: 0xBC40, symSize: 0x5C }
  - { offset: 0x5CE50, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent initWithFrame:]', symObjAddr: 0x0, symBinAddr: 0xBC40, symSize: 0x5C }
  - { offset: 0x5CE93, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent prepare]', symObjAddr: 0x5C, symBinAddr: 0xBC9C, symSize: 0x4C }
  - { offset: 0x5CEC6, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent layoutSubviews]', symObjAddr: 0xA8, symBinAddr: 0xBCE8, symSize: 0x44 }
  - { offset: 0x5CEF9, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent placeSubviews]', symObjAddr: 0xEC, symBinAddr: 0xBD2C, symSize: 0x4 }
  - { offset: 0x5CF28, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent willMoveToSuperview:]', symObjAddr: 0xF0, symBinAddr: 0xBD30, symSize: 0x140 }
  - { offset: 0x5CF6B, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent drawRect:]', symObjAddr: 0x230, symBinAddr: 0xBE70, symSize: 0x5C }
  - { offset: 0x5CFAA, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent addObservers]', symObjAddr: 0x28C, symBinAddr: 0xBECC, symSize: 0x104 }
  - { offset: 0x5CFEA, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent removeObservers]', symObjAddr: 0x390, symBinAddr: 0xBFD0, symSize: 0xB0 }
  - { offset: 0x5D01D, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent observeValueForKeyPath:ofObject:change:context:]', symObjAddr: 0x440, symBinAddr: 0xC080, symSize: 0xD4 }
  - { offset: 0x5D088, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent scrollViewContentOffsetDidChange:]', symObjAddr: 0x514, symBinAddr: 0xC154, symSize: 0x4 }
  - { offset: 0x5D0C3, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent scrollViewContentSizeDidChange:]', symObjAddr: 0x518, symBinAddr: 0xC158, symSize: 0x4 }
  - { offset: 0x5D0FE, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent scrollViewPanStateDidChange:]', symObjAddr: 0x51C, symBinAddr: 0xC15C, symSize: 0x4 }
  - { offset: 0x5D139, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent setRefreshingTarget:refreshingAction:]', symObjAddr: 0x520, symBinAddr: 0xC160, symSize: 0x2C }
  - { offset: 0x5D18C, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent setState:]', symObjAddr: 0x54C, symBinAddr: 0xC18C, symSize: 0x98 }
  - { offset: 0x5D208, size: 0x8, addend: 0x0, symName: '___31-[MJRefreshComponent setState:]_block_invoke', symObjAddr: 0x5E4, symBinAddr: 0xC224, symSize: 0x60 }
  - { offset: 0x5D262, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32w, symObjAddr: 0x644, symBinAddr: 0xC284, symSize: 0xC }
  - { offset: 0x5D28B, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32w, symObjAddr: 0x650, symBinAddr: 0xC290, symSize: 0x8 }
  - { offset: 0x5D2AA, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent beginRefreshing]', symObjAddr: 0x658, symBinAddr: 0xC298, symSize: 0xCC }
  - { offset: 0x5D2DD, size: 0x8, addend: 0x0, symName: '___37-[MJRefreshComponent beginRefreshing]_block_invoke', symObjAddr: 0x724, symBinAddr: 0xC364, symSize: 0xC }
  - { offset: 0x5D31C, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent beginRefreshingWithCompletionBlock:]', symObjAddr: 0x740, symBinAddr: 0xC370, symSize: 0x24 }
  - { offset: 0x5D35F, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent endRefreshing]', symObjAddr: 0x764, symBinAddr: 0xC394, symSize: 0x8C }
  - { offset: 0x5D3B7, size: 0x8, addend: 0x0, symName: '___35-[MJRefreshComponent endRefreshing]_block_invoke', symObjAddr: 0x7F0, symBinAddr: 0xC420, symSize: 0x64 }
  - { offset: 0x5D411, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent endRefreshingWithCompletionBlock:]', symObjAddr: 0x854, symBinAddr: 0xC484, symSize: 0x24 }
  - { offset: 0x5D454, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent isRefreshing]', symObjAddr: 0x878, symBinAddr: 0xC4A8, symSize: 0x40 }
  - { offset: 0x5D48B, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent setAutoChangeAlpha:]', symObjAddr: 0x8B8, symBinAddr: 0xC4E8, symSize: 0x4 }
  - { offset: 0x5D4CF, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent isAutoChangeAlpha]', symObjAddr: 0x8BC, symBinAddr: 0xC4EC, symSize: 0x4 }
  - { offset: 0x5D504, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent setAutomaticallyChangeAlpha:]', symObjAddr: 0x8C0, symBinAddr: 0xC4F0, symSize: 0x54 }
  - { offset: 0x5D543, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent setPullingPercent:]', symObjAddr: 0x914, symBinAddr: 0xC544, symSize: 0x60 }
  - { offset: 0x5D586, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent executeRefreshingCallback]', symObjAddr: 0x974, symBinAddr: 0xC5A4, symSize: 0x8C }
  - { offset: 0x5D5DE, size: 0x8, addend: 0x0, symName: '___47-[MJRefreshComponent executeRefreshingCallback]_block_invoke', symObjAddr: 0xA00, symBinAddr: 0xC630, symSize: 0x1DC }
  - { offset: 0x5D650, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent setEndRefreshingAnimateCompletionBlock:]', symObjAddr: 0xBDC, symBinAddr: 0xC80C, symSize: 0x38 }
  - { offset: 0x5D693, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent refreshingBlock]', symObjAddr: 0xC14, symBinAddr: 0xC844, symSize: 0x10 }
  - { offset: 0x5D6CA, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent setRefreshingBlock:]', symObjAddr: 0xC24, symBinAddr: 0xC854, symSize: 0xC }
  - { offset: 0x5D709, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent refreshingTarget]', symObjAddr: 0xC30, symBinAddr: 0xC860, symSize: 0x20 }
  - { offset: 0x5D740, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent setRefreshingTarget:]', symObjAddr: 0xC50, symBinAddr: 0xC880, symSize: 0x14 }
  - { offset: 0x5D781, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent refreshingAction]', symObjAddr: 0xC64, symBinAddr: 0xC894, symSize: 0x10 }
  - { offset: 0x5D7B8, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent setRefreshingAction:]', symObjAddr: 0xC74, symBinAddr: 0xC8A4, symSize: 0x10 }
  - { offset: 0x5D7F5, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent beginRefreshingCompletionBlock]', symObjAddr: 0xC84, symBinAddr: 0xC8B4, symSize: 0x10 }
  - { offset: 0x5D82C, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent setBeginRefreshingCompletionBlock:]', symObjAddr: 0xC94, symBinAddr: 0xC8C4, symSize: 0xC }
  - { offset: 0x5D86B, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent endRefreshingAnimateCompletionBlock]', symObjAddr: 0xCA0, symBinAddr: 0xC8D0, symSize: 0x10 }
  - { offset: 0x5D8A2, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent endRefreshingAnimationBeginAction]', symObjAddr: 0xCB0, symBinAddr: 0xC8E0, symSize: 0x10 }
  - { offset: 0x5D8D9, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent setEndRefreshingAnimationBeginAction:]', symObjAddr: 0xCC0, symBinAddr: 0xC8F0, symSize: 0xC }
  - { offset: 0x5D918, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent endRefreshingCompletionBlock]', symObjAddr: 0xCCC, symBinAddr: 0xC8FC, symSize: 0x10 }
  - { offset: 0x5D94F, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent setEndRefreshingCompletionBlock:]', symObjAddr: 0xCDC, symBinAddr: 0xC90C, symSize: 0xC }
  - { offset: 0x5D98E, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent state]', symObjAddr: 0xCE8, symBinAddr: 0xC918, symSize: 0x10 }
  - { offset: 0x5D9C5, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent scrollViewOriginalInset]', symObjAddr: 0xCF8, symBinAddr: 0xC928, symSize: 0x18 }
  - { offset: 0x5D9FA, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent scrollView]', symObjAddr: 0xD10, symBinAddr: 0xC940, symSize: 0x20 }
  - { offset: 0x5DA31, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent pullingPercent]', symObjAddr: 0xD30, symBinAddr: 0xC960, symSize: 0x10 }
  - { offset: 0x5DA66, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent isAutomaticallyChangeAlpha]', symObjAddr: 0xD40, symBinAddr: 0xC970, symSize: 0x10 }
  - { offset: 0x5DA9D, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent pan]', symObjAddr: 0xD50, symBinAddr: 0xC980, symSize: 0x10 }
  - { offset: 0x5DAD4, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent setPan:]', symObjAddr: 0xD60, symBinAddr: 0xC990, symSize: 0x14 }
  - { offset: 0x5DB15, size: 0x8, addend: 0x0, symName: '-[MJRefreshComponent .cxx_destruct]', symObjAddr: 0xD74, symBinAddr: 0xC9A4, symSize: 0xB0 }
  - { offset: 0x5DB48, size: 0x8, addend: 0x0, symName: '+[UILabel(MJRefresh) mj_label]', symObjAddr: 0xE24, symBinAddr: 0xCA54, symSize: 0xE0 }
  - { offset: 0x5DB8F, size: 0x8, addend: 0x0, symName: '-[UILabel(MJRefresh) mj_textWidth]', symObjAddr: 0xF04, symBinAddr: 0xCB34, symSize: 0x1A4 }
  - { offset: 0x5DE37, size: 0x8, addend: 0x0, symName: '+[MJRefreshConfig defaultConfig]', symObjAddr: 0x0, symBinAddr: 0xCCD8, symSize: 0x74 }
  - { offset: 0x5DE51, size: 0x8, addend: 0x0, symName: _mj_RefreshConfig, symObjAddr: 0x1E38, symBinAddr: 0x1D430, symSize: 0x0 }
  - { offset: 0x5DE90, size: 0x8, addend: 0x0, symName: '+[MJRefreshConfig defaultConfig]', symObjAddr: 0x0, symBinAddr: 0xCCD8, symSize: 0x74 }
  - { offset: 0x5DEBA, size: 0x8, addend: 0x0, symName: _defaultConfig.onceToken, symObjAddr: 0x1E40, symBinAddr: 0x1D438, symSize: 0x0 }
  - { offset: 0x5DF61, size: 0x8, addend: 0x0, symName: '___32+[MJRefreshConfig defaultConfig]_block_invoke', symObjAddr: 0x74, symBinAddr: 0xCD4C, symSize: 0x2C }
  - { offset: 0x5DFA0, size: 0x8, addend: 0x0, symName: '-[MJRefreshConfig languageCode]', symObjAddr: 0xA0, symBinAddr: 0xCD78, symSize: 0x8 }
  - { offset: 0x5DFD7, size: 0x8, addend: 0x0, symName: '-[MJRefreshConfig setLanguageCode:]', symObjAddr: 0xA8, symBinAddr: 0xCD80, symSize: 0x8 }
  - { offset: 0x5E016, size: 0x8, addend: 0x0, symName: '-[MJRefreshConfig .cxx_destruct]', symObjAddr: 0xB0, symBinAddr: 0xCD88, symSize: 0xC }
  - { offset: 0x5E169, size: 0x8, addend: 0x0, symName: _MJRefreshLabelLeftInset, symObjAddr: 0x0, symBinAddr: 0x14EC8, symSize: 0x0 }
  - { offset: 0x5E18B, size: 0x8, addend: 0x0, symName: _MJRefreshHeaderHeight, symObjAddr: 0x8, symBinAddr: 0x14ED0, symSize: 0x0 }
  - { offset: 0x5E1A1, size: 0x8, addend: 0x0, symName: _MJRefreshFooterHeight, symObjAddr: 0x10, symBinAddr: 0x14ED8, symSize: 0x0 }
  - { offset: 0x5E1B7, size: 0x8, addend: 0x0, symName: _MJRefreshTrailWidth, symObjAddr: 0x18, symBinAddr: 0x14EE0, symSize: 0x0 }
  - { offset: 0x5E1CD, size: 0x8, addend: 0x0, symName: _MJRefreshFastAnimationDuration, symObjAddr: 0x20, symBinAddr: 0x14EE8, symSize: 0x0 }
  - { offset: 0x5E1E3, size: 0x8, addend: 0x0, symName: _MJRefreshSlowAnimationDuration, symObjAddr: 0x28, symBinAddr: 0x14EF0, symSize: 0x0 }
  - { offset: 0x5E1F9, size: 0x8, addend: 0x0, symName: _MJRefreshKeyPathContentOffset, symObjAddr: 0x4C0, symBinAddr: 0x18260, symSize: 0x0 }
  - { offset: 0x5E219, size: 0x8, addend: 0x0, symName: _MJRefreshKeyPathContentInset, symObjAddr: 0x4C8, symBinAddr: 0x18268, symSize: 0x0 }
  - { offset: 0x5E22F, size: 0x8, addend: 0x0, symName: _MJRefreshKeyPathContentSize, symObjAddr: 0x4D0, symBinAddr: 0x18270, symSize: 0x0 }
  - { offset: 0x5E245, size: 0x8, addend: 0x0, symName: _MJRefreshKeyPathPanState, symObjAddr: 0x4D8, symBinAddr: 0x18278, symSize: 0x0 }
  - { offset: 0x5E25B, size: 0x8, addend: 0x0, symName: _MJRefreshHeaderLastUpdatedTimeKey, symObjAddr: 0x4E0, symBinAddr: 0x18280, symSize: 0x0 }
  - { offset: 0x5E271, size: 0x8, addend: 0x0, symName: _MJRefreshHeaderIdleText, symObjAddr: 0x4E8, symBinAddr: 0x18288, symSize: 0x0 }
  - { offset: 0x5E287, size: 0x8, addend: 0x0, symName: _MJRefreshHeaderPullingText, symObjAddr: 0x4F0, symBinAddr: 0x18290, symSize: 0x0 }
  - { offset: 0x5E29D, size: 0x8, addend: 0x0, symName: _MJRefreshHeaderRefreshingText, symObjAddr: 0x4F8, symBinAddr: 0x18298, symSize: 0x0 }
  - { offset: 0x5E2B3, size: 0x8, addend: 0x0, symName: _MJRefreshTrailerIdleText, symObjAddr: 0x500, symBinAddr: 0x182A0, symSize: 0x0 }
  - { offset: 0x5E2C9, size: 0x8, addend: 0x0, symName: _MJRefreshTrailerPullingText, symObjAddr: 0x508, symBinAddr: 0x182A8, symSize: 0x0 }
  - { offset: 0x5E2DF, size: 0x8, addend: 0x0, symName: _MJRefreshAutoFooterIdleText, symObjAddr: 0x510, symBinAddr: 0x182B0, symSize: 0x0 }
  - { offset: 0x5E2F5, size: 0x8, addend: 0x0, symName: _MJRefreshAutoFooterRefreshingText, symObjAddr: 0x518, symBinAddr: 0x182B8, symSize: 0x0 }
  - { offset: 0x5E30B, size: 0x8, addend: 0x0, symName: _MJRefreshAutoFooterNoMoreDataText, symObjAddr: 0x520, symBinAddr: 0x182C0, symSize: 0x0 }
  - { offset: 0x5E321, size: 0x8, addend: 0x0, symName: _MJRefreshBackFooterIdleText, symObjAddr: 0x528, symBinAddr: 0x182C8, symSize: 0x0 }
  - { offset: 0x5E337, size: 0x8, addend: 0x0, symName: _MJRefreshBackFooterPullingText, symObjAddr: 0x530, symBinAddr: 0x182D0, symSize: 0x0 }
  - { offset: 0x5E34D, size: 0x8, addend: 0x0, symName: _MJRefreshBackFooterRefreshingText, symObjAddr: 0x538, symBinAddr: 0x182D8, symSize: 0x0 }
  - { offset: 0x5E363, size: 0x8, addend: 0x0, symName: _MJRefreshBackFooterNoMoreDataText, symObjAddr: 0x540, symBinAddr: 0x182E0, symSize: 0x0 }
  - { offset: 0x5E379, size: 0x8, addend: 0x0, symName: _MJRefreshHeaderLastTimeText, symObjAddr: 0x548, symBinAddr: 0x182E8, symSize: 0x0 }
  - { offset: 0x5E38F, size: 0x8, addend: 0x0, symName: _MJRefreshHeaderDateTodayText, symObjAddr: 0x550, symBinAddr: 0x182F0, symSize: 0x0 }
  - { offset: 0x5E3A5, size: 0x8, addend: 0x0, symName: _MJRefreshHeaderNoneLastDateText, symObjAddr: 0x558, symBinAddr: 0x182F8, symSize: 0x0 }
  - { offset: 0x5E3DD, size: 0x8, addend: 0x0, symName: '+[MJRefreshFooter footerWithRefreshingBlock:]', symObjAddr: 0x0, symBinAddr: 0xCD94, symSize: 0x4C }
  - { offset: 0x5E627, size: 0x8, addend: 0x0, symName: '+[MJRefreshFooter footerWithRefreshingBlock:]', symObjAddr: 0x0, symBinAddr: 0xCD94, symSize: 0x4C }
  - { offset: 0x5E67E, size: 0x8, addend: 0x0, symName: '+[MJRefreshFooter footerWithRefreshingTarget:refreshingAction:]', symObjAddr: 0x4C, symBinAddr: 0xCDE0, symSize: 0x5C }
  - { offset: 0x5E6E5, size: 0x8, addend: 0x0, symName: '-[MJRefreshFooter prepare]', symObjAddr: 0xA8, symBinAddr: 0xCE3C, symSize: 0x54 }
  - { offset: 0x5E718, size: 0x8, addend: 0x0, symName: '-[MJRefreshFooter endRefreshingWithNoMoreData]', symObjAddr: 0xFC, symBinAddr: 0xCE90, symSize: 0x8C }
  - { offset: 0x5E783, size: 0x8, addend: 0x0, symName: '___46-[MJRefreshFooter endRefreshingWithNoMoreData]_block_invoke', symObjAddr: 0x188, symBinAddr: 0xCF1C, symSize: 0x64 }
  - { offset: 0x5E7DD, size: 0x8, addend: 0x0, symName: '-[MJRefreshFooter noticeNoMoreData]', symObjAddr: 0x200, symBinAddr: 0xCF80, symSize: 0x4 }
  - { offset: 0x5E80E, size: 0x8, addend: 0x0, symName: '-[MJRefreshFooter resetNoMoreData]', symObjAddr: 0x204, symBinAddr: 0xCF84, symSize: 0x8C }
  - { offset: 0x5E866, size: 0x8, addend: 0x0, symName: '___34-[MJRefreshFooter resetNoMoreData]_block_invoke', symObjAddr: 0x290, symBinAddr: 0xD010, symSize: 0x64 }
  - { offset: 0x5E8C0, size: 0x8, addend: 0x0, symName: '-[MJRefreshFooter setAutomaticallyHidden:]', symObjAddr: 0x2F4, symBinAddr: 0xD074, symSize: 0x10 }
  - { offset: 0x5E8FD, size: 0x8, addend: 0x0, symName: '-[MJRefreshFooter ignoredScrollViewContentInsetBottom]', symObjAddr: 0x304, symBinAddr: 0xD084, symSize: 0x10 }
  - { offset: 0x5E932, size: 0x8, addend: 0x0, symName: '-[MJRefreshFooter setIgnoredScrollViewContentInsetBottom:]', symObjAddr: 0x314, symBinAddr: 0xD094, symSize: 0x10 }
  - { offset: 0x5E970, size: 0x8, addend: 0x0, symName: '-[MJRefreshFooter isAutomaticallyHidden]', symObjAddr: 0x324, symBinAddr: 0xD0A4, symSize: 0x10 }
  - { offset: 0x5EB19, size: 0x8, addend: 0x0, symName: '-[MJRefreshGifHeader gifView]', symObjAddr: 0x0, symBinAddr: 0xD0B4, symSize: 0x64 }
  - { offset: 0x5EE84, size: 0x8, addend: 0x0, symName: '-[MJRefreshGifHeader gifView]', symObjAddr: 0x0, symBinAddr: 0xD0B4, symSize: 0x64 }
  - { offset: 0x5EEDA, size: 0x8, addend: 0x0, symName: '-[MJRefreshGifHeader stateImages]', symObjAddr: 0x64, symBinAddr: 0xD118, symSize: 0x64 }
  - { offset: 0x5EF11, size: 0x8, addend: 0x0, symName: '-[MJRefreshGifHeader stateDurations]', symObjAddr: 0xC8, symBinAddr: 0xD17C, symSize: 0x64 }
  - { offset: 0x5EF48, size: 0x8, addend: 0x0, symName: '-[MJRefreshGifHeader setImages:duration:forState:]', symObjAddr: 0x12C, symBinAddr: 0xD1E0, symSize: 0x15C }
  - { offset: 0x5EFBB, size: 0x8, addend: 0x0, symName: '-[MJRefreshGifHeader setImages:forState:]', symObjAddr: 0x288, symBinAddr: 0xD33C, symSize: 0x5C }
  - { offset: 0x5F00E, size: 0x8, addend: 0x0, symName: '-[MJRefreshGifHeader prepare]', symObjAddr: 0x2E4, symBinAddr: 0xD398, symSize: 0x4C }
  - { offset: 0x5F041, size: 0x8, addend: 0x0, symName: '-[MJRefreshGifHeader setPullingPercent:]', symObjAddr: 0x330, symBinAddr: 0xD3E4, symSize: 0x164 }
  - { offset: 0x5F0A4, size: 0x8, addend: 0x0, symName: '-[MJRefreshGifHeader placeSubviews]', symObjAddr: 0x494, symBinAddr: 0xD548, symSize: 0x23C }
  - { offset: 0x5F143, size: 0x8, addend: 0x0, symName: '-[MJRefreshGifHeader setState:]', symObjAddr: 0x6D0, symBinAddr: 0xD784, symSize: 0x240 }
  - { offset: 0x5F1AD, size: 0x8, addend: 0x0, symName: '-[MJRefreshGifHeader setStateImages:]', symObjAddr: 0x910, symBinAddr: 0xD9C4, symSize: 0x14 }
  - { offset: 0x5F1EE, size: 0x8, addend: 0x0, symName: '-[MJRefreshGifHeader setStateDurations:]', symObjAddr: 0x924, symBinAddr: 0xD9D8, symSize: 0x14 }
  - { offset: 0x5F22F, size: 0x8, addend: 0x0, symName: '-[MJRefreshGifHeader .cxx_destruct]', symObjAddr: 0x938, symBinAddr: 0xD9EC, symSize: 0x40 }
  - { offset: 0x5F2F4, size: 0x8, addend: 0x0, symName: '+[MJRefreshHeader headerWithRefreshingBlock:]', symObjAddr: 0x0, symBinAddr: 0xDA2C, symSize: 0x4C }
  - { offset: 0x5F30E, size: 0x8, addend: 0x0, symName: _MJRefreshHeaderRefreshing2IdleBoundsKey, symObjAddr: 0x1660, symBinAddr: 0x18300, symSize: 0x0 }
  - { offset: 0x5F32E, size: 0x8, addend: 0x0, symName: _MJRefreshHeaderRefreshingBoundsKey, symObjAddr: 0x1668, symBinAddr: 0x18308, symSize: 0x0 }
  - { offset: 0x5F5B5, size: 0x8, addend: 0x0, symName: '+[MJRefreshHeader headerWithRefreshingBlock:]', symObjAddr: 0x0, symBinAddr: 0xDA2C, symSize: 0x4C }
  - { offset: 0x5F60C, size: 0x8, addend: 0x0, symName: '+[MJRefreshHeader headerWithRefreshingTarget:refreshingAction:]', symObjAddr: 0x4C, symBinAddr: 0xDA78, symSize: 0x5C }
  - { offset: 0x5F673, size: 0x8, addend: 0x0, symName: '-[MJRefreshHeader prepare]', symObjAddr: 0xA8, symBinAddr: 0xDAD4, symSize: 0x68 }
  - { offset: 0x5F6A6, size: 0x8, addend: 0x0, symName: '-[MJRefreshHeader placeSubviews]', symObjAddr: 0x110, symBinAddr: 0xDB3C, symSize: 0x68 }
  - { offset: 0x5F6D9, size: 0x8, addend: 0x0, symName: '-[MJRefreshHeader resetInset]', symObjAddr: 0x178, symBinAddr: 0xDBA4, symSize: 0x11C }
  - { offset: 0x5F71C, size: 0x8, addend: 0x0, symName: '-[MJRefreshHeader scrollViewContentOffsetDidChange:]', symObjAddr: 0x294, symBinAddr: 0xDCC0, symSize: 0x1B8 }
  - { offset: 0x5F79F, size: 0x8, addend: 0x0, symName: '-[MJRefreshHeader setState:]', symObjAddr: 0x44C, symBinAddr: 0xDE78, symSize: 0x8C }
  - { offset: 0x5F7F2, size: 0x8, addend: 0x0, symName: '-[MJRefreshHeader headerEndingAction]', symObjAddr: 0x4D8, symBinAddr: 0xDF04, symSize: 0x4AC }
  - { offset: 0x5F89A, size: 0x8, addend: 0x0, symName: '___37-[MJRefreshHeader headerEndingAction]_block_invoke', symObjAddr: 0x984, symBinAddr: 0xE3B0, symSize: 0xC4 }
  - { offset: 0x5F8E5, size: 0x8, addend: 0x0, symName: '___37-[MJRefreshHeader headerEndingAction]_block_invoke.12', symObjAddr: 0xA58, symBinAddr: 0xE474, symSize: 0x70 }
  - { offset: 0x5F93C, size: 0x8, addend: 0x0, symName: '-[MJRefreshHeader headerRefreshingAction]', symObjAddr: 0xAC8, symBinAddr: 0xE4E4, symSize: 0x32C }
  - { offset: 0x5F9F5, size: 0x8, addend: 0x0, symName: '___41-[MJRefreshHeader headerRefreshingAction]_block_invoke', symObjAddr: 0xDF4, symBinAddr: 0xE810, symSize: 0x110 }
  - { offset: 0x5FA47, size: 0x8, addend: 0x0, symName: '___41-[MJRefreshHeader headerRefreshingAction]_block_invoke_2', symObjAddr: 0xF04, symBinAddr: 0xE920, symSize: 0x17C }
  - { offset: 0x5FAB1, size: 0x8, addend: 0x0, symName: '___41-[MJRefreshHeader headerRefreshingAction]_block_invoke.26', symObjAddr: 0x1094, symBinAddr: 0xEA9C, symSize: 0x2C }
  - { offset: 0x5FAF8, size: 0x8, addend: 0x0, symName: '-[MJRefreshHeader animationDidStop:finished:]', symObjAddr: 0x10C0, symBinAddr: 0xEAC8, symSize: 0x2E4 }
  - { offset: 0x5FB94, size: 0x8, addend: 0x0, symName: '-[MJRefreshHeader lastUpdatedTime]', symObjAddr: 0x13A4, symBinAddr: 0xEDAC, symSize: 0x7C }
  - { offset: 0x5FBCC, size: 0x8, addend: 0x0, symName: '-[MJRefreshHeader setIgnoredScrollViewContentInsetTop:]', symObjAddr: 0x1420, symBinAddr: 0xEE28, symSize: 0x3C }
  - { offset: 0x5FC11, size: 0x8, addend: 0x0, symName: '-[MJRefreshHeader lastUpdatedTimeKey]', symObjAddr: 0x145C, symBinAddr: 0xEE64, symSize: 0x10 }
  - { offset: 0x5FC48, size: 0x8, addend: 0x0, symName: '-[MJRefreshHeader setLastUpdatedTimeKey:]', symObjAddr: 0x146C, symBinAddr: 0xEE74, symSize: 0xC }
  - { offset: 0x5FC87, size: 0x8, addend: 0x0, symName: '-[MJRefreshHeader ignoredScrollViewContentInsetTop]', symObjAddr: 0x1478, symBinAddr: 0xEE80, symSize: 0x10 }
  - { offset: 0x5FCBC, size: 0x8, addend: 0x0, symName: '-[MJRefreshHeader isCollectionViewAnimationBug]', symObjAddr: 0x1488, symBinAddr: 0xEE90, symSize: 0x10 }
  - { offset: 0x5FCF3, size: 0x8, addend: 0x0, symName: '-[MJRefreshHeader setIsCollectionViewAnimationBug:]', symObjAddr: 0x1498, symBinAddr: 0xEEA0, symSize: 0x10 }
  - { offset: 0x5FD2E, size: 0x8, addend: 0x0, symName: '-[MJRefreshHeader insetTDelta]', symObjAddr: 0x14A8, symBinAddr: 0xEEB0, symSize: 0x10 }
  - { offset: 0x5FD63, size: 0x8, addend: 0x0, symName: '-[MJRefreshHeader setInsetTDelta:]', symObjAddr: 0x14B8, symBinAddr: 0xEEC0, symSize: 0x10 }
  - { offset: 0x5FDA1, size: 0x8, addend: 0x0, symName: '-[MJRefreshHeader .cxx_destruct]', symObjAddr: 0x14C8, symBinAddr: 0xEED0, symSize: 0x14 }
  - { offset: 0x6007A, size: 0x8, addend: 0x0, symName: '-[MJRefreshNormalHeader arrowView]', symObjAddr: 0x0, symBinAddr: 0xEEE4, symSize: 0x90 }
  - { offset: 0x603E5, size: 0x8, addend: 0x0, symName: '-[MJRefreshNormalHeader arrowView]', symObjAddr: 0x0, symBinAddr: 0xEEE4, symSize: 0x90 }
  - { offset: 0x6043B, size: 0x8, addend: 0x0, symName: '-[MJRefreshNormalHeader loadingView]', symObjAddr: 0x90, symBinAddr: 0xEF74, symSize: 0x94 }
  - { offset: 0x60491, size: 0x8, addend: 0x0, symName: '-[MJRefreshNormalHeader setActivityIndicatorViewStyle:]', symObjAddr: 0x124, symBinAddr: 0xF008, symSize: 0x54 }
  - { offset: 0x604D4, size: 0x8, addend: 0x0, symName: '-[MJRefreshNormalHeader prepare]', symObjAddr: 0x178, symBinAddr: 0xF05C, symSize: 0x70 }
  - { offset: 0x60507, size: 0x8, addend: 0x0, symName: '-[MJRefreshNormalHeader placeSubviews]', symObjAddr: 0x1E8, symBinAddr: 0xF0CC, symSize: 0x2C0 }
  - { offset: 0x605D6, size: 0x8, addend: 0x0, symName: '-[MJRefreshNormalHeader setState:]', symObjAddr: 0x4A8, symBinAddr: 0xF38C, symSize: 0x2B4 }
  - { offset: 0x60629, size: 0x8, addend: 0x0, symName: '___34-[MJRefreshNormalHeader setState:]_block_invoke', symObjAddr: 0x75C, symBinAddr: 0xF640, symSize: 0x38 }
  - { offset: 0x60668, size: 0x8, addend: 0x0, symName: '___34-[MJRefreshNormalHeader setState:]_block_invoke.8', symObjAddr: 0x7A4, symBinAddr: 0xF678, symSize: 0x9C }
  - { offset: 0x606B3, size: 0x8, addend: 0x0, symName: '___34-[MJRefreshNormalHeader setState:]_block_invoke.10', symObjAddr: 0x840, symBinAddr: 0xF714, symSize: 0x5C }
  - { offset: 0x606F2, size: 0x8, addend: 0x0, symName: '___34-[MJRefreshNormalHeader setState:]_block_invoke_2', symObjAddr: 0x89C, symBinAddr: 0xF770, symSize: 0x68 }
  - { offset: 0x60759, size: 0x8, addend: 0x0, symName: '-[MJRefreshNormalHeader setLoadingView:]', symObjAddr: 0x904, symBinAddr: 0xF7D8, symSize: 0x14 }
  - { offset: 0x6079A, size: 0x8, addend: 0x0, symName: '-[MJRefreshNormalHeader activityIndicatorViewStyle]', symObjAddr: 0x918, symBinAddr: 0xF7EC, symSize: 0x10 }
  - { offset: 0x607D1, size: 0x8, addend: 0x0, symName: '-[MJRefreshNormalHeader .cxx_destruct]', symObjAddr: 0x928, symBinAddr: 0xF7FC, symSize: 0x10 }
  - { offset: 0x60A36, size: 0x8, addend: 0x0, symName: '-[MJRefreshNormalTrailer arrowView]', symObjAddr: 0x0, symBinAddr: 0xF80C, symSize: 0x90 }
  - { offset: 0x60CA6, size: 0x8, addend: 0x0, symName: '-[MJRefreshNormalTrailer arrowView]', symObjAddr: 0x0, symBinAddr: 0xF80C, symSize: 0x90 }
  - { offset: 0x60CFC, size: 0x8, addend: 0x0, symName: '-[MJRefreshNormalTrailer placeSubviews]', symObjAddr: 0x90, symBinAddr: 0xF89C, symSize: 0x360 }
  - { offset: 0x60DB6, size: 0x8, addend: 0x0, symName: '-[MJRefreshNormalTrailer setState:]', symObjAddr: 0x3F0, symBinAddr: 0xFBFC, symSize: 0x168 }
  - { offset: 0x60E09, size: 0x8, addend: 0x0, symName: '___35-[MJRefreshNormalTrailer setState:]_block_invoke', symObjAddr: 0x558, symBinAddr: 0xFD64, symSize: 0x68 }
  - { offset: 0x60E70, size: 0x8, addend: 0x0, symName: '___35-[MJRefreshNormalTrailer setState:]_block_invoke.5', symObjAddr: 0x5D0, symBinAddr: 0xFDCC, symSize: 0x5C }
  - { offset: 0x60EBB, size: 0x8, addend: 0x0, symName: '___35-[MJRefreshNormalTrailer setState:]_block_invoke.7', symObjAddr: 0x62C, symBinAddr: 0xFE28, symSize: 0x5C }
  - { offset: 0x60EFA, size: 0x8, addend: 0x0, symName: '___35-[MJRefreshNormalTrailer setState:]_block_invoke_2', symObjAddr: 0x688, symBinAddr: 0xFE84, symSize: 0x68 }
  - { offset: 0x61199, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateHeader stateTitles]', symObjAddr: 0x0, symBinAddr: 0xFEEC, symSize: 0x64 }
  - { offset: 0x614EF, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateHeader stateTitles]', symObjAddr: 0x0, symBinAddr: 0xFEEC, symSize: 0x64 }
  - { offset: 0x61526, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateHeader stateLabel]', symObjAddr: 0x64, symBinAddr: 0xFF50, symSize: 0x68 }
  - { offset: 0x6155D, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateHeader lastUpdatedTimeLabel]', symObjAddr: 0xCC, symBinAddr: 0xFFB8, symSize: 0x68 }
  - { offset: 0x61594, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateHeader setTitle:forState:]', symObjAddr: 0x134, symBinAddr: 0x10020, symSize: 0x124 }
  - { offset: 0x615E7, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateHeader setLastUpdatedTimeKey:]', symObjAddr: 0x258, symBinAddr: 0x10144, symSize: 0x3DC }
  - { offset: 0x616C5, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateHeader prepare]', symObjAddr: 0x634, symBinAddr: 0x10520, symSize: 0x108 }
  - { offset: 0x616F8, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateHeader placeSubviews]', symObjAddr: 0x73C, symBinAddr: 0x10628, symSize: 0x2E8 }
  - { offset: 0x61756, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateHeader setState:]', symObjAddr: 0xA24, symBinAddr: 0x10910, symSize: 0x110 }
  - { offset: 0x617A9, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateHeader lastUpdatedTimeText]', symObjAddr: 0xB34, symBinAddr: 0x10A20, symSize: 0x10 }
  - { offset: 0x617E0, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateHeader setLastUpdatedTimeText:]', symObjAddr: 0xB44, symBinAddr: 0x10A30, symSize: 0xC }
  - { offset: 0x6181F, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateHeader labelLeftInset]', symObjAddr: 0xB50, symBinAddr: 0x10A3C, symSize: 0x10 }
  - { offset: 0x61854, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateHeader setLabelLeftInset:]', symObjAddr: 0xB60, symBinAddr: 0x10A4C, symSize: 0x10 }
  - { offset: 0x61892, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateHeader setStateTitles:]', symObjAddr: 0xB70, symBinAddr: 0x10A5C, symSize: 0x14 }
  - { offset: 0x618D3, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateHeader .cxx_destruct]', symObjAddr: 0xB84, symBinAddr: 0x10A70, symSize: 0x40 }
  - { offset: 0x61985, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateTrailer stateTitles]', symObjAddr: 0x0, symBinAddr: 0x10AB0, symSize: 0x64 }
  - { offset: 0x61BF2, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateTrailer stateTitles]', symObjAddr: 0x0, symBinAddr: 0x10AB0, symSize: 0x64 }
  - { offset: 0x61C29, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateTrailer stateLabel]', symObjAddr: 0x64, symBinAddr: 0x10B14, symSize: 0x70 }
  - { offset: 0x61C7F, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateTrailer setTitle:forState:]', symObjAddr: 0xD4, symBinAddr: 0x10B84, symSize: 0x90 }
  - { offset: 0x61CD2, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateTrailer prepare]', symObjAddr: 0x164, symBinAddr: 0x10C14, symSize: 0xF0 }
  - { offset: 0x61D05, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateTrailer setState:]', symObjAddr: 0x254, symBinAddr: 0x10D04, symSize: 0xE0 }
  - { offset: 0x61D58, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateTrailer placeSubviews]', symObjAddr: 0x334, symBinAddr: 0x10DE4, symSize: 0x178 }
  - { offset: 0x61DA7, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateTrailer setStateTitles:]', symObjAddr: 0x4AC, symBinAddr: 0x10F5C, symSize: 0x14 }
  - { offset: 0x61DE8, size: 0x8, addend: 0x0, symName: '-[MJRefreshStateTrailer .cxx_destruct]', symObjAddr: 0x4C0, symBinAddr: 0x10F70, symSize: 0x14 }
  - { offset: 0x61E90, size: 0x8, addend: 0x0, symName: '+[MJRefreshTrailer trailerWithRefreshingBlock:]', symObjAddr: 0x0, symBinAddr: 0x10F84, symSize: 0x4C }
  - { offset: 0x620F0, size: 0x8, addend: 0x0, symName: '+[MJRefreshTrailer trailerWithRefreshingBlock:]', symObjAddr: 0x0, symBinAddr: 0x10F84, symSize: 0x4C }
  - { offset: 0x62147, size: 0x8, addend: 0x0, symName: '+[MJRefreshTrailer trailerWithRefreshingTarget:refreshingAction:]', symObjAddr: 0x4C, symBinAddr: 0x10FD0, symSize: 0x5C }
  - { offset: 0x621AE, size: 0x8, addend: 0x0, symName: '-[MJRefreshTrailer scrollViewContentOffsetDidChange:]', symObjAddr: 0xA8, symBinAddr: 0x1102C, symSize: 0x1B8 }
  - { offset: 0x62238, size: 0x8, addend: 0x0, symName: '-[MJRefreshTrailer setState:]', symObjAddr: 0x260, symBinAddr: 0x111E4, symSize: 0x230 }
  - { offset: 0x622A2, size: 0x8, addend: 0x0, symName: '___29-[MJRefreshTrailer setState:]_block_invoke', symObjAddr: 0x490, symBinAddr: 0x11414, symSize: 0xC4 }
  - { offset: 0x622ED, size: 0x8, addend: 0x0, symName: '___29-[MJRefreshTrailer setState:]_block_invoke.3', symObjAddr: 0x564, symBinAddr: 0x114D8, symSize: 0x70 }
  - { offset: 0x62344, size: 0x8, addend: 0x0, symName: '___29-[MJRefreshTrailer setState:]_block_invoke.5', symObjAddr: 0x5D4, symBinAddr: 0x11548, symSize: 0x104 }
  - { offset: 0x623C2, size: 0x8, addend: 0x0, symName: '___29-[MJRefreshTrailer setState:]_block_invoke_2', symObjAddr: 0x6D8, symBinAddr: 0x1164C, symSize: 0x8 }
  - { offset: 0x6240D, size: 0x8, addend: 0x0, symName: '-[MJRefreshTrailer scrollViewContentSizeDidChange:]', symObjAddr: 0x6E0, symBinAddr: 0x11654, symSize: 0xD0 }
  - { offset: 0x6249D, size: 0x8, addend: 0x0, symName: '-[MJRefreshTrailer placeSubviews]', symObjAddr: 0x7B0, symBinAddr: 0x11724, symSize: 0x80 }
  - { offset: 0x624D0, size: 0x8, addend: 0x0, symName: '-[MJRefreshTrailer willMoveToSuperview:]', symObjAddr: 0x830, symBinAddr: 0x117A4, symSize: 0x90 }
  - { offset: 0x62513, size: 0x8, addend: 0x0, symName: '-[MJRefreshTrailer happenOffsetX]', symObjAddr: 0x8C0, symBinAddr: 0x11834, symSize: 0x44 }
  - { offset: 0x6255A, size: 0x8, addend: 0x0, symName: '-[MJRefreshTrailer widthForContentBreakView]', symObjAddr: 0x904, symBinAddr: 0x11878, symSize: 0x84 }
  - { offset: 0x625A1, size: 0x8, addend: 0x0, symName: '-[MJRefreshTrailer ignoredScrollViewContentInsetRight]', symObjAddr: 0x988, symBinAddr: 0x118FC, symSize: 0x10 }
  - { offset: 0x625D6, size: 0x8, addend: 0x0, symName: '-[MJRefreshTrailer setIgnoredScrollViewContentInsetRight:]', symObjAddr: 0x998, symBinAddr: 0x1190C, symSize: 0x10 }
  - { offset: 0x62614, size: 0x8, addend: 0x0, symName: '-[MJRefreshTrailer lastRefreshCount]', symObjAddr: 0x9A8, symBinAddr: 0x1191C, symSize: 0x10 }
  - { offset: 0x6264B, size: 0x8, addend: 0x0, symName: '-[MJRefreshTrailer setLastRefreshCount:]', symObjAddr: 0x9B8, symBinAddr: 0x1192C, symSize: 0x10 }
  - { offset: 0x62688, size: 0x8, addend: 0x0, symName: '-[MJRefreshTrailer lastRightDelta]', symObjAddr: 0x9C8, symBinAddr: 0x1193C, symSize: 0x10 }
  - { offset: 0x626BD, size: 0x8, addend: 0x0, symName: '-[MJRefreshTrailer setLastRightDelta:]', symObjAddr: 0x9D8, symBinAddr: 0x1194C, symSize: 0x10 }
  - { offset: 0x62940, size: 0x8, addend: 0x0, symName: '+[NSBundle(MJRefresh) mj_refreshBundle]', symObjAddr: 0x0, symBinAddr: 0x1195C, symSize: 0xB0 }
  - { offset: 0x6294E, size: 0x8, addend: 0x0, symName: '+[NSBundle(MJRefresh) mj_refreshBundle]', symObjAddr: 0x0, symBinAddr: 0x1195C, symSize: 0xB0 }
  - { offset: 0x62978, size: 0x8, addend: 0x0, symName: _mj_refreshBundle.refreshBundle, symObjAddr: 0x20B0, symBinAddr: 0x1D440, symSize: 0x0 }
  - { offset: 0x629CA, size: 0x8, addend: 0x0, symName: '+[NSBundle(MJRefresh) mj_arrowImage]', symObjAddr: 0xB0, symBinAddr: 0x11A0C, symSize: 0xBC }
  - { offset: 0x629F4, size: 0x8, addend: 0x0, symName: _mj_arrowImage.arrowImage, symObjAddr: 0x20B8, symBinAddr: 0x1D448, symSize: 0x0 }
  - { offset: 0x62A1C, size: 0x8, addend: 0x0, symName: '+[NSBundle(MJRefresh) mj_trailArrowImage]', symObjAddr: 0x16C, symBinAddr: 0x11AC8, symSize: 0xBC }
  - { offset: 0x62A46, size: 0x8, addend: 0x0, symName: _mj_trailArrowImage.arrowImage, symObjAddr: 0x20C0, symBinAddr: 0x1D450, symSize: 0x0 }
  - { offset: 0x62A69, size: 0x8, addend: 0x0, symName: '+[NSBundle(MJRefresh) mj_localizedStringForKey:value:]', symObjAddr: 0x230, symBinAddr: 0x11B8C, symSize: 0x290 }
  - { offset: 0x62A93, size: 0x8, addend: 0x0, symName: '_mj_localizedStringForKey:value:.bundle', symObjAddr: 0x20C8, symBinAddr: 0x1D458, symSize: 0x0 }
  - { offset: 0x62AFD, size: 0x8, addend: 0x0, symName: '+[NSBundle(MJRefresh) mj_localizedStringForKey:]', symObjAddr: 0x228, symBinAddr: 0x11B84, symSize: 0x8 }
  - { offset: 0x62BFE, size: 0x8, addend: 0x0, symName: '+[UIScrollView(MJExtension) initialize]', symObjAddr: 0x0, symBinAddr: 0x11E1C, symSize: 0x6C }
  - { offset: 0x62C0C, size: 0x8, addend: 0x0, symName: '+[UIScrollView(MJExtension) initialize]', symObjAddr: 0x0, symBinAddr: 0x11E1C, symSize: 0x6C }
  - { offset: 0x62C32, size: 0x8, addend: 0x0, symName: _initialize.onceToken, symObjAddr: 0x3A00, symBinAddr: 0x1D460, symSize: 0x0 }
  - { offset: 0x62C96, size: 0x8, addend: 0x0, symName: _respondsToAdjustedContentInset_, symObjAddr: 0x3A08, symBinAddr: 0x1D468, symSize: 0x0 }
  - { offset: 0x62CF6, size: 0x8, addend: 0x0, symName: '___39+[UIScrollView(MJExtension) initialize]_block_invoke', symObjAddr: 0x6C, symBinAddr: 0x11E88, symSize: 0x28 }
  - { offset: 0x62D35, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJExtension) mj_inset]', symObjAddr: 0x94, symBinAddr: 0x11EB0, symSize: 0x14 }
  - { offset: 0x62D6A, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJExtension) setMj_insetT:]', symObjAddr: 0xA8, symBinAddr: 0x11EC4, symSize: 0x84 }
  - { offset: 0x62DBD, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJExtension) mj_insetT]', symObjAddr: 0x12C, symBinAddr: 0x11F48, symSize: 0x4 }
  - { offset: 0x62DF2, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJExtension) setMj_insetB:]', symObjAddr: 0x130, symBinAddr: 0x11F4C, symSize: 0x84 }
  - { offset: 0x62E45, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJExtension) mj_insetB]', symObjAddr: 0x1B4, symBinAddr: 0x11FD0, symSize: 0x18 }
  - { offset: 0x62E7C, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJExtension) setMj_insetL:]', symObjAddr: 0x1CC, symBinAddr: 0x11FE8, symSize: 0x84 }
  - { offset: 0x62ECF, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJExtension) mj_insetL]', symObjAddr: 0x250, symBinAddr: 0x1206C, symSize: 0x18 }
  - { offset: 0x62F06, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJExtension) setMj_insetR:]', symObjAddr: 0x268, symBinAddr: 0x12084, symSize: 0x84 }
  - { offset: 0x62F59, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJExtension) mj_insetR]', symObjAddr: 0x2EC, symBinAddr: 0x12108, symSize: 0x18 }
  - { offset: 0x62F90, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJExtension) setMj_offsetX:]', symObjAddr: 0x304, symBinAddr: 0x12120, symSize: 0x34 }
  - { offset: 0x62FE3, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJExtension) mj_offsetX]', symObjAddr: 0x338, symBinAddr: 0x12154, symSize: 0x4 }
  - { offset: 0x63018, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJExtension) setMj_offsetY:]', symObjAddr: 0x33C, symBinAddr: 0x12158, symSize: 0x34 }
  - { offset: 0x6306B, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJExtension) mj_offsetY]', symObjAddr: 0x370, symBinAddr: 0x1218C, symSize: 0x18 }
  - { offset: 0x630A2, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJExtension) setMj_contentW:]', symObjAddr: 0x388, symBinAddr: 0x121A4, symSize: 0x34 }
  - { offset: 0x630F5, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJExtension) mj_contentW]', symObjAddr: 0x3BC, symBinAddr: 0x121D8, symSize: 0x4 }
  - { offset: 0x6312A, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJExtension) setMj_contentH:]', symObjAddr: 0x3C0, symBinAddr: 0x121DC, symSize: 0x34 }
  - { offset: 0x6317D, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJExtension) mj_contentH]', symObjAddr: 0x3F4, symBinAddr: 0x12210, symSize: 0x18 }
  - { offset: 0x632AA, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJRefresh) setMj_header:]', symObjAddr: 0x0, symBinAddr: 0x12228, symSize: 0x9C }
  - { offset: 0x632C4, size: 0x8, addend: 0x0, symName: _MJRefreshHeaderKey, symObjAddr: 0x30C, symBinAddr: 0x14F00, symSize: 0x0 }
  - { offset: 0x632E6, size: 0x8, addend: 0x0, symName: _MJRefreshFooterKey, symObjAddr: 0x30D, symBinAddr: 0x14F01, symSize: 0x0 }
  - { offset: 0x632FC, size: 0x8, addend: 0x0, symName: _MJRefreshTrailerKey, symObjAddr: 0x30E, symBinAddr: 0x14F02, symSize: 0x0 }
  - { offset: 0x63338, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJRefresh) setMj_header:]', symObjAddr: 0x0, symBinAddr: 0x12228, symSize: 0x9C }
  - { offset: 0x633ED, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJRefresh) mj_header]', symObjAddr: 0x9C, symBinAddr: 0x122C4, symSize: 0xC }
  - { offset: 0x63451, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJRefresh) setMj_footer:]', symObjAddr: 0xA8, symBinAddr: 0x122D0, symSize: 0x9C }
  - { offset: 0x634B9, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJRefresh) mj_footer]', symObjAddr: 0x144, symBinAddr: 0x1236C, symSize: 0xC }
  - { offset: 0x63505, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJRefresh) setMj_trailer:]', symObjAddr: 0x150, symBinAddr: 0x12378, symSize: 0x9C }
  - { offset: 0x6356D, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJRefresh) mj_trailer]', symObjAddr: 0x1EC, symBinAddr: 0x12414, symSize: 0xC }
  - { offset: 0x635B9, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJRefresh) setFooter:]', symObjAddr: 0x1F8, symBinAddr: 0x12420, symSize: 0x4 }
  - { offset: 0x635F8, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJRefresh) footer]', symObjAddr: 0x1FC, symBinAddr: 0x12424, symSize: 0x4 }
  - { offset: 0x6362D, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJRefresh) setHeader:]', symObjAddr: 0x200, symBinAddr: 0x12428, symSize: 0x4 }
  - { offset: 0x6366C, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJRefresh) header]', symObjAddr: 0x204, symBinAddr: 0x1242C, symSize: 0x4 }
  - { offset: 0x636A1, size: 0x8, addend: 0x0, symName: '-[UIScrollView(MJRefresh) mj_totalDataCount]', symObjAddr: 0x208, symBinAddr: 0x12430, symSize: 0x104 }
  - { offset: 0x63A2D, size: 0x8, addend: 0x0, symName: '-[UIView(MJExtension) setMj_x:]', symObjAddr: 0x0, symBinAddr: 0x12534, symSize: 0x34 }
  - { offset: 0x63A3B, size: 0x8, addend: 0x0, symName: '-[UIView(MJExtension) setMj_x:]', symObjAddr: 0x0, symBinAddr: 0x12534, symSize: 0x34 }
  - { offset: 0x63A8E, size: 0x8, addend: 0x0, symName: '-[UIView(MJExtension) mj_x]', symObjAddr: 0x34, symBinAddr: 0x12568, symSize: 0x4 }
  - { offset: 0x63AC3, size: 0x8, addend: 0x0, symName: '-[UIView(MJExtension) setMj_y:]', symObjAddr: 0x38, symBinAddr: 0x1256C, symSize: 0x34 }
  - { offset: 0x63B16, size: 0x8, addend: 0x0, symName: '-[UIView(MJExtension) mj_y]', symObjAddr: 0x6C, symBinAddr: 0x125A0, symSize: 0x18 }
  - { offset: 0x63B4D, size: 0x8, addend: 0x0, symName: '-[UIView(MJExtension) setMj_w:]', symObjAddr: 0x84, symBinAddr: 0x125B8, symSize: 0x34 }
  - { offset: 0x63BA0, size: 0x8, addend: 0x0, symName: '-[UIView(MJExtension) mj_w]', symObjAddr: 0xB8, symBinAddr: 0x125EC, symSize: 0x18 }
  - { offset: 0x63BD7, size: 0x8, addend: 0x0, symName: '-[UIView(MJExtension) setMj_h:]', symObjAddr: 0xD0, symBinAddr: 0x12604, symSize: 0x34 }
  - { offset: 0x63C2A, size: 0x8, addend: 0x0, symName: '-[UIView(MJExtension) mj_h]', symObjAddr: 0x104, symBinAddr: 0x12638, symSize: 0x18 }
  - { offset: 0x63C61, size: 0x8, addend: 0x0, symName: '-[UIView(MJExtension) setMj_size:]', symObjAddr: 0x11C, symBinAddr: 0x12650, symSize: 0x3C }
  - { offset: 0x63CB4, size: 0x8, addend: 0x0, symName: '-[UIView(MJExtension) mj_size]', symObjAddr: 0x158, symBinAddr: 0x1268C, symSize: 0x1C }
  - { offset: 0x63CEB, size: 0x8, addend: 0x0, symName: '-[UIView(MJExtension) setMj_origin:]', symObjAddr: 0x174, symBinAddr: 0x126A8, symSize: 0x3C }
  - { offset: 0x63D3E, size: 0x8, addend: 0x0, symName: '-[UIView(MJExtension) mj_origin]', symObjAddr: 0x1B0, symBinAddr: 0x126E4, symSize: 0x4 }
...
