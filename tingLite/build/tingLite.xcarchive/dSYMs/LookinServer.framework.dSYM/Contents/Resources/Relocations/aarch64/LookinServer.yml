---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Documents/tingliteproject/tingLite/DerivedData/tingLite/Build/Intermediates.noindex/ArchiveIntermediates/tingLite/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/LookinServer.framework/LookinServer'
relocations:
  - { offset: 0x58E54, size: 0x8, addend: 0x0, symName: _LookinServerVersionString, symObjAddr: 0x0, symBinAddr: 0x364A0, symSize: 0x0 }
  - { offset: 0x58E89, size: 0x8, addend: 0x0, symName: _LookinServerVersionNumber, symObjAddr: 0x30, symBinAddr: 0x364D0, symSize: 0x0 }
  - { offset: 0x58EC6, size: 0x8, addend: 0x0, symName: '-[CALayer(Lookin) lookin_removeImplicitAnimations]', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0xC18 }
  - { offset: 0x58ED9, size: 0x8, addend: 0x0, symName: '-[CALayer(Lookin) lookin_removeImplicitAnimations]', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0xC18 }
  - { offset: 0x591E3, size: 0x8, addend: 0x0, symName: '-[CALayer(LookinServer) lks_window]', symObjAddr: 0x0, symBinAddr: 0x4C18, symSize: 0xE0 }
  - { offset: 0x59251, size: 0x8, addend: 0x0, symName: '-[CALayer(LookinServer) lks_window]', symObjAddr: 0x0, symBinAddr: 0x4C18, symSize: 0xE0 }
  - { offset: 0x592AF, size: 0x8, addend: 0x0, symName: '-[CALayer(LookinServer) lks_frameInWindow:]', symObjAddr: 0xE0, symBinAddr: 0x4CF8, symSize: 0x134 }
  - { offset: 0x59326, size: 0x8, addend: 0x0, symName: '-[CALayer(LookinServer) lks_hostView]', symObjAddr: 0x214, symBinAddr: 0x4E2C, symSize: 0xBC }
  - { offset: 0x5937C, size: 0x8, addend: 0x0, symName: '-[CALayer(LookinServer) lks_groupScreenshotWithLowQuality:]', symObjAddr: 0x2D0, symBinAddr: 0x4EE8, symSize: 0x22C }
  - { offset: 0x59541, size: 0x8, addend: 0x0, symName: '-[CALayer(LookinServer) lks_soloScreenshotWithLowQuality:]', symObjAddr: 0x4FC, symBinAddr: 0x5114, symSize: 0x33C }
  - { offset: 0x596E1, size: 0x8, addend: 0x0, symName: '___58-[CALayer(LookinServer) lks_soloScreenshotWithLowQuality:]_block_invoke', symObjAddr: 0x838, symBinAddr: 0x5450, symSize: 0x4C }
  - { offset: 0x59744, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s, symObjAddr: 0x884, symBinAddr: 0x549C, symSize: 0x8 }
  - { offset: 0x5976B, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s, symObjAddr: 0x88C, symBinAddr: 0x54A4, symSize: 0x8 }
  - { offset: 0x5978A, size: 0x8, addend: 0x0, symName: '___58-[CALayer(LookinServer) lks_soloScreenshotWithLowQuality:]_block_invoke.20', symObjAddr: 0x894, symBinAddr: 0x54AC, symSize: 0xC }
  - { offset: 0x597D9, size: 0x8, addend: 0x0, symName: '-[CALayer(LookinServer) lks_relatedClassChainList]', symObjAddr: 0x8A0, symBinAddr: 0x54B8, symSize: 0x170 }
  - { offset: 0x5983F, size: 0x8, addend: 0x0, symName: '+[CALayer(LookinServer) lks_getClassListOfObject:endingClass:]', symObjAddr: 0xA10, symBinAddr: 0x5628, symSize: 0x94 }
  - { offset: 0x598B2, size: 0x8, addend: 0x0, symName: '-[CALayer(LookinServer) lks_selfRelation]', symObjAddr: 0xAA4, symBinAddr: 0x56BC, symSize: 0x20C }
  - { offset: 0x5995C, size: 0x8, addend: 0x0, symName: '___41-[CALayer(LookinServer) lks_selfRelation]_block_invoke', symObjAddr: 0xCB0, symBinAddr: 0x58C8, symSize: 0x9C }
  - { offset: 0x599A3, size: 0x8, addend: 0x0, symName: '-[CALayer(LookinServer) lks_backgroundColor]', symObjAddr: 0xD4C, symBinAddr: 0x5964, symSize: 0x2C }
  - { offset: 0x599DA, size: 0x8, addend: 0x0, symName: '-[CALayer(LookinServer) setLks_backgroundColor:]', symObjAddr: 0xD78, symBinAddr: 0x5990, symSize: 0x30 }
  - { offset: 0x59A1D, size: 0x8, addend: 0x0, symName: '-[CALayer(LookinServer) lks_borderColor]', symObjAddr: 0xDA8, symBinAddr: 0x59C0, symSize: 0x2C }
  - { offset: 0x59A54, size: 0x8, addend: 0x0, symName: '-[CALayer(LookinServer) setLks_borderColor:]', symObjAddr: 0xDD4, symBinAddr: 0x59EC, symSize: 0x30 }
  - { offset: 0x59A97, size: 0x8, addend: 0x0, symName: '-[CALayer(LookinServer) lks_shadowColor]', symObjAddr: 0xE04, symBinAddr: 0x5A1C, symSize: 0x2C }
  - { offset: 0x59ACE, size: 0x8, addend: 0x0, symName: '-[CALayer(LookinServer) setLks_shadowColor:]', symObjAddr: 0xE30, symBinAddr: 0x5A48, symSize: 0x30 }
  - { offset: 0x59B11, size: 0x8, addend: 0x0, symName: '-[CALayer(LookinServer) lks_shadowOffsetWidth]', symObjAddr: 0xE60, symBinAddr: 0x5A78, symSize: 0x4 }
  - { offset: 0x59B46, size: 0x8, addend: 0x0, symName: '-[CALayer(LookinServer) setLks_shadowOffsetWidth:]', symObjAddr: 0xE64, symBinAddr: 0x5A7C, symSize: 0x34 }
  - { offset: 0x59B89, size: 0x8, addend: 0x0, symName: '-[CALayer(LookinServer) lks_shadowOffsetHeight]', symObjAddr: 0xE98, symBinAddr: 0x5AB0, symSize: 0x18 }
  - { offset: 0x59BC0, size: 0x8, addend: 0x0, symName: '-[CALayer(LookinServer) setLks_shadowOffsetHeight:]', symObjAddr: 0xEB0, symBinAddr: 0x5AC8, symSize: 0x34 }
  - { offset: 0x59FD4, size: 0x8, addend: 0x0, symName: '+[LKS_AttrGroupsMaker attrGroupsForLayer:]', symObjAddr: 0x0, symBinAddr: 0x5AFC, symSize: 0xC8 }
  - { offset: 0x5A0BC, size: 0x8, addend: 0x0, symName: '+[LKS_AttrGroupsMaker attrGroupsForLayer:]', symObjAddr: 0x0, symBinAddr: 0x5AFC, symSize: 0xC8 }
  - { offset: 0x5A113, size: 0x8, addend: 0x0, symName: '___42+[LKS_AttrGroupsMaker attrGroupsForLayer:]_block_invoke', symObjAddr: 0xC8, symBinAddr: 0x5BC4, symSize: 0x184 }
  - { offset: 0x5A1CC, size: 0x8, addend: 0x0, symName: '___42+[LKS_AttrGroupsMaker attrGroupsForLayer:]_block_invoke_2', symObjAddr: 0x24C, symBinAddr: 0x5D48, symSize: 0x138 }
  - { offset: 0x5A26A, size: 0x8, addend: 0x0, symName: '___42+[LKS_AttrGroupsMaker attrGroupsForLayer:]_block_invoke_3', symObjAddr: 0x384, symBinAddr: 0x5E80, symSize: 0x154 }
  - { offset: 0x5A36B, size: 0x8, addend: 0x0, symName: '___42+[LKS_AttrGroupsMaker attrGroupsForLayer:]_block_invoke.17', symObjAddr: 0x4E8, symBinAddr: 0x5FD4, symSize: 0x4C }
  - { offset: 0x5A3A6, size: 0x8, addend: 0x0, symName: '+[LKS_AttrGroupsMaker _attributeWithIdentifer:targetObject:]', symObjAddr: 0x534, symBinAddr: 0x6020, symSize: 0x8C0 }
  - { offset: 0x5AE02, size: 0x8, addend: 0x0, symName: '+[LKS_AttrModificationPatchHandler handleLayerOids:lowImageQuality:block:]', symObjAddr: 0x0, symBinAddr: 0x68E0, symSize: 0x1E4 }
  - { offset: 0x5AE1C, size: 0x8, addend: 0x0, symName: '+[LKS_AttrModificationPatchHandler handleLayerOids:lowImageQuality:block:]', symObjAddr: 0x0, symBinAddr: 0x68E0, symSize: 0x1E4 }
  - { offset: 0x5AE9E, size: 0x8, addend: 0x0, symName: '___74+[LKS_AttrModificationPatchHandler handleLayerOids:lowImageQuality:block:]_block_invoke', symObjAddr: 0x1E4, symBinAddr: 0x6AC4, symSize: 0x2C0 }
  - { offset: 0x5AFB1, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40b, symObjAddr: 0x4A4, symBinAddr: 0x6D84, symSize: 0x34 }
  - { offset: 0x5AFDA, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s, symObjAddr: 0x4D8, symBinAddr: 0x6DB8, symSize: 0x28 }
  - { offset: 0x5B2F8, size: 0x8, addend: 0x0, symName: '+[LKS_ConnectionManager sharedInstance]', symObjAddr: 0x0, symBinAddr: 0x6DE0, symSize: 0x40 }
  - { offset: 0x5B31C, size: 0x8, addend: 0x0, symName: _LKS_ConnectionDidEndNotificationName, symObjAddr: 0x1698, symBinAddr: 0x445A0, symSize: 0x0 }
  - { offset: 0x5B326, size: 0x8, addend: 0x0, symName: '+[LKS_ConnectionManager sharedInstance]', symObjAddr: 0x0, symBinAddr: 0x6DE0, symSize: 0x40 }
  - { offset: 0x5B350, size: 0x8, addend: 0x0, symName: _sharedInstance.sharedInstance, symObjAddr: 0x9178, symBinAddr: 0x5A3B0, symSize: 0x0 }
  - { offset: 0x5B366, size: 0x8, addend: 0x0, symName: _sharedInstance.onceToken, symObjAddr: 0x9180, symBinAddr: 0x5A3B8, symSize: 0x0 }
  - { offset: 0x5B5C1, size: 0x8, addend: 0x0, symName: '___39+[LKS_ConnectionManager sharedInstance]_block_invoke', symObjAddr: 0x40, symBinAddr: 0x6E20, symSize: 0x30 }
  - { offset: 0x5B5E8, size: 0x8, addend: 0x0, symName: '+[LKS_ConnectionManager load]', symObjAddr: 0x70, symBinAddr: 0x6E50, symSize: 0x24 }
  - { offset: 0x5B617, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager init]', symObjAddr: 0x94, symBinAddr: 0x6E74, symSize: 0x27C }
  - { offset: 0x5B66C, size: 0x8, addend: 0x0, symName: '___29-[LKS_ConnectionManager init]_block_invoke', symObjAddr: 0x310, symBinAddr: 0x70F0, symSize: 0x38 }
  - { offset: 0x5B69F, size: 0x8, addend: 0x0, symName: '___29-[LKS_ConnectionManager init]_block_invoke_2', symObjAddr: 0x348, symBinAddr: 0x7128, symSize: 0x7C }
  - { offset: 0x5B6D6, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager _handleWillResignActiveNotification]', symObjAddr: 0x3C4, symBinAddr: 0x71A4, symSize: 0xAC }
  - { offset: 0x5B709, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager _handleApplicationDidBecomeActive]', symObjAddr: 0x470, symBinAddr: 0x7250, symSize: 0x28 }
  - { offset: 0x5B73C, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager searchPortToListenIfNoConnection]', symObjAddr: 0x498, symBinAddr: 0x7278, symSize: 0xCC }
  - { offset: 0x5B78B, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager isiOSAppOnMac]', symObjAddr: 0x564, symBinAddr: 0x7344, symSize: 0xD4 }
  - { offset: 0x5B7BE, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager _tryToListenOnPortFrom:to:current:]', symObjAddr: 0x638, symBinAddr: 0x7418, symSize: 0xCC }
  - { offset: 0x5B831, size: 0x8, addend: 0x0, symName: '___59-[LKS_ConnectionManager _tryToListenOnPortFrom:to:current:]_block_invoke', symObjAddr: 0x704, symBinAddr: 0x74E4, symSize: 0xB0 }
  - { offset: 0x5B8F8, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s, symObjAddr: 0x7B4, symBinAddr: 0x7594, symSize: 0x28 }
  - { offset: 0x5B921, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager dealloc]', symObjAddr: 0x804, symBinAddr: 0x75BC, symSize: 0xA0 }
  - { offset: 0x5B954, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager respond:requestType:tag:]', symObjAddr: 0x8A4, symBinAddr: 0x765C, symSize: 0x4 }
  - { offset: 0x5B9AF, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager pushData:type:]', symObjAddr: 0x8A8, symBinAddr: 0x7660, symSize: 0x8 }
  - { offset: 0x5B9FC, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager _sendData:frameOfType:tag:]', symObjAddr: 0x8B0, symBinAddr: 0x7668, symSize: 0xD0 }
  - { offset: 0x5BA8E, size: 0x8, addend: 0x0, symName: '___51-[LKS_ConnectionManager _sendData:frameOfType:tag:]_block_invoke', symObjAddr: 0x980, symBinAddr: 0x7738, symSize: 0x4 }
  - { offset: 0x5BAC1, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager ioFrameChannel:shouldAcceptFrameOfType:tag:payloadSize:]', symObjAddr: 0x984, symBinAddr: 0x773C, symSize: 0xA0 }
  - { offset: 0x5BB30, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager ioFrameChannel:didReceiveFrameOfType:tag:payload:]', symObjAddr: 0xA24, symBinAddr: 0x77DC, symSize: 0x118 }
  - { offset: 0x5BBEB, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager ioFrameChannel:didAcceptConnection:fromAddress:]', symObjAddr: 0xB3C, symBinAddr: 0x78F4, symSize: 0xF0 }
  - { offset: 0x5BC6C, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager ioFrameChannel:didEndWithError:]', symObjAddr: 0xC2C, symBinAddr: 0x79E4, symSize: 0xFC }
  - { offset: 0x5BCDB, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager _handleLocalInspect:]', symObjAddr: 0xD28, symBinAddr: 0x7AE0, symSize: 0x100 }
  - { offset: 0x5BD74, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager handleGetLookinInfo:]', symObjAddr: 0xE28, symBinAddr: 0x7BE0, symSize: 0x9C }
  - { offset: 0x5BDE1, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager applicationIsActive]', symObjAddr: 0xEC4, symBinAddr: 0x7C7C, symSize: 0x8 }
  - { offset: 0x5BE18, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager setApplicationIsActive:]', symObjAddr: 0xECC, symBinAddr: 0x7C84, symSize: 0x8 }
  - { offset: 0x5BE53, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager peerChannel_]', symObjAddr: 0xED4, symBinAddr: 0x7C8C, symSize: 0x18 }
  - { offset: 0x5BE8A, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager setPeerChannel_:]', symObjAddr: 0xEEC, symBinAddr: 0x7CA4, symSize: 0xC }
  - { offset: 0x5BECB, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager requestHandler]', symObjAddr: 0xEF8, symBinAddr: 0x7CB0, symSize: 0x8 }
  - { offset: 0x5BF02, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager setRequestHandler:]', symObjAddr: 0xF00, symBinAddr: 0x7CB8, symSize: 0xC }
  - { offset: 0x5BF43, size: 0x8, addend: 0x0, symName: '-[LKS_ConnectionManager .cxx_destruct]', symObjAddr: 0xF0C, symBinAddr: 0x7CC4, symSize: 0x2C }
  - { offset: 0x5C456, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrGroupsMaker initWithLayer:]', symObjAddr: 0x0, symBinAddr: 0x7CF0, symSize: 0xA0 }
  - { offset: 0x5C5E8, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrGroupsMaker initWithLayer:]', symObjAddr: 0x0, symBinAddr: 0x7CF0, symSize: 0xA0 }
  - { offset: 0x5C62F, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrGroupsMaker execute]', symObjAddr: 0xA0, symBinAddr: 0x7D90, symSize: 0x2FC }
  - { offset: 0x5C6D5, size: 0x8, addend: 0x0, symName: '___36-[LKS_CustomAttrGroupsMaker execute]_block_invoke', symObjAddr: 0x39C, symBinAddr: 0x808C, symSize: 0x114 }
  - { offset: 0x5C76B, size: 0x8, addend: 0x0, symName: '___36-[LKS_CustomAttrGroupsMaker execute]_block_invoke_2', symObjAddr: 0x4B0, symBinAddr: 0x81A0, symSize: 0xDC }
  - { offset: 0x5C7ED, size: 0x8, addend: 0x0, symName: '___36-[LKS_CustomAttrGroupsMaker execute]_block_invoke.25', symObjAddr: 0x59C, symBinAddr: 0x827C, symSize: 0x84 }
  - { offset: 0x5C838, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrGroupsMaker makeAttrsForViewOrLayer:selectorName:]', symObjAddr: 0x620, symBinAddr: 0x8300, symSize: 0x268 }
  - { offset: 0x5C943, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrGroupsMaker makeAttrsFromRawProperties:]', symObjAddr: 0x888, symBinAddr: 0x8568, symSize: 0x23C }
  - { offset: 0x5C9CC, size: 0x8, addend: 0x0, symName: '+[LKS_CustomAttrGroupsMaker attrFromRawDict:saveCustomSetter:groupTitle:]', symObjAddr: 0xAC4, symBinAddr: 0x87A4, symSize: 0xDD4 }
  - { offset: 0x5CCC3, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrGroupsMaker getGroups]', symObjAddr: 0x1898, symBinAddr: 0x9578, symSize: 0x4 }
  - { offset: 0x5CCF9, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrGroupsMaker getCustomDisplayTitle]', symObjAddr: 0x189C, symBinAddr: 0x957C, symSize: 0x4 }
  - { offset: 0x5CD2F, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrGroupsMaker getDanceUISource]', symObjAddr: 0x18A0, symBinAddr: 0x9580, symSize: 0x4 }
  - { offset: 0x5CD65, size: 0x8, addend: 0x0, symName: '+[LKS_CustomAttrGroupsMaker makeGroupsFromRawProperties:saveCustomSetter:]', symObjAddr: 0x18A4, symBinAddr: 0x9584, symSize: 0x2C0 }
  - { offset: 0x5CE26, size: 0x8, addend: 0x0, symName: '___74+[LKS_CustomAttrGroupsMaker makeGroupsFromRawProperties:saveCustomSetter:]_block_invoke', symObjAddr: 0x1B64, symBinAddr: 0x9844, symSize: 0x114 }
  - { offset: 0x5CEC4, size: 0x8, addend: 0x0, symName: '___74+[LKS_CustomAttrGroupsMaker makeGroupsFromRawProperties:saveCustomSetter:]_block_invoke_2', symObjAddr: 0x1C78, symBinAddr: 0x9958, symSize: 0xDC }
  - { offset: 0x5CF4D, size: 0x8, addend: 0x0, symName: '___74+[LKS_CustomAttrGroupsMaker makeGroupsFromRawProperties:saveCustomSetter:]_block_invoke_3', symObjAddr: 0x1D54, symBinAddr: 0x9A34, symSize: 0x84 }
  - { offset: 0x5CF9C, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrGroupsMaker sectionAndAttrs]', symObjAddr: 0x1DD8, symBinAddr: 0x9AB8, symSize: 0x8 }
  - { offset: 0x5CFD3, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrGroupsMaker setSectionAndAttrs:]', symObjAddr: 0x1DE0, symBinAddr: 0x9AC0, symSize: 0xC }
  - { offset: 0x5D014, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrGroupsMaker resolvedCustomDisplayTitle]', symObjAddr: 0x1DEC, symBinAddr: 0x9ACC, symSize: 0x8 }
  - { offset: 0x5D04B, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrGroupsMaker setResolvedCustomDisplayTitle:]', symObjAddr: 0x1DF4, symBinAddr: 0x9AD4, symSize: 0x8 }
  - { offset: 0x5D08A, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrGroupsMaker resolvedDanceUISource]', symObjAddr: 0x1DFC, symBinAddr: 0x9ADC, symSize: 0x8 }
  - { offset: 0x5D0C1, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrGroupsMaker setResolvedDanceUISource:]', symObjAddr: 0x1E04, symBinAddr: 0x9AE4, symSize: 0x8 }
  - { offset: 0x5D100, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrGroupsMaker resolvedGroups]', symObjAddr: 0x1E0C, symBinAddr: 0x9AEC, symSize: 0x8 }
  - { offset: 0x5D137, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrGroupsMaker setResolvedGroups:]', symObjAddr: 0x1E14, symBinAddr: 0x9AF4, symSize: 0xC }
  - { offset: 0x5D178, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrGroupsMaker layer]', symObjAddr: 0x1E20, symBinAddr: 0x9B00, symSize: 0x18 }
  - { offset: 0x5D1AF, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrGroupsMaker setLayer:]', symObjAddr: 0x1E38, symBinAddr: 0x9B18, symSize: 0xC }
  - { offset: 0x5D1F0, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrGroupsMaker .cxx_destruct]', symObjAddr: 0x1E44, symBinAddr: 0x9B24, symSize: 0x50 }
  - { offset: 0x5DA03, size: 0x8, addend: 0x0, symName: '+[LKS_CustomAttrModificationHandler handleModification:]', symObjAddr: 0x0, symBinAddr: 0x9B74, symSize: 0x668 }
  - { offset: 0x5DAE6, size: 0x8, addend: 0x0, symName: '+[LKS_CustomAttrModificationHandler handleModification:]', symObjAddr: 0x0, symBinAddr: 0x9B74, symSize: 0x668 }
  - { offset: 0x5E11B, size: 0x8, addend: 0x0, symName: '+[LKS_CustomAttrSetterManager sharedInstance]', symObjAddr: 0x0, symBinAddr: 0xA1DC, symSize: 0x74 }
  - { offset: 0x5E129, size: 0x8, addend: 0x0, symName: '+[LKS_CustomAttrSetterManager sharedInstance]', symObjAddr: 0x0, symBinAddr: 0xA1DC, symSize: 0x74 }
  - { offset: 0x5E153, size: 0x8, addend: 0x0, symName: _sharedInstance.onceToken, symObjAddr: 0x5628, symBinAddr: 0x5A3C0, symSize: 0x0 }
  - { offset: 0x5E169, size: 0x8, addend: 0x0, symName: _sharedInstance.instance, symObjAddr: 0x5630, symBinAddr: 0x5A3C8, symSize: 0x0 }
  - { offset: 0x5E245, size: 0x8, addend: 0x0, symName: '___45+[LKS_CustomAttrSetterManager sharedInstance]_block_invoke', symObjAddr: 0x74, symBinAddr: 0xA250, symSize: 0x54 }
  - { offset: 0x5E284, size: 0x8, addend: 0x0, symName: '+[LKS_CustomAttrSetterManager allocWithZone:]', symObjAddr: 0xC8, symBinAddr: 0xA2A4, symSize: 0x1C }
  - { offset: 0x5E2C7, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager init]', symObjAddr: 0xE4, symBinAddr: 0xA2C0, symSize: 0x6C }
  - { offset: 0x5E2FE, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager removeAll]', symObjAddr: 0x150, symBinAddr: 0xA32C, symSize: 0x30 }
  - { offset: 0x5E331, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager saveStringSetter:uniqueID:]', symObjAddr: 0x180, symBinAddr: 0xA35C, symSize: 0x74 }
  - { offset: 0x5E384, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager getStringSetterWithID:]', symObjAddr: 0x1F4, symBinAddr: 0xA3D0, symSize: 0x6C }
  - { offset: 0x5E3CB, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager saveNumberSetter:uniqueID:]', symObjAddr: 0x260, symBinAddr: 0xA43C, symSize: 0x74 }
  - { offset: 0x5E41E, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager getNumberSetterWithID:]', symObjAddr: 0x2D4, symBinAddr: 0xA4B0, symSize: 0x6C }
  - { offset: 0x5E465, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager saveBoolSetter:uniqueID:]', symObjAddr: 0x340, symBinAddr: 0xA51C, symSize: 0x74 }
  - { offset: 0x5E4B8, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager getBoolSetterWithID:]', symObjAddr: 0x3B4, symBinAddr: 0xA590, symSize: 0x6C }
  - { offset: 0x5E4FF, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager saveColorSetter:uniqueID:]', symObjAddr: 0x420, symBinAddr: 0xA5FC, symSize: 0x74 }
  - { offset: 0x5E552, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager getColorSetterWithID:]', symObjAddr: 0x494, symBinAddr: 0xA670, symSize: 0x6C }
  - { offset: 0x5E599, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager saveEnumSetter:uniqueID:]', symObjAddr: 0x500, symBinAddr: 0xA6DC, symSize: 0x74 }
  - { offset: 0x5E5EC, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager getEnumSetterWithID:]', symObjAddr: 0x574, symBinAddr: 0xA750, symSize: 0x6C }
  - { offset: 0x5E633, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager saveRectSetter:uniqueID:]', symObjAddr: 0x5E0, symBinAddr: 0xA7BC, symSize: 0x74 }
  - { offset: 0x5E686, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager getRectSetterWithID:]', symObjAddr: 0x654, symBinAddr: 0xA830, symSize: 0x6C }
  - { offset: 0x5E6CD, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager saveSizeSetter:uniqueID:]', symObjAddr: 0x6C0, symBinAddr: 0xA89C, symSize: 0x74 }
  - { offset: 0x5E720, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager getSizeSetterWithID:]', symObjAddr: 0x734, symBinAddr: 0xA910, symSize: 0x6C }
  - { offset: 0x5E767, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager savePointSetter:uniqueID:]', symObjAddr: 0x7A0, symBinAddr: 0xA97C, symSize: 0x74 }
  - { offset: 0x5E7BA, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager getPointSetterWithID:]', symObjAddr: 0x814, symBinAddr: 0xA9F0, symSize: 0x6C }
  - { offset: 0x5E801, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager saveInsetsSetter:uniqueID:]', symObjAddr: 0x880, symBinAddr: 0xAA5C, symSize: 0x74 }
  - { offset: 0x5E854, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager getInsetsSetterWithID:]', symObjAddr: 0x8F4, symBinAddr: 0xAAD0, symSize: 0x6C }
  - { offset: 0x5E89B, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager settersMap]', symObjAddr: 0x960, symBinAddr: 0xAB3C, symSize: 0x8 }
  - { offset: 0x5E8D2, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager setSettersMap:]', symObjAddr: 0x968, symBinAddr: 0xAB44, symSize: 0xC }
  - { offset: 0x5E913, size: 0x8, addend: 0x0, symName: '-[LKS_CustomAttrSetterManager .cxx_destruct]', symObjAddr: 0x974, symBinAddr: 0xAB50, symSize: 0xC }
  - { offset: 0x5ED23, size: 0x8, addend: 0x0, symName: '-[LKS_CustomDisplayItemsMaker initWithLayer:saveAttrSetter:]', symObjAddr: 0x0, symBinAddr: 0xAB5C, symSize: 0xB0 }
  - { offset: 0x5EDF0, size: 0x8, addend: 0x0, symName: '-[LKS_CustomDisplayItemsMaker initWithLayer:saveAttrSetter:]', symObjAddr: 0x0, symBinAddr: 0xAB5C, symSize: 0xB0 }
  - { offset: 0x5EE47, size: 0x8, addend: 0x0, symName: '-[LKS_CustomDisplayItemsMaker make]', symObjAddr: 0xB0, symBinAddr: 0xAC0C, symSize: 0x278 }
  - { offset: 0x5EEE1, size: 0x8, addend: 0x0, symName: '-[LKS_CustomDisplayItemsMaker makeSubitemsForViewOrLayer:selectorName:]', symObjAddr: 0x328, symBinAddr: 0xAE84, symSize: 0x14C }
  - { offset: 0x5EFC0, size: 0x8, addend: 0x0, symName: '-[LKS_CustomDisplayItemsMaker makeSubitemsFromRawData:]', symObjAddr: 0x474, symBinAddr: 0xAFD0, symSize: 0xC0 }
  - { offset: 0x5F023, size: 0x8, addend: 0x0, symName: '-[LKS_CustomDisplayItemsMaker displayItemsFromRawArray:]', symObjAddr: 0x534, symBinAddr: 0xB090, symSize: 0xAC }
  - { offset: 0x5F07A, size: 0x8, addend: 0x0, symName: '___56-[LKS_CustomDisplayItemsMaker displayItemsFromRawArray:]_block_invoke', symObjAddr: 0x5E0, symBinAddr: 0xB13C, symSize: 0x70 }
  - { offset: 0x5F0D9, size: 0x8, addend: 0x0, symName: '-[LKS_CustomDisplayItemsMaker displayItemFromRawDict:]', symObjAddr: 0x660, symBinAddr: 0xB1AC, symSize: 0x2B4 }
  - { offset: 0x5F190, size: 0x8, addend: 0x0, symName: '-[LKS_CustomDisplayItemsMaker layer]', symObjAddr: 0x914, symBinAddr: 0xB460, symSize: 0x18 }
  - { offset: 0x5F1C7, size: 0x8, addend: 0x0, symName: '-[LKS_CustomDisplayItemsMaker setLayer:]', symObjAddr: 0x92C, symBinAddr: 0xB478, symSize: 0xC }
  - { offset: 0x5F208, size: 0x8, addend: 0x0, symName: '-[LKS_CustomDisplayItemsMaker saveAttrSetter]', symObjAddr: 0x938, symBinAddr: 0xB484, symSize: 0x8 }
  - { offset: 0x5F23F, size: 0x8, addend: 0x0, symName: '-[LKS_CustomDisplayItemsMaker setSaveAttrSetter:]', symObjAddr: 0x940, symBinAddr: 0xB48C, symSize: 0x8 }
  - { offset: 0x5F27A, size: 0x8, addend: 0x0, symName: '-[LKS_CustomDisplayItemsMaker allSubitems]', symObjAddr: 0x948, symBinAddr: 0xB494, symSize: 0x8 }
  - { offset: 0x5F2B1, size: 0x8, addend: 0x0, symName: '-[LKS_CustomDisplayItemsMaker setAllSubitems:]', symObjAddr: 0x950, symBinAddr: 0xB49C, symSize: 0xC }
  - { offset: 0x5F2F2, size: 0x8, addend: 0x0, symName: '-[LKS_CustomDisplayItemsMaker .cxx_destruct]', symObjAddr: 0x95C, symBinAddr: 0xB4A8, symSize: 0x2C }
  - { offset: 0x5F799, size: 0x8, addend: 0x0, symName: '+[LKS_EventHandlerMaker makeForView:]', symObjAddr: 0x0, symBinAddr: 0xB4D4, symSize: 0x120 }
  - { offset: 0x5F7AC, size: 0x8, addend: 0x0, symName: '+[LKS_EventHandlerMaker _inheritedRecognizerNameForRecognizer:]', symObjAddr: 0x544, symBinAddr: 0xBA18, symSize: 0x138 }
  - { offset: 0x5F7D6, size: 0x8, addend: 0x0, symName: '__inheritedRecognizerNameForRecognizer:.baseRecognizers', symObjAddr: 0x7890, symBinAddr: 0x5A3D0, symSize: 0x0 }
  - { offset: 0x5F7EC, size: 0x8, addend: 0x0, symName: '__inheritedRecognizerNameForRecognizer:.onceToken', symObjAddr: 0x7898, symBinAddr: 0x5A3D8, symSize: 0x0 }
  - { offset: 0x5F856, size: 0x8, addend: 0x0, symName: '+[LKS_EventHandlerMaker _targetActionHandlersForControl:]', symObjAddr: 0x868, symBinAddr: 0xBD3C, symSize: 0x154 }
  - { offset: 0x5F880, size: 0x8, addend: 0x0, symName: '__targetActionHandlersForControl:.onceToken', symObjAddr: 0x78A0, symBinAddr: 0x5A3E0, symSize: 0x0 }
  - { offset: 0x5F896, size: 0x8, addend: 0x0, symName: '__targetActionHandlersForControl:.allEvents', symObjAddr: 0x78A8, symBinAddr: 0x5A3E8, symSize: 0x0 }
  - { offset: 0x5F904, size: 0x8, addend: 0x0, symName: '+[LKS_EventHandlerMaker _nameFromControlEvent:]', symObjAddr: 0xFE4, symBinAddr: 0xC468, symSize: 0x8C }
  - { offset: 0x5F92E, size: 0x8, addend: 0x0, symName: '__nameFromControlEvent:.onceToken', symObjAddr: 0x78B0, symBinAddr: 0x5A3F0, symSize: 0x0 }
  - { offset: 0x5F944, size: 0x8, addend: 0x0, symName: '__nameFromControlEvent:.eventsAndNames', symObjAddr: 0x78B8, symBinAddr: 0x5A3F8, symSize: 0x0 }
  - { offset: 0x5F9EA, size: 0x8, addend: 0x0, symName: '+[LKS_EventHandlerMaker makeForView:]', symObjAddr: 0x0, symBinAddr: 0xB4D4, symSize: 0x120 }
  - { offset: 0x5FA70, size: 0x8, addend: 0x0, symName: '+[LKS_EventHandlerMaker _gestureHandlersForView:]', symObjAddr: 0x120, symBinAddr: 0xB5F4, symSize: 0xD0 }
  - { offset: 0x5FAC7, size: 0x8, addend: 0x0, symName: '___49+[LKS_EventHandlerMaker _gestureHandlersForView:]_block_invoke', symObjAddr: 0x1F0, symBinAddr: 0xB6C4, symSize: 0x1DC }
  - { offset: 0x5FB55, size: 0x8, addend: 0x0, symName: '___49+[LKS_EventHandlerMaker _gestureHandlersForView:]_block_invoke_2', symObjAddr: 0x3CC, symBinAddr: 0xB8A0, symSize: 0xDC }
  - { offset: 0x5FBCB, size: 0x8, addend: 0x0, symName: '___49+[LKS_EventHandlerMaker _gestureHandlersForView:]_block_invoke_3', symObjAddr: 0x4A8, symBinAddr: 0xB97C, symSize: 0x9C }
  - { offset: 0x5FC61, size: 0x8, addend: 0x0, symName: '___63+[LKS_EventHandlerMaker _inheritedRecognizerNameForRecognizer:]_block_invoke', symObjAddr: 0x67C, symBinAddr: 0xBB50, symSize: 0xE4 }
  - { offset: 0x5FC88, size: 0x8, addend: 0x0, symName: ___Block_byref_object_copy_, symObjAddr: 0x760, symBinAddr: 0xBC34, symSize: 0x10 }
  - { offset: 0x5FCAD, size: 0x8, addend: 0x0, symName: ___Block_byref_object_dispose_, symObjAddr: 0x770, symBinAddr: 0xBC44, symSize: 0x8 }
  - { offset: 0x5FCCC, size: 0x8, addend: 0x0, symName: '___63+[LKS_EventHandlerMaker _inheritedRecognizerNameForRecognizer:]_block_invoke.37', symObjAddr: 0x778, symBinAddr: 0xBC4C, symSize: 0x90 }
  - { offset: 0x5FD75, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40r, symObjAddr: 0x808, symBinAddr: 0xBCDC, symSize: 0x34 }
  - { offset: 0x5FD9E, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40r, symObjAddr: 0x83C, symBinAddr: 0xBD10, symSize: 0x2C }
  - { offset: 0x5FDBD, size: 0x8, addend: 0x0, symName: '___57+[LKS_EventHandlerMaker _targetActionHandlersForControl:]_block_invoke', symObjAddr: 0x9BC, symBinAddr: 0xBE90, symSize: 0x2DC }
  - { offset: 0x5FDE4, size: 0x8, addend: 0x0, symName: '___57+[LKS_EventHandlerMaker _targetActionHandlersForControl:]_block_invoke_2', symObjAddr: 0xC98, symBinAddr: 0xC16C, symSize: 0x140 }
  - { offset: 0x5FEC9, size: 0x8, addend: 0x0, symName: '___57+[LKS_EventHandlerMaker _targetActionHandlersForControl:]_block_invoke_3', symObjAddr: 0xDD8, symBinAddr: 0xC2AC, symSize: 0xC4 }
  - { offset: 0x5FF5F, size: 0x8, addend: 0x0, symName: '___57+[LKS_EventHandlerMaker _targetActionHandlersForControl:]_block_invoke_4', symObjAddr: 0xE9C, symBinAddr: 0xC370, symSize: 0x98 }
  - { offset: 0x5FFF1, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48s, symObjAddr: 0xF84, symBinAddr: 0xC408, symSize: 0x30 }
  - { offset: 0x6001A, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48s, symObjAddr: 0xFB4, symBinAddr: 0xC438, symSize: 0x30 }
  - { offset: 0x60039, size: 0x8, addend: 0x0, symName: '___47+[LKS_EventHandlerMaker _nameFromControlEvent:]_block_invoke', symObjAddr: 0x1070, symBinAddr: 0xC4F4, symSize: 0x398 }
  - { offset: 0x607D5, size: 0x8, addend: 0x0, symName: '-[LKS_ExportManagerMaskView initWithFrame:]', symObjAddr: 0x0, symBinAddr: 0xC88C, symSize: 0x714 }
  - { offset: 0x60816, size: 0x8, addend: 0x0, symName: '+[LKS_ExportManager sharedInstance]', symObjAddr: 0xACC, symBinAddr: 0xD358, symSize: 0x74 }
  - { offset: 0x60840, size: 0x8, addend: 0x0, symName: _sharedInstance.onceToken, symObjAddr: 0x79C8, symBinAddr: 0x5A400, symSize: 0x0 }
  - { offset: 0x60856, size: 0x8, addend: 0x0, symName: _sharedInstance.instance, symObjAddr: 0x79D0, symBinAddr: 0x5A408, symSize: 0x0 }
  - { offset: 0x609B0, size: 0x8, addend: 0x0, symName: '-[LKS_ExportManagerMaskView initWithFrame:]', symObjAddr: 0x0, symBinAddr: 0xC88C, symSize: 0x714 }
  - { offset: 0x609F3, size: 0x8, addend: 0x0, symName: '-[LKS_ExportManagerMaskView layoutSubviews]', symObjAddr: 0x714, symBinAddr: 0xCFA0, symSize: 0x2C0 }
  - { offset: 0x60B43, size: 0x8, addend: 0x0, symName: '-[LKS_ExportManagerMaskView tipsView]', symObjAddr: 0x9D4, symBinAddr: 0xD260, symSize: 0x10 }
  - { offset: 0x60B7A, size: 0x8, addend: 0x0, symName: '-[LKS_ExportManagerMaskView setTipsView:]', symObjAddr: 0x9E4, symBinAddr: 0xD270, symSize: 0x14 }
  - { offset: 0x60BBB, size: 0x8, addend: 0x0, symName: '-[LKS_ExportManagerMaskView firstLabel]', symObjAddr: 0x9F8, symBinAddr: 0xD284, symSize: 0x10 }
  - { offset: 0x60BF2, size: 0x8, addend: 0x0, symName: '-[LKS_ExportManagerMaskView setFirstLabel:]', symObjAddr: 0xA08, symBinAddr: 0xD294, symSize: 0x14 }
  - { offset: 0x60C33, size: 0x8, addend: 0x0, symName: '-[LKS_ExportManagerMaskView secondLabel]', symObjAddr: 0xA1C, symBinAddr: 0xD2A8, symSize: 0x10 }
  - { offset: 0x60C6A, size: 0x8, addend: 0x0, symName: '-[LKS_ExportManagerMaskView setSecondLabel:]', symObjAddr: 0xA2C, symBinAddr: 0xD2B8, symSize: 0x14 }
  - { offset: 0x60CAB, size: 0x8, addend: 0x0, symName: '-[LKS_ExportManagerMaskView thirdLabel]', symObjAddr: 0xA40, symBinAddr: 0xD2CC, symSize: 0x10 }
  - { offset: 0x60CE2, size: 0x8, addend: 0x0, symName: '-[LKS_ExportManagerMaskView setThirdLabel:]', symObjAddr: 0xA50, symBinAddr: 0xD2DC, symSize: 0x14 }
  - { offset: 0x60D23, size: 0x8, addend: 0x0, symName: '-[LKS_ExportManagerMaskView .cxx_destruct]', symObjAddr: 0xA64, symBinAddr: 0xD2F0, symSize: 0x68 }
  - { offset: 0x60DA5, size: 0x8, addend: 0x0, symName: '___35+[LKS_ExportManager sharedInstance]_block_invoke', symObjAddr: 0xB40, symBinAddr: 0xD3CC, symSize: 0x54 }
  - { offset: 0x60DE4, size: 0x8, addend: 0x0, symName: '+[LKS_ExportManager allocWithZone:]', symObjAddr: 0xB94, symBinAddr: 0xD420, symSize: 0x1C }
  - { offset: 0x60E27, size: 0x8, addend: 0x0, symName: '-[LKS_ExportManager exportAndShare]', symObjAddr: 0xBB0, symBinAddr: 0xD43C, symSize: 0x228 }
  - { offset: 0x60EFE, size: 0x8, addend: 0x0, symName: '___35-[LKS_ExportManager exportAndShare]_block_invoke', symObjAddr: 0xDD8, symBinAddr: 0xD664, symSize: 0x478 }
  - { offset: 0x61060, size: 0x8, addend: 0x0, symName: '-[LKS_ExportManager documentController]', symObjAddr: 0x12A0, symBinAddr: 0xDADC, symSize: 0x8 }
  - { offset: 0x61097, size: 0x8, addend: 0x0, symName: '-[LKS_ExportManager setDocumentController:]', symObjAddr: 0x12A8, symBinAddr: 0xDAE4, symSize: 0xC }
  - { offset: 0x610D8, size: 0x8, addend: 0x0, symName: '-[LKS_ExportManager maskView]', symObjAddr: 0x12B4, symBinAddr: 0xDAF0, symSize: 0x8 }
  - { offset: 0x6110F, size: 0x8, addend: 0x0, symName: '-[LKS_ExportManager setMaskView:]', symObjAddr: 0x12BC, symBinAddr: 0xDAF8, symSize: 0xC }
  - { offset: 0x61150, size: 0x8, addend: 0x0, symName: '-[LKS_ExportManager .cxx_destruct]', symObjAddr: 0x12C8, symBinAddr: 0xDB04, symSize: 0x30 }
  - { offset: 0x615B7, size: 0x8, addend: 0x0, symName: '+[LKS_GestureTargetActionsSearcher getTargetActionsFromRecognizer:]', symObjAddr: 0x0, symBinAddr: 0xDB34, symSize: 0x128 }
  - { offset: 0x61615, size: 0x8, addend: 0x0, symName: '+[LKS_GestureTargetActionsSearcher getTargetActionsFromRecognizer:]', symObjAddr: 0x0, symBinAddr: 0xDB34, symSize: 0x128 }
  - { offset: 0x616B4, size: 0x8, addend: 0x0, symName: '___67+[LKS_GestureTargetActionsSearcher getTargetActionsFromRecognizer:]_block_invoke', symObjAddr: 0x128, symBinAddr: 0xDC5C, symSize: 0x110 }
  - { offset: 0x618E7, size: 0x8, addend: 0x0, symName: '+[LKS_Helper descriptionOfObject:]', symObjAddr: 0x0, symBinAddr: 0xDD6C, symSize: 0xBC }
  - { offset: 0x618FA, size: 0x8, addend: 0x0, symName: '+[LKS_Helper bundle]', symObjAddr: 0xBC, symBinAddr: 0xDE28, symSize: 0x8 }
  - { offset: 0x61962, size: 0x8, addend: 0x0, symName: '+[LKS_Helper descriptionOfObject:]', symObjAddr: 0x0, symBinAddr: 0xDD6C, symSize: 0xBC }
  - { offset: 0x61A4D, size: 0x8, addend: 0x0, symName: '+[LKS_HierarchyDetailsHandler sharedInstance]', symObjAddr: 0x0, symBinAddr: 0xDE30, symSize: 0x74 }
  - { offset: 0x61A60, size: 0x8, addend: 0x0, symName: '+[LKS_HierarchyDetailsHandler sharedInstance]', symObjAddr: 0x0, symBinAddr: 0xDE30, symSize: 0x74 }
  - { offset: 0x61A8A, size: 0x8, addend: 0x0, symName: _sharedInstance.onceToken, symObjAddr: 0x66B8, symBinAddr: 0x5A410, symSize: 0x0 }
  - { offset: 0x61AA0, size: 0x8, addend: 0x0, symName: _sharedInstance.instance, symObjAddr: 0x66C0, symBinAddr: 0x5A418, symSize: 0x0 }
  - { offset: 0x61C88, size: 0x8, addend: 0x0, symName: '___45+[LKS_HierarchyDetailsHandler sharedInstance]_block_invoke', symObjAddr: 0x74, symBinAddr: 0xDEA4, symSize: 0x54 }
  - { offset: 0x61CC7, size: 0x8, addend: 0x0, symName: '+[LKS_HierarchyDetailsHandler allocWithZone:]', symObjAddr: 0xC8, symBinAddr: 0xDEF8, symSize: 0x1C }
  - { offset: 0x61D0A, size: 0x8, addend: 0x0, symName: '-[LKS_HierarchyDetailsHandler init]', symObjAddr: 0xE4, symBinAddr: 0xDF14, symSize: 0xE4 }
  - { offset: 0x61D41, size: 0x8, addend: 0x0, symName: '-[LKS_HierarchyDetailsHandler startWithPackages:block:]', symObjAddr: 0x1C8, symBinAddr: 0xDFF8, symSize: 0x1F4 }
  - { offset: 0x61DB5, size: 0x8, addend: 0x0, symName: '-[LKS_HierarchyDetailsHandler bringForwardWithPackages:]', symObjAddr: 0x3BC, symBinAddr: 0xE1EC, symSize: 0x9C }
  - { offset: 0x61E26, size: 0x8, addend: 0x0, symName: '-[LKS_HierarchyDetailsHandler cancel]', symObjAddr: 0x458, symBinAddr: 0xE288, symSize: 0x30 }
  - { offset: 0x61E59, size: 0x8, addend: 0x0, symName: '-[LKS_HierarchyDetailsHandler _dequeueAndHandlePackage]', symObjAddr: 0x488, symBinAddr: 0xE2B8, symSize: 0x58 }
  - { offset: 0x61EB5, size: 0x8, addend: 0x0, symName: '___55-[LKS_HierarchyDetailsHandler _dequeueAndHandlePackage]_block_invoke', symObjAddr: 0x4E0, symBinAddr: 0xE310, symSize: 0x128 }
  - { offset: 0x61F3D, size: 0x8, addend: 0x0, symName: '___55-[LKS_HierarchyDetailsHandler _dequeueAndHandlePackage]_block_invoke_2', symObjAddr: 0x608, symBinAddr: 0xE438, symSize: 0x360 }
  - { offset: 0x62057, size: 0x8, addend: 0x0, symName: '-[LKS_HierarchyDetailsHandler _handleConnectionDidEnd:]', symObjAddr: 0x978, symBinAddr: 0xE798, symSize: 0x4 }
  - { offset: 0x62094, size: 0x8, addend: 0x0, symName: '-[LKS_HierarchyDetailsHandler taskPackages]', symObjAddr: 0x97C, symBinAddr: 0xE79C, symSize: 0x8 }
  - { offset: 0x620CB, size: 0x8, addend: 0x0, symName: '-[LKS_HierarchyDetailsHandler setTaskPackages:]', symObjAddr: 0x984, symBinAddr: 0xE7A4, symSize: 0xC }
  - { offset: 0x6210C, size: 0x8, addend: 0x0, symName: '-[LKS_HierarchyDetailsHandler finishedTasks]', symObjAddr: 0x990, symBinAddr: 0xE7B0, symSize: 0x8 }
  - { offset: 0x62143, size: 0x8, addend: 0x0, symName: '-[LKS_HierarchyDetailsHandler setFinishedTasks:]', symObjAddr: 0x998, symBinAddr: 0xE7B8, symSize: 0xC }
  - { offset: 0x62184, size: 0x8, addend: 0x0, symName: '-[LKS_HierarchyDetailsHandler attrGroupsSyncedOids]', symObjAddr: 0x9A4, symBinAddr: 0xE7C4, symSize: 0x8 }
  - { offset: 0x621BB, size: 0x8, addend: 0x0, symName: '-[LKS_HierarchyDetailsHandler setAttrGroupsSyncedOids:]', symObjAddr: 0x9AC, symBinAddr: 0xE7CC, symSize: 0xC }
  - { offset: 0x621FC, size: 0x8, addend: 0x0, symName: '-[LKS_HierarchyDetailsHandler handlerBlock]', symObjAddr: 0x9B8, symBinAddr: 0xE7D8, symSize: 0x8 }
  - { offset: 0x62233, size: 0x8, addend: 0x0, symName: '-[LKS_HierarchyDetailsHandler setHandlerBlock:]', symObjAddr: 0x9C0, symBinAddr: 0xE7E0, symSize: 0x8 }
  - { offset: 0x62272, size: 0x8, addend: 0x0, symName: '-[LKS_HierarchyDetailsHandler bbb]', symObjAddr: 0x9C8, symBinAddr: 0xE7E8, symSize: 0x8 }
  - { offset: 0x622A9, size: 0x8, addend: 0x0, symName: '-[LKS_HierarchyDetailsHandler setBbb:]', symObjAddr: 0x9D0, symBinAddr: 0xE7F0, symSize: 0x8 }
  - { offset: 0x622E6, size: 0x8, addend: 0x0, symName: '-[LKS_HierarchyDetailsHandler .cxx_destruct]', symObjAddr: 0x9D8, symBinAddr: 0xE7F8, symSize: 0x48 }
  - { offset: 0x62740, size: 0x8, addend: 0x0, symName: '+[LKS_HierarchyDisplayItemsMaker itemsWithScreenshots:attrList:lowImageQuality:readCustomInfo:saveCustomSetter:]', symObjAddr: 0x0, symBinAddr: 0xE840, symSize: 0x160 }
  - { offset: 0x627AD, size: 0x8, addend: 0x0, symName: '+[LKS_HierarchyDisplayItemsMaker itemsWithScreenshots:attrList:lowImageQuality:readCustomInfo:saveCustomSetter:]', symObjAddr: 0x0, symBinAddr: 0xE840, symSize: 0x160 }
  - { offset: 0x62840, size: 0x8, addend: 0x0, symName: '___112+[LKS_HierarchyDisplayItemsMaker itemsWithScreenshots:attrList:lowImageQuality:readCustomInfo:saveCustomSetter:]_block_invoke', symObjAddr: 0x160, symBinAddr: 0xE9A0, symSize: 0xAC }
  - { offset: 0x62926, size: 0x8, addend: 0x0, symName: '+[LKS_HierarchyDisplayItemsMaker _displayItemWithLayer:screenshots:attrList:lowImageQuality:readCustomInfo:saveCustomSetter:]', symObjAddr: 0x21C, symBinAddr: 0xEA4C, symSize: 0x664 }
  - { offset: 0x62AB3, size: 0x8, addend: 0x0, symName: '___125+[LKS_HierarchyDisplayItemsMaker _displayItemWithLayer:screenshots:attrList:lowImageQuality:readCustomInfo:saveCustomSetter:]_block_invoke', symObjAddr: 0x880, symBinAddr: 0xF0B0, symSize: 0x5C }
  - { offset: 0x62B99, size: 0x8, addend: 0x0, symName: '+[LKS_HierarchyDisplayItemsMaker validateFrame:]', symObjAddr: 0x8DC, symBinAddr: 0xF10C, symSize: 0xB8 }
  - { offset: 0x62C6D, size: 0x8, addend: 0x0, symName: '+[LKS_HierarchyDisplayItemsMaker cgRectIsNaN:]', symObjAddr: 0x994, symBinAddr: 0xF1C4, symSize: 0x24 }
  - { offset: 0x62CDF, size: 0x8, addend: 0x0, symName: '+[LKS_HierarchyDisplayItemsMaker cgRectIsInf:]', symObjAddr: 0x9B8, symBinAddr: 0xF1E8, symSize: 0x44 }
  - { offset: 0x62D25, size: 0x8, addend: 0x0, symName: '+[LKS_HierarchyDisplayItemsMaker cgRectIsInf:]', symObjAddr: 0x9B8, symBinAddr: 0xF1E8, symSize: 0x44 }
  - { offset: 0x62D57, size: 0x8, addend: 0x0, symName: '+[LKS_HierarchyDisplayItemsMaker cgRectIsUnreasonable:]', symObjAddr: 0x9FC, symBinAddr: 0xF22C, symSize: 0x5C }
  - { offset: 0x62D99, size: 0x8, addend: 0x0, symName: '+[LKS_HierarchyDisplayItemsMaker cgRectIsUnreasonable:]', symObjAddr: 0x9FC, symBinAddr: 0xF22C, symSize: 0x5C }
  - { offset: 0x633BD, size: 0x8, addend: 0x0, symName: '+[LKS_InbuiltAttrModificationHandler handleModification:completion:]', symObjAddr: 0x0, symBinAddr: 0xF288, symSize: 0xEA8 }
  - { offset: 0x634DB, size: 0x8, addend: 0x0, symName: '+[LKS_InbuiltAttrModificationHandler handleModification:completion:]', symObjAddr: 0x0, symBinAddr: 0xF288, symSize: 0xEA8 }
  - { offset: 0x63A5A, size: 0x8, addend: 0x0, symName: '___68+[LKS_InbuiltAttrModificationHandler handleModification:completion:]_block_invoke', symObjAddr: 0xF1C, symBinAddr: 0x101A4, symSize: 0x3C0 }
  - { offset: 0x63B50, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48s56b, symObjAddr: 0x12DC, symBinAddr: 0x10564, symSize: 0x44 }
  - { offset: 0x63B79, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48s56s, symObjAddr: 0x1320, symBinAddr: 0x105A8, symSize: 0x38 }
  - { offset: 0x63B98, size: 0x8, addend: 0x0, symName: '+[LKS_InbuiltAttrModificationHandler handlePatchWithTasks:block:]', symObjAddr: 0x1358, symBinAddr: 0x105E0, symSize: 0x84 }
  - { offset: 0x63BE7, size: 0x8, addend: 0x0, symName: '___65+[LKS_InbuiltAttrModificationHandler handlePatchWithTasks:block:]_block_invoke', symObjAddr: 0x13DC, symBinAddr: 0x10664, symSize: 0x164 }
  - { offset: 0x63CEF, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32b, symObjAddr: 0x1540, symBinAddr: 0x107C8, symSize: 0x10 }
  - { offset: 0x641ED, size: 0x8, addend: 0x0, symName: '+[LKS_ObjectRegistry sharedInstance]', symObjAddr: 0x0, symBinAddr: 0x107D8, symSize: 0x74 }
  - { offset: 0x641FB, size: 0x8, addend: 0x0, symName: '+[LKS_ObjectRegistry sharedInstance]', symObjAddr: 0x0, symBinAddr: 0x107D8, symSize: 0x74 }
  - { offset: 0x64225, size: 0x8, addend: 0x0, symName: _sharedInstance.onceToken, symObjAddr: 0x27F0, symBinAddr: 0x5A420, symSize: 0x0 }
  - { offset: 0x6423B, size: 0x8, addend: 0x0, symName: _sharedInstance.instance, symObjAddr: 0x27F8, symBinAddr: 0x5A428, symSize: 0x0 }
  - { offset: 0x64317, size: 0x8, addend: 0x0, symName: '___36+[LKS_ObjectRegistry sharedInstance]_block_invoke', symObjAddr: 0x74, symBinAddr: 0x1084C, symSize: 0x54 }
  - { offset: 0x64356, size: 0x8, addend: 0x0, symName: '+[LKS_ObjectRegistry allocWithZone:]', symObjAddr: 0xC8, symBinAddr: 0x108A0, symSize: 0x1C }
  - { offset: 0x64399, size: 0x8, addend: 0x0, symName: '-[LKS_ObjectRegistry init]', symObjAddr: 0xE4, symBinAddr: 0x108BC, symSize: 0x98 }
  - { offset: 0x643D0, size: 0x8, addend: 0x0, symName: '-[LKS_ObjectRegistry addObject:]', symObjAddr: 0x17C, symBinAddr: 0x10954, symSize: 0x90 }
  - { offset: 0x64417, size: 0x8, addend: 0x0, symName: '-[LKS_ObjectRegistry objectWithOid:]', symObjAddr: 0x20C, symBinAddr: 0x109E4, symSize: 0x8C }
  - { offset: 0x6446E, size: 0x8, addend: 0x0, symName: '-[LKS_ObjectRegistry data]', symObjAddr: 0x298, symBinAddr: 0x10A70, symSize: 0x8 }
  - { offset: 0x644A5, size: 0x8, addend: 0x0, symName: '-[LKS_ObjectRegistry setData:]', symObjAddr: 0x2A0, symBinAddr: 0x10A78, symSize: 0xC }
  - { offset: 0x644E6, size: 0x8, addend: 0x0, symName: '-[LKS_ObjectRegistry .cxx_destruct]', symObjAddr: 0x2AC, symBinAddr: 0x10A84, symSize: 0xC }
  - { offset: 0x64638, size: 0x8, addend: 0x0, symName: '-[LKS_RequestHandler init]', symObjAddr: 0x0, symBinAddr: 0x10A90, symSize: 0x2C4 }
  - { offset: 0x64725, size: 0x8, addend: 0x0, symName: '-[LKS_RequestHandler init]', symObjAddr: 0x0, symBinAddr: 0x10A90, symSize: 0x2C4 }
  - { offset: 0x6475C, size: 0x8, addend: 0x0, symName: '-[LKS_RequestHandler canHandleRequestType:]', symObjAddr: 0x2C4, symBinAddr: 0x10D54, symSize: 0x50 }
  - { offset: 0x647A3, size: 0x8, addend: 0x0, symName: '-[LKS_RequestHandler handleRequestType:tag:object:]', symObjAddr: 0x314, symBinAddr: 0x10DA4, symSize: 0x1310 }
  - { offset: 0x64C74, size: 0x8, addend: 0x0, symName: '___51-[LKS_RequestHandler handleRequestType:tag:object:]_block_invoke', symObjAddr: 0x1624, symBinAddr: 0x120B4, symSize: 0xB0 }
  - { offset: 0x64CFE, size: 0x8, addend: 0x0, symName: '___51-[LKS_RequestHandler handleRequestType:tag:object:]_block_invoke_2', symObjAddr: 0x16D4, symBinAddr: 0x12164, symSize: 0xA0 }
  - { offset: 0x64D78, size: 0x8, addend: 0x0, symName: '___51-[LKS_RequestHandler handleRequestType:tag:object:]_block_invoke_3', symObjAddr: 0x1774, symBinAddr: 0x12204, symSize: 0x44 }
  - { offset: 0x64DCF, size: 0x8, addend: 0x0, symName: '___51-[LKS_RequestHandler handleRequestType:tag:object:]_block_invoke_4', symObjAddr: 0x17B8, symBinAddr: 0x12248, symSize: 0xD0 }
  - { offset: 0x64E59, size: 0x8, addend: 0x0, symName: '___51-[LKS_RequestHandler handleRequestType:tag:object:]_block_invoke_5', symObjAddr: 0x1888, symBinAddr: 0x12318, symSize: 0x60 }
  - { offset: 0x64ECB, size: 0x8, addend: 0x0, symName: '-[LKS_RequestHandler _methodNameListForClass:hasArg:]', symObjAddr: 0x1938, symBinAddr: 0x12378, symSize: 0x2A4 }
  - { offset: 0x650BF, size: 0x8, addend: 0x0, symName: '___53-[LKS_RequestHandler _methodNameListForClass:hasArg:]_block_invoke', symObjAddr: 0x1BDC, symBinAddr: 0x1261C, symSize: 0xC }
  - { offset: 0x65112, size: 0x8, addend: 0x0, symName: '-[LKS_RequestHandler _handleInvokeWithObject:selector:resultDescription:resultObject:error:]', symObjAddr: 0x1BF8, symBinAddr: 0x12628, symSize: 0xA34 }
  - { offset: 0x657B4, size: 0x8, addend: 0x0, symName: '-[LKS_RequestHandler _submitResponseWithError:requestType:tag:]', symObjAddr: 0x262C, symBinAddr: 0x1305C, symSize: 0x8C }
  - { offset: 0x65828, size: 0x8, addend: 0x0, symName: '-[LKS_RequestHandler _submitResponseWithData:requestType:tag:]', symObjAddr: 0x26B8, symBinAddr: 0x130E8, symSize: 0x8C }
  - { offset: 0x6589C, size: 0x8, addend: 0x0, symName: '-[LKS_RequestHandler .cxx_destruct]', symObjAddr: 0x2744, symBinAddr: 0x13174, symSize: 0xC }
  - { offset: 0x660B0, size: 0x8, addend: 0x0, symName: '+[LKS_TraceManager sharedInstance]', symObjAddr: 0x0, symBinAddr: 0x13180, symSize: 0x74 }
  - { offset: 0x660C3, size: 0x8, addend: 0x0, symName: '+[LKS_TraceManager sharedInstance]', symObjAddr: 0x0, symBinAddr: 0x13180, symSize: 0x74 }
  - { offset: 0x660ED, size: 0x8, addend: 0x0, symName: _sharedInstance.onceToken, symObjAddr: 0x8FB0, symBinAddr: 0x5A430, symSize: 0x0 }
  - { offset: 0x66103, size: 0x8, addend: 0x0, symName: _sharedInstance.instance, symObjAddr: 0x8FB8, symBinAddr: 0x5A438, symSize: 0x0 }
  - { offset: 0x661F3, size: 0x8, addend: 0x0, symName: '___34+[LKS_TraceManager sharedInstance]_block_invoke', symObjAddr: 0x74, symBinAddr: 0x131F4, symSize: 0x54 }
  - { offset: 0x66232, size: 0x8, addend: 0x0, symName: '+[LKS_TraceManager allocWithZone:]', symObjAddr: 0xC8, symBinAddr: 0x13248, symSize: 0x1C }
  - { offset: 0x66275, size: 0x8, addend: 0x0, symName: '-[LKS_TraceManager addSearchTarger:]', symObjAddr: 0xE4, symBinAddr: 0x13264, symSize: 0xC8 }
  - { offset: 0x662C8, size: 0x8, addend: 0x0, symName: '-[LKS_TraceManager reload]', symObjAddr: 0x1AC, symBinAddr: 0x1332C, symSize: 0x10C }
  - { offset: 0x662FB, size: 0x8, addend: 0x0, symName: '___26-[LKS_TraceManager reload]_block_invoke', symObjAddr: 0x2B8, symBinAddr: 0x13438, symSize: 0x78 }
  - { offset: 0x66362, size: 0x8, addend: 0x0, symName: '___26-[LKS_TraceManager reload]_block_invoke.18', symObjAddr: 0x340, symBinAddr: 0x134B0, symSize: 0x40 }
  - { offset: 0x663C9, size: 0x8, addend: 0x0, symName: '-[LKS_TraceManager _addTraceForLayersRootedByLayer:]', symObjAddr: 0x380, symBinAddr: 0x134F0, symSize: 0x164 }
  - { offset: 0x6643B, size: 0x8, addend: 0x0, symName: '___52-[LKS_TraceManager _addTraceForLayersRootedByLayer:]_block_invoke', symObjAddr: 0x4E4, symBinAddr: 0x13654, symSize: 0xC }
  - { offset: 0x664A2, size: 0x8, addend: 0x0, symName: '-[LKS_TraceManager _buildSpecialTraceForView:]', symObjAddr: 0x4F0, symBinAddr: 0x13660, symSize: 0x4A8 }
  - { offset: 0x6659D, size: 0x8, addend: 0x0, symName: '___46-[LKS_TraceManager _buildSpecialTraceForView:]_block_invoke', symObjAddr: 0x998, symBinAddr: 0x13B08, symSize: 0x184 }
  - { offset: 0x6662F, size: 0x8, addend: 0x0, symName: '___46-[LKS_TraceManager _buildSpecialTraceForView:]_block_invoke.40', symObjAddr: 0xB6C, symBinAddr: 0x13C8C, symSize: 0x13C }
  - { offset: 0x666D1, size: 0x8, addend: 0x0, symName: '___46-[LKS_TraceManager _buildSpecialTraceForView:]_block_invoke.49', symObjAddr: 0xCA8, symBinAddr: 0x13DC8, symSize: 0xE4 }
  - { offset: 0x66753, size: 0x8, addend: 0x0, symName: '___46-[LKS_TraceManager _buildSpecialTraceForView:]_block_invoke.53', symObjAddr: 0xD8C, symBinAddr: 0x13EAC, symSize: 0xE4 }
  - { offset: 0x667D5, size: 0x8, addend: 0x0, symName: '___46-[LKS_TraceManager _buildSpecialTraceForView:]_block_invoke_2', symObjAddr: 0xE70, symBinAddr: 0x13F90, symSize: 0xF8 }
  - { offset: 0x66857, size: 0x8, addend: 0x0, symName: '-[LKS_TraceManager _markIVarsInAllClassLevelsOfObject:]', symObjAddr: 0xF68, symBinAddr: 0x14088, symSize: 0x40 }
  - { offset: 0x668A8, size: 0x8, addend: 0x0, symName: '-[LKS_TraceManager _markIVarsOfObject:class:]', symObjAddr: 0xFA8, symBinAddr: 0x140C8, symSize: 0x5D4 }
  - { offset: 0x66B63, size: 0x8, addend: 0x0, symName: '___45-[LKS_TraceManager _markIVarsOfObject:class:]_block_invoke', symObjAddr: 0x157C, symBinAddr: 0x1469C, symSize: 0x64 }
  - { offset: 0x66BC8, size: 0x8, addend: 0x0, symName: '-[LKS_TraceManager makeDisplayClassNameWithSuper:childClass:]', symObjAddr: 0x15E0, symBinAddr: 0x14700, symSize: 0xB8 }
  - { offset: 0x66C6D, size: 0x8, addend: 0x0, symName: '-[LKS_TraceManager searchTargets]', symObjAddr: 0x1698, symBinAddr: 0x147B8, symSize: 0x8 }
  - { offset: 0x66CA4, size: 0x8, addend: 0x0, symName: '-[LKS_TraceManager setSearchTargets:]', symObjAddr: 0x16A0, symBinAddr: 0x147C0, symSize: 0xC }
  - { offset: 0x66CE5, size: 0x8, addend: 0x0, symName: '-[LKS_TraceManager .cxx_destruct]', symObjAddr: 0x16AC, symBinAddr: 0x147CC, symSize: 0xC }
  - { offset: 0x66D18, size: 0x8, addend: 0x0, symName: ___LKS_InvalidIvarTraces_block_invoke, symObjAddr: 0x16B8, symBinAddr: 0x147D8, symSize: 0x154 }
  - { offset: 0x67479, size: 0x8, addend: 0x0, symName: '+[LKSConfigManager collapsedClassList]', symObjAddr: 0x0, symBinAddr: 0x1492C, symSize: 0x9C }
  - { offset: 0x67496, size: 0x8, addend: 0x0, symName: '+[LKSConfigManager collapsedClassList]', symObjAddr: 0x0, symBinAddr: 0x1492C, symSize: 0x9C }
  - { offset: 0x6752D, size: 0x8, addend: 0x0, symName: '+[LKSConfigManager queryCollapsedClassListWithClass:selector:]', symObjAddr: 0x9C, symBinAddr: 0x149C8, symSize: 0x12C }
  - { offset: 0x67618, size: 0x8, addend: 0x0, symName: '___62+[LKSConfigManager queryCollapsedClassListWithClass:selector:]_block_invoke', symObjAddr: 0x1C8, symBinAddr: 0x14AF4, symSize: 0x50 }
  - { offset: 0x67653, size: 0x8, addend: 0x0, symName: '+[LKSConfigManager colorAlias]', symObjAddr: 0x218, symBinAddr: 0x14B44, symSize: 0x9C }
  - { offset: 0x676C4, size: 0x8, addend: 0x0, symName: '+[LKSConfigManager queryColorAliasWithClass:selector:]', symObjAddr: 0x2B4, symBinAddr: 0x14BE0, symSize: 0x174 }
  - { offset: 0x67789, size: 0x8, addend: 0x0, symName: '___54+[LKSConfigManager queryColorAliasWithClass:selector:]_block_invoke', symObjAddr: 0x428, symBinAddr: 0x14D54, symSize: 0x158 }
  - { offset: 0x67813, size: 0x8, addend: 0x0, symName: '___54+[LKSConfigManager queryColorAliasWithClass:selector:]_block_invoke_2', symObjAddr: 0x580, symBinAddr: 0x14EAC, symSize: 0xAC }
  - { offset: 0x6787E, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32r, symObjAddr: 0x62C, symBinAddr: 0x14F58, symSize: 0x10 }
  - { offset: 0x678A7, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32r, symObjAddr: 0x63C, symBinAddr: 0x14F68, symSize: 0xC }
  - { offset: 0x678C6, size: 0x8, addend: 0x0, symName: '+[LKSConfigManager shouldCaptureScreenshotOfLayer:]', symObjAddr: 0x658, symBinAddr: 0x14F74, symSize: 0x98 }
  - { offset: 0x6791D, size: 0x8, addend: 0x0, symName: '+[LKSConfigManager shouldCaptureImageOfLayer:]', symObjAddr: 0x6F0, symBinAddr: 0x1500C, symSize: 0x1AC }
  - { offset: 0x679FA, size: 0x8, addend: 0x0, symName: '+[LKSConfigManager shouldCaptureImageOfView:]', symObjAddr: 0x89C, symBinAddr: 0x151B8, symSize: 0x1AC }
  - { offset: 0x67D57, size: 0x8, addend: 0x0, symName: '+[Lookin_PTChannel channelWithDelegate:]', symObjAddr: 0x0, symBinAddr: 0x15364, symSize: 0x88 }
  - { offset: 0x67D9D, size: 0x8, addend: 0x0, symName: _kUserInfoKey, symObjAddr: 0x26F0, symBinAddr: 0x36580, symSize: 0x0 }
  - { offset: 0x67DBF, size: 0x8, addend: 0x0, symName: _ChannelUniqueID, symObjAddr: 0xF034, symBinAddr: 0x5A450, symSize: 0x0 }
  - { offset: 0x67DDC, size: 0x8, addend: 0x0, symName: _ChannelInstanceCount, symObjAddr: 0xF038, symBinAddr: 0x5A454, symSize: 0x0 }
  - { offset: 0x67FED, size: 0x8, addend: 0x0, symName: '+[Lookin_PTChannel channelWithDelegate:]', symObjAddr: 0x0, symBinAddr: 0x15364, symSize: 0x88 }
  - { offset: 0x68030, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel initWithProtocol:delegate:]', symObjAddr: 0x88, symBinAddr: 0x153EC, symSize: 0xB4 }
  - { offset: 0x68087, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel initWithProtocol:]', symObjAddr: 0x13C, symBinAddr: 0x154A0, symSize: 0x90 }
  - { offset: 0x680CE, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel init]', symObjAddr: 0x1CC, symBinAddr: 0x15530, symSize: 0x5C }
  - { offset: 0x68105, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel didInit]', symObjAddr: 0x228, symBinAddr: 0x1558C, symSize: 0x24 }
  - { offset: 0x68136, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel dealloc]', symObjAddr: 0x24C, symBinAddr: 0x155B0, symSize: 0x44 }
  - { offset: 0x68169, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel isConnected]', symObjAddr: 0x290, symBinAddr: 0x155F4, symSize: 0x14 }
  - { offset: 0x681A0, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel isListening]', symObjAddr: 0x2A4, symBinAddr: 0x15608, symSize: 0x10 }
  - { offset: 0x681D7, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel userInfo]', symObjAddr: 0x2B4, symBinAddr: 0x15618, symSize: 0xC }
  - { offset: 0x6823B, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel setUserInfo:]', symObjAddr: 0x2C0, symBinAddr: 0x15624, symSize: 0x10 }
  - { offset: 0x682BF, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel setConnState:]', symObjAddr: 0x2D0, symBinAddr: 0x15634, symSize: 0x8 }
  - { offset: 0x682FE, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel setDispatchChannel:]', symObjAddr: 0x2D8, symBinAddr: 0x1563C, symSize: 0x90 }
  - { offset: 0x68350, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel setDispatchSource:]', symObjAddr: 0x368, symBinAddr: 0x156CC, symSize: 0x94 }
  - { offset: 0x6835B, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel setDispatchChannel:].cold.1', symObjAddr: 0x1E94, symBinAddr: 0x2F394, symSize: 0x28 }
  - { offset: 0x68373, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel setDispatchSource:]', symObjAddr: 0x368, symBinAddr: 0x156CC, symSize: 0x94 }
  - { offset: 0x683C5, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel delegate]', symObjAddr: 0x3FC, symBinAddr: 0x15760, symSize: 0x8 }
  - { offset: 0x683D0, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel setDispatchSource:].cold.1', symObjAddr: 0x1EBC, symBinAddr: 0x2F3BC, symSize: 0x28 }
  - { offset: 0x683E8, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel delegate]', symObjAddr: 0x3FC, symBinAddr: 0x15760, symSize: 0x8 }
  - { offset: 0x6841F, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel setDelegate:]', symObjAddr: 0x404, symBinAddr: 0x15768, symSize: 0xC4 }
  - { offset: 0x68462, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel debugTag]', symObjAddr: 0x4C8, symBinAddr: 0x1582C, symSize: 0xE4 }
  - { offset: 0x684A9, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel connectToPort:overUSBHub:deviceID:callback:]', symObjAddr: 0x5AC, symBinAddr: 0x15910, symSize: 0x164 }
  - { offset: 0x6853A, size: 0x8, addend: 0x0, symName: '___63-[Lookin_PTChannel connectToPort:overUSBHub:deviceID:callback:]_block_invoke', symObjAddr: 0x710, symBinAddr: 0x15A74, symSize: 0xB0 }
  - { offset: 0x68545, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel connectToPort:overUSBHub:deviceID:callback:].cold.1', symObjAddr: 0x1EE4, symBinAddr: 0x2F3E4, symSize: 0x28 }
  - { offset: 0x6855D, size: 0x8, addend: 0x0, symName: '___63-[Lookin_PTChannel connectToPort:overUSBHub:deviceID:callback:]_block_invoke', symObjAddr: 0x710, symBinAddr: 0x15A74, symSize: 0xB0 }
  - { offset: 0x685FF, size: 0x8, addend: 0x0, symName: '___63-[Lookin_PTChannel connectToPort:overUSBHub:deviceID:callback:]_block_invoke.30', symObjAddr: 0x81C, symBinAddr: 0x15B24, symSize: 0x40 }
  - { offset: 0x6864E, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel connectToPort:IPv4Address:callback:]', symObjAddr: 0x86C, symBinAddr: 0x15B64, symSize: 0x2B0 }
  - { offset: 0x68999, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel connectToPort:IPv4Address:callback:].cold.2', symObjAddr: 0x1F34, symBinAddr: 0x2F434, symSize: 0x98 }
  - { offset: 0x689B1, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel connectToPort:IPv4Address:callback:].cold.1', symObjAddr: 0x1F0C, symBinAddr: 0x2F40C, symSize: 0x28 }
  - { offset: 0x689C9, size: 0x8, addend: 0x0, symName: '___55-[Lookin_PTChannel connectToPort:IPv4Address:callback:]_block_invoke', symObjAddr: 0xB1C, symBinAddr: 0x15E14, symSize: 0x9C }
  - { offset: 0x68A5B, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel listenOnPort:IPv4Address:callback:]', symObjAddr: 0xBB8, symBinAddr: 0x15EB0, symSize: 0x270 }
  - { offset: 0x68D2E, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel listenOnPort:IPv4Address:callback:].cold.1', symObjAddr: 0x1FCC, symBinAddr: 0x2F4CC, symSize: 0x28 }
  - { offset: 0x68D46, size: 0x8, addend: 0x0, symName: '___54-[Lookin_PTChannel listenOnPort:IPv4Address:callback:]_block_invoke', symObjAddr: 0xE28, symBinAddr: 0x16120, symSize: 0x48 }
  - { offset: 0x68DDA, size: 0x8, addend: 0x0, symName: '___54-[Lookin_PTChannel listenOnPort:IPv4Address:callback:]_block_invoke.39', symObjAddr: 0xE70, symBinAddr: 0x16168, symSize: 0x60 }
  - { offset: 0x68E3B, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel acceptIncomingConnection:]', symObjAddr: 0xED0, symBinAddr: 0x161C8, symSize: 0x2CC }
  - { offset: 0x6908D, size: 0x8, addend: 0x0, symName: ___Block_byref_object_copy_, symObjAddr: 0x119C, symBinAddr: 0x16494, symSize: 0x10 }
  - { offset: 0x690B2, size: 0x8, addend: 0x0, symName: ___Block_byref_object_dispose_, symObjAddr: 0x11AC, symBinAddr: 0x164A4, symSize: 0x8 }
  - { offset: 0x690D1, size: 0x8, addend: 0x0, symName: '___45-[Lookin_PTChannel acceptIncomingConnection:]_block_invoke', symObjAddr: 0x11B4, symBinAddr: 0x164AC, symSize: 0xB0 }
  - { offset: 0x69170, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel close]', symObjAddr: 0x12C4, symBinAddr: 0x1655C, symSize: 0x6C }
  - { offset: 0x691EA, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel cancel]', symObjAddr: 0x1330, symBinAddr: 0x165C8, symSize: 0x6C }
  - { offset: 0x69241, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel startReadingFromConnectedChannel:error:]', symObjAddr: 0x139C, symBinAddr: 0x16634, symSize: 0x170 }
  - { offset: 0x692AC, size: 0x8, addend: 0x0, symName: '___59-[Lookin_PTChannel startReadingFromConnectedChannel:error:]_block_invoke', symObjAddr: 0x150C, symBinAddr: 0x167A4, symSize: 0x7C }
  - { offset: 0x69314, size: 0x8, addend: 0x0, symName: '___59-[Lookin_PTChannel startReadingFromConnectedChannel:error:]_block_invoke.44', symObjAddr: 0x1588, symBinAddr: 0x16820, symSize: 0x1D8 }
  - { offset: 0x69415, size: 0x8, addend: 0x0, symName: '___59-[Lookin_PTChannel startReadingFromConnectedChannel:error:]_block_invoke_2', symObjAddr: 0x1760, symBinAddr: 0x169F8, symSize: 0x40 }
  - { offset: 0x694AF, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32b40b, symObjAddr: 0x17A0, symBinAddr: 0x16A38, symSize: 0x3C }
  - { offset: 0x694D8, size: 0x8, addend: 0x0, symName: '___59-[Lookin_PTChannel startReadingFromConnectedChannel:error:]_block_invoke.46', symObjAddr: 0x17DC, symBinAddr: 0x16A74, symSize: 0xB8 }
  - { offset: 0x695E2, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40b48b, symObjAddr: 0x1894, symBinAddr: 0x16B2C, symSize: 0x44 }
  - { offset: 0x6960B, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48b, symObjAddr: 0x1908, symBinAddr: 0x16B70, symSize: 0x3C }
  - { offset: 0x69634, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel sendFrameOfType:tag:withPayload:callback:]', symObjAddr: 0x1944, symBinAddr: 0x16BAC, symSize: 0xD4 }
  - { offset: 0x696C7, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel description]', symObjAddr: 0x1A18, symBinAddr: 0x16C80, symSize: 0xD0 }
  - { offset: 0x69727, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel protocol]', symObjAddr: 0x1AE8, symBinAddr: 0x16D50, symSize: 0xC }
  - { offset: 0x6975E, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel setProtocol:]', symObjAddr: 0x1AF4, symBinAddr: 0x16D5C, symSize: 0x8 }
  - { offset: 0x6979D, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel uniqueID]', symObjAddr: 0x1AFC, symBinAddr: 0x16D64, symSize: 0x8 }
  - { offset: 0x697D4, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel setUniqueID:]', symObjAddr: 0x1B04, symBinAddr: 0x16D6C, symSize: 0x8 }
  - { offset: 0x69811, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel targetPort]', symObjAddr: 0x1B0C, symBinAddr: 0x16D74, symSize: 0x8 }
  - { offset: 0x69848, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel setTargetPort:]', symObjAddr: 0x1B14, symBinAddr: 0x16D7C, symSize: 0x8 }
  - { offset: 0x69885, size: 0x8, addend: 0x0, symName: '-[Lookin_PTChannel .cxx_destruct]', symObjAddr: 0x1B1C, symBinAddr: 0x16D84, symSize: 0x54 }
  - { offset: 0x698B8, size: 0x8, addend: 0x0, symName: '-[Lookin_PTAddress initWithSockaddr:]', symObjAddr: 0x1B70, symBinAddr: 0x16DD8, symSize: 0x74 }
  - { offset: 0x69904, size: 0x8, addend: 0x0, symName: '-[Lookin_PTAddress name]', symObjAddr: 0x1BE4, symBinAddr: 0x16E4C, symSize: 0xD0 }
  - { offset: 0x6990F, size: 0x8, addend: 0x0, symName: '-[Lookin_PTAddress initWithSockaddr:].cold.1', symObjAddr: 0x1FF4, symBinAddr: 0x2F4F4, symSize: 0x28 }
  - { offset: 0x69927, size: 0x8, addend: 0x0, symName: '-[Lookin_PTAddress name]', symObjAddr: 0x1BE4, symBinAddr: 0x16E4C, symSize: 0xD0 }
  - { offset: 0x69A78, size: 0x8, addend: 0x0, symName: '-[Lookin_PTAddress port]', symObjAddr: 0x1CB4, symBinAddr: 0x16F1C, symSize: 0x20 }
  - { offset: 0x69AB0, size: 0x8, addend: 0x0, symName: '-[Lookin_PTAddress description]', symObjAddr: 0x1CD4, symBinAddr: 0x16F3C, symSize: 0x8C }
  - { offset: 0x69AE8, size: 0x8, addend: 0x0, symName: '-[Lookin_PTData initWithMappedDispatchData:data:length:]', symObjAddr: 0x1D60, symBinAddr: 0x16FC8, symSize: 0x9C }
  - { offset: 0x69B53, size: 0x8, addend: 0x0, symName: '-[Lookin_PTData dealloc]', symObjAddr: 0x1DFC, symBinAddr: 0x17064, symSize: 0x38 }
  - { offset: 0x69B87, size: 0x8, addend: 0x0, symName: '-[Lookin_PTData description]', symObjAddr: 0x1E34, symBinAddr: 0x1709C, symSize: 0x38 }
  - { offset: 0x69BBF, size: 0x8, addend: 0x0, symName: '-[Lookin_PTData dispatchData]', symObjAddr: 0x1E6C, symBinAddr: 0x170D4, symSize: 0xC }
  - { offset: 0x69BF7, size: 0x8, addend: 0x0, symName: '-[Lookin_PTData data]', symObjAddr: 0x1E78, symBinAddr: 0x170E0, symSize: 0x8 }
  - { offset: 0x69C2F, size: 0x8, addend: 0x0, symName: '-[Lookin_PTData length]', symObjAddr: 0x1E80, symBinAddr: 0x170E8, symSize: 0x8 }
  - { offset: 0x69C67, size: 0x8, addend: 0x0, symName: '-[Lookin_PTData .cxx_destruct]', symObjAddr: 0x1E88, symBinAddr: 0x170F0, symSize: 0xC }
  - { offset: 0x6A3F6, size: 0x8, addend: 0x0, symName: '-[Lookin_RQueueLocalIOFrameProtocol setQueue:]', symObjAddr: 0x0, symBinAddr: 0x170FC, symSize: 0x4 }
  - { offset: 0x6A410, size: 0x8, addend: 0x0, symName: _Lookin_PTProtocolErrorDomain, symObjAddr: 0x1388, symBinAddr: 0x44FC8, symSize: 0x0 }
  - { offset: 0x6A424, size: 0x8, addend: 0x0, symName: '+[Lookin_PTProtocol sharedProtocolForQueue:]', symObjAddr: 0x4, symBinAddr: 0x17100, symSize: 0xD4 }
  - { offset: 0x6A44E, size: 0x8, addend: 0x0, symName: '_sharedProtocolForQueue:.currentQueueFrameProtocolKey', symObjAddr: 0x1DC4, symBinAddr: 0x36581, symSize: 0x0 }
  - { offset: 0x6A515, size: 0x8, addend: 0x0, symName: '+[NSData(Lookin_PTProtocol) dataWithContentsOfDispatchData:]', symObjAddr: 0xFD4, symBinAddr: 0x18018, symSize: 0xB4 }
  - { offset: 0x6A6A7, size: 0x8, addend: 0x0, symName: '-[Lookin_RQueueLocalIOFrameProtocol setQueue:]', symObjAddr: 0x0, symBinAddr: 0x170FC, symSize: 0x4 }
  - { offset: 0x6A718, size: 0x8, addend: 0x0, symName: __release_queue_local_protocol, symObjAddr: 0xD8, symBinAddr: 0x171D4, symSize: 0x34 }
  - { offset: 0x6A75C, size: 0x8, addend: 0x0, symName: '-[Lookin_PTProtocol initWithDispatchQueue:]', symObjAddr: 0x10C, symBinAddr: 0x17208, symSize: 0x88 }
  - { offset: 0x6A7A3, size: 0x8, addend: 0x0, symName: '-[Lookin_PTProtocol init]', symObjAddr: 0x194, symBinAddr: 0x17290, symSize: 0xC }
  - { offset: 0x6A7DA, size: 0x8, addend: 0x0, symName: '-[Lookin_PTProtocol dealloc]', symObjAddr: 0x1A0, symBinAddr: 0x1729C, symSize: 0x34 }
  - { offset: 0x6A80D, size: 0x8, addend: 0x0, symName: '-[Lookin_PTProtocol queue]', symObjAddr: 0x1D4, symBinAddr: 0x172D0, symSize: 0x8 }
  - { offset: 0x6A844, size: 0x8, addend: 0x0, symName: '-[Lookin_PTProtocol setQueue:]', symObjAddr: 0x1DC, symBinAddr: 0x172D8, symSize: 0xC }
  - { offset: 0x6A887, size: 0x8, addend: 0x0, symName: '-[Lookin_PTProtocol newTag]', symObjAddr: 0x1E8, symBinAddr: 0x172E4, symSize: 0x14 }
  - { offset: 0x6A8BE, size: 0x8, addend: 0x0, symName: '-[Lookin_PTProtocol createDispatchDataWithFrameOfType:frameTag:payload:]', symObjAddr: 0x1FC, symBinAddr: 0x172F8, symSize: 0x11C }
  - { offset: 0x6A9EF, size: 0x8, addend: 0x0, symName: '___72-[Lookin_PTProtocol createDispatchDataWithFrameOfType:frameTag:payload:]_block_invoke', symObjAddr: 0x318, symBinAddr: 0x17414, symSize: 0x18 }
  - { offset: 0x6AA7C, size: 0x8, addend: 0x0, symName: '-[Lookin_PTProtocol createDispatchDataWithFrameOfType:frameTag:payload:].cold.1', symObjAddr: 0x11CC, symBinAddr: 0x2F51C, symSize: 0x28 }
  - { offset: 0x6AA94, size: 0x8, addend: 0x0, symName: '___72-[Lookin_PTProtocol createDispatchDataWithFrameOfType:frameTag:payload:]_block_invoke', symObjAddr: 0x318, symBinAddr: 0x17414, symSize: 0x18 }
  - { offset: 0x6AAF1, size: 0x8, addend: 0x0, symName: '-[Lookin_PTProtocol sendFrameOfType:tag:withPayload:overChannel:callback:]', symObjAddr: 0x330, symBinAddr: 0x1742C, symSize: 0xF4 }
  - { offset: 0x6ABF5, size: 0x8, addend: 0x0, symName: '___74-[Lookin_PTProtocol sendFrameOfType:tag:withPayload:overChannel:callback:]_block_invoke', symObjAddr: 0x424, symBinAddr: 0x17520, symSize: 0xA0 }
  - { offset: 0x6AC95, size: 0x8, addend: 0x0, symName: '-[Lookin_PTProtocol readFrameOverChannel:callback:]', symObjAddr: 0x4DC, symBinAddr: 0x175C0, symSize: 0xDC }
  - { offset: 0x6AD4F, size: 0x8, addend: 0x0, symName: ___Block_byref_object_copy_, symObjAddr: 0x5B8, symBinAddr: 0x1769C, symSize: 0x10 }
  - { offset: 0x6AD74, size: 0x8, addend: 0x0, symName: ___Block_byref_object_dispose_, symObjAddr: 0x5C8, symBinAddr: 0x176AC, symSize: 0x8 }
  - { offset: 0x6AD93, size: 0x8, addend: 0x0, symName: '___51-[Lookin_PTProtocol readFrameOverChannel:callback:]_block_invoke', symObjAddr: 0x5D0, symBinAddr: 0x176B4, symSize: 0x24C }
  - { offset: 0x6AF41, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32b40r, symObjAddr: 0x81C, symBinAddr: 0x17900, symSize: 0x3C }
  - { offset: 0x6AF6A, size: 0x8, addend: 0x0, symName: '-[Lookin_PTProtocol readPayloadOfSize:overChannel:callback:]', symObjAddr: 0x884, symBinAddr: 0x1793C, symSize: 0xE0 }
  - { offset: 0x6B012, size: 0x8, addend: 0x0, symName: '___60-[Lookin_PTProtocol readPayloadOfSize:overChannel:callback:]_block_invoke', symObjAddr: 0x964, symBinAddr: 0x17A1C, symSize: 0x194 }
  - { offset: 0x6B150, size: 0x8, addend: 0x0, symName: '-[Lookin_PTProtocol readAndDiscardDataOfSize:overChannel:callback:]', symObjAddr: 0xAF8, symBinAddr: 0x17BB0, symSize: 0xA0 }
  - { offset: 0x6B1E8, size: 0x8, addend: 0x0, symName: '___67-[Lookin_PTProtocol readAndDiscardDataOfSize:overChannel:callback:]_block_invoke', symObjAddr: 0xB98, symBinAddr: 0x17C50, symSize: 0xC8 }
  - { offset: 0x6B2C4, size: 0x8, addend: 0x0, symName: '-[Lookin_PTProtocol readFramesOverChannel:onFrame:]', symObjAddr: 0xC60, symBinAddr: 0x17D18, symSize: 0xC4 }
  - { offset: 0x6B31A, size: 0x8, addend: 0x0, symName: '___51-[Lookin_PTProtocol readFramesOverChannel:onFrame:]_block_invoke', symObjAddr: 0xD24, symBinAddr: 0x17DDC, symSize: 0xC4 }
  - { offset: 0x6B3F9, size: 0x8, addend: 0x0, symName: '___51-[Lookin_PTProtocol readFramesOverChannel:onFrame:]_block_invoke_2', symObjAddr: 0xDE8, symBinAddr: 0x17EA0, symSize: 0x1C }
  - { offset: 0x6B46E, size: 0x8, addend: 0x0, symName: '-[Lookin_PTProtocol .cxx_destruct]', symObjAddr: 0xE70, symBinAddr: 0x17EBC, symSize: 0xC }
  - { offset: 0x6B4A1, size: 0x8, addend: 0x0, symName: '-[Lookin_PTDispatchData initWithDispatchData:]', symObjAddr: 0xE7C, symBinAddr: 0x17EC8, symSize: 0x88 }
  - { offset: 0x6B4EA, size: 0x8, addend: 0x0, symName: '-[Lookin_PTDispatchData dealloc]', symObjAddr: 0xF04, symBinAddr: 0x17F50, symSize: 0x34 }
  - { offset: 0x6B51E, size: 0x8, addend: 0x0, symName: '-[Lookin_PTDispatchData .cxx_destruct]', symObjAddr: 0xF38, symBinAddr: 0x17F84, symSize: 0xC }
  - { offset: 0x6B552, size: 0x8, addend: 0x0, symName: '-[NSData(Lookin_PTProtocol) createReferencingDispatchData]', symObjAddr: 0xF44, symBinAddr: 0x17F90, symSize: 0x80 }
  - { offset: 0x6B5A7, size: 0x8, addend: 0x0, symName: '___58-[NSData(Lookin_PTProtocol) createReferencingDispatchData]_block_invoke', symObjAddr: 0xFC4, symBinAddr: 0x18010, symSize: 0x8 }
  - { offset: 0x6B607, size: 0x8, addend: 0x0, symName: '-[NSDictionary(Lookin_PTProtocol) createReferencingDispatchData]', symObjAddr: 0x1088, symBinAddr: 0x180CC, symSize: 0x8C }
  - { offset: 0x6B67F, size: 0x8, addend: 0x0, symName: '+[NSDictionary(Lookin_PTProtocol) dictionaryWithContentsOfDispatchData:]', symObjAddr: 0x1114, symBinAddr: 0x18158, symSize: 0xB8 }
  - { offset: 0x6BDF3, size: 0x8, addend: 0x0, symName: '+[Lookin_PTUSBHub sharedHub]', symObjAddr: 0x0, symBinAddr: 0x18210, symSize: 0x40 }
  - { offset: 0x6BE0D, size: 0x8, addend: 0x0, symName: _Lookin_PTUSBHubErrorDomain, symObjAddr: 0x27B0, symBinAddr: 0x45090, symSize: 0x0 }
  - { offset: 0x6BE2D, size: 0x8, addend: 0x0, symName: _Lookin_PTUSBDeviceDidAttachNotification, symObjAddr: 0x27B8, symBinAddr: 0x45098, symSize: 0x0 }
  - { offset: 0x6BE43, size: 0x8, addend: 0x0, symName: _Lookin_PTUSBDeviceDidDetachNotification, symObjAddr: 0x27C0, symBinAddr: 0x450A0, symSize: 0x0 }
  - { offset: 0x6BE4D, size: 0x8, addend: 0x0, symName: '+[Lookin_PTUSBHub sharedHub]', symObjAddr: 0x0, symBinAddr: 0x18210, symSize: 0x40 }
  - { offset: 0x6BE77, size: 0x8, addend: 0x0, symName: _sharedHub.gSharedHub, symObjAddr: 0xE530, symBinAddr: 0x5A458, symSize: 0x0 }
  - { offset: 0x6BE8D, size: 0x8, addend: 0x0, symName: _sharedHub.onceToken, symObjAddr: 0xE538, symBinAddr: 0x5A460, symSize: 0x0 }
  - { offset: 0x6BF89, size: 0x8, addend: 0x0, symName: '+[Lookin_PTUSBChannel packetDictionaryWithPacketType:payload:]', symObjAddr: 0x668, symBinAddr: 0x1880C, symSize: 0x118 }
  - { offset: 0x6BFB5, size: 0x8, addend: 0x0, symName: '_packetDictionaryWithPacketType:payload:.bundleName', symObjAddr: 0xE540, symBinAddr: 0x5A468, symSize: 0x0 }
  - { offset: 0x6BFCC, size: 0x8, addend: 0x0, symName: '_packetDictionaryWithPacketType:payload:.bundleVersion', symObjAddr: 0xE548, symBinAddr: 0x5A470, symSize: 0x0 }
  - { offset: 0x6BFE3, size: 0x8, addend: 0x0, symName: '_packetDictionaryWithPacketType:payload:.onceToken', symObjAddr: 0xE550, symBinAddr: 0x5A478, symSize: 0x0 }
  - { offset: 0x6C07F, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel scheduleReadPacketWithCallback:]', symObjAddr: 0x11F4, symBinAddr: 0x19388, symSize: 0xA0 }
  - { offset: 0x6C1E7, size: 0x8, addend: 0x0, symName: '___28+[Lookin_PTUSBHub sharedHub]_block_invoke', symObjAddr: 0x40, symBinAddr: 0x18250, symSize: 0x50 }
  - { offset: 0x6C20E, size: 0x8, addend: 0x0, symName: '___28+[Lookin_PTUSBHub sharedHub]_block_invoke_2', symObjAddr: 0x90, symBinAddr: 0x182A0, symSize: 0x2C }
  - { offset: 0x6C263, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBHub init]', symObjAddr: 0xBC, symBinAddr: 0x182CC, symSize: 0x58 }
  - { offset: 0x6C29A, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBHub listenOnQueue:onStart:onEnd:]', symObjAddr: 0x114, symBinAddr: 0x18324, symSize: 0x140 }
  - { offset: 0x6C342, size: 0x8, addend: 0x0, symName: '___47-[Lookin_PTUSBHub listenOnQueue:onStart:onEnd:]_block_invoke', symObjAddr: 0x254, symBinAddr: 0x18464, symSize: 0xC }
  - { offset: 0x6C391, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBHub connectToDevice:port:onStart:onEnd:]', symObjAddr: 0x270, symBinAddr: 0x18470, symSize: 0x1E4 }
  - { offset: 0x6C451, size: 0x8, addend: 0x0, symName: '___54-[Lookin_PTUSBHub connectToDevice:port:onStart:onEnd:]_block_invoke', symObjAddr: 0x454, symBinAddr: 0x18654, symSize: 0xBC }
  - { offset: 0x6C519, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBHub handleBroadcastPacket:]', symObjAddr: 0x56C, symBinAddr: 0x18710, symSize: 0xF0 }
  - { offset: 0x6C57A, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBHub .cxx_destruct]', symObjAddr: 0x65C, symBinAddr: 0x18800, symSize: 0xC }
  - { offset: 0x6C5AD, size: 0x8, addend: 0x0, symName: '___62+[Lookin_PTUSBChannel packetDictionaryWithPacketType:payload:]_block_invoke', symObjAddr: 0x780, symBinAddr: 0x18924, symSize: 0xC0 }
  - { offset: 0x6C5F6, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel init]', symObjAddr: 0x840, symBinAddr: 0x189E4, symSize: 0x58 }
  - { offset: 0x6C62E, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel dealloc]', symObjAddr: 0x898, symBinAddr: 0x18A3C, symSize: 0x50 }
  - { offset: 0x6C662, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel valid]', symObjAddr: 0x8E8, symBinAddr: 0x18A8C, symSize: 0x10 }
  - { offset: 0x6C69A, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel dispatchChannel]', symObjAddr: 0x8F8, symBinAddr: 0x18A9C, symSize: 0x8 }
  - { offset: 0x6C6D2, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel fileDescriptor]', symObjAddr: 0x900, symBinAddr: 0x18AA4, symSize: 0x8 }
  - { offset: 0x6C72B, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel openOnQueue:error:onEnd:]', symObjAddr: 0x908, symBinAddr: 0x18AAC, symSize: 0x220 }
  - { offset: 0x6C9B5, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel openOnQueue:error:onEnd:].cold.1', symObjAddr: 0x1DB8, symBinAddr: 0x2F544, symSize: 0x28 }
  - { offset: 0x6C9CD, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel openOnQueue:error:onEnd:].cold.2', symObjAddr: 0x1DE0, symBinAddr: 0x2F56C, symSize: 0x28 }
  - { offset: 0x6C9E5, size: 0x8, addend: 0x0, symName: '___47-[Lookin_PTUSBChannel openOnQueue:error:onEnd:]_block_invoke', symObjAddr: 0xB28, symBinAddr: 0x18CCC, symSize: 0x94 }
  - { offset: 0x6CA98, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel listenWithBroadcastHandler:callback:]', symObjAddr: 0xBCC, symBinAddr: 0x18D60, symSize: 0xD8 }
  - { offset: 0x6CAFF, size: 0x8, addend: 0x0, symName: '___59-[Lookin_PTUSBChannel listenWithBroadcastHandler:callback:]_block_invoke', symObjAddr: 0xCA4, symBinAddr: 0x18E38, symSize: 0x84 }
  - { offset: 0x6CBA4, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel errorFromPlistResponse:error:]', symObjAddr: 0xD28, symBinAddr: 0x18EBC, symSize: 0x164 }
  - { offset: 0x6CC4B, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel nextPacketTag]', symObjAddr: 0xE8C, symBinAddr: 0x19020, symSize: 0x14 }
  - { offset: 0x6CC83, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel sendRequest:callback:]', symObjAddr: 0xEA0, symBinAddr: 0x19034, symSize: 0xC8 }
  - { offset: 0x6CCEA, size: 0x8, addend: 0x0, symName: '___44-[Lookin_PTUSBChannel sendRequest:callback:]_block_invoke', symObjAddr: 0xF68, symBinAddr: 0x190FC, symSize: 0xCC }
  - { offset: 0x6CD79, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel setNeedsReadingPacket]', symObjAddr: 0x1034, symBinAddr: 0x191C8, symSize: 0x14 }
  - { offset: 0x6CDAB, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel scheduleReadPacketWithBroadcastHandler:]', symObjAddr: 0x1048, symBinAddr: 0x191DC, symSize: 0x8C }
  - { offset: 0x6CDF3, size: 0x8, addend: 0x0, symName: '___62-[Lookin_PTUSBChannel scheduleReadPacketWithBroadcastHandler:]_block_invoke', symObjAddr: 0x10D4, symBinAddr: 0x19268, symSize: 0x120 }
  - { offset: 0x6CDFE, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel scheduleReadPacketWithBroadcastHandler:].cold.1', symObjAddr: 0x1E08, symBinAddr: 0x2F594, symSize: 0x28 }
  - { offset: 0x6CE16, size: 0x8, addend: 0x0, symName: '___62-[Lookin_PTUSBChannel scheduleReadPacketWithBroadcastHandler:]_block_invoke', symObjAddr: 0x10D4, symBinAddr: 0x19268, symSize: 0x120 }
  - { offset: 0x6CF49, size: 0x8, addend: 0x0, symName: '___54-[Lookin_PTUSBChannel scheduleReadPacketWithCallback:]_block_invoke', symObjAddr: 0x1294, symBinAddr: 0x19428, symSize: 0x16C }
  - { offset: 0x6D0E4, size: 0x8, addend: 0x0, symName: _usbmux_packet_alloc, symObjAddr: 0x1400, symBinAddr: 0x19594, symSize: 0x48 }
  - { offset: 0x6D110, size: 0x8, addend: 0x0, symName: _usbmux_packet_alloc, symObjAddr: 0x1400, symBinAddr: 0x19594, symSize: 0x48 }
  - { offset: 0x6D17A, size: 0x8, addend: 0x0, symName: '___54-[Lookin_PTUSBChannel scheduleReadPacketWithCallback:]_block_invoke_2', symObjAddr: 0x1448, symBinAddr: 0x195DC, symSize: 0x384 }
  - { offset: 0x6D185, size: 0x8, addend: 0x0, symName: '___54-[Lookin_PTUSBChannel scheduleReadPacketWithCallback:]_block_invoke.cold.1', symObjAddr: 0x1E30, symBinAddr: 0x2F5BC, symSize: 0x28 }
  - { offset: 0x6D1BA, size: 0x8, addend: 0x0, symName: _usbmux_packet_alloc.cold.1, symObjAddr: 0x1E58, symBinAddr: 0x2F5E4, symSize: 0x28 }
  - { offset: 0x6D221, size: 0x8, addend: 0x0, symName: '___54-[Lookin_PTUSBChannel scheduleReadPacketWithCallback:]_block_invoke_2', symObjAddr: 0x1448, symBinAddr: 0x195DC, symSize: 0x384 }
  - { offset: 0x6D490, size: 0x8, addend: 0x0, symName: '___54-[Lookin_PTUSBChannel scheduleReadPacketWithCallback:]_block_invoke_2.cold.1', symObjAddr: 0x1E80, symBinAddr: 0x2F60C, symSize: 0x28 }
  - { offset: 0x6D52C, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel sendPacketOfType:overProtocol:tag:payload:callback:]', symObjAddr: 0x17CC, symBinAddr: 0x19960, symSize: 0x154 }
  - { offset: 0x6D6A2, size: 0x8, addend: 0x0, symName: '___74-[Lookin_PTUSBChannel sendPacketOfType:overProtocol:tag:payload:callback:]_block_invoke', symObjAddr: 0x1920, symBinAddr: 0x19AB4, symSize: 0x14 }
  - { offset: 0x6D6CE, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel sendPacketOfType:overProtocol:tag:payload:callback:].cold.1', symObjAddr: 0x1EA8, symBinAddr: 0x2F634, symSize: 0x28 }
  - { offset: 0x6D6E6, size: 0x8, addend: 0x0, symName: '___74-[Lookin_PTUSBChannel sendPacketOfType:overProtocol:tag:payload:callback:]_block_invoke', symObjAddr: 0x1920, symBinAddr: 0x19AB4, symSize: 0x14 }
  - { offset: 0x6D751, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel sendPacket:tag:callback:]', symObjAddr: 0x1934, symBinAddr: 0x19AC8, symSize: 0xBC }
  - { offset: 0x6D7EE, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel sendDispatchData:callback:]', symObjAddr: 0x19F0, symBinAddr: 0x19B84, symSize: 0x9C }
  - { offset: 0x6D8A6, size: 0x8, addend: 0x0, symName: '___49-[Lookin_PTUSBChannel sendDispatchData:callback:]_block_invoke', symObjAddr: 0x1A8C, symBinAddr: 0x19C20, symSize: 0x94 }
  - { offset: 0x6D94B, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel sendData:callback:]', symObjAddr: 0x1B20, symBinAddr: 0x19CB4, symSize: 0xF4 }
  - { offset: 0x6D9DD, size: 0x8, addend: 0x0, symName: '___41-[Lookin_PTUSBChannel sendData:callback:]_block_invoke', symObjAddr: 0x1C14, symBinAddr: 0x19DA8, symSize: 0x8 }
  - { offset: 0x6DA1B, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel readFromOffset:length:callback:]', symObjAddr: 0x1C1C, symBinAddr: 0x19DB0, symSize: 0xA8 }
  - { offset: 0x6DAB4, size: 0x8, addend: 0x0, symName: '___54-[Lookin_PTUSBChannel readFromOffset:length:callback:]_block_invoke', symObjAddr: 0x1CC4, symBinAddr: 0x19E58, symSize: 0x90 }
  - { offset: 0x6DB60, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel cancel]', symObjAddr: 0x1D54, symBinAddr: 0x19EE8, symSize: 0x14 }
  - { offset: 0x6DBBD, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel stop]', symObjAddr: 0x1D68, symBinAddr: 0x19EFC, symSize: 0x14 }
  - { offset: 0x6DC06, size: 0x8, addend: 0x0, symName: '-[Lookin_PTUSBChannel .cxx_destruct]', symObjAddr: 0x1D7C, symBinAddr: 0x19F10, symSize: 0x3C }
  - { offset: 0x6E57F, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo copyWithZone:]', symObjAddr: 0x0, symBinAddr: 0x19F4C, symSize: 0x148 }
  - { offset: 0x6E592, size: 0x8, addend: 0x0, symName: '+[LookinAppInfo getAppInfoIdentifier]', symObjAddr: 0xFB0, symBinAddr: 0x1AEFC, symSize: 0x40 }
  - { offset: 0x6E5BC, size: 0x8, addend: 0x0, symName: _getAppInfoIdentifier.onceToken, symObjAddr: 0x86C8, symBinAddr: 0x5A480, symSize: 0x0 }
  - { offset: 0x6E5D2, size: 0x8, addend: 0x0, symName: _getAppInfoIdentifier.identifier, symObjAddr: 0x86D0, symBinAddr: 0x5A488, symSize: 0x0 }
  - { offset: 0x6E80B, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo copyWithZone:]', symObjAddr: 0x0, symBinAddr: 0x19F4C, symSize: 0x148 }
  - { offset: 0x6E862, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo initWithCoder:]', symObjAddr: 0x148, symBinAddr: 0x1A094, symSize: 0x2EC }
  - { offset: 0x6E8D8, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo encodeWithCoder:]', symObjAddr: 0x434, symBinAddr: 0x1A380, symSize: 0x2AC }
  - { offset: 0x6E96F, size: 0x8, addend: 0x0, symName: '+[LookinAppInfo supportsSecureCoding]', symObjAddr: 0x6E0, symBinAddr: 0x1A62C, symSize: 0x8 }
  - { offset: 0x6E9A2, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo isEqual:]', symObjAddr: 0x6E8, symBinAddr: 0x1A634, symSize: 0x78 }
  - { offset: 0x6E9E9, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo hash]', symObjAddr: 0x760, symBinAddr: 0x1A6AC, symSize: 0xA8 }
  - { offset: 0x6EA20, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo isEqualToAppInfo:]', symObjAddr: 0x808, symBinAddr: 0x1A754, symSize: 0x164 }
  - { offset: 0x6EA74, size: 0x8, addend: 0x0, symName: '+[LookinAppInfo currentInfoWithScreenshot:icon:localIdentifiers:]', symObjAddr: 0x96C, symBinAddr: 0x1A8B8, symSize: 0x3B4 }
  - { offset: 0x6EB47, size: 0x8, addend: 0x0, symName: '+[LookinAppInfo appName]', symObjAddr: 0xD20, symBinAddr: 0x1AC6C, symSize: 0xBC }
  - { offset: 0x6EBAA, size: 0x8, addend: 0x0, symName: '+[LookinAppInfo appIcon]', symObjAddr: 0xDDC, symBinAddr: 0x1AD28, symSize: 0x10C }
  - { offset: 0x6EBED, size: 0x8, addend: 0x0, symName: '+[LookinAppInfo screenshotImage]', symObjAddr: 0xEE8, symBinAddr: 0x1AE34, symSize: 0xC0 }
  - { offset: 0x6ECAD, size: 0x8, addend: 0x0, symName: '+[LookinAppInfo isSimulator]', symObjAddr: 0xFA8, symBinAddr: 0x1AEF4, symSize: 0x8 }
  - { offset: 0x6ED21, size: 0x8, addend: 0x0, symName: '___37+[LookinAppInfo getAppInfoIdentifier]_block_invoke', symObjAddr: 0xFF0, symBinAddr: 0x1AF3C, symSize: 0x44 }
  - { offset: 0x6ED48, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo appInfoIdentifier]', symObjAddr: 0x1034, symBinAddr: 0x1AF80, symSize: 0x8 }
  - { offset: 0x6ED7F, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo setAppInfoIdentifier:]', symObjAddr: 0x103C, symBinAddr: 0x1AF88, symSize: 0x8 }
  - { offset: 0x6EDBC, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo shouldUseCache]', symObjAddr: 0x1044, symBinAddr: 0x1AF90, symSize: 0x8 }
  - { offset: 0x6EDF3, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo setShouldUseCache:]', symObjAddr: 0x104C, symBinAddr: 0x1AF98, symSize: 0x8 }
  - { offset: 0x6EE2E, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo serverVersion]', symObjAddr: 0x1054, symBinAddr: 0x1AFA0, symSize: 0x8 }
  - { offset: 0x6EE65, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo setServerVersion:]', symObjAddr: 0x105C, symBinAddr: 0x1AFA8, symSize: 0x8 }
  - { offset: 0x6EEA2, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo serverReadableVersion]', symObjAddr: 0x1064, symBinAddr: 0x1AFB0, symSize: 0x8 }
  - { offset: 0x6EED9, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo setServerReadableVersion:]', symObjAddr: 0x106C, symBinAddr: 0x1AFB8, symSize: 0x8 }
  - { offset: 0x6EF16, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo swiftEnabledInLookinServer]', symObjAddr: 0x1074, symBinAddr: 0x1AFC0, symSize: 0x8 }
  - { offset: 0x6EF4D, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo setSwiftEnabledInLookinServer:]', symObjAddr: 0x107C, symBinAddr: 0x1AFC8, symSize: 0x8 }
  - { offset: 0x6EF8A, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo screenshot]', symObjAddr: 0x1084, symBinAddr: 0x1AFD0, symSize: 0x8 }
  - { offset: 0x6EFC1, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo setScreenshot:]', symObjAddr: 0x108C, symBinAddr: 0x1AFD8, symSize: 0xC }
  - { offset: 0x6F002, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo appIcon]', symObjAddr: 0x1098, symBinAddr: 0x1AFE4, symSize: 0x8 }
  - { offset: 0x6F039, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo setAppIcon:]', symObjAddr: 0x10A0, symBinAddr: 0x1AFEC, symSize: 0xC }
  - { offset: 0x6F07A, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo appName]', symObjAddr: 0x10AC, symBinAddr: 0x1AFF8, symSize: 0x8 }
  - { offset: 0x6F0B1, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo setAppName:]', symObjAddr: 0x10B4, symBinAddr: 0x1B000, symSize: 0x8 }
  - { offset: 0x6F0F0, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo appBundleIdentifier]', symObjAddr: 0x10BC, symBinAddr: 0x1B008, symSize: 0x8 }
  - { offset: 0x6F127, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo setAppBundleIdentifier:]', symObjAddr: 0x10C4, symBinAddr: 0x1B010, symSize: 0x8 }
  - { offset: 0x6F166, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo deviceDescription]', symObjAddr: 0x10CC, symBinAddr: 0x1B018, symSize: 0x8 }
  - { offset: 0x6F19D, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo setDeviceDescription:]', symObjAddr: 0x10D4, symBinAddr: 0x1B020, symSize: 0x8 }
  - { offset: 0x6F1DC, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo osDescription]', symObjAddr: 0x10DC, symBinAddr: 0x1B028, symSize: 0x8 }
  - { offset: 0x6F213, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo setOsDescription:]', symObjAddr: 0x10E4, symBinAddr: 0x1B030, symSize: 0x8 }
  - { offset: 0x6F252, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo osMainVersion]', symObjAddr: 0x10EC, symBinAddr: 0x1B038, symSize: 0x8 }
  - { offset: 0x6F289, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo setOsMainVersion:]', symObjAddr: 0x10F4, symBinAddr: 0x1B040, symSize: 0x8 }
  - { offset: 0x6F2C6, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo deviceType]', symObjAddr: 0x10FC, symBinAddr: 0x1B048, symSize: 0x8 }
  - { offset: 0x6F2FD, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo setDeviceType:]', symObjAddr: 0x1104, symBinAddr: 0x1B050, symSize: 0x8 }
  - { offset: 0x6F33A, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo screenWidth]', symObjAddr: 0x110C, symBinAddr: 0x1B058, symSize: 0x8 }
  - { offset: 0x6F36F, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo setScreenWidth:]', symObjAddr: 0x1114, symBinAddr: 0x1B060, symSize: 0x8 }
  - { offset: 0x6F3AD, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo screenHeight]', symObjAddr: 0x111C, symBinAddr: 0x1B068, symSize: 0x8 }
  - { offset: 0x6F3E2, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo setScreenHeight:]', symObjAddr: 0x1124, symBinAddr: 0x1B070, symSize: 0x8 }
  - { offset: 0x6F420, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo screenScale]', symObjAddr: 0x112C, symBinAddr: 0x1B078, symSize: 0x8 }
  - { offset: 0x6F455, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo setScreenScale:]', symObjAddr: 0x1134, symBinAddr: 0x1B080, symSize: 0x8 }
  - { offset: 0x6F493, size: 0x8, addend: 0x0, symName: '-[LookinAppInfo .cxx_destruct]', symObjAddr: 0x113C, symBinAddr: 0x1B088, symSize: 0x60 }
  - { offset: 0x6F5EB, size: 0x8, addend: 0x0, symName: '-[LookinAttribute copyWithZone:]', symObjAddr: 0x0, symBinAddr: 0x1B0E8, symSize: 0x118 }
  - { offset: 0x6FB14, size: 0x8, addend: 0x0, symName: '-[LookinAttribute copyWithZone:]', symObjAddr: 0x0, symBinAddr: 0x1B0E8, symSize: 0x118 }
  - { offset: 0x6FB6B, size: 0x8, addend: 0x0, symName: '-[LookinAttribute encodeWithCoder:]', symObjAddr: 0x118, symBinAddr: 0x1B200, symSize: 0x140 }
  - { offset: 0x6FBAE, size: 0x8, addend: 0x0, symName: '-[LookinAttribute initWithCoder:]', symObjAddr: 0x258, symBinAddr: 0x1B340, symSize: 0x174 }
  - { offset: 0x6FBF5, size: 0x8, addend: 0x0, symName: '+[LookinAttribute supportsSecureCoding]', symObjAddr: 0x3CC, symBinAddr: 0x1B4B4, symSize: 0x8 }
  - { offset: 0x6FC28, size: 0x8, addend: 0x0, symName: '-[LookinAttribute isUserCustom]', symObjAddr: 0x3D4, symBinAddr: 0x1B4BC, symSize: 0x48 }
  - { offset: 0x6FC5F, size: 0x8, addend: 0x0, symName: '-[LookinAttribute identifier]', symObjAddr: 0x41C, symBinAddr: 0x1B504, symSize: 0x8 }
  - { offset: 0x6FC96, size: 0x8, addend: 0x0, symName: '-[LookinAttribute setIdentifier:]', symObjAddr: 0x424, symBinAddr: 0x1B50C, symSize: 0x8 }
  - { offset: 0x6FCD5, size: 0x8, addend: 0x0, symName: '-[LookinAttribute displayTitle]', symObjAddr: 0x42C, symBinAddr: 0x1B514, symSize: 0x8 }
  - { offset: 0x6FD0C, size: 0x8, addend: 0x0, symName: '-[LookinAttribute setDisplayTitle:]', symObjAddr: 0x434, symBinAddr: 0x1B51C, symSize: 0x8 }
  - { offset: 0x6FD4B, size: 0x8, addend: 0x0, symName: '-[LookinAttribute attrType]', symObjAddr: 0x43C, symBinAddr: 0x1B524, symSize: 0x8 }
  - { offset: 0x6FD82, size: 0x8, addend: 0x0, symName: '-[LookinAttribute setAttrType:]', symObjAddr: 0x444, symBinAddr: 0x1B52C, symSize: 0x8 }
  - { offset: 0x6FDBF, size: 0x8, addend: 0x0, symName: '-[LookinAttribute value]', symObjAddr: 0x44C, symBinAddr: 0x1B534, symSize: 0x8 }
  - { offset: 0x6FDF6, size: 0x8, addend: 0x0, symName: '-[LookinAttribute setValue:]', symObjAddr: 0x454, symBinAddr: 0x1B53C, symSize: 0xC }
  - { offset: 0x6FE37, size: 0x8, addend: 0x0, symName: '-[LookinAttribute extraValue]', symObjAddr: 0x460, symBinAddr: 0x1B548, symSize: 0x8 }
  - { offset: 0x6FE6E, size: 0x8, addend: 0x0, symName: '-[LookinAttribute setExtraValue:]', symObjAddr: 0x468, symBinAddr: 0x1B550, symSize: 0xC }
  - { offset: 0x6FEAF, size: 0x8, addend: 0x0, symName: '-[LookinAttribute customSetterID]', symObjAddr: 0x474, symBinAddr: 0x1B55C, symSize: 0x8 }
  - { offset: 0x6FEE6, size: 0x8, addend: 0x0, symName: '-[LookinAttribute setCustomSetterID:]', symObjAddr: 0x47C, symBinAddr: 0x1B564, symSize: 0x8 }
  - { offset: 0x6FF25, size: 0x8, addend: 0x0, symName: '-[LookinAttribute targetDisplayItem]', symObjAddr: 0x484, symBinAddr: 0x1B56C, symSize: 0x18 }
  - { offset: 0x6FF5C, size: 0x8, addend: 0x0, symName: '-[LookinAttribute setTargetDisplayItem:]', symObjAddr: 0x49C, symBinAddr: 0x1B584, symSize: 0xC }
  - { offset: 0x6FF9D, size: 0x8, addend: 0x0, symName: '-[LookinAttribute .cxx_destruct]', symObjAddr: 0x4A8, symBinAddr: 0x1B590, symSize: 0x5C }
  - { offset: 0x70051, size: 0x8, addend: 0x0, symName: '-[LookinAttributeModification encodeWithCoder:]', symObjAddr: 0x0, symBinAddr: 0x1B5EC, symSize: 0x128 }
  - { offset: 0x70232, size: 0x8, addend: 0x0, symName: '-[LookinAttributeModification encodeWithCoder:]', symObjAddr: 0x0, symBinAddr: 0x1B5EC, symSize: 0x128 }
  - { offset: 0x70296, size: 0x8, addend: 0x0, symName: '-[LookinAttributeModification initWithCoder:]', symObjAddr: 0x128, symBinAddr: 0x1B714, symSize: 0x14C }
  - { offset: 0x702FE, size: 0x8, addend: 0x0, symName: '+[LookinAttributeModification supportsSecureCoding]', symObjAddr: 0x274, symBinAddr: 0x1B860, symSize: 0x8 }
  - { offset: 0x70331, size: 0x8, addend: 0x0, symName: '-[LookinAttributeModification targetOid]', symObjAddr: 0x27C, symBinAddr: 0x1B868, symSize: 0x8 }
  - { offset: 0x70368, size: 0x8, addend: 0x0, symName: '-[LookinAttributeModification setTargetOid:]', symObjAddr: 0x284, symBinAddr: 0x1B870, symSize: 0x8 }
  - { offset: 0x703A5, size: 0x8, addend: 0x0, symName: '-[LookinAttributeModification setterSelector]', symObjAddr: 0x28C, symBinAddr: 0x1B878, symSize: 0x8 }
  - { offset: 0x703DC, size: 0x8, addend: 0x0, symName: '-[LookinAttributeModification setSetterSelector:]', symObjAddr: 0x294, symBinAddr: 0x1B880, symSize: 0x8 }
  - { offset: 0x70419, size: 0x8, addend: 0x0, symName: '-[LookinAttributeModification getterSelector]', symObjAddr: 0x29C, symBinAddr: 0x1B888, symSize: 0x8 }
  - { offset: 0x70450, size: 0x8, addend: 0x0, symName: '-[LookinAttributeModification setGetterSelector:]', symObjAddr: 0x2A4, symBinAddr: 0x1B890, symSize: 0x8 }
  - { offset: 0x7048D, size: 0x8, addend: 0x0, symName: '-[LookinAttributeModification attrType]', symObjAddr: 0x2AC, symBinAddr: 0x1B898, symSize: 0x8 }
  - { offset: 0x704C4, size: 0x8, addend: 0x0, symName: '-[LookinAttributeModification setAttrType:]', symObjAddr: 0x2B4, symBinAddr: 0x1B8A0, symSize: 0x8 }
  - { offset: 0x70501, size: 0x8, addend: 0x0, symName: '-[LookinAttributeModification value]', symObjAddr: 0x2BC, symBinAddr: 0x1B8A8, symSize: 0x8 }
  - { offset: 0x70538, size: 0x8, addend: 0x0, symName: '-[LookinAttributeModification setValue:]', symObjAddr: 0x2C4, symBinAddr: 0x1B8B0, symSize: 0xC }
  - { offset: 0x70579, size: 0x8, addend: 0x0, symName: '-[LookinAttributeModification clientReadableVersion]', symObjAddr: 0x2D0, symBinAddr: 0x1B8BC, symSize: 0x8 }
  - { offset: 0x705B0, size: 0x8, addend: 0x0, symName: '-[LookinAttributeModification setClientReadableVersion:]', symObjAddr: 0x2D8, symBinAddr: 0x1B8C4, symSize: 0x8 }
  - { offset: 0x705EF, size: 0x8, addend: 0x0, symName: '-[LookinAttributeModification .cxx_destruct]', symObjAddr: 0x2E0, symBinAddr: 0x1B8CC, symSize: 0x30 }
  - { offset: 0x70684, size: 0x8, addend: 0x0, symName: '-[LookinAttributesGroup copyWithZone:]', symObjAddr: 0x0, symBinAddr: 0x1B8FC, symSize: 0xD4 }
  - { offset: 0x70703, size: 0x8, addend: 0x0, symName: '-[LookinAttributesGroup copyWithZone:]', symObjAddr: 0x0, symBinAddr: 0x1B8FC, symSize: 0xD4 }
  - { offset: 0x7075A, size: 0x8, addend: 0x0, symName: '___38-[LookinAttributesGroup copyWithZone:]_block_invoke', symObjAddr: 0xD4, symBinAddr: 0x1B9D0, symSize: 0x18 }
  - { offset: 0x707A1, size: 0x8, addend: 0x0, symName: '-[LookinAttributesGroup encodeWithCoder:]', symObjAddr: 0xEC, symBinAddr: 0x1B9E8, symSize: 0xC4 }
  - { offset: 0x707E4, size: 0x8, addend: 0x0, symName: '-[LookinAttributesGroup initWithCoder:]', symObjAddr: 0x1B0, symBinAddr: 0x1BAAC, symSize: 0xF8 }
  - { offset: 0x7082B, size: 0x8, addend: 0x0, symName: '-[LookinAttributesGroup hash]', symObjAddr: 0x2A8, symBinAddr: 0x1BBA4, symSize: 0x3C }
  - { offset: 0x70862, size: 0x8, addend: 0x0, symName: '-[LookinAttributesGroup isEqual:]', symObjAddr: 0x2E4, symBinAddr: 0x1BBE0, symSize: 0x164 }
  - { offset: 0x708D0, size: 0x8, addend: 0x0, symName: '+[LookinAttributesGroup supportsSecureCoding]', symObjAddr: 0x448, symBinAddr: 0x1BD44, symSize: 0x8 }
  - { offset: 0x70903, size: 0x8, addend: 0x0, symName: '-[LookinAttributesGroup uniqueKey]', symObjAddr: 0x450, symBinAddr: 0x1BD4C, symSize: 0x74 }
  - { offset: 0x7093A, size: 0x8, addend: 0x0, symName: '-[LookinAttributesGroup isUserCustom]', symObjAddr: 0x4C4, symBinAddr: 0x1BDC0, symSize: 0x48 }
  - { offset: 0x70971, size: 0x8, addend: 0x0, symName: '-[LookinAttributesGroup userCustomTitle]', symObjAddr: 0x50C, symBinAddr: 0x1BE08, symSize: 0x8 }
  - { offset: 0x709A8, size: 0x8, addend: 0x0, symName: '-[LookinAttributesGroup setUserCustomTitle:]', symObjAddr: 0x514, symBinAddr: 0x1BE10, symSize: 0x8 }
  - { offset: 0x709E7, size: 0x8, addend: 0x0, symName: '-[LookinAttributesGroup identifier]', symObjAddr: 0x51C, symBinAddr: 0x1BE18, symSize: 0x8 }
  - { offset: 0x70A1E, size: 0x8, addend: 0x0, symName: '-[LookinAttributesGroup setIdentifier:]', symObjAddr: 0x524, symBinAddr: 0x1BE20, symSize: 0x8 }
  - { offset: 0x70A5D, size: 0x8, addend: 0x0, symName: '-[LookinAttributesGroup attrSections]', symObjAddr: 0x52C, symBinAddr: 0x1BE28, symSize: 0x8 }
  - { offset: 0x70A94, size: 0x8, addend: 0x0, symName: '-[LookinAttributesGroup setAttrSections:]', symObjAddr: 0x534, symBinAddr: 0x1BE30, symSize: 0x8 }
  - { offset: 0x70AD3, size: 0x8, addend: 0x0, symName: '-[LookinAttributesGroup .cxx_destruct]', symObjAddr: 0x53C, symBinAddr: 0x1BE38, symSize: 0x3C }
  - { offset: 0x70C9B, size: 0x8, addend: 0x0, symName: '-[LookinAttributesSection copyWithZone:]', symObjAddr: 0x0, symBinAddr: 0x1BE74, symSize: 0xAC }
  - { offset: 0x70DD5, size: 0x8, addend: 0x0, symName: '-[LookinAttributesSection copyWithZone:]', symObjAddr: 0x0, symBinAddr: 0x1BE74, symSize: 0xAC }
  - { offset: 0x70E2C, size: 0x8, addend: 0x0, symName: '___40-[LookinAttributesSection copyWithZone:]_block_invoke', symObjAddr: 0xAC, symBinAddr: 0x1BF20, symSize: 0x18 }
  - { offset: 0x70E73, size: 0x8, addend: 0x0, symName: '-[LookinAttributesSection encodeWithCoder:]', symObjAddr: 0xC4, symBinAddr: 0x1BF38, symSize: 0x94 }
  - { offset: 0x70EB6, size: 0x8, addend: 0x0, symName: '-[LookinAttributesSection initWithCoder:]', symObjAddr: 0x158, symBinAddr: 0x1BFCC, symSize: 0xC8 }
  - { offset: 0x70EFD, size: 0x8, addend: 0x0, symName: '+[LookinAttributesSection supportsSecureCoding]', symObjAddr: 0x220, symBinAddr: 0x1C094, symSize: 0x8 }
  - { offset: 0x70F30, size: 0x8, addend: 0x0, symName: '-[LookinAttributesSection isUserCustom]', symObjAddr: 0x228, symBinAddr: 0x1C09C, symSize: 0x48 }
  - { offset: 0x70F67, size: 0x8, addend: 0x0, symName: '-[LookinAttributesSection identifier]', symObjAddr: 0x270, symBinAddr: 0x1C0E4, symSize: 0x8 }
  - { offset: 0x70F9E, size: 0x8, addend: 0x0, symName: '-[LookinAttributesSection setIdentifier:]', symObjAddr: 0x278, symBinAddr: 0x1C0EC, symSize: 0x8 }
  - { offset: 0x70FDD, size: 0x8, addend: 0x0, symName: '-[LookinAttributesSection attributes]', symObjAddr: 0x280, symBinAddr: 0x1C0F4, symSize: 0x8 }
  - { offset: 0x71014, size: 0x8, addend: 0x0, symName: '-[LookinAttributesSection setAttributes:]', symObjAddr: 0x288, symBinAddr: 0x1C0FC, symSize: 0x8 }
  - { offset: 0x71053, size: 0x8, addend: 0x0, symName: '-[LookinAttributesSection .cxx_destruct]', symObjAddr: 0x290, symBinAddr: 0x1C104, symSize: 0x30 }
  - { offset: 0x7127F, size: 0x8, addend: 0x0, symName: _LookinAttrGroup_None, symObjAddr: 0x1C50, symBinAddr: 0x452F8, symSize: 0x0 }
  - { offset: 0x712AA, size: 0x8, addend: 0x0, symName: _LookinAttrGroup_Class, symObjAddr: 0x1C58, symBinAddr: 0x45300, symSize: 0x0 }
  - { offset: 0x712C0, size: 0x8, addend: 0x0, symName: _LookinAttrGroup_Relation, symObjAddr: 0x1C60, symBinAddr: 0x45308, symSize: 0x0 }
  - { offset: 0x712D6, size: 0x8, addend: 0x0, symName: _LookinAttrGroup_Layout, symObjAddr: 0x1C68, symBinAddr: 0x45310, symSize: 0x0 }
  - { offset: 0x712EC, size: 0x8, addend: 0x0, symName: _LookinAttrGroup_AutoLayout, symObjAddr: 0x1C70, symBinAddr: 0x45318, symSize: 0x0 }
  - { offset: 0x71302, size: 0x8, addend: 0x0, symName: _LookinAttrGroup_ViewLayer, symObjAddr: 0x1C78, symBinAddr: 0x45320, symSize: 0x0 }
  - { offset: 0x71318, size: 0x8, addend: 0x0, symName: _LookinAttrGroup_UIImageView, symObjAddr: 0x1C80, symBinAddr: 0x45328, symSize: 0x0 }
  - { offset: 0x7132E, size: 0x8, addend: 0x0, symName: _LookinAttrGroup_UILabel, symObjAddr: 0x1C88, symBinAddr: 0x45330, symSize: 0x0 }
  - { offset: 0x71344, size: 0x8, addend: 0x0, symName: _LookinAttrGroup_UIControl, symObjAddr: 0x1C90, symBinAddr: 0x45338, symSize: 0x0 }
  - { offset: 0x7135A, size: 0x8, addend: 0x0, symName: _LookinAttrGroup_UIButton, symObjAddr: 0x1C98, symBinAddr: 0x45340, symSize: 0x0 }
  - { offset: 0x71370, size: 0x8, addend: 0x0, symName: _LookinAttrGroup_UIScrollView, symObjAddr: 0x1CA0, symBinAddr: 0x45348, symSize: 0x0 }
  - { offset: 0x71386, size: 0x8, addend: 0x0, symName: _LookinAttrGroup_UITableView, symObjAddr: 0x1CA8, symBinAddr: 0x45350, symSize: 0x0 }
  - { offset: 0x7139C, size: 0x8, addend: 0x0, symName: _LookinAttrGroup_UITextView, symObjAddr: 0x1CB0, symBinAddr: 0x45358, symSize: 0x0 }
  - { offset: 0x713B2, size: 0x8, addend: 0x0, symName: _LookinAttrGroup_UITextField, symObjAddr: 0x1CB8, symBinAddr: 0x45360, symSize: 0x0 }
  - { offset: 0x713C8, size: 0x8, addend: 0x0, symName: _LookinAttrGroup_UIVisualEffectView, symObjAddr: 0x1CC0, symBinAddr: 0x45368, symSize: 0x0 }
  - { offset: 0x713DE, size: 0x8, addend: 0x0, symName: _LookinAttrGroup_UIStackView, symObjAddr: 0x1CC8, symBinAddr: 0x45370, symSize: 0x0 }
  - { offset: 0x713F4, size: 0x8, addend: 0x0, symName: _LookinAttrGroup_UserCustom, symObjAddr: 0x1CD0, symBinAddr: 0x45378, symSize: 0x0 }
  - { offset: 0x7140A, size: 0x8, addend: 0x0, symName: _LookinAttrSec_None, symObjAddr: 0x1CD8, symBinAddr: 0x45380, symSize: 0x0 }
  - { offset: 0x71430, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UserCustom, symObjAddr: 0x1CE0, symBinAddr: 0x45388, symSize: 0x0 }
  - { offset: 0x71446, size: 0x8, addend: 0x0, symName: _LookinAttrSec_Class_Class, symObjAddr: 0x1CE8, symBinAddr: 0x45390, symSize: 0x0 }
  - { offset: 0x7145C, size: 0x8, addend: 0x0, symName: _LookinAttrSec_Relation_Relation, symObjAddr: 0x1CF0, symBinAddr: 0x45398, symSize: 0x0 }
  - { offset: 0x71472, size: 0x8, addend: 0x0, symName: _LookinAttrSec_Layout_Frame, symObjAddr: 0x1CF8, symBinAddr: 0x453A0, symSize: 0x0 }
  - { offset: 0x71488, size: 0x8, addend: 0x0, symName: _LookinAttrSec_Layout_Bounds, symObjAddr: 0x1D00, symBinAddr: 0x453A8, symSize: 0x0 }
  - { offset: 0x7149E, size: 0x8, addend: 0x0, symName: _LookinAttrSec_Layout_SafeArea, symObjAddr: 0x1D08, symBinAddr: 0x453B0, symSize: 0x0 }
  - { offset: 0x714B4, size: 0x8, addend: 0x0, symName: _LookinAttrSec_Layout_Position, symObjAddr: 0x1D10, symBinAddr: 0x453B8, symSize: 0x0 }
  - { offset: 0x714CA, size: 0x8, addend: 0x0, symName: _LookinAttrSec_Layout_AnchorPoint, symObjAddr: 0x1D18, symBinAddr: 0x453C0, symSize: 0x0 }
  - { offset: 0x714E0, size: 0x8, addend: 0x0, symName: _LookinAttrSec_AutoLayout_Hugging, symObjAddr: 0x1D20, symBinAddr: 0x453C8, symSize: 0x0 }
  - { offset: 0x714F6, size: 0x8, addend: 0x0, symName: _LookinAttrSec_AutoLayout_Resistance, symObjAddr: 0x1D28, symBinAddr: 0x453D0, symSize: 0x0 }
  - { offset: 0x7150C, size: 0x8, addend: 0x0, symName: _LookinAttrSec_AutoLayout_Constraints, symObjAddr: 0x1D30, symBinAddr: 0x453D8, symSize: 0x0 }
  - { offset: 0x71522, size: 0x8, addend: 0x0, symName: _LookinAttrSec_AutoLayout_IntrinsicSize, symObjAddr: 0x1D38, symBinAddr: 0x453E0, symSize: 0x0 }
  - { offset: 0x71538, size: 0x8, addend: 0x0, symName: _LookinAttrSec_ViewLayer_Visibility, symObjAddr: 0x1D40, symBinAddr: 0x453E8, symSize: 0x0 }
  - { offset: 0x7154E, size: 0x8, addend: 0x0, symName: _LookinAttrSec_ViewLayer_InterationAndMasks, symObjAddr: 0x1D48, symBinAddr: 0x453F0, symSize: 0x0 }
  - { offset: 0x71564, size: 0x8, addend: 0x0, symName: _LookinAttrSec_ViewLayer_Corner, symObjAddr: 0x1D50, symBinAddr: 0x453F8, symSize: 0x0 }
  - { offset: 0x7157A, size: 0x8, addend: 0x0, symName: _LookinAttrSec_ViewLayer_BgColor, symObjAddr: 0x1D58, symBinAddr: 0x45400, symSize: 0x0 }
  - { offset: 0x71590, size: 0x8, addend: 0x0, symName: _LookinAttrSec_ViewLayer_Border, symObjAddr: 0x1D60, symBinAddr: 0x45408, symSize: 0x0 }
  - { offset: 0x715A6, size: 0x8, addend: 0x0, symName: _LookinAttrSec_ViewLayer_Shadow, symObjAddr: 0x1D68, symBinAddr: 0x45410, symSize: 0x0 }
  - { offset: 0x715BC, size: 0x8, addend: 0x0, symName: _LookinAttrSec_ViewLayer_ContentMode, symObjAddr: 0x1D70, symBinAddr: 0x45418, symSize: 0x0 }
  - { offset: 0x715D2, size: 0x8, addend: 0x0, symName: _LookinAttrSec_ViewLayer_TintColor, symObjAddr: 0x1D78, symBinAddr: 0x45420, symSize: 0x0 }
  - { offset: 0x715E8, size: 0x8, addend: 0x0, symName: _LookinAttrSec_ViewLayer_Tag, symObjAddr: 0x1D80, symBinAddr: 0x45428, symSize: 0x0 }
  - { offset: 0x715FE, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIImageView_Name, symObjAddr: 0x1D88, symBinAddr: 0x45430, symSize: 0x0 }
  - { offset: 0x71614, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIImageView_Open, symObjAddr: 0x1D90, symBinAddr: 0x45438, symSize: 0x0 }
  - { offset: 0x7162A, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UILabel_Text, symObjAddr: 0x1D98, symBinAddr: 0x45440, symSize: 0x0 }
  - { offset: 0x71640, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UILabel_Font, symObjAddr: 0x1DA0, symBinAddr: 0x45448, symSize: 0x0 }
  - { offset: 0x71656, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UILabel_NumberOfLines, symObjAddr: 0x1DA8, symBinAddr: 0x45450, symSize: 0x0 }
  - { offset: 0x7166C, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UILabel_TextColor, symObjAddr: 0x1DB0, symBinAddr: 0x45458, symSize: 0x0 }
  - { offset: 0x71682, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UILabel_BreakMode, symObjAddr: 0x1DB8, symBinAddr: 0x45460, symSize: 0x0 }
  - { offset: 0x71698, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UILabel_Alignment, symObjAddr: 0x1DC0, symBinAddr: 0x45468, symSize: 0x0 }
  - { offset: 0x716AE, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UILabel_CanAdjustFont, symObjAddr: 0x1DC8, symBinAddr: 0x45470, symSize: 0x0 }
  - { offset: 0x716C4, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIControl_EnabledSelected, symObjAddr: 0x1DD0, symBinAddr: 0x45478, symSize: 0x0 }
  - { offset: 0x716DA, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIControl_VerAlignment, symObjAddr: 0x1DD8, symBinAddr: 0x45480, symSize: 0x0 }
  - { offset: 0x716F0, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIControl_HorAlignment, symObjAddr: 0x1DE0, symBinAddr: 0x45488, symSize: 0x0 }
  - { offset: 0x71706, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIControl_QMUIOutsideEdge, symObjAddr: 0x1DE8, symBinAddr: 0x45490, symSize: 0x0 }
  - { offset: 0x7171C, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIButton_ContentInsets, symObjAddr: 0x1DF0, symBinAddr: 0x45498, symSize: 0x0 }
  - { offset: 0x71732, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIButton_TitleInsets, symObjAddr: 0x1DF8, symBinAddr: 0x454A0, symSize: 0x0 }
  - { offset: 0x71748, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIButton_ImageInsets, symObjAddr: 0x1E00, symBinAddr: 0x454A8, symSize: 0x0 }
  - { offset: 0x7175E, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIScrollView_ContentInset, symObjAddr: 0x1E08, symBinAddr: 0x454B0, symSize: 0x0 }
  - { offset: 0x71774, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIScrollView_AdjustedInset, symObjAddr: 0x1E10, symBinAddr: 0x454B8, symSize: 0x0 }
  - { offset: 0x7178A, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIScrollView_IndicatorInset, symObjAddr: 0x1E18, symBinAddr: 0x454C0, symSize: 0x0 }
  - { offset: 0x717A0, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIScrollView_Offset, symObjAddr: 0x1E20, symBinAddr: 0x454C8, symSize: 0x0 }
  - { offset: 0x717B6, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIScrollView_ContentSize, symObjAddr: 0x1E28, symBinAddr: 0x454D0, symSize: 0x0 }
  - { offset: 0x717CC, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIScrollView_Behavior, symObjAddr: 0x1E30, symBinAddr: 0x454D8, symSize: 0x0 }
  - { offset: 0x717E2, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIScrollView_ShowsIndicator, symObjAddr: 0x1E38, symBinAddr: 0x454E0, symSize: 0x0 }
  - { offset: 0x717F8, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIScrollView_Bounce, symObjAddr: 0x1E40, symBinAddr: 0x454E8, symSize: 0x0 }
  - { offset: 0x7180E, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIScrollView_ScrollPaging, symObjAddr: 0x1E48, symBinAddr: 0x454F0, symSize: 0x0 }
  - { offset: 0x71824, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIScrollView_ContentTouches, symObjAddr: 0x1E50, symBinAddr: 0x454F8, symSize: 0x0 }
  - { offset: 0x7183A, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIScrollView_Zoom, symObjAddr: 0x1E58, symBinAddr: 0x45500, symSize: 0x0 }
  - { offset: 0x71850, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIScrollView_QMUIInitialInset, symObjAddr: 0x1E60, symBinAddr: 0x45508, symSize: 0x0 }
  - { offset: 0x71866, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UITableView_Style, symObjAddr: 0x1E68, symBinAddr: 0x45510, symSize: 0x0 }
  - { offset: 0x7187C, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UITableView_SectionsNumber, symObjAddr: 0x1E70, symBinAddr: 0x45518, symSize: 0x0 }
  - { offset: 0x71892, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UITableView_RowsNumber, symObjAddr: 0x1E78, symBinAddr: 0x45520, symSize: 0x0 }
  - { offset: 0x718A8, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UITableView_SeparatorStyle, symObjAddr: 0x1E80, symBinAddr: 0x45528, symSize: 0x0 }
  - { offset: 0x718BE, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UITableView_SeparatorColor, symObjAddr: 0x1E88, symBinAddr: 0x45530, symSize: 0x0 }
  - { offset: 0x718D4, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UITableView_SeparatorInset, symObjAddr: 0x1E90, symBinAddr: 0x45538, symSize: 0x0 }
  - { offset: 0x718EA, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UITextView_Basic, symObjAddr: 0x1E98, symBinAddr: 0x45540, symSize: 0x0 }
  - { offset: 0x71900, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UITextView_Text, symObjAddr: 0x1EA0, symBinAddr: 0x45548, symSize: 0x0 }
  - { offset: 0x71916, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UITextView_Font, symObjAddr: 0x1EA8, symBinAddr: 0x45550, symSize: 0x0 }
  - { offset: 0x7192C, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UITextView_TextColor, symObjAddr: 0x1EB0, symBinAddr: 0x45558, symSize: 0x0 }
  - { offset: 0x71942, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UITextView_Alignment, symObjAddr: 0x1EB8, symBinAddr: 0x45560, symSize: 0x0 }
  - { offset: 0x71958, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UITextView_ContainerInset, symObjAddr: 0x1EC0, symBinAddr: 0x45568, symSize: 0x0 }
  - { offset: 0x7196E, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UITextField_Text, symObjAddr: 0x1EC8, symBinAddr: 0x45570, symSize: 0x0 }
  - { offset: 0x71984, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UITextField_Placeholder, symObjAddr: 0x1ED0, symBinAddr: 0x45578, symSize: 0x0 }
  - { offset: 0x7199A, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UITextField_Font, symObjAddr: 0x1ED8, symBinAddr: 0x45580, symSize: 0x0 }
  - { offset: 0x719B0, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UITextField_TextColor, symObjAddr: 0x1EE0, symBinAddr: 0x45588, symSize: 0x0 }
  - { offset: 0x719C6, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UITextField_Alignment, symObjAddr: 0x1EE8, symBinAddr: 0x45590, symSize: 0x0 }
  - { offset: 0x719DC, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UITextField_Clears, symObjAddr: 0x1EF0, symBinAddr: 0x45598, symSize: 0x0 }
  - { offset: 0x719F2, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UITextField_CanAdjustFont, symObjAddr: 0x1EF8, symBinAddr: 0x455A0, symSize: 0x0 }
  - { offset: 0x71A08, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UITextField_ClearButtonMode, symObjAddr: 0x1F00, symBinAddr: 0x455A8, symSize: 0x0 }
  - { offset: 0x71A1E, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIVisualEffectView_Style, symObjAddr: 0x1F08, symBinAddr: 0x455B0, symSize: 0x0 }
  - { offset: 0x71A34, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIVisualEffectView_QMUIForegroundColor, symObjAddr: 0x1F10, symBinAddr: 0x455B8, symSize: 0x0 }
  - { offset: 0x71A4A, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIStackView_Axis, symObjAddr: 0x1F18, symBinAddr: 0x455C0, symSize: 0x0 }
  - { offset: 0x71A60, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIStackView_Distribution, symObjAddr: 0x1F20, symBinAddr: 0x455C8, symSize: 0x0 }
  - { offset: 0x71A76, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIStackView_Alignment, symObjAddr: 0x1F28, symBinAddr: 0x455D0, symSize: 0x0 }
  - { offset: 0x71A8C, size: 0x8, addend: 0x0, symName: _LookinAttrSec_UIStackView_Spacing, symObjAddr: 0x1F30, symBinAddr: 0x455D8, symSize: 0x0 }
  - { offset: 0x71AA2, size: 0x8, addend: 0x0, symName: _LookinAttr_None, symObjAddr: 0x1F38, symBinAddr: 0x455E0, symSize: 0x0 }
  - { offset: 0x71AC8, size: 0x8, addend: 0x0, symName: _LookinAttr_UserCustom, symObjAddr: 0x1F40, symBinAddr: 0x455E8, symSize: 0x0 }
  - { offset: 0x71ADE, size: 0x8, addend: 0x0, symName: _LookinAttr_Class_Class_Class, symObjAddr: 0x1F48, symBinAddr: 0x455F0, symSize: 0x0 }
  - { offset: 0x71AF4, size: 0x8, addend: 0x0, symName: _LookinAttr_Relation_Relation_Relation, symObjAddr: 0x1F50, symBinAddr: 0x455F8, symSize: 0x0 }
  - { offset: 0x71B0A, size: 0x8, addend: 0x0, symName: _LookinAttr_Layout_Frame_Frame, symObjAddr: 0x1F58, symBinAddr: 0x45600, symSize: 0x0 }
  - { offset: 0x71B20, size: 0x8, addend: 0x0, symName: _LookinAttr_Layout_Bounds_Bounds, symObjAddr: 0x1F60, symBinAddr: 0x45608, symSize: 0x0 }
  - { offset: 0x71B36, size: 0x8, addend: 0x0, symName: _LookinAttr_Layout_SafeArea_SafeArea, symObjAddr: 0x1F68, symBinAddr: 0x45610, symSize: 0x0 }
  - { offset: 0x71B4C, size: 0x8, addend: 0x0, symName: _LookinAttr_Layout_Position_Position, symObjAddr: 0x1F70, symBinAddr: 0x45618, symSize: 0x0 }
  - { offset: 0x71B62, size: 0x8, addend: 0x0, symName: _LookinAttr_Layout_AnchorPoint_AnchorPoint, symObjAddr: 0x1F78, symBinAddr: 0x45620, symSize: 0x0 }
  - { offset: 0x71B78, size: 0x8, addend: 0x0, symName: _LookinAttr_AutoLayout_Hugging_Hor, symObjAddr: 0x1F80, symBinAddr: 0x45628, symSize: 0x0 }
  - { offset: 0x71B8E, size: 0x8, addend: 0x0, symName: _LookinAttr_AutoLayout_Hugging_Ver, symObjAddr: 0x1F88, symBinAddr: 0x45630, symSize: 0x0 }
  - { offset: 0x71BA4, size: 0x8, addend: 0x0, symName: _LookinAttr_AutoLayout_Resistance_Hor, symObjAddr: 0x1F90, symBinAddr: 0x45638, symSize: 0x0 }
  - { offset: 0x71BBA, size: 0x8, addend: 0x0, symName: _LookinAttr_AutoLayout_Resistance_Ver, symObjAddr: 0x1F98, symBinAddr: 0x45640, symSize: 0x0 }
  - { offset: 0x71BD0, size: 0x8, addend: 0x0, symName: _LookinAttr_AutoLayout_Constraints_Constraints, symObjAddr: 0x1FA0, symBinAddr: 0x45648, symSize: 0x0 }
  - { offset: 0x71BE6, size: 0x8, addend: 0x0, symName: _LookinAttr_AutoLayout_IntrinsicSize_Size, symObjAddr: 0x1FA8, symBinAddr: 0x45650, symSize: 0x0 }
  - { offset: 0x71BFC, size: 0x8, addend: 0x0, symName: _LookinAttr_ViewLayer_Visibility_Hidden, symObjAddr: 0x1FB0, symBinAddr: 0x45658, symSize: 0x0 }
  - { offset: 0x71C12, size: 0x8, addend: 0x0, symName: _LookinAttr_ViewLayer_Visibility_Opacity, symObjAddr: 0x1FB8, symBinAddr: 0x45660, symSize: 0x0 }
  - { offset: 0x71C28, size: 0x8, addend: 0x0, symName: _LookinAttr_ViewLayer_InterationAndMasks_Interaction, symObjAddr: 0x1FC0, symBinAddr: 0x45668, symSize: 0x0 }
  - { offset: 0x71C3E, size: 0x8, addend: 0x0, symName: _LookinAttr_ViewLayer_InterationAndMasks_MasksToBounds, symObjAddr: 0x1FC8, symBinAddr: 0x45670, symSize: 0x0 }
  - { offset: 0x71C54, size: 0x8, addend: 0x0, symName: _LookinAttr_ViewLayer_Corner_Radius, symObjAddr: 0x1FD0, symBinAddr: 0x45678, symSize: 0x0 }
  - { offset: 0x71C6A, size: 0x8, addend: 0x0, symName: _LookinAttr_ViewLayer_BgColor_BgColor, symObjAddr: 0x1FD8, symBinAddr: 0x45680, symSize: 0x0 }
  - { offset: 0x71C80, size: 0x8, addend: 0x0, symName: _LookinAttr_ViewLayer_Border_Color, symObjAddr: 0x1FE0, symBinAddr: 0x45688, symSize: 0x0 }
  - { offset: 0x71C96, size: 0x8, addend: 0x0, symName: _LookinAttr_ViewLayer_Border_Width, symObjAddr: 0x1FE8, symBinAddr: 0x45690, symSize: 0x0 }
  - { offset: 0x71CAC, size: 0x8, addend: 0x0, symName: _LookinAttr_ViewLayer_Shadow_Color, symObjAddr: 0x1FF0, symBinAddr: 0x45698, symSize: 0x0 }
  - { offset: 0x71CC2, size: 0x8, addend: 0x0, symName: _LookinAttr_ViewLayer_Shadow_Opacity, symObjAddr: 0x1FF8, symBinAddr: 0x456A0, symSize: 0x0 }
  - { offset: 0x71CD8, size: 0x8, addend: 0x0, symName: _LookinAttr_ViewLayer_Shadow_Radius, symObjAddr: 0x2000, symBinAddr: 0x456A8, symSize: 0x0 }
  - { offset: 0x71CEE, size: 0x8, addend: 0x0, symName: _LookinAttr_ViewLayer_Shadow_OffsetW, symObjAddr: 0x2008, symBinAddr: 0x456B0, symSize: 0x0 }
  - { offset: 0x71D04, size: 0x8, addend: 0x0, symName: _LookinAttr_ViewLayer_Shadow_OffsetH, symObjAddr: 0x2010, symBinAddr: 0x456B8, symSize: 0x0 }
  - { offset: 0x71D1A, size: 0x8, addend: 0x0, symName: _LookinAttr_ViewLayer_ContentMode_Mode, symObjAddr: 0x2018, symBinAddr: 0x456C0, symSize: 0x0 }
  - { offset: 0x71D30, size: 0x8, addend: 0x0, symName: _LookinAttr_ViewLayer_TintColor_Color, symObjAddr: 0x2020, symBinAddr: 0x456C8, symSize: 0x0 }
  - { offset: 0x71D46, size: 0x8, addend: 0x0, symName: _LookinAttr_ViewLayer_TintColor_Mode, symObjAddr: 0x2028, symBinAddr: 0x456D0, symSize: 0x0 }
  - { offset: 0x71D5C, size: 0x8, addend: 0x0, symName: _LookinAttr_ViewLayer_Tag_Tag, symObjAddr: 0x2030, symBinAddr: 0x456D8, symSize: 0x0 }
  - { offset: 0x71D72, size: 0x8, addend: 0x0, symName: _LookinAttr_UIImageView_Name_Name, symObjAddr: 0x2038, symBinAddr: 0x456E0, symSize: 0x0 }
  - { offset: 0x71D88, size: 0x8, addend: 0x0, symName: _LookinAttr_UIImageView_Open_Open, symObjAddr: 0x2040, symBinAddr: 0x456E8, symSize: 0x0 }
  - { offset: 0x71D9E, size: 0x8, addend: 0x0, symName: _LookinAttr_UILabel_Text_Text, symObjAddr: 0x2048, symBinAddr: 0x456F0, symSize: 0x0 }
  - { offset: 0x71DB4, size: 0x8, addend: 0x0, symName: _LookinAttr_UILabel_Font_Name, symObjAddr: 0x2050, symBinAddr: 0x456F8, symSize: 0x0 }
  - { offset: 0x71DCA, size: 0x8, addend: 0x0, symName: _LookinAttr_UILabel_Font_Size, symObjAddr: 0x2058, symBinAddr: 0x45700, symSize: 0x0 }
  - { offset: 0x71DE0, size: 0x8, addend: 0x0, symName: _LookinAttr_UILabel_NumberOfLines_NumberOfLines, symObjAddr: 0x2060, symBinAddr: 0x45708, symSize: 0x0 }
  - { offset: 0x71DF6, size: 0x8, addend: 0x0, symName: _LookinAttr_UILabel_TextColor_Color, symObjAddr: 0x2068, symBinAddr: 0x45710, symSize: 0x0 }
  - { offset: 0x71E0C, size: 0x8, addend: 0x0, symName: _LookinAttr_UILabel_Alignment_Alignment, symObjAddr: 0x2070, symBinAddr: 0x45718, symSize: 0x0 }
  - { offset: 0x71E22, size: 0x8, addend: 0x0, symName: _LookinAttr_UILabel_BreakMode_Mode, symObjAddr: 0x2078, symBinAddr: 0x45720, symSize: 0x0 }
  - { offset: 0x71E38, size: 0x8, addend: 0x0, symName: _LookinAttr_UILabel_CanAdjustFont_CanAdjustFont, symObjAddr: 0x2080, symBinAddr: 0x45728, symSize: 0x0 }
  - { offset: 0x71E4E, size: 0x8, addend: 0x0, symName: _LookinAttr_UIControl_EnabledSelected_Enabled, symObjAddr: 0x2088, symBinAddr: 0x45730, symSize: 0x0 }
  - { offset: 0x71E64, size: 0x8, addend: 0x0, symName: _LookinAttr_UIControl_EnabledSelected_Selected, symObjAddr: 0x2090, symBinAddr: 0x45738, symSize: 0x0 }
  - { offset: 0x71E7A, size: 0x8, addend: 0x0, symName: _LookinAttr_UIControl_VerAlignment_Alignment, symObjAddr: 0x2098, symBinAddr: 0x45740, symSize: 0x0 }
  - { offset: 0x71E90, size: 0x8, addend: 0x0, symName: _LookinAttr_UIControl_HorAlignment_Alignment, symObjAddr: 0x20A0, symBinAddr: 0x45748, symSize: 0x0 }
  - { offset: 0x71EA6, size: 0x8, addend: 0x0, symName: _LookinAttr_UIControl_QMUIOutsideEdge_Edge, symObjAddr: 0x20A8, symBinAddr: 0x45750, symSize: 0x0 }
  - { offset: 0x71EBC, size: 0x8, addend: 0x0, symName: _LookinAttr_UIButton_ContentInsets_Insets, symObjAddr: 0x20B0, symBinAddr: 0x45758, symSize: 0x0 }
  - { offset: 0x71ED2, size: 0x8, addend: 0x0, symName: _LookinAttr_UIButton_TitleInsets_Insets, symObjAddr: 0x20B8, symBinAddr: 0x45760, symSize: 0x0 }
  - { offset: 0x71EE8, size: 0x8, addend: 0x0, symName: _LookinAttr_UIButton_ImageInsets_Insets, symObjAddr: 0x20C0, symBinAddr: 0x45768, symSize: 0x0 }
  - { offset: 0x71EFE, size: 0x8, addend: 0x0, symName: _LookinAttr_UIScrollView_Offset_Offset, symObjAddr: 0x20C8, symBinAddr: 0x45770, symSize: 0x0 }
  - { offset: 0x71F14, size: 0x8, addend: 0x0, symName: _LookinAttr_UIScrollView_ContentSize_Size, symObjAddr: 0x20D0, symBinAddr: 0x45778, symSize: 0x0 }
  - { offset: 0x71F2A, size: 0x8, addend: 0x0, symName: _LookinAttr_UIScrollView_ContentInset_Inset, symObjAddr: 0x20D8, symBinAddr: 0x45780, symSize: 0x0 }
  - { offset: 0x71F40, size: 0x8, addend: 0x0, symName: _LookinAttr_UIScrollView_AdjustedInset_Inset, symObjAddr: 0x20E0, symBinAddr: 0x45788, symSize: 0x0 }
  - { offset: 0x71F56, size: 0x8, addend: 0x0, symName: _LookinAttr_UIScrollView_Behavior_Behavior, symObjAddr: 0x20E8, symBinAddr: 0x45790, symSize: 0x0 }
  - { offset: 0x71F6C, size: 0x8, addend: 0x0, symName: _LookinAttr_UIScrollView_IndicatorInset_Inset, symObjAddr: 0x20F0, symBinAddr: 0x45798, symSize: 0x0 }
  - { offset: 0x71F82, size: 0x8, addend: 0x0, symName: _LookinAttr_UIScrollView_ScrollPaging_ScrollEnabled, symObjAddr: 0x20F8, symBinAddr: 0x457A0, symSize: 0x0 }
  - { offset: 0x71F98, size: 0x8, addend: 0x0, symName: _LookinAttr_UIScrollView_ScrollPaging_PagingEnabled, symObjAddr: 0x2100, symBinAddr: 0x457A8, symSize: 0x0 }
  - { offset: 0x71FAE, size: 0x8, addend: 0x0, symName: _LookinAttr_UIScrollView_Bounce_Ver, symObjAddr: 0x2108, symBinAddr: 0x457B0, symSize: 0x0 }
  - { offset: 0x71FC4, size: 0x8, addend: 0x0, symName: _LookinAttr_UIScrollView_Bounce_Hor, symObjAddr: 0x2110, symBinAddr: 0x457B8, symSize: 0x0 }
  - { offset: 0x71FDA, size: 0x8, addend: 0x0, symName: _LookinAttr_UIScrollView_ShowsIndicator_Hor, symObjAddr: 0x2118, symBinAddr: 0x457C0, symSize: 0x0 }
  - { offset: 0x71FF0, size: 0x8, addend: 0x0, symName: _LookinAttr_UIScrollView_ShowsIndicator_Ver, symObjAddr: 0x2120, symBinAddr: 0x457C8, symSize: 0x0 }
  - { offset: 0x72006, size: 0x8, addend: 0x0, symName: _LookinAttr_UIScrollView_ContentTouches_Delay, symObjAddr: 0x2128, symBinAddr: 0x457D0, symSize: 0x0 }
  - { offset: 0x7201C, size: 0x8, addend: 0x0, symName: _LookinAttr_UIScrollView_ContentTouches_CanCancel, symObjAddr: 0x2130, symBinAddr: 0x457D8, symSize: 0x0 }
  - { offset: 0x72032, size: 0x8, addend: 0x0, symName: _LookinAttr_UIScrollView_Zoom_MinScale, symObjAddr: 0x2138, symBinAddr: 0x457E0, symSize: 0x0 }
  - { offset: 0x72048, size: 0x8, addend: 0x0, symName: _LookinAttr_UIScrollView_Zoom_MaxScale, symObjAddr: 0x2140, symBinAddr: 0x457E8, symSize: 0x0 }
  - { offset: 0x7205E, size: 0x8, addend: 0x0, symName: _LookinAttr_UIScrollView_Zoom_Scale, symObjAddr: 0x2148, symBinAddr: 0x457F0, symSize: 0x0 }
  - { offset: 0x72074, size: 0x8, addend: 0x0, symName: _LookinAttr_UIScrollView_Zoom_Bounce, symObjAddr: 0x2150, symBinAddr: 0x457F8, symSize: 0x0 }
  - { offset: 0x7208A, size: 0x8, addend: 0x0, symName: _LookinAttr_UIScrollView_QMUIInitialInset_Inset, symObjAddr: 0x2158, symBinAddr: 0x45800, symSize: 0x0 }
  - { offset: 0x720A0, size: 0x8, addend: 0x0, symName: _LookinAttr_UITableView_Style_Style, symObjAddr: 0x2160, symBinAddr: 0x45808, symSize: 0x0 }
  - { offset: 0x720B6, size: 0x8, addend: 0x0, symName: _LookinAttr_UITableView_SectionsNumber_Number, symObjAddr: 0x2168, symBinAddr: 0x45810, symSize: 0x0 }
  - { offset: 0x720CC, size: 0x8, addend: 0x0, symName: _LookinAttr_UITableView_RowsNumber_Number, symObjAddr: 0x2170, symBinAddr: 0x45818, symSize: 0x0 }
  - { offset: 0x720E2, size: 0x8, addend: 0x0, symName: _LookinAttr_UITableView_SeparatorInset_Inset, symObjAddr: 0x2178, symBinAddr: 0x45820, symSize: 0x0 }
  - { offset: 0x720F8, size: 0x8, addend: 0x0, symName: _LookinAttr_UITableView_SeparatorColor_Color, symObjAddr: 0x2180, symBinAddr: 0x45828, symSize: 0x0 }
  - { offset: 0x7210E, size: 0x8, addend: 0x0, symName: _LookinAttr_UITableView_SeparatorStyle_Style, symObjAddr: 0x2188, symBinAddr: 0x45830, symSize: 0x0 }
  - { offset: 0x72124, size: 0x8, addend: 0x0, symName: _LookinAttr_UITextView_Font_Name, symObjAddr: 0x2190, symBinAddr: 0x45838, symSize: 0x0 }
  - { offset: 0x7213A, size: 0x8, addend: 0x0, symName: _LookinAttr_UITextView_Font_Size, symObjAddr: 0x2198, symBinAddr: 0x45840, symSize: 0x0 }
  - { offset: 0x72150, size: 0x8, addend: 0x0, symName: _LookinAttr_UITextView_Basic_Editable, symObjAddr: 0x21A0, symBinAddr: 0x45848, symSize: 0x0 }
  - { offset: 0x72166, size: 0x8, addend: 0x0, symName: _LookinAttr_UITextView_Basic_Selectable, symObjAddr: 0x21A8, symBinAddr: 0x45850, symSize: 0x0 }
  - { offset: 0x7217C, size: 0x8, addend: 0x0, symName: _LookinAttr_UITextView_Text_Text, symObjAddr: 0x21B0, symBinAddr: 0x45858, symSize: 0x0 }
  - { offset: 0x72192, size: 0x8, addend: 0x0, symName: _LookinAttr_UITextView_TextColor_Color, symObjAddr: 0x21B8, symBinAddr: 0x45860, symSize: 0x0 }
  - { offset: 0x721A8, size: 0x8, addend: 0x0, symName: _LookinAttr_UITextView_Alignment_Alignment, symObjAddr: 0x21C0, symBinAddr: 0x45868, symSize: 0x0 }
  - { offset: 0x721BE, size: 0x8, addend: 0x0, symName: _LookinAttr_UITextView_ContainerInset_Inset, symObjAddr: 0x21C8, symBinAddr: 0x45870, symSize: 0x0 }
  - { offset: 0x721D4, size: 0x8, addend: 0x0, symName: _LookinAttr_UITextField_Text_Text, symObjAddr: 0x21D0, symBinAddr: 0x45878, symSize: 0x0 }
  - { offset: 0x721EA, size: 0x8, addend: 0x0, symName: _LookinAttr_UITextField_Placeholder_Placeholder, symObjAddr: 0x21D8, symBinAddr: 0x45880, symSize: 0x0 }
  - { offset: 0x72200, size: 0x8, addend: 0x0, symName: _LookinAttr_UITextField_Font_Name, symObjAddr: 0x21E0, symBinAddr: 0x45888, symSize: 0x0 }
  - { offset: 0x72216, size: 0x8, addend: 0x0, symName: _LookinAttr_UITextField_Font_Size, symObjAddr: 0x21E8, symBinAddr: 0x45890, symSize: 0x0 }
  - { offset: 0x7222C, size: 0x8, addend: 0x0, symName: _LookinAttr_UITextField_TextColor_Color, symObjAddr: 0x21F0, symBinAddr: 0x45898, symSize: 0x0 }
  - { offset: 0x72242, size: 0x8, addend: 0x0, symName: _LookinAttr_UITextField_Alignment_Alignment, symObjAddr: 0x21F8, symBinAddr: 0x458A0, symSize: 0x0 }
  - { offset: 0x72258, size: 0x8, addend: 0x0, symName: _LookinAttr_UITextField_Clears_ClearsOnBeginEditing, symObjAddr: 0x2200, symBinAddr: 0x458A8, symSize: 0x0 }
  - { offset: 0x7226E, size: 0x8, addend: 0x0, symName: _LookinAttr_UITextField_Clears_ClearsOnInsertion, symObjAddr: 0x2208, symBinAddr: 0x458B0, symSize: 0x0 }
  - { offset: 0x72284, size: 0x8, addend: 0x0, symName: _LookinAttr_UITextField_CanAdjustFont_CanAdjustFont, symObjAddr: 0x2210, symBinAddr: 0x458B8, symSize: 0x0 }
  - { offset: 0x7229A, size: 0x8, addend: 0x0, symName: _LookinAttr_UITextField_CanAdjustFont_MinSize, symObjAddr: 0x2218, symBinAddr: 0x458C0, symSize: 0x0 }
  - { offset: 0x722B0, size: 0x8, addend: 0x0, symName: _LookinAttr_UITextField_ClearButtonMode_Mode, symObjAddr: 0x2220, symBinAddr: 0x458C8, symSize: 0x0 }
  - { offset: 0x722C6, size: 0x8, addend: 0x0, symName: _LookinAttr_UIVisualEffectView_Style_Style, symObjAddr: 0x2228, symBinAddr: 0x458D0, symSize: 0x0 }
  - { offset: 0x722DC, size: 0x8, addend: 0x0, symName: _LookinAttr_UIVisualEffectView_QMUIForegroundColor_Color, symObjAddr: 0x2230, symBinAddr: 0x458D8, symSize: 0x0 }
  - { offset: 0x722F2, size: 0x8, addend: 0x0, symName: _LookinAttr_UIStackView_Axis_Axis, symObjAddr: 0x2238, symBinAddr: 0x458E0, symSize: 0x0 }
  - { offset: 0x72308, size: 0x8, addend: 0x0, symName: _LookinAttr_UIStackView_Distribution_Distribution, symObjAddr: 0x2240, symBinAddr: 0x458E8, symSize: 0x0 }
  - { offset: 0x7231E, size: 0x8, addend: 0x0, symName: _LookinAttr_UIStackView_Alignment_Alignment, symObjAddr: 0x2248, symBinAddr: 0x458F0, symSize: 0x0 }
  - { offset: 0x72334, size: 0x8, addend: 0x0, symName: _LookinAttr_UIStackView_Spacing_Spacing, symObjAddr: 0x2250, symBinAddr: 0x458F8, symSize: 0x0 }
  - { offset: 0x72373, size: 0x8, addend: 0x0, symName: '+[LookinAutoLayoutConstraint instanceFromNSConstraint:isEffective:firstItemType:secondItemType:]', symObjAddr: 0x0, symBinAddr: 0x1C134, symSize: 0x1E4 }
  - { offset: 0x725C9, size: 0x8, addend: 0x0, symName: '+[LookinAutoLayoutConstraint instanceFromNSConstraint:isEffective:firstItemType:secondItemType:]', symObjAddr: 0x0, symBinAddr: 0x1C134, symSize: 0x1E4 }
  - { offset: 0x7264C, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint setFirstAttribute:]', symObjAddr: 0x1E4, symBinAddr: 0x1C318, symSize: 0x8 }
  - { offset: 0x7268B, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint setSecondAttribute:]', symObjAddr: 0x1EC, symBinAddr: 0x1C320, symSize: 0x8 }
  - { offset: 0x726CA, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint _assertUnknownAttribute:]', symObjAddr: 0x1F4, symBinAddr: 0x1C328, symSize: 0x4 }
  - { offset: 0x72705, size: 0x8, addend: 0x0, symName: '+[LookinAutoLayoutConstraint descriptionWithItemObject:type:detailed:]', symObjAddr: 0x1F8, symBinAddr: 0x1C32C, symSize: 0x148 }
  - { offset: 0x72768, size: 0x8, addend: 0x0, symName: '+[LookinAutoLayoutConstraint descriptionWithAttribute:]', symObjAddr: 0x340, symBinAddr: 0x1C474, symSize: 0x94 }
  - { offset: 0x727AB, size: 0x8, addend: 0x0, symName: '+[LookinAutoLayoutConstraint symbolWithRelation:]', symObjAddr: 0x3D4, symBinAddr: 0x1C508, symSize: 0x28 }
  - { offset: 0x727EC, size: 0x8, addend: 0x0, symName: '+[LookinAutoLayoutConstraint descriptionWithRelation:]', symObjAddr: 0x3FC, symBinAddr: 0x1C530, symSize: 0x28 }
  - { offset: 0x7282D, size: 0x8, addend: 0x0, symName: '+[LookinAutoLayoutConstraint supportsSecureCoding]', symObjAddr: 0x424, symBinAddr: 0x1C558, symSize: 0x8 }
  - { offset: 0x72860, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint encodeWithCoder:]', symObjAddr: 0x42C, symBinAddr: 0x1C560, symSize: 0x1EC }
  - { offset: 0x728A3, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint initWithCoder:]', symObjAddr: 0x618, symBinAddr: 0x1C74C, symSize: 0x220 }
  - { offset: 0x728EA, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint effective]', symObjAddr: 0x838, symBinAddr: 0x1C96C, symSize: 0x8 }
  - { offset: 0x72921, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint setEffective:]', symObjAddr: 0x840, symBinAddr: 0x1C974, symSize: 0x8 }
  - { offset: 0x7295C, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint active]', symObjAddr: 0x848, symBinAddr: 0x1C97C, symSize: 0x8 }
  - { offset: 0x72993, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint setActive:]', symObjAddr: 0x850, symBinAddr: 0x1C984, symSize: 0x8 }
  - { offset: 0x729CE, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint shouldBeArchived]', symObjAddr: 0x858, symBinAddr: 0x1C98C, symSize: 0x8 }
  - { offset: 0x72A05, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint setShouldBeArchived:]', symObjAddr: 0x860, symBinAddr: 0x1C994, symSize: 0x8 }
  - { offset: 0x72A40, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint firstItem]', symObjAddr: 0x868, symBinAddr: 0x1C99C, symSize: 0x8 }
  - { offset: 0x72A77, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint setFirstItem:]', symObjAddr: 0x870, symBinAddr: 0x1C9A4, symSize: 0xC }
  - { offset: 0x72AB8, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint firstItemType]', symObjAddr: 0x87C, symBinAddr: 0x1C9B0, symSize: 0x8 }
  - { offset: 0x72AEF, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint setFirstItemType:]', symObjAddr: 0x884, symBinAddr: 0x1C9B8, symSize: 0x8 }
  - { offset: 0x72B2C, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint firstAttribute]', symObjAddr: 0x88C, symBinAddr: 0x1C9C0, symSize: 0x8 }
  - { offset: 0x72B63, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint relation]', symObjAddr: 0x894, symBinAddr: 0x1C9C8, symSize: 0x8 }
  - { offset: 0x72B9A, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint setRelation:]', symObjAddr: 0x89C, symBinAddr: 0x1C9D0, symSize: 0x8 }
  - { offset: 0x72BD7, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint secondItem]', symObjAddr: 0x8A4, symBinAddr: 0x1C9D8, symSize: 0x8 }
  - { offset: 0x72C0E, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint setSecondItem:]', symObjAddr: 0x8AC, symBinAddr: 0x1C9E0, symSize: 0xC }
  - { offset: 0x72C4F, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint secondItemType]', symObjAddr: 0x8B8, symBinAddr: 0x1C9EC, symSize: 0x8 }
  - { offset: 0x72C86, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint setSecondItemType:]', symObjAddr: 0x8C0, symBinAddr: 0x1C9F4, symSize: 0x8 }
  - { offset: 0x72CC3, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint secondAttribute]', symObjAddr: 0x8C8, symBinAddr: 0x1C9FC, symSize: 0x8 }
  - { offset: 0x72CFA, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint multiplier]', symObjAddr: 0x8D0, symBinAddr: 0x1CA04, symSize: 0x8 }
  - { offset: 0x72D2F, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint setMultiplier:]', symObjAddr: 0x8D8, symBinAddr: 0x1CA0C, symSize: 0x8 }
  - { offset: 0x72D6D, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint constant]', symObjAddr: 0x8E0, symBinAddr: 0x1CA14, symSize: 0x8 }
  - { offset: 0x72DA2, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint setConstant:]', symObjAddr: 0x8E8, symBinAddr: 0x1CA1C, symSize: 0x8 }
  - { offset: 0x72DE0, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint priority]', symObjAddr: 0x8F0, symBinAddr: 0x1CA24, symSize: 0x8 }
  - { offset: 0x72E15, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint setPriority:]', symObjAddr: 0x8F8, symBinAddr: 0x1CA2C, symSize: 0x8 }
  - { offset: 0x72E53, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint identifier]', symObjAddr: 0x900, symBinAddr: 0x1CA34, symSize: 0x8 }
  - { offset: 0x72E8A, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint setIdentifier:]', symObjAddr: 0x908, symBinAddr: 0x1CA3C, symSize: 0x8 }
  - { offset: 0x72EC9, size: 0x8, addend: 0x0, symName: '-[LookinAutoLayoutConstraint .cxx_destruct]', symObjAddr: 0x910, symBinAddr: 0x1CA44, symSize: 0x3C }
  - { offset: 0x72FA6, size: 0x8, addend: 0x0, symName: '-[LookinConnectionAttachment init]', symObjAddr: 0x0, symBinAddr: 0x1CA80, symSize: 0x34 }
  - { offset: 0x73077, size: 0x8, addend: 0x0, symName: '-[LookinConnectionAttachment init]', symObjAddr: 0x0, symBinAddr: 0x1CA80, symSize: 0x34 }
  - { offset: 0x730AE, size: 0x8, addend: 0x0, symName: '-[LookinConnectionAttachment encodeWithCoder:]', symObjAddr: 0x34, symBinAddr: 0x1CAB4, symSize: 0xA8 }
  - { offset: 0x730F1, size: 0x8, addend: 0x0, symName: '-[LookinConnectionAttachment initWithCoder:]', symObjAddr: 0xDC, symBinAddr: 0x1CB5C, symSize: 0xDC }
  - { offset: 0x73138, size: 0x8, addend: 0x0, symName: '+[LookinConnectionAttachment supportsSecureCoding]', symObjAddr: 0x1B8, symBinAddr: 0x1CC38, symSize: 0x8 }
  - { offset: 0x7316B, size: 0x8, addend: 0x0, symName: '-[LookinConnectionAttachment dataType]', symObjAddr: 0x1C0, symBinAddr: 0x1CC40, symSize: 0x8 }
  - { offset: 0x731A2, size: 0x8, addend: 0x0, symName: '-[LookinConnectionAttachment setDataType:]', symObjAddr: 0x1C8, symBinAddr: 0x1CC48, symSize: 0x8 }
  - { offset: 0x731DF, size: 0x8, addend: 0x0, symName: '-[LookinConnectionAttachment data]', symObjAddr: 0x1D0, symBinAddr: 0x1CC50, symSize: 0x8 }
  - { offset: 0x73216, size: 0x8, addend: 0x0, symName: '-[LookinConnectionAttachment setData:]', symObjAddr: 0x1D8, symBinAddr: 0x1CC58, symSize: 0xC }
  - { offset: 0x73257, size: 0x8, addend: 0x0, symName: '-[LookinConnectionAttachment .cxx_destruct]', symObjAddr: 0x1E4, symBinAddr: 0x1CC64, symSize: 0xC }
  - { offset: 0x73322, size: 0x8, addend: 0x0, symName: '-[LookinConnectionResponseAttachment encodeWithCoder:]', symObjAddr: 0x0, symBinAddr: 0x1CC70, symSize: 0x150 }
  - { offset: 0x73485, size: 0x8, addend: 0x0, symName: '-[LookinConnectionResponseAttachment encodeWithCoder:]', symObjAddr: 0x0, symBinAddr: 0x1CC70, symSize: 0x150 }
  - { offset: 0x734C8, size: 0x8, addend: 0x0, symName: '-[LookinConnectionResponseAttachment init]', symObjAddr: 0x150, symBinAddr: 0x1CDC0, symSize: 0x60 }
  - { offset: 0x734FF, size: 0x8, addend: 0x0, symName: '-[LookinConnectionResponseAttachment initWithCoder:]', symObjAddr: 0x1B0, symBinAddr: 0x1CE20, symSize: 0x13C }
  - { offset: 0x73546, size: 0x8, addend: 0x0, symName: '+[LookinConnectionResponseAttachment supportsSecureCoding]', symObjAddr: 0x2EC, symBinAddr: 0x1CF5C, symSize: 0x8 }
  - { offset: 0x73579, size: 0x8, addend: 0x0, symName: '+[LookinConnectionResponseAttachment attachmentWithError:]', symObjAddr: 0x2F4, symBinAddr: 0x1CF64, symSize: 0x4C }
  - { offset: 0x735CC, size: 0x8, addend: 0x0, symName: '-[LookinConnectionResponseAttachment lookinServerVersion]', symObjAddr: 0x340, symBinAddr: 0x1CFB0, symSize: 0x10 }
  - { offset: 0x73603, size: 0x8, addend: 0x0, symName: '-[LookinConnectionResponseAttachment setLookinServerVersion:]', symObjAddr: 0x350, symBinAddr: 0x1CFC0, symSize: 0x10 }
  - { offset: 0x73640, size: 0x8, addend: 0x0, symName: '-[LookinConnectionResponseAttachment error]', symObjAddr: 0x360, symBinAddr: 0x1CFD0, symSize: 0x10 }
  - { offset: 0x73677, size: 0x8, addend: 0x0, symName: '-[LookinConnectionResponseAttachment setError:]', symObjAddr: 0x370, symBinAddr: 0x1CFE0, symSize: 0x14 }
  - { offset: 0x736B8, size: 0x8, addend: 0x0, symName: '-[LookinConnectionResponseAttachment appIsInBackground]', symObjAddr: 0x384, symBinAddr: 0x1CFF4, symSize: 0x10 }
  - { offset: 0x736EF, size: 0x8, addend: 0x0, symName: '-[LookinConnectionResponseAttachment setAppIsInBackground:]', symObjAddr: 0x394, symBinAddr: 0x1D004, symSize: 0x10 }
  - { offset: 0x7372A, size: 0x8, addend: 0x0, symName: '-[LookinConnectionResponseAttachment dataTotalCount]', symObjAddr: 0x3A4, symBinAddr: 0x1D014, symSize: 0x10 }
  - { offset: 0x73761, size: 0x8, addend: 0x0, symName: '-[LookinConnectionResponseAttachment setDataTotalCount:]', symObjAddr: 0x3B4, symBinAddr: 0x1D024, symSize: 0x10 }
  - { offset: 0x7379E, size: 0x8, addend: 0x0, symName: '-[LookinConnectionResponseAttachment currentDataCount]', symObjAddr: 0x3C4, symBinAddr: 0x1D034, symSize: 0x10 }
  - { offset: 0x737D5, size: 0x8, addend: 0x0, symName: '-[LookinConnectionResponseAttachment setCurrentDataCount:]', symObjAddr: 0x3D4, symBinAddr: 0x1D044, symSize: 0x10 }
  - { offset: 0x73812, size: 0x8, addend: 0x0, symName: '-[LookinConnectionResponseAttachment .cxx_destruct]', symObjAddr: 0x3E4, symBinAddr: 0x1D054, symSize: 0x14 }
  - { offset: 0x738C1, size: 0x8, addend: 0x0, symName: '-[LookinCustomAttrModification encodeWithCoder:]', symObjAddr: 0x0, symBinAddr: 0x1D068, symSize: 0xB0 }
  - { offset: 0x73A3A, size: 0x8, addend: 0x0, symName: '-[LookinCustomAttrModification encodeWithCoder:]', symObjAddr: 0x0, symBinAddr: 0x1D068, symSize: 0xB0 }
  - { offset: 0x73A7D, size: 0x8, addend: 0x0, symName: '-[LookinCustomAttrModification initWithCoder:]', symObjAddr: 0xB0, symBinAddr: 0x1D118, symSize: 0xE4 }
  - { offset: 0x73AC4, size: 0x8, addend: 0x0, symName: '+[LookinCustomAttrModification supportsSecureCoding]', symObjAddr: 0x194, symBinAddr: 0x1D1FC, symSize: 0x8 }
  - { offset: 0x73AF7, size: 0x8, addend: 0x0, symName: '-[LookinCustomAttrModification attrType]', symObjAddr: 0x19C, symBinAddr: 0x1D204, symSize: 0x8 }
  - { offset: 0x73B2E, size: 0x8, addend: 0x0, symName: '-[LookinCustomAttrModification setAttrType:]', symObjAddr: 0x1A4, symBinAddr: 0x1D20C, symSize: 0x8 }
  - { offset: 0x73B6B, size: 0x8, addend: 0x0, symName: '-[LookinCustomAttrModification customSetterID]', symObjAddr: 0x1AC, symBinAddr: 0x1D214, symSize: 0x8 }
  - { offset: 0x73BA2, size: 0x8, addend: 0x0, symName: '-[LookinCustomAttrModification setCustomSetterID:]', symObjAddr: 0x1B4, symBinAddr: 0x1D21C, symSize: 0x8 }
  - { offset: 0x73BE1, size: 0x8, addend: 0x0, symName: '-[LookinCustomAttrModification value]', symObjAddr: 0x1BC, symBinAddr: 0x1D224, symSize: 0x8 }
  - { offset: 0x73C18, size: 0x8, addend: 0x0, symName: '-[LookinCustomAttrModification setValue:]', symObjAddr: 0x1C4, symBinAddr: 0x1D22C, symSize: 0xC }
  - { offset: 0x73C59, size: 0x8, addend: 0x0, symName: '-[LookinCustomAttrModification .cxx_destruct]', symObjAddr: 0x1D0, symBinAddr: 0x1D238, symSize: 0x30 }
  - { offset: 0x73D01, size: 0x8, addend: 0x0, symName: '-[LookinCustomDisplayItemInfo copyWithZone:]', symObjAddr: 0x0, symBinAddr: 0x1D268, symSize: 0x14C }
  - { offset: 0x73D8F, size: 0x8, addend: 0x0, symName: '-[LookinCustomDisplayItemInfo copyWithZone:]', symObjAddr: 0x0, symBinAddr: 0x1D268, symSize: 0x14C }
  - { offset: 0x73E05, size: 0x8, addend: 0x0, symName: '-[LookinCustomDisplayItemInfo encodeWithCoder:]', symObjAddr: 0x14C, symBinAddr: 0x1D3B4, symSize: 0xF4 }
  - { offset: 0x73E48, size: 0x8, addend: 0x0, symName: '-[LookinCustomDisplayItemInfo initWithCoder:]', symObjAddr: 0x240, symBinAddr: 0x1D4A8, symSize: 0x128 }
  - { offset: 0x73E8F, size: 0x8, addend: 0x0, symName: '+[LookinCustomDisplayItemInfo supportsSecureCoding]', symObjAddr: 0x368, symBinAddr: 0x1D5D0, symSize: 0x8 }
  - { offset: 0x73EC2, size: 0x8, addend: 0x0, symName: '-[LookinCustomDisplayItemInfo frameInWindow]', symObjAddr: 0x370, symBinAddr: 0x1D5D8, symSize: 0x8 }
  - { offset: 0x73EF9, size: 0x8, addend: 0x0, symName: '-[LookinCustomDisplayItemInfo setFrameInWindow:]', symObjAddr: 0x378, symBinAddr: 0x1D5E0, symSize: 0xC }
  - { offset: 0x73F3A, size: 0x8, addend: 0x0, symName: '-[LookinCustomDisplayItemInfo title]', symObjAddr: 0x384, symBinAddr: 0x1D5EC, symSize: 0x8 }
  - { offset: 0x73F71, size: 0x8, addend: 0x0, symName: '-[LookinCustomDisplayItemInfo setTitle:]', symObjAddr: 0x38C, symBinAddr: 0x1D5F4, symSize: 0x8 }
  - { offset: 0x73FB0, size: 0x8, addend: 0x0, symName: '-[LookinCustomDisplayItemInfo subtitle]', symObjAddr: 0x394, symBinAddr: 0x1D5FC, symSize: 0x8 }
  - { offset: 0x73FE7, size: 0x8, addend: 0x0, symName: '-[LookinCustomDisplayItemInfo setSubtitle:]', symObjAddr: 0x39C, symBinAddr: 0x1D604, symSize: 0x8 }
  - { offset: 0x74026, size: 0x8, addend: 0x0, symName: '-[LookinCustomDisplayItemInfo danceuiSource]', symObjAddr: 0x3A4, symBinAddr: 0x1D60C, symSize: 0x8 }
  - { offset: 0x7405D, size: 0x8, addend: 0x0, symName: '-[LookinCustomDisplayItemInfo setDanceuiSource:]', symObjAddr: 0x3AC, symBinAddr: 0x1D614, symSize: 0x8 }
  - { offset: 0x7409C, size: 0x8, addend: 0x0, symName: '-[LookinCustomDisplayItemInfo .cxx_destruct]', symObjAddr: 0x3B4, symBinAddr: 0x1D61C, symSize: 0x48 }
  - { offset: 0x74172, size: 0x8, addend: 0x0, symName: '+[LookinDashboardBlueprint groupIDs]', symObjAddr: 0x0, symBinAddr: 0x1D664, symSize: 0x40 }
  - { offset: 0x74180, size: 0x8, addend: 0x0, symName: '+[LookinDashboardBlueprint groupIDs]', symObjAddr: 0x0, symBinAddr: 0x1D664, symSize: 0x40 }
  - { offset: 0x741AA, size: 0x8, addend: 0x0, symName: _groupIDs.array, symObjAddr: 0x12B68, symBinAddr: 0x5A490, symSize: 0x0 }
  - { offset: 0x741C0, size: 0x8, addend: 0x0, symName: _groupIDs.onceToken, symObjAddr: 0x12B70, symBinAddr: 0x5A498, symSize: 0x0 }
  - { offset: 0x74206, size: 0x8, addend: 0x0, symName: '+[LookinDashboardBlueprint sectionIDsForGroupID:]', symObjAddr: 0x188, symBinAddr: 0x1D7EC, symSize: 0x74 }
  - { offset: 0x74230, size: 0x8, addend: 0x0, symName: '_sectionIDsForGroupID:.dict', symObjAddr: 0x12B78, symBinAddr: 0x5A4A0, symSize: 0x0 }
  - { offset: 0x74246, size: 0x8, addend: 0x0, symName: '_sectionIDsForGroupID:.onceToken', symObjAddr: 0x12B80, symBinAddr: 0x5A4A8, symSize: 0x0 }
  - { offset: 0x74295, size: 0x8, addend: 0x0, symName: '+[LookinDashboardBlueprint attrIDsForSectionID:]', symObjAddr: 0xA18, symBinAddr: 0x1E07C, symSize: 0x74 }
  - { offset: 0x742BF, size: 0x8, addend: 0x0, symName: '_attrIDsForSectionID:.dict', symObjAddr: 0x12B88, symBinAddr: 0x5A4B0, symSize: 0x0 }
  - { offset: 0x742D5, size: 0x8, addend: 0x0, symName: '_attrIDsForSectionID:.onceToken', symObjAddr: 0x12B90, symBinAddr: 0x5A4B8, symSize: 0x0 }
  - { offset: 0x7431F, size: 0x8, addend: 0x0, symName: '+[LookinDashboardBlueprint groupTitleWithGroupID:]', symObjAddr: 0x26F0, symBinAddr: 0x1FD54, symSize: 0x74 }
  - { offset: 0x7434B, size: 0x8, addend: 0x0, symName: '_groupTitleWithGroupID:.onceToken', symObjAddr: 0x12B98, symBinAddr: 0x5A4C0, symSize: 0x0 }
  - { offset: 0x74362, size: 0x8, addend: 0x0, symName: '_groupTitleWithGroupID:.rawInfo', symObjAddr: 0x12BA0, symBinAddr: 0x5A4C8, symSize: 0x0 }
  - { offset: 0x743BF, size: 0x8, addend: 0x0, symName: '+[LookinDashboardBlueprint sectionTitleWithSectionID:]', symObjAddr: 0x2950, symBinAddr: 0x1FFB4, symSize: 0x74 }
  - { offset: 0x743EB, size: 0x8, addend: 0x0, symName: '_sectionTitleWithSectionID:.onceToken', symObjAddr: 0x12BA8, symBinAddr: 0x5A4D0, symSize: 0x0 }
  - { offset: 0x74402, size: 0x8, addend: 0x0, symName: '_sectionTitleWithSectionID:.rawInfo', symObjAddr: 0x12BB0, symBinAddr: 0x5A4D8, symSize: 0x0 }
  - { offset: 0x7444E, size: 0x8, addend: 0x0, symName: '+[LookinDashboardBlueprint _infoForAttrID:]', symObjAddr: 0x300C, symBinAddr: 0x20670, symSize: 0x74 }
  - { offset: 0x7447A, size: 0x8, addend: 0x0, symName: '__infoForAttrID:.dict', symObjAddr: 0x12BB8, symBinAddr: 0x5A4E0, symSize: 0x0 }
  - { offset: 0x74491, size: 0x8, addend: 0x0, symName: '__infoForAttrID:.onceToken', symObjAddr: 0x12BC0, symBinAddr: 0x5A4E8, symSize: 0x0 }
  - { offset: 0x7460A, size: 0x8, addend: 0x0, symName: '___36+[LookinDashboardBlueprint groupIDs]_block_invoke', symObjAddr: 0x40, symBinAddr: 0x1D6A4, symSize: 0x148 }
  - { offset: 0x74631, size: 0x8, addend: 0x0, symName: '___49+[LookinDashboardBlueprint sectionIDsForGroupID:]_block_invoke', symObjAddr: 0x1FC, symBinAddr: 0x1D860, symSize: 0x81C }
  - { offset: 0x74658, size: 0x8, addend: 0x0, symName: '___48+[LookinDashboardBlueprint attrIDsForSectionID:]_block_invoke', symObjAddr: 0xA8C, symBinAddr: 0x1E0F0, symSize: 0x16F4 }
  - { offset: 0x7467F, size: 0x8, addend: 0x0, symName: '+[LookinDashboardBlueprint getHostGroupID:sectionID:fromAttrID:]', symObjAddr: 0x2180, symBinAddr: 0x1F7E4, symSize: 0x188 }
  - { offset: 0x74712, size: 0x8, addend: 0x0, symName: ___Block_byref_object_copy_, symObjAddr: 0x2308, symBinAddr: 0x1F96C, symSize: 0x10 }
  - { offset: 0x74737, size: 0x8, addend: 0x0, symName: ___Block_byref_object_dispose_, symObjAddr: 0x2318, symBinAddr: 0x1F97C, symSize: 0x8 }
  - { offset: 0x74756, size: 0x8, addend: 0x0, symName: '___64+[LookinDashboardBlueprint getHostGroupID:sectionID:fromAttrID:]_block_invoke', symObjAddr: 0x2320, symBinAddr: 0x1F984, symSize: 0xD4 }
  - { offset: 0x747FA, size: 0x8, addend: 0x0, symName: '___64+[LookinDashboardBlueprint getHostGroupID:sectionID:fromAttrID:]_block_invoke_2', symObjAddr: 0x23F4, symBinAddr: 0x1FA58, symSize: 0xEC }
  - { offset: 0x748C0, size: 0x8, addend: 0x0, symName: '___64+[LookinDashboardBlueprint getHostGroupID:sectionID:fromAttrID:]_block_invoke_3', symObjAddr: 0x24E0, symBinAddr: 0x1FB44, symSize: 0x6C }
  - { offset: 0x74993, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48s56r64r, symObjAddr: 0x254C, symBinAddr: 0x1FBB0, symSize: 0x54 }
  - { offset: 0x749BC, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48s56r64r, symObjAddr: 0x25A0, symBinAddr: 0x1FC04, symSize: 0x48 }
  - { offset: 0x749DB, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48r56r, symObjAddr: 0x25E8, symBinAddr: 0x1FC4C, symSize: 0x4C }
  - { offset: 0x74A04, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48r56r, symObjAddr: 0x2634, symBinAddr: 0x1FC98, symSize: 0x40 }
  - { offset: 0x74A23, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40r48r, symObjAddr: 0x2674, symBinAddr: 0x1FCD8, symSize: 0x44 }
  - { offset: 0x74A4C, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40r48r, symObjAddr: 0x26B8, symBinAddr: 0x1FD1C, symSize: 0x38 }
  - { offset: 0x74A6B, size: 0x8, addend: 0x0, symName: '___50+[LookinDashboardBlueprint groupTitleWithGroupID:]_block_invoke', symObjAddr: 0x2764, symBinAddr: 0x1FDC8, symSize: 0x1EC }
  - { offset: 0x74A94, size: 0x8, addend: 0x0, symName: '___54+[LookinDashboardBlueprint sectionTitleWithSectionID:]_block_invoke', symObjAddr: 0x29C4, symBinAddr: 0x20028, symSize: 0x648 }
  - { offset: 0x74ABD, size: 0x8, addend: 0x0, symName: '___43+[LookinDashboardBlueprint _infoForAttrID:]_block_invoke', symObjAddr: 0x3080, symBinAddr: 0x206E4, symSize: 0x3D0C }
  - { offset: 0x74AE6, size: 0x8, addend: 0x0, symName: '+[LookinDashboardBlueprint objectAttrTypeWithAttrID:]', symObjAddr: 0x6D8C, symBinAddr: 0x243F0, symSize: 0x64 }
  - { offset: 0x74B51, size: 0x8, addend: 0x0, symName: '+[LookinDashboardBlueprint classNameWithAttrID:]', symObjAddr: 0x6DF0, symBinAddr: 0x24454, symSize: 0x4C }
  - { offset: 0x74BBC, size: 0x8, addend: 0x0, symName: '+[LookinDashboardBlueprint isUIViewPropertyWithAttrID:]', symObjAddr: 0x6E3C, symBinAddr: 0x244A0, symSize: 0x44 }
  - { offset: 0x74C16, size: 0x8, addend: 0x0, symName: '+[LookinDashboardBlueprint enumListNameWithAttrID:]', symObjAddr: 0x6E80, symBinAddr: 0x244E4, symSize: 0x4C }
  - { offset: 0x74C81, size: 0x8, addend: 0x0, symName: '+[LookinDashboardBlueprint needPatchAfterModificationWithAttrID:]', symObjAddr: 0x6ECC, symBinAddr: 0x24530, symSize: 0x64 }
  - { offset: 0x74CEC, size: 0x8, addend: 0x0, symName: '+[LookinDashboardBlueprint fullTitleWithAttrID:]', symObjAddr: 0x6F30, symBinAddr: 0x24594, symSize: 0x4C }
  - { offset: 0x74D57, size: 0x8, addend: 0x0, symName: '+[LookinDashboardBlueprint briefTitleWithAttrID:]', symObjAddr: 0x6F7C, symBinAddr: 0x245E0, symSize: 0x6C }
  - { offset: 0x74DC2, size: 0x8, addend: 0x0, symName: '+[LookinDashboardBlueprint getterWithAttrID:]', symObjAddr: 0x6FE8, symBinAddr: 0x2464C, symSize: 0x144 }
  - { offset: 0x74E8E, size: 0x8, addend: 0x0, symName: '+[LookinDashboardBlueprint setterWithAttrID:]', symObjAddr: 0x712C, symBinAddr: 0x24790, symSize: 0x134 }
  - { offset: 0x74F2F, size: 0x8, addend: 0x0, symName: '+[LookinDashboardBlueprint hideIfNilWithAttrID:]', symObjAddr: 0x7260, symBinAddr: 0x248C4, symSize: 0x64 }
  - { offset: 0x74F9A, size: 0x8, addend: 0x0, symName: '+[LookinDashboardBlueprint minAvailableOSVersionWithAttrID:]', symObjAddr: 0x72C4, symBinAddr: 0x24928, symSize: 0x64 }
  - { offset: 0x755CD, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem copyWithZone:]', symObjAddr: 0x0, symBinAddr: 0x2498C, symSize: 0x350 }
  - { offset: 0x75CAD, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem copyWithZone:]', symObjAddr: 0x0, symBinAddr: 0x2498C, symSize: 0x350 }
  - { offset: 0x75D04, size: 0x8, addend: 0x0, symName: '___34-[LookinDisplayItem copyWithZone:]_block_invoke', symObjAddr: 0x350, symBinAddr: 0x24CDC, symSize: 0x18 }
  - { offset: 0x75D4B, size: 0x8, addend: 0x0, symName: '___34-[LookinDisplayItem copyWithZone:]_block_invoke_2', symObjAddr: 0x368, symBinAddr: 0x24CF4, symSize: 0x18 }
  - { offset: 0x75D92, size: 0x8, addend: 0x0, symName: '___34-[LookinDisplayItem copyWithZone:]_block_invoke_3', symObjAddr: 0x380, symBinAddr: 0x24D0C, symSize: 0x18 }
  - { offset: 0x75DD9, size: 0x8, addend: 0x0, symName: '___34-[LookinDisplayItem copyWithZone:]_block_invoke_4', symObjAddr: 0x398, symBinAddr: 0x24D24, symSize: 0x18 }
  - { offset: 0x75E20, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem encodeWithCoder:]', symObjAddr: 0x3B0, symBinAddr: 0x24D3C, symSize: 0x40C }
  - { offset: 0x75E63, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem initWithCoder:]', symObjAddr: 0x7BC, symBinAddr: 0x25148, symSize: 0x49C }
  - { offset: 0x75ED9, size: 0x8, addend: 0x0, symName: '+[LookinDisplayItem supportsSecureCoding]', symObjAddr: 0xC58, symBinAddr: 0x255E4, symSize: 0x8 }
  - { offset: 0x75F0C, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem init]', symObjAddr: 0xC60, symBinAddr: 0x255EC, symSize: 0x50 }
  - { offset: 0x75F43, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem displayingObject]', symObjAddr: 0xCB0, symBinAddr: 0x2563C, symSize: 0x5C }
  - { offset: 0x75F7A, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setAttributesGroupList:]', symObjAddr: 0xD0C, symBinAddr: 0x25698, symSize: 0x80 }
  - { offset: 0x75FBD, size: 0x8, addend: 0x0, symName: '___44-[LookinDisplayItem setAttributesGroupList:]_block_invoke', symObjAddr: 0xD8C, symBinAddr: 0x25718, symSize: 0x7C }
  - { offset: 0x76024, size: 0x8, addend: 0x0, symName: '___44-[LookinDisplayItem setAttributesGroupList:]_block_invoke_2', symObjAddr: 0xE08, symBinAddr: 0x25794, symSize: 0x7C }
  - { offset: 0x7608B, size: 0x8, addend: 0x0, symName: '___44-[LookinDisplayItem setAttributesGroupList:]_block_invoke_3', symObjAddr: 0xE84, symBinAddr: 0x25810, symSize: 0xC }
  - { offset: 0x760F2, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setCustomAttrGroupList:]', symObjAddr: 0xEA0, symBinAddr: 0x2581C, symSize: 0x80 }
  - { offset: 0x76135, size: 0x8, addend: 0x0, symName: '___44-[LookinDisplayItem setCustomAttrGroupList:]_block_invoke', symObjAddr: 0xF20, symBinAddr: 0x2589C, symSize: 0x7C }
  - { offset: 0x7619C, size: 0x8, addend: 0x0, symName: '___44-[LookinDisplayItem setCustomAttrGroupList:]_block_invoke_2', symObjAddr: 0xF9C, symBinAddr: 0x25918, symSize: 0x7C }
  - { offset: 0x76203, size: 0x8, addend: 0x0, symName: '___44-[LookinDisplayItem setCustomAttrGroupList:]_block_invoke_3', symObjAddr: 0x1018, symBinAddr: 0x25994, symSize: 0xC }
  - { offset: 0x7626A, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem representedForSystemClass]', symObjAddr: 0x1024, symBinAddr: 0x259A0, symSize: 0xB8 }
  - { offset: 0x762A1, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem itemIsKindOfClassWithName:]', symObjAddr: 0x10DC, symBinAddr: 0x25A58, symSize: 0x5C }
  - { offset: 0x762E8, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem itemIsKindOfClassesWithNames:]', symObjAddr: 0x1138, symBinAddr: 0x25AB4, symSize: 0x11C }
  - { offset: 0x76353, size: 0x8, addend: 0x0, symName: '___50-[LookinDisplayItem itemIsKindOfClassesWithNames:]_block_invoke', symObjAddr: 0x1254, symBinAddr: 0x25BD0, symSize: 0xC8 }
  - { offset: 0x763BE, size: 0x8, addend: 0x0, symName: '___50-[LookinDisplayItem itemIsKindOfClassesWithNames:]_block_invoke_2', symObjAddr: 0x131C, symBinAddr: 0x25C98, symSize: 0x84 }
  - { offset: 0x76454, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setSubitems:]', symObjAddr: 0x1400, symBinAddr: 0x25D1C, symSize: 0xBC }
  - { offset: 0x76497, size: 0x8, addend: 0x0, symName: '___33-[LookinDisplayItem setSubitems:]_block_invoke', symObjAddr: 0x14BC, symBinAddr: 0x25DD8, symSize: 0xC }
  - { offset: 0x764E6, size: 0x8, addend: 0x0, symName: '___33-[LookinDisplayItem setSubitems:]_block_invoke_2', symObjAddr: 0x14C8, symBinAddr: 0x25DE4, symSize: 0x44 }
  - { offset: 0x7654E, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setIsExpandable:]', symObjAddr: 0x150C, symBinAddr: 0x25E28, symSize: 0x1C }
  - { offset: 0x76591, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setIsExpanded:]', symObjAddr: 0x1528, symBinAddr: 0x25E44, symSize: 0x64 }
  - { offset: 0x765D6, size: 0x8, addend: 0x0, symName: '___35-[LookinDisplayItem setIsExpanded:]_block_invoke', symObjAddr: 0x158C, symBinAddr: 0x25EA8, symSize: 0x8 }
  - { offset: 0x7662A, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setSoloScreenshot:]', symObjAddr: 0x1594, symBinAddr: 0x25EB0, symSize: 0x5C }
  - { offset: 0x7666F, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem notifySelectionChangeToDelegates]', symObjAddr: 0x15F0, symBinAddr: 0x25F0C, symSize: 0x8 }
  - { offset: 0x766A1, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem notifyHoverChangeToDelegates]', symObjAddr: 0x15F8, symBinAddr: 0x25F14, symSize: 0x8 }
  - { offset: 0x766D3, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setDoNotFetchScreenshotReason:]', symObjAddr: 0x1600, symBinAddr: 0x25F1C, symSize: 0x1C }
  - { offset: 0x76716, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setGroupScreenshot:]', symObjAddr: 0x161C, symBinAddr: 0x25F38, symSize: 0x5C }
  - { offset: 0x7675B, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setDisplayingInHierarchy:]', symObjAddr: 0x1678, symBinAddr: 0x25F94, symSize: 0x64 }
  - { offset: 0x767A0, size: 0x8, addend: 0x0, symName: '___46-[LookinDisplayItem setDisplayingInHierarchy:]_block_invoke', symObjAddr: 0x16DC, symBinAddr: 0x25FF8, symSize: 0x8 }
  - { offset: 0x767F4, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem _updateDisplayingInHierarchyProperty]', symObjAddr: 0x16E4, symBinAddr: 0x26000, symSize: 0xB4 }
  - { offset: 0x76828, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setIsHidden:]', symObjAddr: 0x1798, symBinAddr: 0x260B4, symSize: 0x8 }
  - { offset: 0x76867, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setAlpha:]', symObjAddr: 0x17A0, symBinAddr: 0x260BC, symSize: 0x8 }
  - { offset: 0x768A9, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setInHiddenHierarchy:]', symObjAddr: 0x17A8, symBinAddr: 0x260C4, symSize: 0x64 }
  - { offset: 0x768EE, size: 0x8, addend: 0x0, symName: '___42-[LookinDisplayItem setInHiddenHierarchy:]_block_invoke', symObjAddr: 0x180C, symBinAddr: 0x26128, symSize: 0x8 }
  - { offset: 0x76942, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem _updateInHiddenHierarchyProperty]', symObjAddr: 0x1814, symBinAddr: 0x26130, symSize: 0x7C }
  - { offset: 0x76976, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem enumerateSelfAndAncestors:]', symObjAddr: 0x1890, symBinAddr: 0x261AC, symSize: 0xA4 }
  - { offset: 0x76A0E, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem enumerateAncestors:]', symObjAddr: 0x1934, symBinAddr: 0x26250, symSize: 0x50 }
  - { offset: 0x76A53, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem enumerateSelfAndChildren:]', symObjAddr: 0x1984, symBinAddr: 0x262A0, symSize: 0xB0 }
  - { offset: 0x76AB3, size: 0x8, addend: 0x0, symName: '___46-[LookinDisplayItem enumerateSelfAndChildren:]_block_invoke', symObjAddr: 0x1A34, symBinAddr: 0x26350, symSize: 0xC }
  - { offset: 0x76B1C, size: 0x8, addend: 0x0, symName: '+[LookinDisplayItem flatItemsFromHierarchicalItems:]', symObjAddr: 0x1A50, symBinAddr: 0x2635C, symSize: 0xA8 }
  - { offset: 0x76B76, size: 0x8, addend: 0x0, symName: '___52+[LookinDisplayItem flatItemsFromHierarchicalItems:]_block_invoke', symObjAddr: 0x1AF8, symBinAddr: 0x26404, symSize: 0xF8 }
  - { offset: 0x76BF4, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem description]', symObjAddr: 0x1BF0, symBinAddr: 0x264FC, symSize: 0x64 }
  - { offset: 0x76C2C, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setPreviewItemDelegate:]', symObjAddr: 0x1C54, symBinAddr: 0x26560, symSize: 0x9C }
  - { offset: 0x76C71, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setRowViewDelegate:]', symObjAddr: 0x1CF0, symBinAddr: 0x265FC, symSize: 0xA8 }
  - { offset: 0x76CB6, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setFrame:]', symObjAddr: 0x1D98, symBinAddr: 0x266A4, symSize: 0xC }
  - { offset: 0x76CF9, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem recursivelyNotifyFrameToRootMayChange]', symObjAddr: 0x1DA4, symBinAddr: 0x266B0, symSize: 0x48 }
  - { offset: 0x76D2D, size: 0x8, addend: 0x0, symName: '___58-[LookinDisplayItem recursivelyNotifyFrameToRootMayChange]_block_invoke', symObjAddr: 0x1DEC, symBinAddr: 0x266F8, symSize: 0x8 }
  - { offset: 0x76D81, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setBounds:]', symObjAddr: 0x1DF4, symBinAddr: 0x26700, symSize: 0xC }
  - { offset: 0x76DC4, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setInNoPreviewHierarchy:]', symObjAddr: 0x1E00, symBinAddr: 0x2670C, symSize: 0x64 }
  - { offset: 0x76E09, size: 0x8, addend: 0x0, symName: '___45-[LookinDisplayItem setInNoPreviewHierarchy:]_block_invoke', symObjAddr: 0x1E64, symBinAddr: 0x26770, symSize: 0x8 }
  - { offset: 0x76E5D, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setNoPreview:]', symObjAddr: 0x1E6C, symBinAddr: 0x26778, symSize: 0x8 }
  - { offset: 0x76E9C, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem _updateInNoPreviewHierarchy]', symObjAddr: 0x1E74, symBinAddr: 0x26780, symSize: 0x60 }
  - { offset: 0x76ED0, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem appropriateScreenshot]', symObjAddr: 0x1ED4, symBinAddr: 0x267E0, symSize: 0x54 }
  - { offset: 0x76F08, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem _notifyDelegatesWith:]', symObjAddr: 0x1F28, symBinAddr: 0x26834, symSize: 0x70 }
  - { offset: 0x76F4D, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem isMatchedWithSearchString:]', symObjAddr: 0x1F98, symBinAddr: 0x268A4, symSize: 0x184 }
  - { offset: 0x76FA7, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setIsInSearch:]', symObjAddr: 0x211C, symBinAddr: 0x26A28, symSize: 0xC }
  - { offset: 0x76FE6, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setHighlightedSearchString:]', symObjAddr: 0x2128, symBinAddr: 0x26A34, symSize: 0x30 }
  - { offset: 0x7702B, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem queryAllAttrGroupList]', symObjAddr: 0x2158, symBinAddr: 0x26A64, symSize: 0xC8 }
  - { offset: 0x77074, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem title]', symObjAddr: 0x2220, symBinAddr: 0x26B2C, symSize: 0x124 }
  - { offset: 0x770AC, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem subtitle]', symObjAddr: 0x2344, symBinAddr: 0x26C50, symSize: 0x220 }
  - { offset: 0x77126, size: 0x8, addend: 0x0, symName: '___29-[LookinDisplayItem subtitle]_block_invoke', symObjAddr: 0x2564, symBinAddr: 0x26E70, symSize: 0x8 }
  - { offset: 0x77171, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem customInfo]', symObjAddr: 0x256C, symBinAddr: 0x26E78, symSize: 0x8 }
  - { offset: 0x771A8, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setCustomInfo:]', symObjAddr: 0x2574, symBinAddr: 0x26E80, symSize: 0xC }
  - { offset: 0x771E9, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem subitems]', symObjAddr: 0x2580, symBinAddr: 0x26E8C, symSize: 0x8 }
  - { offset: 0x77220, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem isHidden]', symObjAddr: 0x2588, symBinAddr: 0x26E94, symSize: 0x8 }
  - { offset: 0x77257, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem alpha]', symObjAddr: 0x2590, symBinAddr: 0x26E9C, symSize: 0x8 }
  - { offset: 0x7728C, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem frame]', symObjAddr: 0x2598, symBinAddr: 0x26EA4, symSize: 0xC }
  - { offset: 0x772C1, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem bounds]', symObjAddr: 0x25A4, symBinAddr: 0x26EB0, symSize: 0xC }
  - { offset: 0x772F6, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem soloScreenshot]', symObjAddr: 0x25B0, symBinAddr: 0x26EBC, symSize: 0x8 }
  - { offset: 0x7732D, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem groupScreenshot]', symObjAddr: 0x25B8, symBinAddr: 0x26EC4, symSize: 0x8 }
  - { offset: 0x77364, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem viewObject]', symObjAddr: 0x25C0, symBinAddr: 0x26ECC, symSize: 0x8 }
  - { offset: 0x7739B, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setViewObject:]', symObjAddr: 0x25C8, symBinAddr: 0x26ED4, symSize: 0xC }
  - { offset: 0x773DC, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem layerObject]', symObjAddr: 0x25D4, symBinAddr: 0x26EE0, symSize: 0x8 }
  - { offset: 0x77413, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setLayerObject:]', symObjAddr: 0x25DC, symBinAddr: 0x26EE8, symSize: 0xC }
  - { offset: 0x77454, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem hostViewControllerObject]', symObjAddr: 0x25E8, symBinAddr: 0x26EF4, symSize: 0x8 }
  - { offset: 0x7748B, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setHostViewControllerObject:]', symObjAddr: 0x25F0, symBinAddr: 0x26EFC, symSize: 0xC }
  - { offset: 0x774CC, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem attributesGroupList]', symObjAddr: 0x25FC, symBinAddr: 0x26F08, symSize: 0x8 }
  - { offset: 0x77503, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem customAttrGroupList]', symObjAddr: 0x2604, symBinAddr: 0x26F10, symSize: 0x8 }
  - { offset: 0x7753A, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem eventHandlers]', symObjAddr: 0x260C, symBinAddr: 0x26F18, symSize: 0x8 }
  - { offset: 0x77571, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setEventHandlers:]', symObjAddr: 0x2614, symBinAddr: 0x26F20, symSize: 0x8 }
  - { offset: 0x775B0, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem representedAsKeyWindow]', symObjAddr: 0x261C, symBinAddr: 0x26F28, symSize: 0x8 }
  - { offset: 0x775E7, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setRepresentedAsKeyWindow:]', symObjAddr: 0x2624, symBinAddr: 0x26F30, symSize: 0x8 }
  - { offset: 0x77622, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem backgroundColor]', symObjAddr: 0x262C, symBinAddr: 0x26F38, symSize: 0x8 }
  - { offset: 0x77659, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setBackgroundColor:]', symObjAddr: 0x2634, symBinAddr: 0x26F40, symSize: 0xC }
  - { offset: 0x7769A, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem shouldCaptureImage]', symObjAddr: 0x2640, symBinAddr: 0x26F4C, symSize: 0x8 }
  - { offset: 0x776D1, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setShouldCaptureImage:]', symObjAddr: 0x2648, symBinAddr: 0x26F54, symSize: 0x8 }
  - { offset: 0x7770C, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem customDisplayTitle]', symObjAddr: 0x2650, symBinAddr: 0x26F5C, symSize: 0x8 }
  - { offset: 0x77743, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setCustomDisplayTitle:]', symObjAddr: 0x2658, symBinAddr: 0x26F64, symSize: 0x8 }
  - { offset: 0x77782, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem danceuiSource]', symObjAddr: 0x2660, symBinAddr: 0x26F6C, symSize: 0x8 }
  - { offset: 0x777B9, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setDanceuiSource:]', symObjAddr: 0x2668, symBinAddr: 0x26F74, symSize: 0x8 }
  - { offset: 0x777F8, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem previewItemDelegate]', symObjAddr: 0x2670, symBinAddr: 0x26F7C, symSize: 0x18 }
  - { offset: 0x7782F, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem rowViewDelegate]', symObjAddr: 0x2688, symBinAddr: 0x26F94, symSize: 0x18 }
  - { offset: 0x77866, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem superItem]', symObjAddr: 0x26A0, symBinAddr: 0x26FAC, symSize: 0x18 }
  - { offset: 0x7789D, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setSuperItem:]', symObjAddr: 0x26B8, symBinAddr: 0x26FC4, symSize: 0xC }
  - { offset: 0x778DE, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem isExpanded]', symObjAddr: 0x26C4, symBinAddr: 0x26FD0, symSize: 0x8 }
  - { offset: 0x77915, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem isExpandable]', symObjAddr: 0x26CC, symBinAddr: 0x26FD8, symSize: 0x8 }
  - { offset: 0x7794C, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem displayingInHierarchy]', symObjAddr: 0x26D4, symBinAddr: 0x26FE0, symSize: 0x8 }
  - { offset: 0x77983, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem inHiddenHierarchy]', symObjAddr: 0x26DC, symBinAddr: 0x26FE8, symSize: 0x8 }
  - { offset: 0x779BA, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem screenshotEncodeType]', symObjAddr: 0x26E4, symBinAddr: 0x26FF0, symSize: 0x8 }
  - { offset: 0x779F1, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setScreenshotEncodeType:]', symObjAddr: 0x26EC, symBinAddr: 0x26FF8, symSize: 0x8 }
  - { offset: 0x77A2E, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem doNotFetchScreenshotReason]', symObjAddr: 0x26F4, symBinAddr: 0x27000, symSize: 0x8 }
  - { offset: 0x77A65, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem previewLayer]', symObjAddr: 0x26FC, symBinAddr: 0x27008, symSize: 0x18 }
  - { offset: 0x77A9C, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setPreviewLayer:]', symObjAddr: 0x2714, symBinAddr: 0x27020, symSize: 0xC }
  - { offset: 0x77ADD, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem previewNode]', symObjAddr: 0x2720, symBinAddr: 0x2702C, symSize: 0x18 }
  - { offset: 0x77B14, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setPreviewNode:]', symObjAddr: 0x2738, symBinAddr: 0x27044, symSize: 0xC }
  - { offset: 0x77B55, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem noPreview]', symObjAddr: 0x2744, symBinAddr: 0x27050, symSize: 0x8 }
  - { offset: 0x77B8C, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem inNoPreviewHierarchy]', symObjAddr: 0x274C, symBinAddr: 0x27058, symSize: 0x8 }
  - { offset: 0x77BC3, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem previewZIndex]', symObjAddr: 0x2754, symBinAddr: 0x27060, symSize: 0x8 }
  - { offset: 0x77BFA, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setPreviewZIndex:]', symObjAddr: 0x275C, symBinAddr: 0x27068, symSize: 0x8 }
  - { offset: 0x77C37, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem preferToBeCollapsed]', symObjAddr: 0x2764, symBinAddr: 0x27070, symSize: 0x8 }
  - { offset: 0x77C6E, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setPreferToBeCollapsed:]', symObjAddr: 0x276C, symBinAddr: 0x27078, symSize: 0x8 }
  - { offset: 0x77CA9, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem hasDeterminedExpansion]', symObjAddr: 0x2774, symBinAddr: 0x27080, symSize: 0x8 }
  - { offset: 0x77CE0, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setHasDeterminedExpansion:]', symObjAddr: 0x277C, symBinAddr: 0x27088, symSize: 0x8 }
  - { offset: 0x77D1B, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem isInSearch]', symObjAddr: 0x2784, symBinAddr: 0x27090, symSize: 0x8 }
  - { offset: 0x77D52, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem highlightedSearchString]', symObjAddr: 0x278C, symBinAddr: 0x27098, symSize: 0x8 }
  - { offset: 0x77D89, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem frameToRoot]', symObjAddr: 0x2794, symBinAddr: 0x270A0, symSize: 0xC }
  - { offset: 0x77DBE, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setFrameToRoot:]', symObjAddr: 0x27A0, symBinAddr: 0x270AC, symSize: 0xC }
  - { offset: 0x77DFD, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem indentLevel]', symObjAddr: 0x27AC, symBinAddr: 0x270B8, symSize: 0x8 }
  - { offset: 0x77E34, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem setIndentLevel:]', symObjAddr: 0x27B4, symBinAddr: 0x270C0, symSize: 0x8 }
  - { offset: 0x77E71, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItem .cxx_destruct]', symObjAddr: 0x27BC, symBinAddr: 0x270C8, symSize: 0xE8 }
  - { offset: 0x78B4B, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail encodeWithCoder:]', symObjAddr: 0x0, symBinAddr: 0x271B0, symSize: 0x288 }
  - { offset: 0x78CA4, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail encodeWithCoder:]', symObjAddr: 0x0, symBinAddr: 0x271B0, symSize: 0x288 }
  - { offset: 0x78CE7, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail initWithCoder:]', symObjAddr: 0x288, symBinAddr: 0x27438, symSize: 0x2D0 }
  - { offset: 0x78D2E, size: 0x8, addend: 0x0, symName: '+[LookinDisplayItemDetail supportsSecureCoding]', symObjAddr: 0x558, symBinAddr: 0x27708, symSize: 0x8 }
  - { offset: 0x78D61, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail displayItemOid]', symObjAddr: 0x560, symBinAddr: 0x27710, symSize: 0x8 }
  - { offset: 0x78D98, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail setDisplayItemOid:]', symObjAddr: 0x568, symBinAddr: 0x27718, symSize: 0x8 }
  - { offset: 0x78DD5, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail groupScreenshot]', symObjAddr: 0x570, symBinAddr: 0x27720, symSize: 0x8 }
  - { offset: 0x78E0C, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail setGroupScreenshot:]', symObjAddr: 0x578, symBinAddr: 0x27728, symSize: 0xC }
  - { offset: 0x78E4D, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail soloScreenshot]', symObjAddr: 0x584, symBinAddr: 0x27734, symSize: 0x8 }
  - { offset: 0x78E84, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail setSoloScreenshot:]', symObjAddr: 0x58C, symBinAddr: 0x2773C, symSize: 0xC }
  - { offset: 0x78EC5, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail frameValue]', symObjAddr: 0x598, symBinAddr: 0x27748, symSize: 0x8 }
  - { offset: 0x78EFC, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail setFrameValue:]', symObjAddr: 0x5A0, symBinAddr: 0x27750, symSize: 0xC }
  - { offset: 0x78F3D, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail boundsValue]', symObjAddr: 0x5AC, symBinAddr: 0x2775C, symSize: 0x8 }
  - { offset: 0x78F74, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail setBoundsValue:]', symObjAddr: 0x5B4, symBinAddr: 0x27764, symSize: 0xC }
  - { offset: 0x78FB5, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail hiddenValue]', symObjAddr: 0x5C0, symBinAddr: 0x27770, symSize: 0x8 }
  - { offset: 0x78FEC, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail setHiddenValue:]', symObjAddr: 0x5C8, symBinAddr: 0x27778, symSize: 0xC }
  - { offset: 0x7902D, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail alphaValue]', symObjAddr: 0x5D4, symBinAddr: 0x27784, symSize: 0x8 }
  - { offset: 0x79064, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail setAlphaValue:]', symObjAddr: 0x5DC, symBinAddr: 0x2778C, symSize: 0xC }
  - { offset: 0x790A5, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail customDisplayTitle]', symObjAddr: 0x5E8, symBinAddr: 0x27798, symSize: 0x8 }
  - { offset: 0x790DC, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail setCustomDisplayTitle:]', symObjAddr: 0x5F0, symBinAddr: 0x277A0, symSize: 0x8 }
  - { offset: 0x7911B, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail danceUISource]', symObjAddr: 0x5F8, symBinAddr: 0x277A8, symSize: 0x8 }
  - { offset: 0x79152, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail setDanceUISource:]', symObjAddr: 0x600, symBinAddr: 0x277B0, symSize: 0x8 }
  - { offset: 0x79191, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail attributesGroupList]', symObjAddr: 0x608, symBinAddr: 0x277B8, symSize: 0x8 }
  - { offset: 0x791C8, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail setAttributesGroupList:]', symObjAddr: 0x610, symBinAddr: 0x277C0, symSize: 0x8 }
  - { offset: 0x79207, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail customAttrGroupList]', symObjAddr: 0x618, symBinAddr: 0x277C8, symSize: 0x8 }
  - { offset: 0x7923E, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail setCustomAttrGroupList:]', symObjAddr: 0x620, symBinAddr: 0x277D0, symSize: 0x8 }
  - { offset: 0x7927D, size: 0x8, addend: 0x0, symName: '-[LookinDisplayItemDetail .cxx_destruct]', symObjAddr: 0x628, symBinAddr: 0x277D8, symSize: 0x90 }
  - { offset: 0x79382, size: 0x8, addend: 0x0, symName: '-[LookinEventHandler copyWithZone:]', symObjAddr: 0x0, symBinAddr: 0x27868, symSize: 0x170 }
  - { offset: 0x794AF, size: 0x8, addend: 0x0, symName: '-[LookinEventHandler copyWithZone:]', symObjAddr: 0x0, symBinAddr: 0x27868, symSize: 0x170 }
  - { offset: 0x79506, size: 0x8, addend: 0x0, symName: '___35-[LookinEventHandler copyWithZone:]_block_invoke', symObjAddr: 0x170, symBinAddr: 0x279D8, symSize: 0x18 }
  - { offset: 0x7954D, size: 0x8, addend: 0x0, symName: '-[LookinEventHandler encodeWithCoder:]', symObjAddr: 0x188, symBinAddr: 0x279F0, symSize: 0x1A0 }
  - { offset: 0x79590, size: 0x8, addend: 0x0, symName: '-[LookinEventHandler initWithCoder:]', symObjAddr: 0x328, symBinAddr: 0x27B90, symSize: 0x1C4 }
  - { offset: 0x795D7, size: 0x8, addend: 0x0, symName: '+[LookinEventHandler supportsSecureCoding]', symObjAddr: 0x4EC, symBinAddr: 0x27D54, symSize: 0x8 }
  - { offset: 0x7960A, size: 0x8, addend: 0x0, symName: '-[LookinEventHandler handlerType]', symObjAddr: 0x4F4, symBinAddr: 0x27D5C, symSize: 0x8 }
  - { offset: 0x79641, size: 0x8, addend: 0x0, symName: '-[LookinEventHandler setHandlerType:]', symObjAddr: 0x4FC, symBinAddr: 0x27D64, symSize: 0x8 }
  - { offset: 0x7967E, size: 0x8, addend: 0x0, symName: '-[LookinEventHandler eventName]', symObjAddr: 0x504, symBinAddr: 0x27D6C, symSize: 0x8 }
  - { offset: 0x796B5, size: 0x8, addend: 0x0, symName: '-[LookinEventHandler setEventName:]', symObjAddr: 0x50C, symBinAddr: 0x27D74, symSize: 0x8 }
  - { offset: 0x796F4, size: 0x8, addend: 0x0, symName: '-[LookinEventHandler targetActions]', symObjAddr: 0x514, symBinAddr: 0x27D7C, symSize: 0x8 }
  - { offset: 0x7972B, size: 0x8, addend: 0x0, symName: '-[LookinEventHandler setTargetActions:]', symObjAddr: 0x51C, symBinAddr: 0x27D84, symSize: 0x8 }
  - { offset: 0x7976A, size: 0x8, addend: 0x0, symName: '-[LookinEventHandler inheritedRecognizerName]', symObjAddr: 0x524, symBinAddr: 0x27D8C, symSize: 0x8 }
  - { offset: 0x797A1, size: 0x8, addend: 0x0, symName: '-[LookinEventHandler setInheritedRecognizerName:]', symObjAddr: 0x52C, symBinAddr: 0x27D94, symSize: 0x8 }
  - { offset: 0x797E0, size: 0x8, addend: 0x0, symName: '-[LookinEventHandler gestureRecognizerIsEnabled]', symObjAddr: 0x534, symBinAddr: 0x27D9C, symSize: 0x8 }
  - { offset: 0x79817, size: 0x8, addend: 0x0, symName: '-[LookinEventHandler setGestureRecognizerIsEnabled:]', symObjAddr: 0x53C, symBinAddr: 0x27DA4, symSize: 0x8 }
  - { offset: 0x79852, size: 0x8, addend: 0x0, symName: '-[LookinEventHandler gestureRecognizerDelegator]', symObjAddr: 0x544, symBinAddr: 0x27DAC, symSize: 0x8 }
  - { offset: 0x79889, size: 0x8, addend: 0x0, symName: '-[LookinEventHandler setGestureRecognizerDelegator:]', symObjAddr: 0x54C, symBinAddr: 0x27DB4, symSize: 0x8 }
  - { offset: 0x798C8, size: 0x8, addend: 0x0, symName: '-[LookinEventHandler recognizerIvarTraces]', symObjAddr: 0x554, symBinAddr: 0x27DBC, symSize: 0x8 }
  - { offset: 0x798FF, size: 0x8, addend: 0x0, symName: '-[LookinEventHandler setRecognizerIvarTraces:]', symObjAddr: 0x55C, symBinAddr: 0x27DC4, symSize: 0x8 }
  - { offset: 0x7993E, size: 0x8, addend: 0x0, symName: '-[LookinEventHandler recognizerOid]', symObjAddr: 0x564, symBinAddr: 0x27DCC, symSize: 0x8 }
  - { offset: 0x79975, size: 0x8, addend: 0x0, symName: '-[LookinEventHandler setRecognizerOid:]', symObjAddr: 0x56C, symBinAddr: 0x27DD4, symSize: 0x8 }
  - { offset: 0x799B2, size: 0x8, addend: 0x0, symName: '-[LookinEventHandler .cxx_destruct]', symObjAddr: 0x574, symBinAddr: 0x27DDC, symSize: 0x54 }
  - { offset: 0x79B68, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyFile encodeWithCoder:]', symObjAddr: 0x0, symBinAddr: 0x27E30, symSize: 0xE0 }
  - { offset: 0x79C35, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyFile encodeWithCoder:]', symObjAddr: 0x0, symBinAddr: 0x27E30, symSize: 0xE0 }
  - { offset: 0x79C78, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyFile initWithCoder:]', symObjAddr: 0xE0, symBinAddr: 0x27F10, symSize: 0x114 }
  - { offset: 0x79CBF, size: 0x8, addend: 0x0, symName: '+[LookinHierarchyFile supportsSecureCoding]', symObjAddr: 0x1F4, symBinAddr: 0x28024, symSize: 0x8 }
  - { offset: 0x79CF2, size: 0x8, addend: 0x0, symName: '+[LookinHierarchyFile verifyHierarchyFile:]', symObjAddr: 0x1FC, symBinAddr: 0x2802C, symSize: 0x458 }
  - { offset: 0x79D83, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyFile serverVersion]', symObjAddr: 0x654, symBinAddr: 0x28484, symSize: 0x8 }
  - { offset: 0x79DBA, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyFile setServerVersion:]', symObjAddr: 0x65C, symBinAddr: 0x2848C, symSize: 0x8 }
  - { offset: 0x79DF7, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyFile hierarchyInfo]', symObjAddr: 0x664, symBinAddr: 0x28494, symSize: 0x8 }
  - { offset: 0x79E2E, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyFile setHierarchyInfo:]', symObjAddr: 0x66C, symBinAddr: 0x2849C, symSize: 0xC }
  - { offset: 0x79E6F, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyFile soloScreenshots]', symObjAddr: 0x678, symBinAddr: 0x284A8, symSize: 0x8 }
  - { offset: 0x79EA6, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyFile setSoloScreenshots:]', symObjAddr: 0x680, symBinAddr: 0x284B0, symSize: 0x8 }
  - { offset: 0x79EE5, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyFile groupScreenshots]', symObjAddr: 0x688, symBinAddr: 0x284B8, symSize: 0x8 }
  - { offset: 0x79F1C, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyFile setGroupScreenshots:]', symObjAddr: 0x690, symBinAddr: 0x284C0, symSize: 0x8 }
  - { offset: 0x79F5B, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyFile .cxx_destruct]', symObjAddr: 0x698, symBinAddr: 0x284C8, symSize: 0x3C }
  - { offset: 0x7A054, size: 0x8, addend: 0x0, symName: '+[LookinHierarchyInfo staticInfoWithLookinVersion:]', symObjAddr: 0x0, symBinAddr: 0x28504, symSize: 0x14C }
  - { offset: 0x7A272, size: 0x8, addend: 0x0, symName: '+[LookinHierarchyInfo staticInfoWithLookinVersion:]', symObjAddr: 0x0, symBinAddr: 0x28504, symSize: 0x14C }
  - { offset: 0x7A2D5, size: 0x8, addend: 0x0, symName: '+[LookinHierarchyInfo exportedInfo]', symObjAddr: 0x14C, symBinAddr: 0x28650, symSize: 0x108 }
  - { offset: 0x7A318, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyInfo encodeWithCoder:]', symObjAddr: 0x254, symBinAddr: 0x28758, symSize: 0x110 }
  - { offset: 0x7A35B, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyInfo initWithCoder:]', symObjAddr: 0x364, symBinAddr: 0x28868, symSize: 0x144 }
  - { offset: 0x7A3A2, size: 0x8, addend: 0x0, symName: '+[LookinHierarchyInfo supportsSecureCoding]', symObjAddr: 0x4A8, symBinAddr: 0x289AC, symSize: 0x8 }
  - { offset: 0x7A3D5, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyInfo copyWithZone:]', symObjAddr: 0x4B0, symBinAddr: 0x289B4, symSize: 0x120 }
  - { offset: 0x7A42C, size: 0x8, addend: 0x0, symName: '___36-[LookinHierarchyInfo copyWithZone:]_block_invoke', symObjAddr: 0x5D0, symBinAddr: 0x28AD4, symSize: 0x18 }
  - { offset: 0x7A473, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyInfo displayItems]', symObjAddr: 0x5E8, symBinAddr: 0x28AEC, symSize: 0x8 }
  - { offset: 0x7A4AA, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyInfo setDisplayItems:]', symObjAddr: 0x5F0, symBinAddr: 0x28AF4, symSize: 0x8 }
  - { offset: 0x7A4E9, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyInfo colorAlias]', symObjAddr: 0x5F8, symBinAddr: 0x28AFC, symSize: 0x8 }
  - { offset: 0x7A520, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyInfo setColorAlias:]', symObjAddr: 0x600, symBinAddr: 0x28B04, symSize: 0x8 }
  - { offset: 0x7A55F, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyInfo collapsedClassList]', symObjAddr: 0x608, symBinAddr: 0x28B0C, symSize: 0x8 }
  - { offset: 0x7A596, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyInfo setCollapsedClassList:]', symObjAddr: 0x610, symBinAddr: 0x28B14, symSize: 0x8 }
  - { offset: 0x7A5D5, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyInfo appInfo]', symObjAddr: 0x618, symBinAddr: 0x28B1C, symSize: 0x8 }
  - { offset: 0x7A60C, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyInfo setAppInfo:]', symObjAddr: 0x620, symBinAddr: 0x28B24, symSize: 0xC }
  - { offset: 0x7A64D, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyInfo serverVersion]', symObjAddr: 0x62C, symBinAddr: 0x28B30, symSize: 0x8 }
  - { offset: 0x7A684, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyInfo setServerVersion:]', symObjAddr: 0x634, symBinAddr: 0x28B38, symSize: 0x8 }
  - { offset: 0x7A6C1, size: 0x8, addend: 0x0, symName: '-[LookinHierarchyInfo .cxx_destruct]', symObjAddr: 0x63C, symBinAddr: 0x28B40, symSize: 0x48 }
  - { offset: 0x7AB60, size: 0x8, addend: 0x0, symName: '-[LookinIvarTrace hash]', symObjAddr: 0x0, symBinAddr: 0x28B88, symSize: 0x6C }
  - { offset: 0x7AB7A, size: 0x8, addend: 0x0, symName: _LookinIvarTraceRelationValue_Self, symObjAddr: 0x550, symBinAddr: 0x45F40, symSize: 0x0 }
  - { offset: 0x7AC2D, size: 0x8, addend: 0x0, symName: '-[LookinIvarTrace hash]', symObjAddr: 0x0, symBinAddr: 0x28B88, symSize: 0x6C }
  - { offset: 0x7AC64, size: 0x8, addend: 0x0, symName: '-[LookinIvarTrace isEqual:]', symObjAddr: 0x6C, symBinAddr: 0x28BF4, symSize: 0x12C }
  - { offset: 0x7ACB7, size: 0x8, addend: 0x0, symName: '-[LookinIvarTrace copyWithZone:]', symObjAddr: 0x198, symBinAddr: 0x28D20, symSize: 0xB4 }
  - { offset: 0x7AD0E, size: 0x8, addend: 0x0, symName: '-[LookinIvarTrace encodeWithCoder:]', symObjAddr: 0x24C, symBinAddr: 0x28DD4, symSize: 0xC4 }
  - { offset: 0x7AD51, size: 0x8, addend: 0x0, symName: '-[LookinIvarTrace initWithCoder:]', symObjAddr: 0x310, symBinAddr: 0x28E98, symSize: 0xF8 }
  - { offset: 0x7AD98, size: 0x8, addend: 0x0, symName: '+[LookinIvarTrace supportsSecureCoding]', symObjAddr: 0x408, symBinAddr: 0x28F90, symSize: 0x8 }
  - { offset: 0x7ADCB, size: 0x8, addend: 0x0, symName: '-[LookinIvarTrace relation]', symObjAddr: 0x410, symBinAddr: 0x28F98, symSize: 0x8 }
  - { offset: 0x7AE02, size: 0x8, addend: 0x0, symName: '-[LookinIvarTrace setRelation:]', symObjAddr: 0x418, symBinAddr: 0x28FA0, symSize: 0x8 }
  - { offset: 0x7AE41, size: 0x8, addend: 0x0, symName: '-[LookinIvarTrace hostClassName]', symObjAddr: 0x420, symBinAddr: 0x28FA8, symSize: 0x8 }
  - { offset: 0x7AE78, size: 0x8, addend: 0x0, symName: '-[LookinIvarTrace setHostClassName:]', symObjAddr: 0x428, symBinAddr: 0x28FB0, symSize: 0x8 }
  - { offset: 0x7AEB7, size: 0x8, addend: 0x0, symName: '-[LookinIvarTrace ivarName]', symObjAddr: 0x430, symBinAddr: 0x28FB8, symSize: 0x8 }
  - { offset: 0x7AEEE, size: 0x8, addend: 0x0, symName: '-[LookinIvarTrace setIvarName:]', symObjAddr: 0x438, symBinAddr: 0x28FC0, symSize: 0x8 }
  - { offset: 0x7AF2D, size: 0x8, addend: 0x0, symName: '-[LookinIvarTrace hostObject]', symObjAddr: 0x440, symBinAddr: 0x28FC8, symSize: 0x18 }
  - { offset: 0x7AF64, size: 0x8, addend: 0x0, symName: '-[LookinIvarTrace setHostObject:]', symObjAddr: 0x458, symBinAddr: 0x28FE0, symSize: 0xC }
  - { offset: 0x7AFA5, size: 0x8, addend: 0x0, symName: '-[LookinIvarTrace .cxx_destruct]', symObjAddr: 0x464, symBinAddr: 0x28FEC, symSize: 0x44 }
  - { offset: 0x7B059, size: 0x8, addend: 0x0, symName: '+[LookinObject instanceWithObject:]', symObjAddr: 0x0, symBinAddr: 0x29030, symSize: 0x11C }
  - { offset: 0x7B139, size: 0x8, addend: 0x0, symName: '+[LookinObject instanceWithObject:]', symObjAddr: 0x0, symBinAddr: 0x29030, symSize: 0x11C }
  - { offset: 0x7B18C, size: 0x8, addend: 0x0, symName: '-[LookinObject copyWithZone:]', symObjAddr: 0x11C, symBinAddr: 0x2914C, symSize: 0x110 }
  - { offset: 0x7B1E3, size: 0x8, addend: 0x0, symName: '___29-[LookinObject copyWithZone:]_block_invoke', symObjAddr: 0x22C, symBinAddr: 0x2925C, symSize: 0x18 }
  - { offset: 0x7B22A, size: 0x8, addend: 0x0, symName: '-[LookinObject encodeWithCoder:]', symObjAddr: 0x244, symBinAddr: 0x29274, symSize: 0x138 }
  - { offset: 0x7B26D, size: 0x8, addend: 0x0, symName: '-[LookinObject initWithCoder:]', symObjAddr: 0x37C, symBinAddr: 0x293AC, symSize: 0x15C }
  - { offset: 0x7B2B4, size: 0x8, addend: 0x0, symName: '-[LookinObject setClassChainList:]', symObjAddr: 0x4D8, symBinAddr: 0x29508, symSize: 0x78 }
  - { offset: 0x7B2F7, size: 0x8, addend: 0x0, symName: '+[LookinObject supportsSecureCoding]', symObjAddr: 0x550, symBinAddr: 0x29580, symSize: 0x8 }
  - { offset: 0x7B32A, size: 0x8, addend: 0x0, symName: '-[LookinObject oid]', symObjAddr: 0x558, symBinAddr: 0x29588, symSize: 0x8 }
  - { offset: 0x7B361, size: 0x8, addend: 0x0, symName: '-[LookinObject setOid:]', symObjAddr: 0x560, symBinAddr: 0x29590, symSize: 0x8 }
  - { offset: 0x7B39E, size: 0x8, addend: 0x0, symName: '-[LookinObject memoryAddress]', symObjAddr: 0x568, symBinAddr: 0x29598, symSize: 0x8 }
  - { offset: 0x7B3D5, size: 0x8, addend: 0x0, symName: '-[LookinObject setMemoryAddress:]', symObjAddr: 0x570, symBinAddr: 0x295A0, symSize: 0x8 }
  - { offset: 0x7B414, size: 0x8, addend: 0x0, symName: '-[LookinObject classChainList]', symObjAddr: 0x578, symBinAddr: 0x295A8, symSize: 0x8 }
  - { offset: 0x7B44B, size: 0x8, addend: 0x0, symName: '-[LookinObject specialTrace]', symObjAddr: 0x580, symBinAddr: 0x295B0, symSize: 0x8 }
  - { offset: 0x7B482, size: 0x8, addend: 0x0, symName: '-[LookinObject setSpecialTrace:]', symObjAddr: 0x588, symBinAddr: 0x295B8, symSize: 0x8 }
  - { offset: 0x7B4C1, size: 0x8, addend: 0x0, symName: '-[LookinObject ivarTraces]', symObjAddr: 0x590, symBinAddr: 0x295C0, symSize: 0x8 }
  - { offset: 0x7B4F8, size: 0x8, addend: 0x0, symName: '-[LookinObject setIvarTraces:]', symObjAddr: 0x598, symBinAddr: 0x295C8, symSize: 0x8 }
  - { offset: 0x7B537, size: 0x8, addend: 0x0, symName: '-[LookinObject completedSelfClassName]', symObjAddr: 0x5A0, symBinAddr: 0x295D0, symSize: 0x8 }
  - { offset: 0x7B56E, size: 0x8, addend: 0x0, symName: '-[LookinObject shortSelfClassName]', symObjAddr: 0x5A8, symBinAddr: 0x295D8, symSize: 0x8 }
  - { offset: 0x7B5A5, size: 0x8, addend: 0x0, symName: '-[LookinObject .cxx_destruct]', symObjAddr: 0x5B0, symBinAddr: 0x295E0, symSize: 0x60 }
  - { offset: 0x7B78E, size: 0x8, addend: 0x0, symName: '-[LookinStaticAsyncUpdateTask encodeWithCoder:]', symObjAddr: 0x0, symBinAddr: 0x29640, symSize: 0xC4 }
  - { offset: 0x7B880, size: 0x8, addend: 0x0, symName: '-[LookinStaticAsyncUpdateTask encodeWithCoder:]', symObjAddr: 0x0, symBinAddr: 0x29640, symSize: 0xC4 }
  - { offset: 0x7B8C3, size: 0x8, addend: 0x0, symName: '-[LookinStaticAsyncUpdateTask initWithCoder:]', symObjAddr: 0xC4, symBinAddr: 0x29704, symSize: 0xE8 }
  - { offset: 0x7B90A, size: 0x8, addend: 0x0, symName: '+[LookinStaticAsyncUpdateTask supportsSecureCoding]', symObjAddr: 0x1AC, symBinAddr: 0x297EC, symSize: 0x8 }
  - { offset: 0x7B93D, size: 0x8, addend: 0x0, symName: '-[LookinStaticAsyncUpdateTask hash]', symObjAddr: 0x1B4, symBinAddr: 0x297F4, symSize: 0x30 }
  - { offset: 0x7B974, size: 0x8, addend: 0x0, symName: '-[LookinStaticAsyncUpdateTask isEqual:]', symObjAddr: 0x1E4, symBinAddr: 0x29824, symSize: 0xC8 }
  - { offset: 0x7B9C7, size: 0x8, addend: 0x0, symName: '-[LookinStaticAsyncUpdateTask oid]', symObjAddr: 0x2AC, symBinAddr: 0x298EC, symSize: 0x8 }
  - { offset: 0x7B9FE, size: 0x8, addend: 0x0, symName: '-[LookinStaticAsyncUpdateTask setOid:]', symObjAddr: 0x2B4, symBinAddr: 0x298F4, symSize: 0x8 }
  - { offset: 0x7BA3B, size: 0x8, addend: 0x0, symName: '-[LookinStaticAsyncUpdateTask taskType]', symObjAddr: 0x2BC, symBinAddr: 0x298FC, symSize: 0x8 }
  - { offset: 0x7BA72, size: 0x8, addend: 0x0, symName: '-[LookinStaticAsyncUpdateTask setTaskType:]', symObjAddr: 0x2C4, symBinAddr: 0x29904, symSize: 0x8 }
  - { offset: 0x7BAAF, size: 0x8, addend: 0x0, symName: '-[LookinStaticAsyncUpdateTask clientReadableVersion]', symObjAddr: 0x2CC, symBinAddr: 0x2990C, symSize: 0x8 }
  - { offset: 0x7BAE6, size: 0x8, addend: 0x0, symName: '-[LookinStaticAsyncUpdateTask setClientReadableVersion:]', symObjAddr: 0x2D4, symBinAddr: 0x29914, symSize: 0x8 }
  - { offset: 0x7BB25, size: 0x8, addend: 0x0, symName: '-[LookinStaticAsyncUpdateTask frameSize]', symObjAddr: 0x2DC, symBinAddr: 0x2991C, symSize: 0x8 }
  - { offset: 0x7BB5A, size: 0x8, addend: 0x0, symName: '-[LookinStaticAsyncUpdateTask setFrameSize:]', symObjAddr: 0x2E4, symBinAddr: 0x29924, symSize: 0x8 }
  - { offset: 0x7BB99, size: 0x8, addend: 0x0, symName: '-[LookinStaticAsyncUpdateTask .cxx_destruct]', symObjAddr: 0x2EC, symBinAddr: 0x2992C, symSize: 0xC }
  - { offset: 0x7BBCC, size: 0x8, addend: 0x0, symName: '-[LookinStaticAsyncUpdateTasksPackage encodeWithCoder:]', symObjAddr: 0x2F8, symBinAddr: 0x29938, symSize: 0x5C }
  - { offset: 0x7BC0F, size: 0x8, addend: 0x0, symName: '-[LookinStaticAsyncUpdateTasksPackage initWithCoder:]', symObjAddr: 0x354, symBinAddr: 0x29994, symSize: 0x98 }
  - { offset: 0x7BC56, size: 0x8, addend: 0x0, symName: '+[LookinStaticAsyncUpdateTasksPackage supportsSecureCoding]', symObjAddr: 0x3EC, symBinAddr: 0x29A2C, symSize: 0x8 }
  - { offset: 0x7BC89, size: 0x8, addend: 0x0, symName: '-[LookinStaticAsyncUpdateTasksPackage tasks]', symObjAddr: 0x3F4, symBinAddr: 0x29A34, symSize: 0x8 }
  - { offset: 0x7BCC0, size: 0x8, addend: 0x0, symName: '-[LookinStaticAsyncUpdateTasksPackage setTasks:]', symObjAddr: 0x3FC, symBinAddr: 0x29A3C, symSize: 0x8 }
  - { offset: 0x7BCFF, size: 0x8, addend: 0x0, symName: '-[LookinStaticAsyncUpdateTasksPackage .cxx_destruct]', symObjAddr: 0x404, symBinAddr: 0x29A44, symSize: 0xC }
  - { offset: 0x7BDE1, size: 0x8, addend: 0x0, symName: '-[LookinTwoTuple encodeWithCoder:]', symObjAddr: 0x0, symBinAddr: 0x29A50, symSize: 0x94 }
  - { offset: 0x7BE81, size: 0x8, addend: 0x0, symName: '-[LookinTwoTuple encodeWithCoder:]', symObjAddr: 0x0, symBinAddr: 0x29A50, symSize: 0x94 }
  - { offset: 0x7BEC4, size: 0x8, addend: 0x0, symName: '-[LookinTwoTuple initWithCoder:]', symObjAddr: 0x94, symBinAddr: 0x29AE4, symSize: 0xC8 }
  - { offset: 0x7BF0B, size: 0x8, addend: 0x0, symName: '+[LookinTwoTuple supportsSecureCoding]', symObjAddr: 0x15C, symBinAddr: 0x29BAC, symSize: 0x8 }
  - { offset: 0x7BF3E, size: 0x8, addend: 0x0, symName: '-[LookinTwoTuple hash]', symObjAddr: 0x164, symBinAddr: 0x29BB4, symSize: 0x6C }
  - { offset: 0x7BF75, size: 0x8, addend: 0x0, symName: '-[LookinTwoTuple isEqual:]', symObjAddr: 0x1D0, symBinAddr: 0x29C20, symSize: 0x12C }
  - { offset: 0x7BFC8, size: 0x8, addend: 0x0, symName: '-[LookinTwoTuple first]', symObjAddr: 0x2FC, symBinAddr: 0x29D4C, symSize: 0x8 }
  - { offset: 0x7BFFF, size: 0x8, addend: 0x0, symName: '-[LookinTwoTuple setFirst:]', symObjAddr: 0x304, symBinAddr: 0x29D54, symSize: 0xC }
  - { offset: 0x7C040, size: 0x8, addend: 0x0, symName: '-[LookinTwoTuple second]', symObjAddr: 0x310, symBinAddr: 0x29D60, symSize: 0x8 }
  - { offset: 0x7C077, size: 0x8, addend: 0x0, symName: '-[LookinTwoTuple setSecond:]', symObjAddr: 0x318, symBinAddr: 0x29D68, symSize: 0xC }
  - { offset: 0x7C0B8, size: 0x8, addend: 0x0, symName: '-[LookinTwoTuple .cxx_destruct]', symObjAddr: 0x324, symBinAddr: 0x29D74, symSize: 0x30 }
  - { offset: 0x7C0EB, size: 0x8, addend: 0x0, symName: '+[LookinStringTwoTuple tupleWithFirst:second:]', symObjAddr: 0x354, symBinAddr: 0x29DA4, symSize: 0x78 }
  - { offset: 0x7C14E, size: 0x8, addend: 0x0, symName: '-[LookinStringTwoTuple copyWithZone:]', symObjAddr: 0x3CC, symBinAddr: 0x29E1C, symSize: 0x8C }
  - { offset: 0x7C1A5, size: 0x8, addend: 0x0, symName: '-[LookinStringTwoTuple encodeWithCoder:]', symObjAddr: 0x458, symBinAddr: 0x29EA8, symSize: 0x94 }
  - { offset: 0x7C1E8, size: 0x8, addend: 0x0, symName: '-[LookinStringTwoTuple initWithCoder:]', symObjAddr: 0x4EC, symBinAddr: 0x29F3C, symSize: 0xC8 }
  - { offset: 0x7C22F, size: 0x8, addend: 0x0, symName: '+[LookinStringTwoTuple supportsSecureCoding]', symObjAddr: 0x5B4, symBinAddr: 0x2A004, symSize: 0x8 }
  - { offset: 0x7C262, size: 0x8, addend: 0x0, symName: '-[LookinStringTwoTuple first]', symObjAddr: 0x5BC, symBinAddr: 0x2A00C, symSize: 0x8 }
  - { offset: 0x7C299, size: 0x8, addend: 0x0, symName: '-[LookinStringTwoTuple setFirst:]', symObjAddr: 0x5C4, symBinAddr: 0x2A014, symSize: 0x8 }
  - { offset: 0x7C2D8, size: 0x8, addend: 0x0, symName: '-[LookinStringTwoTuple second]', symObjAddr: 0x5CC, symBinAddr: 0x2A01C, symSize: 0x8 }
  - { offset: 0x7C30F, size: 0x8, addend: 0x0, symName: '-[LookinStringTwoTuple setSecond:]', symObjAddr: 0x5D4, symBinAddr: 0x2A024, symSize: 0x8 }
  - { offset: 0x7C34E, size: 0x8, addend: 0x0, symName: '-[LookinStringTwoTuple .cxx_destruct]', symObjAddr: 0x5DC, symBinAddr: 0x2A02C, symSize: 0x30 }
  - { offset: 0x7C435, size: 0x8, addend: 0x0, symName: '+[LookinWeakContainer containerWithObject:]', symObjAddr: 0x0, symBinAddr: 0x2A05C, symSize: 0x4C }
  - { offset: 0x7C497, size: 0x8, addend: 0x0, symName: '+[LookinWeakContainer containerWithObject:]', symObjAddr: 0x0, symBinAddr: 0x2A05C, symSize: 0x4C }
  - { offset: 0x7C4EA, size: 0x8, addend: 0x0, symName: '-[LookinWeakContainer hash]', symObjAddr: 0x4C, symBinAddr: 0x2A0A8, symSize: 0x3C }
  - { offset: 0x7C521, size: 0x8, addend: 0x0, symName: '-[LookinWeakContainer isEqual:]', symObjAddr: 0x88, symBinAddr: 0x2A0E4, symSize: 0xD4 }
  - { offset: 0x7C574, size: 0x8, addend: 0x0, symName: '-[LookinWeakContainer object]', symObjAddr: 0x15C, symBinAddr: 0x2A1B8, symSize: 0x18 }
  - { offset: 0x7C5AB, size: 0x8, addend: 0x0, symName: '-[LookinWeakContainer setObject:]', symObjAddr: 0x174, symBinAddr: 0x2A1D0, symSize: 0xC }
  - { offset: 0x7C5EC, size: 0x8, addend: 0x0, symName: '-[LookinWeakContainer .cxx_destruct]', symObjAddr: 0x180, symBinAddr: 0x2A1DC, symSize: 0x8 }
  - { offset: 0x7C68D, size: 0x8, addend: 0x0, symName: '-[NSArray(Lookin) lookin_resizeWithCount:add:remove:doNext:]', symObjAddr: 0x0, symBinAddr: 0x2A1E4, symSize: 0x1AC }
  - { offset: 0x7C6DA, size: 0x8, addend: 0x0, symName: '-[NSArray(Lookin) lookin_resizeWithCount:add:remove:doNext:]', symObjAddr: 0x0, symBinAddr: 0x2A1E4, symSize: 0x1AC }
  - { offset: 0x7C859, size: 0x8, addend: 0x0, symName: '+[NSArray(Lookin) lookin_arrayWithCount:block:]', symObjAddr: 0x1AC, symBinAddr: 0x2A390, symSize: 0xBC }
  - { offset: 0x7C915, size: 0x8, addend: 0x0, symName: '-[NSArray(Lookin) lookin_hasIndex:]', symObjAddr: 0x268, symBinAddr: 0x2A44C, symSize: 0x3C }
  - { offset: 0x7C95C, size: 0x8, addend: 0x0, symName: '-[NSArray(Lookin) lookin_map:]', symObjAddr: 0x2A4, symBinAddr: 0x2A488, symSize: 0xEC }
  - { offset: 0x7C9B3, size: 0x8, addend: 0x0, symName: '___30-[NSArray(Lookin) lookin_map:]_block_invoke', symObjAddr: 0x390, symBinAddr: 0x2A574, symSize: 0x54 }
  - { offset: 0x7CA66, size: 0x8, addend: 0x0, symName: '-[NSArray(Lookin) lookin_filter:]', symObjAddr: 0x440, symBinAddr: 0x2A5C8, symSize: 0xDC }
  - { offset: 0x7CABD, size: 0x8, addend: 0x0, symName: '___33-[NSArray(Lookin) lookin_filter:]_block_invoke', symObjAddr: 0x51C, symBinAddr: 0x2A6A4, symSize: 0x4C }
  - { offset: 0x7CB44, size: 0x8, addend: 0x0, symName: '-[NSArray(Lookin) lookin_firstFiltered:]', symObjAddr: 0x568, symBinAddr: 0x2A6F0, symSize: 0x104 }
  - { offset: 0x7CB9F, size: 0x8, addend: 0x0, symName: ___Block_byref_object_copy_, symObjAddr: 0x66C, symBinAddr: 0x2A7F4, symSize: 0x10 }
  - { offset: 0x7CBC4, size: 0x8, addend: 0x0, symName: ___Block_byref_object_dispose_, symObjAddr: 0x67C, symBinAddr: 0x2A804, symSize: 0x8 }
  - { offset: 0x7CBE3, size: 0x8, addend: 0x0, symName: '___40-[NSArray(Lookin) lookin_firstFiltered:]_block_invoke', symObjAddr: 0x684, symBinAddr: 0x2A80C, symSize: 0x6C }
  - { offset: 0x7CC6E, size: 0x8, addend: 0x0, symName: '-[NSArray(Lookin) lookin_lastFiltered:]', symObjAddr: 0x758, symBinAddr: 0x2A878, symSize: 0x108 }
  - { offset: 0x7CCC9, size: 0x8, addend: 0x0, symName: '___39-[NSArray(Lookin) lookin_lastFiltered:]_block_invoke', symObjAddr: 0x860, symBinAddr: 0x2A980, symSize: 0x6C }
  - { offset: 0x7CD54, size: 0x8, addend: 0x0, symName: '-[NSArray(Lookin) lookin_reduce:]', symObjAddr: 0x8CC, symBinAddr: 0x2A9EC, symSize: 0x100 }
  - { offset: 0x7CDAF, size: 0x8, addend: 0x0, symName: '___33-[NSArray(Lookin) lookin_reduce:]_block_invoke', symObjAddr: 0x9CC, symBinAddr: 0x2AAEC, symSize: 0x54 }
  - { offset: 0x7CE43, size: 0x8, addend: 0x0, symName: '-[NSArray(Lookin) lookin_reduceCGFloat:initialAccumlator:]', symObjAddr: 0xA20, symBinAddr: 0x2AB40, symSize: 0xDC }
  - { offset: 0x7CEAE, size: 0x8, addend: 0x0, symName: '___58-[NSArray(Lookin) lookin_reduceCGFloat:initialAccumlator:]_block_invoke', symObjAddr: 0xAFC, symBinAddr: 0x2AC1C, symSize: 0x4C }
  - { offset: 0x7CF42, size: 0x8, addend: 0x0, symName: '-[NSArray(Lookin) lookin_reduceInteger:initialAccumlator:]', symObjAddr: 0xB48, symBinAddr: 0x2AC68, symSize: 0xE0 }
  - { offset: 0x7CFAD, size: 0x8, addend: 0x0, symName: '___58-[NSArray(Lookin) lookin_reduceInteger:initialAccumlator:]_block_invoke', symObjAddr: 0xC28, symBinAddr: 0x2AD48, symSize: 0x44 }
  - { offset: 0x7D041, size: 0x8, addend: 0x0, symName: '-[NSArray(Lookin) lookin_all:]', symObjAddr: 0xC6C, symBinAddr: 0x2AD8C, symSize: 0xEC }
  - { offset: 0x7D09C, size: 0x8, addend: 0x0, symName: '___30-[NSArray(Lookin) lookin_all:]_block_invoke', symObjAddr: 0xD58, symBinAddr: 0x2AE78, symSize: 0x44 }
  - { offset: 0x7D147, size: 0x8, addend: 0x0, symName: '-[NSArray(Lookin) lookin_any:]', symObjAddr: 0xD9C, symBinAddr: 0x2AEBC, symSize: 0xE8 }
  - { offset: 0x7D1A2, size: 0x8, addend: 0x0, symName: '___30-[NSArray(Lookin) lookin_any:]_block_invoke', symObjAddr: 0xE84, symBinAddr: 0x2AFA4, symSize: 0x44 }
  - { offset: 0x7D249, size: 0x8, addend: 0x0, symName: '-[NSArray(Lookin) lookin_arrayByRemovingObject:]', symObjAddr: 0xEC8, symBinAddr: 0x2AFE8, symSize: 0x88 }
  - { offset: 0x7D2A0, size: 0x8, addend: 0x0, symName: '-[NSArray(Lookin) lookin_nonredundantArray]', symObjAddr: 0xF50, symBinAddr: 0x2B070, symSize: 0x50 }
  - { offset: 0x7D2F7, size: 0x8, addend: 0x0, symName: '-[NSArray(Lookin) lookin_safeObjectAtIndex:]', symObjAddr: 0xFA0, symBinAddr: 0x2B0C0, symSize: 0x5C }
  - { offset: 0x7D33E, size: 0x8, addend: 0x0, symName: '-[NSArray(Lookin) lookin_sortedArrayByStringLength]', symObjAddr: 0xFFC, symBinAddr: 0x2B11C, symSize: 0xC }
  - { offset: 0x7D37F, size: 0x8, addend: 0x0, symName: '___51-[NSArray(Lookin) lookin_sortedArrayByStringLength]_block_invoke', symObjAddr: 0x1008, symBinAddr: 0x2B128, symSize: 0x90 }
  - { offset: 0x7D3CA, size: 0x8, addend: 0x0, symName: '-[NSMutableArray(Lookin) lookin_dequeueWithCount:add:notDequeued:doNext:]', symObjAddr: 0x1098, symBinAddr: 0x2B1B8, symSize: 0x178 }
  - { offset: 0x7D538, size: 0x8, addend: 0x0, symName: '-[NSMutableArray(Lookin) lookin_removeObjectsPassingTest:]', symObjAddr: 0x1210, symBinAddr: 0x2B330, symSize: 0xCC }
  - { offset: 0x7D58E, size: 0x8, addend: 0x0, symName: '___58-[NSMutableArray(Lookin) lookin_removeObjectsPassingTest:]_block_invoke', symObjAddr: 0x12DC, symBinAddr: 0x2B3FC, symSize: 0x4C }
  - { offset: 0x7E04E, size: 0x8, addend: 0x0, symName: '-[NSObject(Lookin) lookin_allBindObjects]', symObjAddr: 0x0, symBinAddr: 0x2B448, symSize: 0x6C }
  - { offset: 0x7E06D, size: 0x8, addend: 0x0, symName: _kAssociatedObjectKey_LookinAllBindObjects, symObjAddr: 0x4E0B, symBinAddr: 0x5A4F0, symSize: 0x0 }
  - { offset: 0x7E11D, size: 0x8, addend: 0x0, symName: '-[NSObject(Lookin) lookin_allBindObjects]', symObjAddr: 0x0, symBinAddr: 0x2B448, symSize: 0x6C }
  - { offset: 0x7E1DA, size: 0x8, addend: 0x0, symName: '-[NSObject(Lookin) lookin_bindObject:forKey:]', symObjAddr: 0x6C, symBinAddr: 0x2B4B4, symSize: 0xD4 }
  - { offset: 0x7E22D, size: 0x8, addend: 0x0, symName: '-[NSObject(Lookin) lookin_getBindObjectForKey:]', symObjAddr: 0x140, symBinAddr: 0x2B588, symSize: 0xEC }
  - { offset: 0x7E28B, size: 0x8, addend: 0x0, symName: '-[NSObject(Lookin) lookin_bindObjectWeakly:forKey:]', symObjAddr: 0x22C, symBinAddr: 0x2B674, symSize: 0xA0 }
  - { offset: 0x7E2FD, size: 0x8, addend: 0x0, symName: '-[NSObject(Lookin) lookin_bindDouble:forKey:]', symObjAddr: 0x2CC, symBinAddr: 0x2B714, symSize: 0x78 }
  - { offset: 0x7E350, size: 0x8, addend: 0x0, symName: '-[NSObject(Lookin) lookin_getBindDoubleForKey:]', symObjAddr: 0x344, symBinAddr: 0x2B78C, symSize: 0x68 }
  - { offset: 0x7E3C2, size: 0x8, addend: 0x0, symName: '-[NSObject(Lookin) lookin_bindBOOL:forKey:]', symObjAddr: 0x3AC, symBinAddr: 0x2B7F4, symSize: 0x70 }
  - { offset: 0x7E415, size: 0x8, addend: 0x0, symName: '-[NSObject(Lookin) lookin_getBindBOOLForKey:]', symObjAddr: 0x41C, symBinAddr: 0x2B864, symSize: 0x64 }
  - { offset: 0x7E487, size: 0x8, addend: 0x0, symName: '-[NSObject(Lookin) lookin_bindLong:forKey:]', symObjAddr: 0x480, symBinAddr: 0x2B8C8, symSize: 0x70 }
  - { offset: 0x7E4DA, size: 0x8, addend: 0x0, symName: '-[NSObject(Lookin) lookin_getBindLongForKey:]', symObjAddr: 0x4F0, symBinAddr: 0x2B938, symSize: 0x64 }
  - { offset: 0x7E550, size: 0x8, addend: 0x0, symName: '-[NSObject(Lookin) lookin_bindPoint:forKey:]', symObjAddr: 0x554, symBinAddr: 0x2B99C, symSize: 0x80 }
  - { offset: 0x7E59F, size: 0x8, addend: 0x0, symName: '-[NSObject(Lookin) lookin_getBindPointForKey:]', symObjAddr: 0x5D4, symBinAddr: 0x2BA1C, symSize: 0x7C }
  - { offset: 0x7E615, size: 0x8, addend: 0x0, symName: '-[NSObject(Lookin) lookin_clearBindForKey:]', symObjAddr: 0x650, symBinAddr: 0x2BA98, symSize: 0xC }
  - { offset: 0x7E656, size: 0x8, addend: 0x0, symName: '-[NSObject(Lookin_Coding) lookin_encodedObjectWithType:]', symObjAddr: 0x65C, symBinAddr: 0x2BAA4, symSize: 0x1DC }
  - { offset: 0x7E74F, size: 0x8, addend: 0x0, symName: '-[NSObject(Lookin_Coding) lookin_decodedObjectWithType:]', symObjAddr: 0x838, symBinAddr: 0x2BC80, symSize: 0x178 }
  - { offset: 0x7E8BF, size: 0x8, addend: 0x0, symName: '-[NSObject(LookinServer) lks_registerOid]', symObjAddr: 0x0, symBinAddr: 0x2BDF8, symSize: 0x68 }
  - { offset: 0x7E8D2, size: 0x8, addend: 0x0, symName: '+[NSObject(LookinServer) lks_allObjectsWithTraces]', symObjAddr: 0x30C, symBinAddr: 0x2C104, symSize: 0x40 }
  - { offset: 0x7E8FC, size: 0x8, addend: 0x0, symName: _lks_allObjectsWithTraces.onceToken, symObjAddr: 0x4108, symBinAddr: 0x5A4F8, symSize: 0x0 }
  - { offset: 0x7E912, size: 0x8, addend: 0x0, symName: _lks_allObjectsWithTraces.lks_allObjectsWithTraces, symObjAddr: 0x4110, symBinAddr: 0x5A500, symSize: 0x0 }
  - { offset: 0x7E959, size: 0x8, addend: 0x0, symName: '-[NSObject(LookinServer) lks_registerOid]', symObjAddr: 0x0, symBinAddr: 0x2BDF8, symSize: 0x68 }
  - { offset: 0x7E9AF, size: 0x8, addend: 0x0, symName: '-[NSObject(LookinServer) setLks_oid:]', symObjAddr: 0x68, symBinAddr: 0x2BE60, symSize: 0x4C }
  - { offset: 0x7E9F2, size: 0x8, addend: 0x0, symName: '-[NSObject(LookinServer) lks_oid]', symObjAddr: 0xB4, symBinAddr: 0x2BEAC, symSize: 0x44 }
  - { offset: 0x7EA39, size: 0x8, addend: 0x0, symName: '+[NSObject(LookinServer) lks_objectWithOid:]', symObjAddr: 0xF8, symBinAddr: 0x2BEF0, symSize: 0x54 }
  - { offset: 0x7EA7C, size: 0x8, addend: 0x0, symName: '-[NSObject(LookinServer) setLks_ivarTraces:]', symObjAddr: 0x14C, symBinAddr: 0x2BF44, symSize: 0x88 }
  - { offset: 0x7EABF, size: 0x8, addend: 0x0, symName: '-[NSObject(LookinServer) lks_ivarTraces]', symObjAddr: 0x1D4, symBinAddr: 0x2BFCC, symSize: 0xC }
  - { offset: 0x7EAF4, size: 0x8, addend: 0x0, symName: '-[NSObject(LookinServer) setLks_specialTrace:]', symObjAddr: 0x1E0, symBinAddr: 0x2BFD8, symSize: 0x60 }
  - { offset: 0x7EB37, size: 0x8, addend: 0x0, symName: '-[NSObject(LookinServer) lks_specialTrace]', symObjAddr: 0x240, symBinAddr: 0x2C038, symSize: 0xC }
  - { offset: 0x7EB6C, size: 0x8, addend: 0x0, symName: '+[NSObject(LookinServer) lks_clearAllObjectsTraces]', symObjAddr: 0x24C, symBinAddr: 0x2C044, symSize: 0x84 }
  - { offset: 0x7EB9B, size: 0x8, addend: 0x0, symName: '___51+[NSObject(LookinServer) lks_clearAllObjectsTraces]_block_invoke', symObjAddr: 0x2D0, symBinAddr: 0x2C0C8, symSize: 0x3C }
  - { offset: 0x7EC38, size: 0x8, addend: 0x0, symName: '___50+[NSObject(LookinServer) lks_allObjectsWithTraces]_block_invoke', symObjAddr: 0x34C, symBinAddr: 0x2C144, symSize: 0x34 }
  - { offset: 0x7EC5F, size: 0x8, addend: 0x0, symName: '-[NSObject(LookinServer) lks_classChainListWithSwiftPrefix:]', symObjAddr: 0x380, symBinAddr: 0x2C178, symSize: 0xD0 }
  - { offset: 0x7ED31, size: 0x8, addend: 0x0, symName: '-[NSObject(LookinServer) lks_shortClassName]', symObjAddr: 0x450, symBinAddr: 0x2C248, symSize: 0x48 }
  - { offset: 0x7EF76, size: 0x8, addend: 0x0, symName: '-[NSSet(Lookin) lookin_map:]', symObjAddr: 0x0, symBinAddr: 0x2C290, symSize: 0xEC }
  - { offset: 0x7EF84, size: 0x8, addend: 0x0, symName: '-[NSSet(Lookin) lookin_map:]', symObjAddr: 0x0, symBinAddr: 0x2C290, symSize: 0xEC }
  - { offset: 0x7EFDB, size: 0x8, addend: 0x0, symName: '___28-[NSSet(Lookin) lookin_map:]_block_invoke', symObjAddr: 0xEC, symBinAddr: 0x2C37C, symSize: 0x48 }
  - { offset: 0x7F076, size: 0x8, addend: 0x0, symName: '-[NSSet(Lookin) lookin_firstFiltered:]', symObjAddr: 0x190, symBinAddr: 0x2C3C4, symSize: 0x104 }
  - { offset: 0x7F0D1, size: 0x8, addend: 0x0, symName: ___Block_byref_object_copy_, symObjAddr: 0x294, symBinAddr: 0x2C4C8, symSize: 0x10 }
  - { offset: 0x7F0F6, size: 0x8, addend: 0x0, symName: ___Block_byref_object_dispose_, symObjAddr: 0x2A4, symBinAddr: 0x2C4D8, symSize: 0x8 }
  - { offset: 0x7F115, size: 0x8, addend: 0x0, symName: '___38-[NSSet(Lookin) lookin_firstFiltered:]_block_invoke', symObjAddr: 0x2AC, symBinAddr: 0x2C4E0, symSize: 0x6C }
  - { offset: 0x7F194, size: 0x8, addend: 0x0, symName: '-[NSSet(Lookin) lookin_filter:]', symObjAddr: 0x380, symBinAddr: 0x2C54C, symSize: 0xDC }
  - { offset: 0x7F1EB, size: 0x8, addend: 0x0, symName: '___31-[NSSet(Lookin) lookin_filter:]_block_invoke', symObjAddr: 0x45C, symBinAddr: 0x2C628, symSize: 0x4C }
  - { offset: 0x7F266, size: 0x8, addend: 0x0, symName: '-[NSSet(Lookin) lookin_any:]', symObjAddr: 0x4A8, symBinAddr: 0x2C674, symSize: 0xE8 }
  - { offset: 0x7F2C1, size: 0x8, addend: 0x0, symName: '___28-[NSSet(Lookin) lookin_any:]_block_invoke', symObjAddr: 0x590, symBinAddr: 0x2C75C, symSize: 0x44 }
  - { offset: 0x7F721, size: 0x8, addend: 0x0, symName: '+[NSString(Lookin) lookin_stringFromDouble:decimal:]', symObjAddr: 0x0, symBinAddr: 0x2C7A0, symSize: 0x190 }
  - { offset: 0x7F739, size: 0x8, addend: 0x0, symName: '+[NSString(Lookin) lookin_stringFromDouble:decimal:]', symObjAddr: 0x0, symBinAddr: 0x2C7A0, symSize: 0x190 }
  - { offset: 0x7F7CB, size: 0x8, addend: 0x0, symName: '+[NSString(Lookin) lookin_stringFromInset:]', symObjAddr: 0x190, symBinAddr: 0x2C930, symSize: 0x108 }
  - { offset: 0x7F80E, size: 0x8, addend: 0x0, symName: '+[NSString(Lookin) lookin_stringFromSize:]', symObjAddr: 0x298, symBinAddr: 0x2CA38, symSize: 0xA4 }
  - { offset: 0x7F851, size: 0x8, addend: 0x0, symName: '+[NSString(Lookin) lookin_stringFromPoint:]', symObjAddr: 0x33C, symBinAddr: 0x2CADC, symSize: 0xA4 }
  - { offset: 0x7F894, size: 0x8, addend: 0x0, symName: '+[NSString(Lookin) lookin_stringFromRect:]', symObjAddr: 0x3E0, symBinAddr: 0x2CB80, symSize: 0x108 }
  - { offset: 0x7F8D7, size: 0x8, addend: 0x0, symName: '+[NSString(Lookin) lookin_rgbaStringFromColor:]', symObjAddr: 0x4E8, symBinAddr: 0x2CC88, symSize: 0x11C }
  - { offset: 0x7F976, size: 0x8, addend: 0x0, symName: '-[NSString(Lookin) lookin_safeInitWithUTF8String:]', symObjAddr: 0x604, symBinAddr: 0x2CDA4, symSize: 0x34 }
  - { offset: 0x7F9BD, size: 0x8, addend: 0x0, symName: '-[NSString(Lookin) lookin_shortClassNameString]', symObjAddr: 0x638, symBinAddr: 0x2CDD8, symSize: 0x128 }
  - { offset: 0x7FA98, size: 0x8, addend: 0x0, symName: '-[NSString(Lookin) lookin_numbericOSVersion]', symObjAddr: 0x760, symBinAddr: 0x2CF00, symSize: 0xF4 }
  - { offset: 0x7FBAB, size: 0x8, addend: 0x0, symName: '+[UIBlurEffect(LookinServer) load]', symObjAddr: 0x0, symBinAddr: 0x2CFF4, symSize: 0x6C }
  - { offset: 0x7FBB9, size: 0x8, addend: 0x0, symName: '+[UIBlurEffect(LookinServer) load]', symObjAddr: 0x0, symBinAddr: 0x2CFF4, symSize: 0x6C }
  - { offset: 0x7FBDF, size: 0x8, addend: 0x0, symName: _load.onceToken, symObjAddr: 0x28F0, symBinAddr: 0x5A508, symSize: 0x0 }
  - { offset: 0x7FC86, size: 0x8, addend: 0x0, symName: '___34+[UIBlurEffect(LookinServer) load]_block_invoke', symObjAddr: 0x6C, symBinAddr: 0x2D060, symSize: 0x50 }
  - { offset: 0x7FD70, size: 0x8, addend: 0x0, symName: '+[UIBlurEffect(LookinServer) lks_effectWithStyle:]', symObjAddr: 0xBC, symBinAddr: 0x2D0B0, symSize: 0x70 }
  - { offset: 0x7FDC7, size: 0x8, addend: 0x0, symName: '-[UIBlurEffect(LookinServer) setLks_effectStyleNumber:]', symObjAddr: 0x12C, symBinAddr: 0x2D120, symSize: 0xC }
  - { offset: 0x7FE06, size: 0x8, addend: 0x0, symName: '-[UIBlurEffect(LookinServer) lks_effectStyleNumber]', symObjAddr: 0x138, symBinAddr: 0x2D12C, symSize: 0xC }
  - { offset: 0x7FF52, size: 0x8, addend: 0x0, symName: '-[UIColor(LookinServer) lks_rgbaComponents]', symObjAddr: 0x0, symBinAddr: 0x2D138, symSize: 0x1A0 }
  - { offset: 0x7FF60, size: 0x8, addend: 0x0, symName: '-[UIColor(LookinServer) lks_rgbaComponents]', symObjAddr: 0x0, symBinAddr: 0x2D138, symSize: 0x1A0 }
  - { offset: 0x80093, size: 0x8, addend: 0x0, symName: '+[UIColor(LookinServer) lks_colorFromRGBAComponents:]', symObjAddr: 0x1A0, symBinAddr: 0x2D2D8, symSize: 0x134 }
  - { offset: 0x800E6, size: 0x8, addend: 0x0, symName: '-[UIColor(LookinServer) lks_rgbaString]', symObjAddr: 0x2D4, symBinAddr: 0x2D40C, symSize: 0xFC }
  - { offset: 0x801B7, size: 0x8, addend: 0x0, symName: '-[UIColor(LookinServer) lks_hexString]', symObjAddr: 0x3D0, symBinAddr: 0x2D508, symSize: 0x230 }
  - { offset: 0x802C8, size: 0x8, addend: 0x0, symName: '+[UIColor(LookinServer) _alignColorHexStringLength:]', symObjAddr: 0x600, symBinAddr: 0x2D738, symSize: 0x64 }
  - { offset: 0x8030B, size: 0x8, addend: 0x0, symName: '+[UIColor(LookinServer) _hexStringWithInteger:]', symObjAddr: 0x664, symBinAddr: 0x2D79C, symSize: 0xB4 }
  - { offset: 0x803B0, size: 0x8, addend: 0x0, symName: '+[UIColor(LookinServer) _hexLetterStringWithInteger:]', symObjAddr: 0x718, symBinAddr: 0x2D850, symSize: 0x98 }
  - { offset: 0x80403, size: 0x8, addend: 0x0, symName: '+[UIColor(LookinServer) lks_colorWithCGColor:]', symObjAddr: 0x7B0, symBinAddr: 0x2D8E8, symSize: 0x60 }
  - { offset: 0x804F5, size: 0x8, addend: 0x0, symName: '+[UIImage(LookinServer) load]', symObjAddr: 0x0, symBinAddr: 0x2D948, symSize: 0x6C }
  - { offset: 0x80508, size: 0x8, addend: 0x0, symName: '+[UIImage(LookinServer) load]', symObjAddr: 0x0, symBinAddr: 0x2D948, symSize: 0x6C }
  - { offset: 0x8052E, size: 0x8, addend: 0x0, symName: _load.onceToken, symObjAddr: 0x3CF8, symBinAddr: 0x5A510, symSize: 0x0 }
  - { offset: 0x805D5, size: 0x8, addend: 0x0, symName: '___29+[UIImage(LookinServer) load]_block_invoke', symObjAddr: 0x6C, symBinAddr: 0x2D9B4, symSize: 0x11C }
  - { offset: 0x80755, size: 0x8, addend: 0x0, symName: '+[UIImage(LookinServer) lks_imageNamed:inBundle:withConfiguration:]', symObjAddr: 0x188, symBinAddr: 0x2DAD0, symSize: 0x6C }
  - { offset: 0x807CC, size: 0x8, addend: 0x0, symName: '+[UIImage(LookinServer) lks_imageNamed:inBundle:compatibleWithTraitCollection:]', symObjAddr: 0x1F4, symBinAddr: 0x2DB3C, symSize: 0x6C }
  - { offset: 0x80843, size: 0x8, addend: 0x0, symName: '+[UIImage(LookinServer) lks_imageNamed:]', symObjAddr: 0x260, symBinAddr: 0x2DBA8, symSize: 0x54 }
  - { offset: 0x8089A, size: 0x8, addend: 0x0, symName: '+[UIImage(LookinServer) lks_imageWithContentsOfFile:]', symObjAddr: 0x2B4, symBinAddr: 0x2DBFC, symSize: 0xE0 }
  - { offset: 0x80901, size: 0x8, addend: 0x0, symName: '-[UIImage(LookinServer) setLks_imageSourceName:]', symObjAddr: 0x394, symBinAddr: 0x2DCDC, symSize: 0x40 }
  - { offset: 0x80944, size: 0x8, addend: 0x0, symName: '-[UIImage(LookinServer) lks_imageSourceName]', symObjAddr: 0x3D4, symBinAddr: 0x2DD1C, symSize: 0xC }
  - { offset: 0x80979, size: 0x8, addend: 0x0, symName: '-[UIImage(LookinServer) lookin_data]', symObjAddr: 0x3E0, symBinAddr: 0x2DD28, symSize: 0x4 }
  - { offset: 0x809B1, size: 0x8, addend: 0x0, symName: '-[UIImage(LookinServer) lookin_data]', symObjAddr: 0x3E0, symBinAddr: 0x2DD28, symSize: 0x4 }
  - { offset: 0x80B2C, size: 0x8, addend: 0x0, symName: '-[UIImageView(LookinServer) lks_imageSourceName]', symObjAddr: 0x0, symBinAddr: 0x2DD2C, symSize: 0x44 }
  - { offset: 0x80B3F, size: 0x8, addend: 0x0, symName: '-[UIImageView(LookinServer) lks_imageSourceName]', symObjAddr: 0x0, symBinAddr: 0x2DD2C, symSize: 0x44 }
  - { offset: 0x80B76, size: 0x8, addend: 0x0, symName: '-[UIImageView(LookinServer) lks_imageViewOidIfHasImage]', symObjAddr: 0x44, symBinAddr: 0x2DD70, symSize: 0x5C }
  - { offset: 0x80C30, size: 0x8, addend: 0x0, symName: '-[UILabel(LookinServer) lks_fontSize]', symObjAddr: 0x0, symBinAddr: 0x2DDCC, symSize: 0x44 }
  - { offset: 0x80C3E, size: 0x8, addend: 0x0, symName: '-[UILabel(LookinServer) lks_fontSize]', symObjAddr: 0x0, symBinAddr: 0x2DDCC, symSize: 0x44 }
  - { offset: 0x80C75, size: 0x8, addend: 0x0, symName: '-[UILabel(LookinServer) setLks_fontSize:]', symObjAddr: 0x44, symBinAddr: 0x2DE10, symSize: 0x6C }
  - { offset: 0x80CC8, size: 0x8, addend: 0x0, symName: '-[UILabel(LookinServer) lks_fontName]', symObjAddr: 0xB0, symBinAddr: 0x2DE7C, symSize: 0x44 }
  - { offset: 0x80D5B, size: 0x8, addend: 0x0, symName: '-[UITableView(LookinServer) lks_numberOfRows]', symObjAddr: 0x0, symBinAddr: 0x2DEC0, symSize: 0xA8 }
  - { offset: 0x80D69, size: 0x8, addend: 0x0, symName: '-[UITableView(LookinServer) lks_numberOfRows]', symObjAddr: 0x0, symBinAddr: 0x2DEC0, symSize: 0xA8 }
  - { offset: 0x80DEF, size: 0x8, addend: 0x0, symName: '___45-[UITableView(LookinServer) lks_numberOfRows]_block_invoke', symObjAddr: 0xA8, symBinAddr: 0x2DF68, symSize: 0x34 }
  - { offset: 0x80FFA, size: 0x8, addend: 0x0, symName: '-[UITextField(LookinServer) lks_fontSize]', symObjAddr: 0x0, symBinAddr: 0x2DF9C, symSize: 0x44 }
  - { offset: 0x81008, size: 0x8, addend: 0x0, symName: '-[UITextField(LookinServer) lks_fontSize]', symObjAddr: 0x0, symBinAddr: 0x2DF9C, symSize: 0x44 }
  - { offset: 0x8103F, size: 0x8, addend: 0x0, symName: '-[UITextField(LookinServer) setLks_fontSize:]', symObjAddr: 0x44, symBinAddr: 0x2DFE0, symSize: 0x6C }
  - { offset: 0x81092, size: 0x8, addend: 0x0, symName: '-[UITextField(LookinServer) lks_fontName]', symObjAddr: 0xB0, symBinAddr: 0x2E04C, symSize: 0x44 }
  - { offset: 0x81125, size: 0x8, addend: 0x0, symName: '-[UITextView(LookinServer) lks_fontSize]', symObjAddr: 0x0, symBinAddr: 0x2E090, symSize: 0x44 }
  - { offset: 0x81133, size: 0x8, addend: 0x0, symName: '-[UITextView(LookinServer) lks_fontSize]', symObjAddr: 0x0, symBinAddr: 0x2E090, symSize: 0x44 }
  - { offset: 0x8116A, size: 0x8, addend: 0x0, symName: '-[UITextView(LookinServer) setLks_fontSize:]', symObjAddr: 0x44, symBinAddr: 0x2E0D4, symSize: 0x6C }
  - { offset: 0x811BD, size: 0x8, addend: 0x0, symName: '-[UITextView(LookinServer) lks_fontName]', symObjAddr: 0xB0, symBinAddr: 0x2E140, symSize: 0x44 }
  - { offset: 0x81250, size: 0x8, addend: 0x0, symName: '-[UIView(LookinServer) lks_findHostViewController]', symObjAddr: 0x0, symBinAddr: 0x2E184, symSize: 0xAC }
  - { offset: 0x8129B, size: 0x8, addend: 0x0, symName: '-[UIView(LookinServer) lks_findHostViewController]', symObjAddr: 0x0, symBinAddr: 0x2E184, symSize: 0xAC }
  - { offset: 0x812F2, size: 0x8, addend: 0x0, symName: '-[UIView(LookinServer) lks_subviewAtPoint:preferredClasses:]', symObjAddr: 0xAC, symBinAddr: 0x2E230, symSize: 0x130 }
  - { offset: 0x81375, size: 0x8, addend: 0x0, symName: '___60-[UIView(LookinServer) lks_subviewAtPoint:preferredClasses:]_block_invoke', symObjAddr: 0x1DC, symBinAddr: 0x2E360, symSize: 0xC }
  - { offset: 0x813C8, size: 0x8, addend: 0x0, symName: '___60-[UIView(LookinServer) lks_subviewAtPoint:preferredClasses:]_block_invoke.14', symObjAddr: 0x1F8, symBinAddr: 0x2E36C, symSize: 0x70 }
  - { offset: 0x8145E, size: 0x8, addend: 0x0, symName: '-[UIView(LookinServer) lks_bestSize]', symObjAddr: 0x268, symBinAddr: 0x2E3DC, symSize: 0x10 }
  - { offset: 0x81493, size: 0x8, addend: 0x0, symName: '-[UIView(LookinServer) lks_bestWidth]', symObjAddr: 0x278, symBinAddr: 0x2E3EC, symSize: 0x4 }
  - { offset: 0x814C8, size: 0x8, addend: 0x0, symName: '-[UIView(LookinServer) lks_bestHeight]', symObjAddr: 0x27C, symBinAddr: 0x2E3F0, symSize: 0x18 }
  - { offset: 0x814FF, size: 0x8, addend: 0x0, symName: '-[UIView(LookinServer) setLks_isChildrenViewOfTabBar:]', symObjAddr: 0x294, symBinAddr: 0x2E408, symSize: 0xC }
  - { offset: 0x81543, size: 0x8, addend: 0x0, symName: '-[UIView(LookinServer) lks_isChildrenViewOfTabBar]', symObjAddr: 0x2A0, symBinAddr: 0x2E414, symSize: 0xC }
  - { offset: 0x81578, size: 0x8, addend: 0x0, symName: '-[UIView(LookinServer) setLks_verticalContentHuggingPriority:]', symObjAddr: 0x2AC, symBinAddr: 0x2E420, symSize: 0x8 }
  - { offset: 0x815B8, size: 0x8, addend: 0x0, symName: '-[UIView(LookinServer) lks_verticalContentHuggingPriority]', symObjAddr: 0x2B4, symBinAddr: 0x2E428, symSize: 0x8 }
  - { offset: 0x815ED, size: 0x8, addend: 0x0, symName: '-[UIView(LookinServer) setLks_horizontalContentHuggingPriority:]', symObjAddr: 0x2BC, symBinAddr: 0x2E430, symSize: 0x8 }
  - { offset: 0x8162D, size: 0x8, addend: 0x0, symName: '-[UIView(LookinServer) lks_horizontalContentHuggingPriority]', symObjAddr: 0x2C4, symBinAddr: 0x2E438, symSize: 0x8 }
  - { offset: 0x81662, size: 0x8, addend: 0x0, symName: '-[UIView(LookinServer) setLks_verticalContentCompressionResistancePriority:]', symObjAddr: 0x2CC, symBinAddr: 0x2E440, symSize: 0x8 }
  - { offset: 0x816A2, size: 0x8, addend: 0x0, symName: '-[UIView(LookinServer) lks_verticalContentCompressionResistancePriority]', symObjAddr: 0x2D4, symBinAddr: 0x2E448, symSize: 0x8 }
  - { offset: 0x816D7, size: 0x8, addend: 0x0, symName: '-[UIView(LookinServer) setLks_horizontalContentCompressionResistancePriority:]', symObjAddr: 0x2DC, symBinAddr: 0x2E450, symSize: 0x8 }
  - { offset: 0x81717, size: 0x8, addend: 0x0, symName: '-[UIView(LookinServer) lks_horizontalContentCompressionResistancePriority]', symObjAddr: 0x2E4, symBinAddr: 0x2E458, symSize: 0x8 }
  - { offset: 0x8174C, size: 0x8, addend: 0x0, symName: '+[UIView(LookinServer) lks_rebuildGlobalInvolvedRawConstraints]', symObjAddr: 0x2EC, symBinAddr: 0x2E460, symSize: 0x124 }
  - { offset: 0x8177F, size: 0x8, addend: 0x0, symName: '___63+[UIView(LookinServer) lks_rebuildGlobalInvolvedRawConstraints]_block_invoke', symObjAddr: 0x410, symBinAddr: 0x2E584, symSize: 0xC }
  - { offset: 0x817E6, size: 0x8, addend: 0x0, symName: '___63+[UIView(LookinServer) lks_rebuildGlobalInvolvedRawConstraints]_block_invoke_2', symObjAddr: 0x41C, symBinAddr: 0x2E590, symSize: 0xC }
  - { offset: 0x8184D, size: 0x8, addend: 0x0, symName: '+[UIView(LookinServer) lks_addInvolvedRawConstraintsForViewsRootedByView:]', symObjAddr: 0x428, symBinAddr: 0x2E59C, symSize: 0xB8 }
  - { offset: 0x81890, size: 0x8, addend: 0x0, symName: '___74+[UIView(LookinServer) lks_addInvolvedRawConstraintsForViewsRootedByView:]_block_invoke', symObjAddr: 0x4E0, symBinAddr: 0x2E654, symSize: 0x1D0 }
  - { offset: 0x8190E, size: 0x8, addend: 0x0, symName: '___74+[UIView(LookinServer) lks_addInvolvedRawConstraintsForViewsRootedByView:]_block_invoke_2', symObjAddr: 0x6B0, symBinAddr: 0x2E824, symSize: 0xC }
  - { offset: 0x81975, size: 0x8, addend: 0x0, symName: '+[UIView(LookinServer) lks_removeInvolvedRawConstraintsForViewsRootedByView:]', symObjAddr: 0x6BC, symBinAddr: 0x2E830, symSize: 0xB0 }
  - { offset: 0x819B8, size: 0x8, addend: 0x0, symName: '___77+[UIView(LookinServer) lks_removeInvolvedRawConstraintsForViewsRootedByView:]_block_invoke', symObjAddr: 0x76C, symBinAddr: 0x2E8E0, symSize: 0xC }
  - { offset: 0x81A1F, size: 0x8, addend: 0x0, symName: '-[UIView(LookinServer) setLks_involvedRawConstraints:]', symObjAddr: 0x778, symBinAddr: 0x2E8EC, symSize: 0xC }
  - { offset: 0x81A5E, size: 0x8, addend: 0x0, symName: '-[UIView(LookinServer) lks_involvedRawConstraints]', symObjAddr: 0x784, symBinAddr: 0x2E8F8, symSize: 0xC }
  - { offset: 0x81A93, size: 0x8, addend: 0x0, symName: '-[UIView(LookinServer) lks_constraints]', symObjAddr: 0x790, symBinAddr: 0x2E904, symSize: 0x140 }
  - { offset: 0x81AEA, size: 0x8, addend: 0x0, symName: '___39-[UIView(LookinServer) lks_constraints]_block_invoke', symObjAddr: 0x8D0, symBinAddr: 0x2EA44, symSize: 0xF0 }
  - { offset: 0x81BB3, size: 0x8, addend: 0x0, symName: '-[UIView(LookinServer) _lks_constraintItemTypeForItem:]', symObjAddr: 0xA10, symBinAddr: 0x2EB34, symSize: 0xF8 }
  - { offset: 0x82265, size: 0x8, addend: 0x0, symName: '+[UIViewController(LookinServer) lks_visibleViewController]', symObjAddr: 0x0, symBinAddr: 0x2EC2C, symSize: 0x88 }
  - { offset: 0x82273, size: 0x8, addend: 0x0, symName: '+[UIViewController(LookinServer) lks_visibleViewController]', symObjAddr: 0x0, symBinAddr: 0x2EC2C, symSize: 0x88 }
  - { offset: 0x822C6, size: 0x8, addend: 0x0, symName: '-[UIViewController(LookinServer) lks_visibleViewControllerIfExist]', symObjAddr: 0x88, symBinAddr: 0x2ECB4, symSize: 0x15C }
  - { offset: 0x8237C, size: 0x8, addend: 0x0, symName: '-[UIVisualEffectView(LookinServer) setLks_blurEffectStyleNumber:]', symObjAddr: 0x0, symBinAddr: 0x2EE10, symSize: 0x50 }
  - { offset: 0x8238A, size: 0x8, addend: 0x0, symName: '-[UIVisualEffectView(LookinServer) setLks_blurEffectStyleNumber:]', symObjAddr: 0x0, symBinAddr: 0x2EE10, symSize: 0x50 }
  - { offset: 0x823ED, size: 0x8, addend: 0x0, symName: '-[UIVisualEffectView(LookinServer) lks_blurEffectStyleNumber]', symObjAddr: 0x50, symBinAddr: 0x2EE60, symSize: 0x6C }
...
