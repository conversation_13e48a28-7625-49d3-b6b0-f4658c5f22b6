// swift-interface-format-version: 1.0
// swift-compiler-version: Apple Swift version 5.5 (swiftlang-1300.0.31.1 clang-1300.0.29.1)
// swift-module-flags: -target arm64-apple-ios10.0 -enable-objc-interop -enable-library-evolution -swift-version 5 -enforce-exclusivity=checked -O -module-name Kingfisher
import AVKit
import Accelerate
import CommonCrypto
import CoreGraphics
import CoreImage
import Foundation
import ImageIO
@_exported import Kingfisher
import MobileCoreServices
import Swift
import UIKit
import _Concurrency
public protocol ImageModifier {
  func modify(_ image: Kingfisher.KFCrossPlatformImage) -> Kingfisher.KFCrossPlatformImage
}
public struct AnyImageModifier : Kingfisher.ImageModifier {
  public init(modify: @escaping (Kingfisher.KFCrossPlatformImage) throws -> Kingfisher.KFCrossPlatformImage)
  public func modify(_ image: Kingfisher.KFCrossPlatformImage) -> Kingfisher.KFCrossPlatformImage
}
public struct RenderingModeImageModifier : Kingfisher.ImageModifier {
  public let renderingMode: UIKit.UIImage.RenderingMode
  public init(renderingMode: UIKit.UIImage.RenderingMode = .automatic)
  public func modify(_ image: Kingfisher.KFCrossPlatformImage) -> Kingfisher.KFCrossPlatformImage
}
public struct FlipsForRightToLeftLayoutDirectionImageModifier : Kingfisher.ImageModifier {
  public init()
  public func modify(_ image: Kingfisher.KFCrossPlatformImage) -> Kingfisher.KFCrossPlatformImage
}
public struct AlignmentRectInsetsImageModifier : Kingfisher.ImageModifier {
  public let alignmentInsets: UIKit.UIEdgeInsets
  public init(alignmentInsets: UIKit.UIEdgeInsets)
  public func modify(_ image: Kingfisher.KFCrossPlatformImage) -> Kingfisher.KFCrossPlatformImage
}
public protocol CacheSerializer {
  func data(with image: Kingfisher.KFCrossPlatformImage, original: Foundation.Data?) -> Foundation.Data?
  func image(with data: Foundation.Data, options: Kingfisher.KingfisherParsedOptionsInfo) -> Kingfisher.KFCrossPlatformImage?
  @available(*, deprecated, message: "Deprecated. Implement the method with same name but with `KingfisherParsedOptionsInfo` instead.")
  func image(with data: Foundation.Data, options: Kingfisher.KingfisherOptionsInfo?) -> Kingfisher.KFCrossPlatformImage?
}
extension Kingfisher.CacheSerializer {
  public func image(with data: Foundation.Data, options: Kingfisher.KingfisherOptionsInfo?) -> Kingfisher.KFCrossPlatformImage?
}
public struct DefaultCacheSerializer : Kingfisher.CacheSerializer {
  public static let `default`: Kingfisher.DefaultCacheSerializer
  public var compressionQuality: CoreGraphics.CGFloat
  public var preferCacheOriginalData: Swift.Bool
  public init()
  public func data(with image: Kingfisher.KFCrossPlatformImage, original: Foundation.Data?) -> Foundation.Data?
  public func image(with data: Foundation.Data, options: Kingfisher.KingfisherParsedOptionsInfo) -> Kingfisher.KFCrossPlatformImage?
}
public protocol ImageDataProvider {
  var cacheKey: Swift.String { get }
  func data(handler: @escaping (Swift.Result<Foundation.Data, Swift.Error>) -> Swift.Void)
  var contentURL: Foundation.URL? { get }
}
extension Kingfisher.ImageDataProvider {
  public var contentURL: Foundation.URL? {
    get
  }
}
public struct LocalFileImageDataProvider : Kingfisher.ImageDataProvider {
  public let fileURL: Foundation.URL
  public init(fileURL: Foundation.URL, cacheKey: Swift.String? = nil)
  public var cacheKey: Swift.String
  public func data(handler: (Swift.Result<Foundation.Data, Swift.Error>) -> Swift.Void)
  public var contentURL: Foundation.URL? {
    get
  }
}
public struct Base64ImageDataProvider : Kingfisher.ImageDataProvider {
  public let base64String: Swift.String
  public init(base64String: Swift.String, cacheKey: Swift.String)
  public var cacheKey: Swift.String
  public func data(handler: (Swift.Result<Foundation.Data, Swift.Error>) -> Swift.Void)
}
public struct RawImageDataProvider : Kingfisher.ImageDataProvider {
  public let data: Foundation.Data
  public init(data: Foundation.Data, cacheKey: Swift.String)
  public var cacheKey: Swift.String
  public func data(handler: @escaping (Swift.Result<Foundation.Data, Swift.Error>) -> Swift.Void)
}
public protocol AnimatedImageViewDelegate : AnyObject {
  func animatedImageView(_ imageView: Kingfisher.AnimatedImageView, didPlayAnimationLoops count: Swift.UInt)
  func animatedImageViewDidFinishAnimating(_ imageView: Kingfisher.AnimatedImageView)
}
extension Kingfisher.AnimatedImageViewDelegate {
  public func animatedImageView(_ imageView: Kingfisher.AnimatedImageView, didPlayAnimationLoops count: Swift.UInt)
  public func animatedImageViewDidFinishAnimating(_ imageView: Kingfisher.AnimatedImageView)
}
@objc @_inheritsConvenienceInitializers @_Concurrency.MainActor(unsafe) open class AnimatedImageView : UIKit.UIImageView {
  public enum RepeatCount : Swift.Equatable {
    case once
    case finite(count: Swift.UInt)
    case infinite
    public static func == (lhs: Kingfisher.AnimatedImageView.RepeatCount, rhs: Kingfisher.AnimatedImageView.RepeatCount) -> Swift.Bool
  }
  @_Concurrency.MainActor(unsafe) public var autoPlayAnimatedImage: Swift.Bool
  @_Concurrency.MainActor(unsafe) public var framePreloadCount: Swift.Int
  @_Concurrency.MainActor(unsafe) public var needsPrescaling: Swift.Bool
  @_Concurrency.MainActor(unsafe) public var backgroundDecode: Swift.Bool
  @_Concurrency.MainActor(unsafe) public var runLoopMode: Foundation.RunLoop.Mode {
    get
    set
  }
  @_Concurrency.MainActor(unsafe) public var repeatCount: Kingfisher.AnimatedImageView.RepeatCount {
    get
    set
  }
  @_Concurrency.MainActor(unsafe) weak public var delegate: Kingfisher.AnimatedImageViewDelegate?
  @_Concurrency.MainActor(unsafe) @objc override dynamic open var image: Kingfisher.KFCrossPlatformImage? {
    @_Concurrency.MainActor(unsafe) @objc get
    @_Concurrency.MainActor(unsafe) @objc set
  }
  @objc deinit
  @_Concurrency.MainActor(unsafe) @objc override dynamic open var isAnimating: Swift.Bool {
    @_Concurrency.MainActor(unsafe) @objc get
  }
  @_Concurrency.MainActor(unsafe) @objc override dynamic open func startAnimating()
  @_Concurrency.MainActor(unsafe) @objc override dynamic open func stopAnimating()
  @_Concurrency.MainActor(unsafe) @objc override dynamic open func display(_ layer: QuartzCore.CALayer)
  @_Concurrency.MainActor(unsafe) @objc override dynamic open func didMoveToWindow()
  @_Concurrency.MainActor(unsafe) @objc override dynamic open func didMoveToSuperview()
  @_Concurrency.MainActor(unsafe) @objc override dynamic public init(image: UIKit.UIImage?)
  @available(iOS 3.0, *)
  @_Concurrency.MainActor(unsafe) @objc override dynamic public init(image: UIKit.UIImage?, highlightedImage: UIKit.UIImage?)
  @_Concurrency.MainActor(unsafe) @objc override dynamic public init(frame: CoreGraphics.CGRect)
  @_Concurrency.MainActor(unsafe) @objc required dynamic public init?(coder: Foundation.NSCoder)
}
public enum StorageExpiration {
  case never
  case seconds(Foundation.TimeInterval)
  case days(Swift.Int)
  case date(Foundation.Date)
  case expired
}
public enum ExpirationExtending {
  case none
  case cacheTime
  case expirationTime(_: Kingfisher.StorageExpiration)
}
public protocol CacheCostCalculable {
  var cacheCost: Swift.Int { get }
}
public protocol DataTransformable {
  func toData() throws -> Foundation.Data
  static func fromData(_ data: Foundation.Data) throws -> Self
  static var empty: Self { get }
}
public protocol Placeholder {
  func add(to imageView: Kingfisher.KFCrossPlatformImageView)
  func remove(from imageView: Kingfisher.KFCrossPlatformImageView)
}
extension UIKit.UIImage : Kingfisher.Placeholder {
  public func add(to imageView: Kingfisher.KFCrossPlatformImageView)
  public func remove(from imageView: Kingfisher.KFCrossPlatformImageView)
}
extension Kingfisher.Placeholder where Self : UIKit.UIView {
  public func add(to imageView: Kingfisher.KFCrossPlatformImageView)
  public func remove(from imageView: Kingfisher.KFCrossPlatformImageView)
}
public enum DiskStorage {
  public class Backend<T> where T : Kingfisher.DataTransformable {
    public var config: Kingfisher.DiskStorage.Config
    final public let directoryURL: Foundation.URL
    public init(config: Kingfisher.DiskStorage.Config) throws
    public func cacheFileURL(forKey key: Swift.String) -> Foundation.URL
    @objc deinit
  }
}
extension Kingfisher.DiskStorage {
  public struct Config {
    public var sizeLimit: Swift.UInt
    public var expiration: Kingfisher.StorageExpiration
    public var pathExtension: Swift.String?
    public var usesHashedFileName: Swift.Bool
    public init(name: Swift.String, sizeLimit: Swift.UInt, fileManager: Foundation.FileManager = .default, directory: Foundation.URL? = nil)
  }
}
public enum MemoryStorage {
  public class Backend<T> where T : Kingfisher.CacheCostCalculable {
    public var config: Kingfisher.MemoryStorage.Config {
      get
      set
    }
    public init(config: Kingfisher.MemoryStorage.Config)
    @objc deinit
  }
}
extension Kingfisher.MemoryStorage {
  public struct Config {
    public var totalCostLimit: Swift.Int
    public var countLimit: Swift.Int
    public var expiration: Kingfisher.StorageExpiration
    public let cleanInterval: Foundation.TimeInterval
    public init(totalCostLimit: Swift.Int, cleanInterval: Foundation.TimeInterval = 120)
  }
}
public struct AVAssetImageDataProvider : Kingfisher.ImageDataProvider {
  public enum AVAssetImageDataProviderError : Swift.Error {
    case userCancelled
    case invalidImage(_: CoreGraphics.CGImage?)
  }
  public let assetImageGenerator: AVFoundation.AVAssetImageGenerator
  public let time: CoreMedia.CMTime
  public var cacheKey: Swift.String {
    get
  }
  public init(assetImageGenerator: AVFoundation.AVAssetImageGenerator, time: CoreMedia.CMTime)
  public init(assetURL: Foundation.URL, time: CoreMedia.CMTime)
  public init(assetURL: Foundation.URL, seconds: Foundation.TimeInterval)
  public func data(handler: @escaping (Swift.Result<Foundation.Data, Swift.Error>) -> Swift.Void)
}
public enum ImageTransition {
  case none
  case fade(Foundation.TimeInterval)
  case flipFromLeft(Foundation.TimeInterval)
  case flipFromRight(Foundation.TimeInterval)
  case flipFromTop(Foundation.TimeInterval)
  case flipFromBottom(Foundation.TimeInterval)
  case custom(duration: Foundation.TimeInterval, options: UIKit.UIView.AnimationOptions, animations: ((UIKit.UIImageView, UIKit.UIImage) -> Swift.Void)?, completion: ((Swift.Bool) -> Swift.Void)?)
}
public struct ImageProgressive {
  public static let `default`: Kingfisher.ImageProgressive
  public init(isBlur: Swift.Bool, isFastestScan: Swift.Bool, scanInterval: Foundation.TimeInterval)
}
public enum ImageProcessItem {
  case image(Kingfisher.KFCrossPlatformImage)
  case data(Foundation.Data)
}
public protocol ImageProcessor {
  var identifier: Swift.String { get }
  @available(*, deprecated, message: "Deprecated. Implement the method with same name but with `KingfisherParsedOptionsInfo` instead.")
  func process(item: Kingfisher.ImageProcessItem, options: Kingfisher.KingfisherOptionsInfo) -> Kingfisher.KFCrossPlatformImage?
  func process(item: Kingfisher.ImageProcessItem, options: Kingfisher.KingfisherParsedOptionsInfo) -> Kingfisher.KFCrossPlatformImage?
}
extension Kingfisher.ImageProcessor {
  public func process(item: Kingfisher.ImageProcessItem, options: Kingfisher.KingfisherOptionsInfo) -> Kingfisher.KFCrossPlatformImage?
}
extension Kingfisher.ImageProcessor {
  public func append(another: Kingfisher.ImageProcessor) -> Kingfisher.ImageProcessor
}
public struct DefaultImageProcessor : Kingfisher.ImageProcessor {
  public static let `default`: Kingfisher.DefaultImageProcessor
  public let identifier: Swift.String
  public init()
  public func process(item: Kingfisher.ImageProcessItem, options: Kingfisher.KingfisherParsedOptionsInfo) -> Kingfisher.KFCrossPlatformImage?
}
public struct RectCorner : Swift.OptionSet {
  public let rawValue: Swift.Int
  public static let topLeft: Kingfisher.RectCorner
  public static let topRight: Kingfisher.RectCorner
  public static let bottomLeft: Kingfisher.RectCorner
  public static let bottomRight: Kingfisher.RectCorner
  public static let all: Kingfisher.RectCorner
  public init(rawValue: Swift.Int)
  public typealias ArrayLiteralElement = Kingfisher.RectCorner
  public typealias Element = Kingfisher.RectCorner
  public typealias RawValue = Swift.Int
}
public struct BlendImageProcessor : Kingfisher.ImageProcessor {
  public let identifier: Swift.String
  public let blendMode: CoreGraphics.CGBlendMode
  public let alpha: CoreGraphics.CGFloat
  public let backgroundColor: Kingfisher.KFCrossPlatformColor?
  public init(blendMode: CoreGraphics.CGBlendMode, alpha: CoreGraphics.CGFloat = 1.0, backgroundColor: Kingfisher.KFCrossPlatformColor? = nil)
  public func process(item: Kingfisher.ImageProcessItem, options: Kingfisher.KingfisherParsedOptionsInfo) -> Kingfisher.KFCrossPlatformImage?
}
public struct RoundCornerImageProcessor : Kingfisher.ImageProcessor {
  public enum Radius {
    case widthFraction(CoreGraphics.CGFloat)
    case heightFraction(CoreGraphics.CGFloat)
    case point(CoreGraphics.CGFloat)
  }
  public let identifier: Swift.String
  @available(*, deprecated, message: "Use `radius` property instead.")
  public var cornerRadius: CoreGraphics.CGFloat {
    get
  }
  public let radius: Kingfisher.RoundCornerImageProcessor.Radius
  public let roundingCorners: Kingfisher.RectCorner
  public let targetSize: CoreGraphics.CGSize?
  public let backgroundColor: Kingfisher.KFCrossPlatformColor?
  public init(cornerRadius: CoreGraphics.CGFloat, targetSize: CoreGraphics.CGSize? = nil, roundingCorners corners: Kingfisher.RectCorner = .all, backgroundColor: Kingfisher.KFCrossPlatformColor? = nil)
  public init(radius: Kingfisher.RoundCornerImageProcessor.Radius, targetSize: CoreGraphics.CGSize? = nil, roundingCorners corners: Kingfisher.RectCorner = .all, backgroundColor: Kingfisher.KFCrossPlatformColor? = nil)
  public func process(item: Kingfisher.ImageProcessItem, options: Kingfisher.KingfisherParsedOptionsInfo) -> Kingfisher.KFCrossPlatformImage?
}
public enum ContentMode {
  case none
  case aspectFit
  case aspectFill
  public static func == (a: Kingfisher.ContentMode, b: Kingfisher.ContentMode) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
public struct ResizingImageProcessor : Kingfisher.ImageProcessor {
  public let identifier: Swift.String
  public let referenceSize: CoreGraphics.CGSize
  public let targetContentMode: Kingfisher.ContentMode
  public init(referenceSize: CoreGraphics.CGSize, mode: Kingfisher.ContentMode = .none)
  public func process(item: Kingfisher.ImageProcessItem, options: Kingfisher.KingfisherParsedOptionsInfo) -> Kingfisher.KFCrossPlatformImage?
}
public struct BlurImageProcessor : Kingfisher.ImageProcessor {
  public let identifier: Swift.String
  public let blurRadius: CoreGraphics.CGFloat
  public init(blurRadius: CoreGraphics.CGFloat)
  public func process(item: Kingfisher.ImageProcessItem, options: Kingfisher.KingfisherParsedOptionsInfo) -> Kingfisher.KFCrossPlatformImage?
}
public struct OverlayImageProcessor : Kingfisher.ImageProcessor {
  public let identifier: Swift.String
  public let overlay: Kingfisher.KFCrossPlatformColor
  public let fraction: CoreGraphics.CGFloat
  public init(overlay: Kingfisher.KFCrossPlatformColor, fraction: CoreGraphics.CGFloat = 0.5)
  public func process(item: Kingfisher.ImageProcessItem, options: Kingfisher.KingfisherParsedOptionsInfo) -> Kingfisher.KFCrossPlatformImage?
}
public struct TintImageProcessor : Kingfisher.ImageProcessor {
  public let identifier: Swift.String
  public let tint: Kingfisher.KFCrossPlatformColor
  public init(tint: Kingfisher.KFCrossPlatformColor)
  public func process(item: Kingfisher.ImageProcessItem, options: Kingfisher.KingfisherParsedOptionsInfo) -> Kingfisher.KFCrossPlatformImage?
}
public struct ColorControlsProcessor : Kingfisher.ImageProcessor {
  public let identifier: Swift.String
  public let brightness: CoreGraphics.CGFloat
  public let contrast: CoreGraphics.CGFloat
  public let saturation: CoreGraphics.CGFloat
  public let inputEV: CoreGraphics.CGFloat
  public init(brightness: CoreGraphics.CGFloat, contrast: CoreGraphics.CGFloat, saturation: CoreGraphics.CGFloat, inputEV: CoreGraphics.CGFloat)
  public func process(item: Kingfisher.ImageProcessItem, options: Kingfisher.KingfisherParsedOptionsInfo) -> Kingfisher.KFCrossPlatformImage?
}
public struct BlackWhiteProcessor : Kingfisher.ImageProcessor {
  public let identifier: Swift.String
  public init()
  public func process(item: Kingfisher.ImageProcessItem, options: Kingfisher.KingfisherParsedOptionsInfo) -> Kingfisher.KFCrossPlatformImage?
}
public struct CroppingImageProcessor : Kingfisher.ImageProcessor {
  public let identifier: Swift.String
  public let size: CoreGraphics.CGSize
  public let anchor: CoreGraphics.CGPoint
  public init(size: CoreGraphics.CGSize, anchor: CoreGraphics.CGPoint = CGPoint(x: 0.5, y: 0.5))
  public func process(item: Kingfisher.ImageProcessItem, options: Kingfisher.KingfisherParsedOptionsInfo) -> Kingfisher.KFCrossPlatformImage?
}
public struct DownsamplingImageProcessor : Kingfisher.ImageProcessor {
  public let size: CoreGraphics.CGSize
  public let identifier: Swift.String
  public init(size: CoreGraphics.CGSize)
  public func process(item: Kingfisher.ImageProcessItem, options: Kingfisher.KingfisherParsedOptionsInfo) -> Kingfisher.KFCrossPlatformImage?
}
@available(*, deprecated, renamed: "|>", message: "Will be removed soon. Use `|>` instead.")
public func >> (left: Kingfisher.ImageProcessor, right: Kingfisher.ImageProcessor) -> Kingfisher.ImageProcessor
infix operator |> : AdditionPrecedence
public func |> (left: Kingfisher.ImageProcessor, right: Kingfisher.ImageProcessor) -> Kingfisher.ImageProcessor
extension Kingfisher.KingfisherWrapper where Base : UIKit.UIImage {
  public var normalized: Kingfisher.KFCrossPlatformImage {
    get
  }
}
extension Kingfisher.KingfisherWrapper where Base : UIKit.UIImage {
  public func pngRepresentation() -> Foundation.Data?
  public func jpegRepresentation(compressionQuality: CoreGraphics.CGFloat) -> Foundation.Data?
  public func gifRepresentation() -> Foundation.Data?
  public func data(format: Kingfisher.ImageFormat, compressionQuality: CoreGraphics.CGFloat = 1.0) -> Foundation.Data?
}
extension Kingfisher.KingfisherWrapper where Base : UIKit.UIImage {
  public static func animatedImage(data: Foundation.Data, options: Kingfisher.ImageCreatingOptions) -> Kingfisher.KFCrossPlatformImage?
  public static func image(data: Foundation.Data, options: Kingfisher.ImageCreatingOptions) -> Kingfisher.KFCrossPlatformImage?
  public static func downsampledImage(data: Foundation.Data, to pointSize: CoreGraphics.CGSize, scale: CoreGraphics.CGFloat) -> Kingfisher.KFCrossPlatformImage?
}
extension Swift.String : Kingfisher.KingfisherCompatibleValue {
}
public protocol ImageDownloaderDelegate : AnyObject {
  func imageDownloader(_ downloader: Kingfisher.ImageDownloader, willDownloadImageForURL url: Foundation.URL, with request: Foundation.URLRequest?)
  func imageDownloader(_ downloader: Kingfisher.ImageDownloader, didFinishDownloadingImageForURL url: Foundation.URL, with response: Foundation.URLResponse?, error: Swift.Error?)
  func imageDownloader(_ downloader: Kingfisher.ImageDownloader, didDownload data: Foundation.Data, for url: Foundation.URL) -> Foundation.Data?
  func imageDownloader(_ downloader: Kingfisher.ImageDownloader, didDownload image: Kingfisher.KFCrossPlatformImage, for url: Foundation.URL, with response: Foundation.URLResponse?)
  func isValidStatusCode(_ code: Swift.Int, for downloader: Kingfisher.ImageDownloader) -> Swift.Bool
}
extension Kingfisher.ImageDownloaderDelegate {
  public func imageDownloader(_ downloader: Kingfisher.ImageDownloader, willDownloadImageForURL url: Foundation.URL, with request: Foundation.URLRequest?)
  public func imageDownloader(_ downloader: Kingfisher.ImageDownloader, didFinishDownloadingImageForURL url: Foundation.URL, with response: Foundation.URLResponse?, error: Swift.Error?)
  public func imageDownloader(_ downloader: Kingfisher.ImageDownloader, didDownload image: Kingfisher.KFCrossPlatformImage, for url: Foundation.URL, with response: Foundation.URLResponse?)
  public func isValidStatusCode(_ code: Swift.Int, for downloader: Kingfisher.ImageDownloader) -> Swift.Bool
  public func imageDownloader(_ downloader: Kingfisher.ImageDownloader, didDownload data: Foundation.Data, for url: Foundation.URL) -> Foundation.Data?
}
public typealias KFCrossPlatformImage = UIKit.UIImage
public typealias KFCrossPlatformColor = UIKit.UIColor
public typealias KFCrossPlatformImageView = UIKit.UIImageView
public typealias KFCrossPlatformView = UIKit.UIView
public typealias KFCrossPlatformButton = UIKit.UIButton
public struct KingfisherWrapper<Base> {
  public let base: Base
  public init(_ base: Base)
}
public protocol KingfisherCompatible : AnyObject {
}
public protocol KingfisherCompatibleValue {
}
extension Kingfisher.KingfisherCompatible {
  public var kf: Kingfisher.KingfisherWrapper<Self> {
    get
    set
  }
}
extension Kingfisher.KingfisherCompatibleValue {
  public var kf: Kingfisher.KingfisherWrapper<Self> {
    get
    set
  }
}
extension UIKit.UIImage : Kingfisher.KingfisherCompatible {
}
extension UIKit.UIImageView : Kingfisher.KingfisherCompatible {
}
extension UIKit.UIButton : Kingfisher.KingfisherCompatible {
}
extension UIKit.NSTextAttachment : Kingfisher.KingfisherCompatible {
}
public enum CallbackQueue {
  case mainAsync
  case mainCurrentOrAsync
  case untouch
  case dispatch(Dispatch.DispatchQueue)
  public func execute(_ block: @escaping () -> Swift.Void)
}
public struct FormatIndicatedCacheSerializer : Kingfisher.CacheSerializer {
  public static let png: Kingfisher.FormatIndicatedCacheSerializer
  public static let jpeg: Kingfisher.FormatIndicatedCacheSerializer
  public static func jpeg(compressionQuality: CoreGraphics.CGFloat) -> Kingfisher.FormatIndicatedCacheSerializer
  public static let gif: Kingfisher.FormatIndicatedCacheSerializer
  public func data(with image: Kingfisher.KFCrossPlatformImage, original: Foundation.Data?) -> Foundation.Data?
  public func image(with data: Foundation.Data, options: Kingfisher.KingfisherParsedOptionsInfo) -> Kingfisher.KFCrossPlatformImage?
}
extension CoreGraphics.CGSize : Kingfisher.KingfisherCompatibleValue {
}
extension Kingfisher.KingfisherWrapper where Base == CoreGraphics.CGSize {
  public func resize(to size: CoreGraphics.CGSize, for contentMode: Kingfisher.ContentMode) -> CoreGraphics.CGSize
  public func constrained(_ size: CoreGraphics.CGSize) -> CoreGraphics.CGSize
  public func filling(_ size: CoreGraphics.CGSize) -> CoreGraphics.CGSize
  public func constrainedRect(for size: CoreGraphics.CGSize, anchor: CoreGraphics.CGPoint) -> CoreGraphics.CGRect
}
public typealias DownloadProgressBlock = ((_ receivedSize: Swift.Int64, _ totalSize: Swift.Int64) -> Swift.Void)
public struct RetrieveImageResult {
  public let image: Kingfisher.KFCrossPlatformImage
  public let cacheType: Kingfisher.CacheType
  public let source: Kingfisher.Source
  public let originalSource: Kingfisher.Source
}
public struct PropagationError {
  public let source: Kingfisher.Source
  public let error: Kingfisher.KingfisherError
}
public typealias DownloadTaskUpdatedBlock = ((_ newTask: Kingfisher.DownloadTask?) -> Swift.Void)
public class KingfisherManager {
  public static let shared: Kingfisher.KingfisherManager
  public var cache: Kingfisher.ImageCache
  public var downloader: Kingfisher.ImageDownloader
  public var defaultOptions: Kingfisher.KingfisherOptionsInfo
  public init(downloader: Kingfisher.ImageDownloader, cache: Kingfisher.ImageCache)
  @discardableResult
  public func retrieveImage(with resource: Kingfisher.Resource, options: Kingfisher.KingfisherOptionsInfo? = nil, progressBlock: Kingfisher.DownloadProgressBlock? = nil, downloadTaskUpdated: Kingfisher.DownloadTaskUpdatedBlock? = nil, completionHandler: ((Swift.Result<Kingfisher.RetrieveImageResult, Kingfisher.KingfisherError>) -> Swift.Void)?) -> Kingfisher.DownloadTask?
  public func retrieveImage(with source: Kingfisher.Source, options: Kingfisher.KingfisherOptionsInfo? = nil, progressBlock: Kingfisher.DownloadProgressBlock? = nil, downloadTaskUpdated: Kingfisher.DownloadTaskUpdatedBlock? = nil, completionHandler: ((Swift.Result<Kingfisher.RetrieveImageResult, Kingfisher.KingfisherError>) -> Swift.Void)?) -> Kingfisher.DownloadTask?
  @objc deinit
}
public protocol ImageDownloadRequestModifier {
  func modified(for request: Foundation.URLRequest) -> Foundation.URLRequest?
}
public struct AnyModifier : Kingfisher.ImageDownloadRequestModifier {
  public func modified(for request: Foundation.URLRequest) -> Foundation.URLRequest?
  public init(modify: @escaping (Foundation.URLRequest) -> Foundation.URLRequest?)
}
public typealias IndicatorView = UIKit.UIView
public enum IndicatorType {
  case none
  case activity
  case image(imageData: Foundation.Data)
  case custom(indicator: Kingfisher.Indicator)
}
public protocol Indicator {
  func startAnimatingView()
  func stopAnimatingView()
  var centerOffset: CoreGraphics.CGPoint { get }
  var view: Kingfisher.IndicatorView { get }
  func sizeStrategy(in imageView: Kingfisher.KFCrossPlatformImageView) -> Kingfisher.IndicatorSizeStrategy
}
public enum IndicatorSizeStrategy {
  case intrinsicSize
  case full
  case size(CoreGraphics.CGSize)
}
extension Kingfisher.Indicator {
  public var centerOffset: CoreGraphics.CGPoint {
    get
  }
  public func sizeStrategy(in imageView: Kingfisher.KFCrossPlatformImageView) -> Kingfisher.IndicatorSizeStrategy
}
public struct ImageLoadingResult {
  public let image: Kingfisher.KFCrossPlatformImage
  public let url: Foundation.URL?
  public let originalData: Foundation.Data
}
public struct DownloadTask {
  public let sessionTask: Kingfisher.SessionDataTask
  public let cancelToken: Kingfisher.SessionDataTask.CancelToken
  public func cancel()
}
open class ImageDownloader {
  public static let `default`: Kingfisher.ImageDownloader
  open var downloadTimeout: Swift.Double
  open var trustedHosts: Swift.Set<Swift.String>?
  open var sessionConfiguration: Foundation.URLSessionConfiguration {
    get
    set
  }
  open var requestsUsePipelining: Swift.Bool
  weak open var delegate: Kingfisher.ImageDownloaderDelegate?
  weak open var authenticationChallengeResponder: Kingfisher.AuthenticationChallengeResponsable?
  public init(name: Swift.String)
  @objc deinit
  @discardableResult
  open func downloadImage(with url: Foundation.URL, options: Kingfisher.KingfisherParsedOptionsInfo, completionHandler: ((Swift.Result<Kingfisher.ImageLoadingResult, Kingfisher.KingfisherError>) -> Swift.Void)? = nil) -> Kingfisher.DownloadTask?
  @discardableResult
  open func downloadImage(with url: Foundation.URL, options: Kingfisher.KingfisherOptionsInfo? = nil, progressBlock: Kingfisher.DownloadProgressBlock? = nil, completionHandler: ((Swift.Result<Kingfisher.ImageLoadingResult, Kingfisher.KingfisherError>) -> Swift.Void)? = nil) -> Kingfisher.DownloadTask?
}
extension Kingfisher.ImageDownloader {
  public func cancelAll()
  public func cancel(url: Foundation.URL)
}
extension Kingfisher.ImageDownloader : Kingfisher.AuthenticationChallengeResponsable {
}
extension Kingfisher.ImageDownloader : Kingfisher.ImageDownloaderDelegate {
}
@_hasMissingDesignatedInitializers public class RetryContext {
  final public let source: Kingfisher.Source
  final public let error: Kingfisher.KingfisherError
  public var retriedCount: Swift.Int
  public var userInfo: Any? {
    get
  }
  @objc deinit
}
public enum RetryDecision {
  case retry(userInfo: Any?)
  case stop
}
public protocol RetryStrategy {
  func retry(context: Kingfisher.RetryContext, retryHandler: @escaping (Kingfisher.RetryDecision) -> Swift.Void)
}
public struct DelayRetryStrategy : Kingfisher.RetryStrategy {
  public enum Interval {
    case seconds(Foundation.TimeInterval)
    case accumulated(Foundation.TimeInterval)
    case custom(block: (_ retriedCount: Swift.Int) -> Foundation.TimeInterval)
  }
  public let maxRetryCount: Swift.Int
  public let retryInterval: Kingfisher.DelayRetryStrategy.Interval
  public init(maxRetryCount: Swift.Int, retryInterval: Kingfisher.DelayRetryStrategy.Interval = .seconds(3))
  public func retry(context: Kingfisher.RetryContext, retryHandler: @escaping (Kingfisher.RetryDecision) -> Swift.Void)
}
public enum ImageFormat {
  case unknown
  case PNG
  case JPEG
  case GIF
  public enum JPEGMarker {
    case SOF0
    case SOF2
    case DHT
    case DQT
    case DRI
    case SOS
    case RSTn(Swift.UInt8)
    case APPn
    case COM
    case EOI
  }
  public static func == (a: Kingfisher.ImageFormat, b: Kingfisher.ImageFormat) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
extension Foundation.Data : Kingfisher.KingfisherCompatibleValue {
}
extension Kingfisher.KingfisherWrapper where Base == Foundation.Data {
  public var imageFormat: Kingfisher.ImageFormat {
    get
  }
  public func contains(jpeg marker: Kingfisher.ImageFormat.JPEGMarker) -> Swift.Bool
}
extension Kingfisher.KingfisherWrapper where Base : UIKit.NSTextAttachment {
  @discardableResult
  public func setImage(with source: Kingfisher.Source?, attributedView: Kingfisher.KFCrossPlatformView, placeholder: Kingfisher.KFCrossPlatformImage? = nil, options: Kingfisher.KingfisherOptionsInfo? = nil, progressBlock: Kingfisher.DownloadProgressBlock? = nil, completionHandler: ((Swift.Result<Kingfisher.RetrieveImageResult, Kingfisher.KingfisherError>) -> Swift.Void)? = nil) -> Kingfisher.DownloadTask?
  @discardableResult
  public func setImage(with resource: Kingfisher.Resource?, attributedView: Kingfisher.KFCrossPlatformView, placeholder: Kingfisher.KFCrossPlatformImage? = nil, options: Kingfisher.KingfisherOptionsInfo? = nil, progressBlock: Kingfisher.DownloadProgressBlock? = nil, completionHandler: ((Swift.Result<Kingfisher.RetrieveImageResult, Kingfisher.KingfisherError>) -> Swift.Void)? = nil) -> Kingfisher.DownloadTask?
  public func cancelDownloadTask()
}
extension Kingfisher.KingfisherWrapper where Base : UIKit.NSTextAttachment {
  public var taskIdentifier: Kingfisher.Source.Identifier.Value? {
    get
  }
}
extension Foundation.NSNotification.Name {
  public static let KingfisherDidCleanDiskCache: Foundation.Notification.Name
}
public let KingfisherDiskCacheCleanedHashKey: Swift.String
public enum CacheType {
  case none
  case memory
  case disk
  public var cached: Swift.Bool {
    get
  }
  public static func == (a: Kingfisher.CacheType, b: Kingfisher.CacheType) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
public struct CacheStoreResult {
  public let memoryCacheResult: Swift.Result<(), Swift.Never>
  public let diskCacheResult: Swift.Result<(), Kingfisher.KingfisherError>
}
extension UIKit.UIImage : Kingfisher.CacheCostCalculable {
  public var cacheCost: Swift.Int {
    get
  }
}
extension Foundation.Data : Kingfisher.DataTransformable {
  public func toData() throws -> Foundation.Data
  public static func fromData(_ data: Foundation.Data) throws -> Foundation.Data
  public static let empty: Foundation.Data
}
public enum ImageCacheResult {
  case disk(Kingfisher.KFCrossPlatformImage)
  case memory(Kingfisher.KFCrossPlatformImage)
  case none
  public var image: Kingfisher.KFCrossPlatformImage? {
    get
  }
  public var cacheType: Kingfisher.CacheType {
    get
  }
}
open class ImageCache {
  public static let `default`: Kingfisher.ImageCache
  final public let memoryStorage: Kingfisher.MemoryStorage.Backend<Kingfisher.KFCrossPlatformImage>
  final public let diskStorage: Kingfisher.DiskStorage.Backend<Foundation.Data>
  public typealias DiskCachePathClosure = (Foundation.URL, Swift.String) -> Foundation.URL
  public init(memoryStorage: Kingfisher.MemoryStorage.Backend<Kingfisher.KFCrossPlatformImage>, diskStorage: Kingfisher.DiskStorage.Backend<Foundation.Data>)
  convenience public init(name: Swift.String)
  convenience public init(name: Swift.String, cacheDirectoryURL: Foundation.URL?, diskCachePathClosure: Kingfisher.ImageCache.DiskCachePathClosure? = nil) throws
  @objc deinit
  open func store(_ image: Kingfisher.KFCrossPlatformImage, original: Foundation.Data? = nil, forKey key: Swift.String, options: Kingfisher.KingfisherParsedOptionsInfo, toDisk: Swift.Bool = true, completionHandler: ((Kingfisher.CacheStoreResult) -> Swift.Void)? = nil)
  open func store(_ image: Kingfisher.KFCrossPlatformImage, original: Foundation.Data? = nil, forKey key: Swift.String, processorIdentifier identifier: Swift.String = "", cacheSerializer serializer: Kingfisher.CacheSerializer = DefaultCacheSerializer.default, toDisk: Swift.Bool = true, callbackQueue: Kingfisher.CallbackQueue = .untouch, completionHandler: ((Kingfisher.CacheStoreResult) -> Swift.Void)? = nil)
  open func storeToDisk(_ data: Foundation.Data, forKey key: Swift.String, processorIdentifier identifier: Swift.String = "", expiration: Kingfisher.StorageExpiration? = nil, callbackQueue: Kingfisher.CallbackQueue = .untouch, completionHandler: ((Kingfisher.CacheStoreResult) -> Swift.Void)? = nil)
  open func removeImage(forKey key: Swift.String, processorIdentifier identifier: Swift.String = "", fromMemory: Swift.Bool = true, fromDisk: Swift.Bool = true, callbackQueue: Kingfisher.CallbackQueue = .untouch, completionHandler: (() -> Swift.Void)? = nil)
  open func retrieveImage(forKey key: Swift.String, options: Kingfisher.KingfisherOptionsInfo? = nil, callbackQueue: Kingfisher.CallbackQueue = .mainCurrentOrAsync, completionHandler: ((Swift.Result<Kingfisher.ImageCacheResult, Kingfisher.KingfisherError>) -> Swift.Void)?)
  open func retrieveImageInMemoryCache(forKey key: Swift.String, options: Kingfisher.KingfisherOptionsInfo? = nil) -> Kingfisher.KFCrossPlatformImage?
  open func retrieveImageInDiskCache(forKey key: Swift.String, options: Kingfisher.KingfisherOptionsInfo? = nil, callbackQueue: Kingfisher.CallbackQueue = .untouch, completionHandler: @escaping (Swift.Result<Kingfisher.KFCrossPlatformImage?, Kingfisher.KingfisherError>) -> Swift.Void)
  public func clearCache(completion handler: (() -> Swift.Void)? = nil)
  @objc public func clearMemoryCache()
  open func clearDiskCache(completion handler: (() -> Swift.Void)? = nil)
  open func cleanExpiredCache(completion handler: (() -> Swift.Void)? = nil)
  open func cleanExpiredMemoryCache()
  open func cleanExpiredDiskCache(completion handler: (() -> Swift.Void)? = nil)
  @objc public func backgroundCleanExpiredDiskCache()
  open func imageCachedType(forKey key: Swift.String, processorIdentifier identifier: Swift.String = DefaultImageProcessor.default.identifier) -> Kingfisher.CacheType
  public func isCached(forKey key: Swift.String, processorIdentifier identifier: Swift.String = DefaultImageProcessor.default.identifier) -> Swift.Bool
  open func hash(forKey key: Swift.String, processorIdentifier identifier: Swift.String = DefaultImageProcessor.default.identifier) -> Swift.String
  open func calculateDiskStorageSize(completion handler: @escaping ((Swift.Result<Swift.UInt, Kingfisher.KingfisherError>) -> Swift.Void))
  open func cachePath(forKey key: Swift.String, processorIdentifier identifier: Swift.String = DefaultImageProcessor.default.identifier) -> Swift.String
}
extension UIKit.UIApplication : Kingfisher.KingfisherCompatible {
}
extension Kingfisher.KingfisherWrapper where Base : UIKit.UIApplication {
  public static var shared: UIKit.UIApplication? {
    get
  }
}
extension Kingfisher.ImageCache {
  @available(*, deprecated, renamed: "init(name:cacheDirectoryURL:diskCachePathClosure:)", message: "Use `init(name:cacheDirectoryURL:diskCachePathClosure:)` instead")
  convenience public init(name: Swift.String, path: Swift.String?, diskCachePathClosure: Kingfisher.ImageCache.DiskCachePathClosure? = nil) throws
}
public typealias PrefetcherProgressBlock = ((_ skippedResources: [Kingfisher.Resource], _ failedResources: [Kingfisher.Resource], _ completedResources: [Kingfisher.Resource]) -> Swift.Void)
public typealias PrefetcherSourceProgressBlock = ((_ skippedSources: [Kingfisher.Source], _ failedSources: [Kingfisher.Source], _ completedSources: [Kingfisher.Source]) -> Swift.Void)
public typealias PrefetcherCompletionHandler = ((_ skippedResources: [Kingfisher.Resource], _ failedResources: [Kingfisher.Resource], _ completedResources: [Kingfisher.Resource]) -> Swift.Void)
public typealias PrefetcherSourceCompletionHandler = ((_ skippedSources: [Kingfisher.Source], _ failedSources: [Kingfisher.Source], _ completedSources: [Kingfisher.Source]) -> Swift.Void)
@_hasMissingDesignatedInitializers public class ImagePrefetcher : Swift.CustomStringConvertible {
  public var description: Swift.String {
    get
  }
  public var maxConcurrentDownloads: Swift.Int
  convenience public init(urls: [Foundation.URL], options: Kingfisher.KingfisherOptionsInfo? = nil, progressBlock: Kingfisher.PrefetcherProgressBlock? = nil, completionHandler: Kingfisher.PrefetcherCompletionHandler? = nil)
  convenience public init(resources: [Kingfisher.Resource], options: Kingfisher.KingfisherOptionsInfo? = nil, progressBlock: Kingfisher.PrefetcherProgressBlock? = nil, completionHandler: Kingfisher.PrefetcherCompletionHandler? = nil)
  convenience public init(sources: [Kingfisher.Source], options: Kingfisher.KingfisherOptionsInfo? = nil, progressBlock: Kingfisher.PrefetcherSourceProgressBlock? = nil, completionHandler: Kingfisher.PrefetcherSourceCompletionHandler? = nil)
  public func start()
  public func stop()
  @objc deinit
}
extension Kingfisher.KingfisherWrapper where Base : UIKit.UIImageView {
  @discardableResult
  public func setImage(with source: Kingfisher.Source?, placeholder: Kingfisher.Placeholder? = nil, options: Kingfisher.KingfisherOptionsInfo? = nil, progressBlock: Kingfisher.DownloadProgressBlock? = nil, completionHandler: ((Swift.Result<Kingfisher.RetrieveImageResult, Kingfisher.KingfisherError>) -> Swift.Void)? = nil) -> Kingfisher.DownloadTask?
  @discardableResult
  public func setImage(with resource: Kingfisher.Resource?, placeholder: Kingfisher.Placeholder? = nil, options: Kingfisher.KingfisherOptionsInfo? = nil, progressBlock: Kingfisher.DownloadProgressBlock? = nil, completionHandler: ((Swift.Result<Kingfisher.RetrieveImageResult, Kingfisher.KingfisherError>) -> Swift.Void)? = nil) -> Kingfisher.DownloadTask?
  @discardableResult
  public func setImage(with provider: Kingfisher.ImageDataProvider?, placeholder: Kingfisher.Placeholder? = nil, options: Kingfisher.KingfisherOptionsInfo? = nil, progressBlock: Kingfisher.DownloadProgressBlock? = nil, completionHandler: ((Swift.Result<Kingfisher.RetrieveImageResult, Kingfisher.KingfisherError>) -> Swift.Void)? = nil) -> Kingfisher.DownloadTask?
  public func cancelDownloadTask()
}
extension Kingfisher.KingfisherWrapper where Base : UIKit.UIImageView {
  public var taskIdentifier: Kingfisher.Source.Identifier.Value? {
    get
  }
  public var indicatorType: Kingfisher.IndicatorType {
    get
    set
  }
  public var indicator: Kingfisher.Indicator? {
    get
  }
  public var placeholder: Kingfisher.Placeholder? {
    get
  }
}
extension Kingfisher.KingfisherWrapper where Base : UIKit.UIImageView {
  @available(*, deprecated, message: "Use `taskIdentifier` instead to identify a setting task.")
  public var webURL: Foundation.URL? {
    get
  }
}
extension Kingfisher.KingfisherWrapper where Base : UIKit.UIButton {
  @discardableResult
  public func setImage(with source: Kingfisher.Source?, for state: UIKit.UIControl.State, placeholder: UIKit.UIImage? = nil, options: Kingfisher.KingfisherOptionsInfo? = nil, progressBlock: Kingfisher.DownloadProgressBlock? = nil, completionHandler: ((Swift.Result<Kingfisher.RetrieveImageResult, Kingfisher.KingfisherError>) -> Swift.Void)? = nil) -> Kingfisher.DownloadTask?
  @discardableResult
  public func setImage(with resource: Kingfisher.Resource?, for state: UIKit.UIControl.State, placeholder: UIKit.UIImage? = nil, options: Kingfisher.KingfisherOptionsInfo? = nil, progressBlock: Kingfisher.DownloadProgressBlock? = nil, completionHandler: ((Swift.Result<Kingfisher.RetrieveImageResult, Kingfisher.KingfisherError>) -> Swift.Void)? = nil) -> Kingfisher.DownloadTask?
  public func cancelImageDownloadTask()
  @discardableResult
  public func setBackgroundImage(with source: Kingfisher.Source?, for state: UIKit.UIControl.State, placeholder: UIKit.UIImage? = nil, options: Kingfisher.KingfisherOptionsInfo? = nil, progressBlock: Kingfisher.DownloadProgressBlock? = nil, completionHandler: ((Swift.Result<Kingfisher.RetrieveImageResult, Kingfisher.KingfisherError>) -> Swift.Void)? = nil) -> Kingfisher.DownloadTask?
  @discardableResult
  public func setBackgroundImage(with resource: Kingfisher.Resource?, for state: UIKit.UIControl.State, placeholder: UIKit.UIImage? = nil, options: Kingfisher.KingfisherOptionsInfo? = nil, progressBlock: Kingfisher.DownloadProgressBlock? = nil, completionHandler: ((Swift.Result<Kingfisher.RetrieveImageResult, Kingfisher.KingfisherError>) -> Swift.Void)? = nil) -> Kingfisher.DownloadTask?
  public func cancelBackgroundImageDownloadTask()
}
extension Kingfisher.KingfisherWrapper where Base : UIKit.UIButton {
  public func taskIdentifier(for state: UIKit.UIControl.State) -> Kingfisher.Source.Identifier.Value?
}
extension Kingfisher.KingfisherWrapper where Base : UIKit.UIButton {
  public func backgroundTaskIdentifier(for state: UIKit.UIControl.State) -> Kingfisher.Source.Identifier.Value?
}
extension Kingfisher.KingfisherWrapper where Base : UIKit.UIButton {
  @available(*, deprecated, message: "Use `taskIdentifier` instead to identify a setting task.")
  public func webURL(for state: UIKit.UIControl.State) -> Foundation.URL?
  @available(*, deprecated, message: "Use `backgroundTaskIdentifier` instead to identify a setting task.")
  public func backgroundWebURL(for state: UIKit.UIControl.State) -> Foundation.URL?
}
public struct ImageCreatingOptions {
  public let scale: CoreGraphics.CGFloat
  public let duration: Foundation.TimeInterval
  public let preloadAll: Swift.Bool
  public let onlyFirstFrame: Swift.Bool
  public init(scale: CoreGraphics.CGFloat = 1.0, duration: Foundation.TimeInterval = 0.0, preloadAll: Swift.Bool = false, onlyFirstFrame: Swift.Bool = false)
}
public enum Source {
  public enum Identifier {
    public typealias Value = Swift.UInt
  }
  case network(Kingfisher.Resource)
  case provider(Kingfisher.ImageDataProvider)
  public var cacheKey: Swift.String {
    get
  }
  public var url: Foundation.URL? {
    get
  }
}
public typealias Transformer = (CoreImage.CIImage) -> CoreImage.CIImage?
public protocol CIImageProcessor : Kingfisher.ImageProcessor {
  var filter: Kingfisher.Filter { get }
}
extension Kingfisher.CIImageProcessor {
  public func process(item: Kingfisher.ImageProcessItem, options: Kingfisher.KingfisherParsedOptionsInfo) -> Kingfisher.KFCrossPlatformImage?
}
public struct Filter {
  public init(transform: @escaping Kingfisher.Transformer)
  public static var tint: (_ color: Kingfisher.KFCrossPlatformColor) -> Kingfisher.Filter
  public typealias ColorElement = (CoreGraphics.CGFloat, CoreGraphics.CGFloat, CoreGraphics.CGFloat, CoreGraphics.CGFloat)
  public static var colorControl: (_ arg: Kingfisher.Filter.ColorElement) -> Kingfisher.Filter
}
extension Kingfisher.KingfisherWrapper where Base : UIKit.UIImage {
  public func apply(_ filter: Kingfisher.Filter) -> Kingfisher.KFCrossPlatformImage
}
public protocol Resource {
  var cacheKey: Swift.String { get }
  var downloadURL: Foundation.URL { get }
}
extension Kingfisher.Resource {
  public func convertToSource() -> Kingfisher.Source
}
public struct ImageResource : Kingfisher.Resource {
  public init(downloadURL: Foundation.URL, cacheKey: Swift.String? = nil)
  public let cacheKey: Swift.String
  public let downloadURL: Foundation.URL
}
extension Foundation.URL : Kingfisher.Resource {
  public var cacheKey: Swift.String {
    get
  }
  public var downloadURL: Foundation.URL {
    get
  }
}
public protocol ImageDownloadRedirectHandler {
  func handleHTTPRedirection(for task: Kingfisher.SessionDataTask, response: Foundation.HTTPURLResponse, newRequest: Foundation.URLRequest, completionHandler: @escaping (Foundation.URLRequest?) -> Swift.Void)
}
public struct AnyRedirectHandler : Kingfisher.ImageDownloadRedirectHandler {
  public func handleHTTPRedirection(for task: Kingfisher.SessionDataTask, response: Foundation.HTTPURLResponse, newRequest: Foundation.URLRequest, completionHandler: @escaping (Foundation.URLRequest?) -> Swift.Void)
  public init(handle: @escaping (Kingfisher.SessionDataTask, Foundation.HTTPURLResponse, Foundation.URLRequest, (Foundation.URLRequest?) -> Swift.Void) -> Swift.Void)
}
extension Kingfisher.KingfisherWrapper where Base : UIKit.UIImage {
  public func image(withBlendMode blendMode: CoreGraphics.CGBlendMode, alpha: CoreGraphics.CGFloat = 1.0, backgroundColor: Kingfisher.KFCrossPlatformColor? = nil) -> Kingfisher.KFCrossPlatformImage
  public func image(withRoundRadius radius: CoreGraphics.CGFloat, fit size: CoreGraphics.CGSize, roundingCorners corners: Kingfisher.RectCorner = .all, backgroundColor: Kingfisher.KFCrossPlatformColor? = nil) -> Kingfisher.KFCrossPlatformImage
  public func resize(to size: CoreGraphics.CGSize) -> Kingfisher.KFCrossPlatformImage
  public func resize(to targetSize: CoreGraphics.CGSize, for contentMode: Kingfisher.ContentMode) -> Kingfisher.KFCrossPlatformImage
  public func crop(to size: CoreGraphics.CGSize, anchorOn anchor: CoreGraphics.CGPoint) -> Kingfisher.KFCrossPlatformImage
  public func blurred(withRadius radius: CoreGraphics.CGFloat) -> Kingfisher.KFCrossPlatformImage
  public func overlaying(with color: Kingfisher.KFCrossPlatformColor, fraction: CoreGraphics.CGFloat) -> Kingfisher.KFCrossPlatformImage
  public func tinted(with color: Kingfisher.KFCrossPlatformColor) -> Kingfisher.KFCrossPlatformImage
  public func adjusted(brightness: CoreGraphics.CGFloat, contrast: CoreGraphics.CGFloat, saturation: CoreGraphics.CGFloat, inputEV: CoreGraphics.CGFloat) -> Kingfisher.KFCrossPlatformImage
  public func scaled(to scale: CoreGraphics.CGFloat) -> Kingfisher.KFCrossPlatformImage
}
extension Kingfisher.KingfisherWrapper where Base : UIKit.UIImage {
  public var decoded: Kingfisher.KFCrossPlatformImage {
    get
  }
  public func decoded(scale: CoreGraphics.CGFloat) -> Kingfisher.KFCrossPlatformImage
}
@_hasMissingDesignatedInitializers public class SessionDataTask {
  public typealias CancelToken = Swift.Int
  public var mutableData: Foundation.Data {
    get
  }
  final public let task: Foundation.URLSessionDataTask
  @objc deinit
}
public enum KingfisherError : Swift.Error {
  public enum RequestErrorReason {
    case emptyRequest
    case invalidURL(request: Foundation.URLRequest)
    case taskCancelled(task: Kingfisher.SessionDataTask, token: Kingfisher.SessionDataTask.CancelToken)
  }
  public enum ResponseErrorReason {
    case invalidURLResponse(response: Foundation.URLResponse)
    case invalidHTTPStatusCode(response: Foundation.HTTPURLResponse)
    case URLSessionError(error: Swift.Error)
    case dataModifyingFailed(task: Kingfisher.SessionDataTask)
    case noURLResponse(task: Kingfisher.SessionDataTask)
  }
  public enum CacheErrorReason {
    case fileEnumeratorCreationFailed(url: Foundation.URL)
    case invalidFileEnumeratorContent(url: Foundation.URL)
    case invalidURLResource(error: Swift.Error, key: Swift.String, url: Foundation.URL)
    case cannotLoadDataFromDisk(url: Foundation.URL, error: Swift.Error)
    case cannotCreateDirectory(path: Swift.String, error: Swift.Error)
    case imageNotExisting(key: Swift.String)
    case cannotConvertToData(object: Any, error: Swift.Error)
    case cannotSerializeImage(image: Kingfisher.KFCrossPlatformImage?, original: Foundation.Data?, serializer: Kingfisher.CacheSerializer)
    case cannotCreateCacheFile(fileURL: Foundation.URL, key: Swift.String, data: Foundation.Data, error: Swift.Error)
    case cannotSetCacheFileAttribute(filePath: Swift.String, attributes: [Foundation.FileAttributeKey : Any], error: Swift.Error)
  }
  public enum ProcessorErrorReason {
    case processingFailed(processor: Kingfisher.ImageProcessor, item: Kingfisher.ImageProcessItem)
  }
  public enum ImageSettingErrorReason {
    case emptySource
    case notCurrentSourceTask(result: Kingfisher.RetrieveImageResult?, error: Swift.Error?, source: Kingfisher.Source)
    case dataProviderError(provider: Kingfisher.ImageDataProvider, error: Swift.Error)
    case alternativeSourcesExhausted([Kingfisher.PropagationError])
  }
  case requestError(reason: Kingfisher.KingfisherError.RequestErrorReason)
  case responseError(reason: Kingfisher.KingfisherError.ResponseErrorReason)
  case cacheError(reason: Kingfisher.KingfisherError.CacheErrorReason)
  case processorError(reason: Kingfisher.KingfisherError.ProcessorErrorReason)
  case imageSettingError(reason: Kingfisher.KingfisherError.ImageSettingErrorReason)
  public var isTaskCancelled: Swift.Bool {
    get
  }
  public func isInvalidResponseStatusCode(_ code: Swift.Int) -> Swift.Bool
  public var isInvalidResponseStatusCode: Swift.Bool {
    get
  }
  public var isNotCurrentTask: Swift.Bool {
    get
  }
}
extension Kingfisher.KingfisherError : Foundation.LocalizedError {
  public var errorDescription: Swift.String? {
    get
  }
}
extension Kingfisher.KingfisherError : Foundation.CustomNSError {
  public static let domain: Swift.String
  public var errorCode: Swift.Int {
    get
  }
}
extension Kingfisher.KingfisherWrapper where Base : UIKit.UIImage {
  @available(*, deprecated, message: "Will be removed soon. Pass parameters with `ImageCreatingOptions`, use `image(with:options:)` instead.")
  public static func image(data: Foundation.Data, scale: CoreGraphics.CGFloat, preloadAllAnimationData: Swift.Bool, onlyFirstFrame: Swift.Bool) -> Kingfisher.KFCrossPlatformImage?
  @available(*, deprecated, message: "Will be removed soon. Pass parameters with `ImageCreatingOptions`, use `animatedImage(with:options:)` instead.")
  public static func animated(with data: Foundation.Data, scale: CoreGraphics.CGFloat = 1.0, duration: Foundation.TimeInterval = 0.0, preloadAll: Swift.Bool, onlyFirstFrame: Swift.Bool = false) -> Kingfisher.KFCrossPlatformImage?
}
@available(*, deprecated, message: "Will be removed soon. Use `Result<RetrieveImageResult>` based callback instead")
public typealias CompletionHandler = ((_ image: Kingfisher.KFCrossPlatformImage?, _ error: Foundation.NSError?, _ cacheType: Kingfisher.CacheType, _ imageURL: Foundation.URL?) -> Swift.Void)
@available(*, deprecated, message: "Will be removed soon. Use `Result<ImageLoadingResult>` based callback instead")
public typealias ImageDownloaderCompletionHandler = ((_ image: Kingfisher.KFCrossPlatformImage?, _ error: Foundation.NSError?, _ url: Foundation.URL?, _ originalData: Foundation.Data?) -> Swift.Void)
@available(*, deprecated, message: "Will be removed soon. Use `DownloadTask` to cancel a task.")
extension Kingfisher.RetrieveImageTask {
  @available(*, deprecated, message: "RetrieveImageTask.empty will be removed soon. Use `nil` to represent a no task.")
  public static let empty: Kingfisher.RetrieveImageTask
}
extension Kingfisher.KingfisherManager {
  @available(*, deprecated, message: "Use `Result` based callback instead.")
  @discardableResult
  public func retrieveImage(with resource: Kingfisher.Resource, options: Kingfisher.KingfisherOptionsInfo?, progressBlock: Kingfisher.DownloadProgressBlock?, completionHandler: Kingfisher.CompletionHandler?) -> Kingfisher.DownloadTask?
}
extension Kingfisher.ImageDownloader {
  @available(*, deprecated, message: "Use `Result` based callback instead.")
  @discardableResult
  open func downloadImage(with url: Foundation.URL, retrieveImageTask: Kingfisher.RetrieveImageTask? = nil, options: Kingfisher.KingfisherOptionsInfo? = nil, progressBlock: Kingfisher.ImageDownloaderProgressBlock? = nil, completionHandler: Kingfisher.ImageDownloaderCompletionHandler?) -> Kingfisher.DownloadTask?
}
@available(*, deprecated, message: "RetrieveImageDownloadTask is removed. Use `DownloadTask` to cancel a task.")
public struct RetrieveImageDownloadTask {
}
@_hasMissingDesignatedInitializers @available(*, deprecated, message: "RetrieveImageTask is removed. Use `DownloadTask` to cancel a task.")
final public class RetrieveImageTask {
  @objc deinit
}
@available(*, deprecated, renamed: "DownloadProgressBlock", message: "Use `DownloadProgressBlock` instead.")
public typealias ImageDownloaderProgressBlock = Kingfisher.DownloadProgressBlock
extension Kingfisher.KingfisherWrapper where Base : UIKit.UIImageView {
  @available(*, deprecated, message: "Use `Result` based callback instead.")
  @discardableResult
  public func setImage(with resource: Kingfisher.Resource?, placeholder: Kingfisher.Placeholder? = nil, options: Kingfisher.KingfisherOptionsInfo? = nil, progressBlock: Kingfisher.DownloadProgressBlock? = nil, completionHandler: Kingfisher.CompletionHandler?) -> Kingfisher.DownloadTask?
}
extension Kingfisher.KingfisherWrapper where Base : UIKit.UIButton {
  @available(*, deprecated, message: "Use `Result` based callback instead.")
  @discardableResult
  public func setImage(with resource: Kingfisher.Resource?, for state: UIKit.UIControl.State, placeholder: UIKit.UIImage? = nil, options: Kingfisher.KingfisherOptionsInfo? = nil, progressBlock: Kingfisher.DownloadProgressBlock? = nil, completionHandler: Kingfisher.CompletionHandler?) -> Kingfisher.DownloadTask?
  @available(*, deprecated, message: "Use `Result` based callback instead.")
  @discardableResult
  public func setBackgroundImage(with resource: Kingfisher.Resource?, for state: UIKit.UIControl.State, placeholder: UIKit.UIImage? = nil, options: Kingfisher.KingfisherOptionsInfo? = nil, progressBlock: Kingfisher.DownloadProgressBlock? = nil, completionHandler: Kingfisher.CompletionHandler?) -> Kingfisher.DownloadTask?
}
extension Kingfisher.ImageCache {
  @available(*, deprecated, renamed: "memoryStorage.config.totalCostLimit", message: "Use `memoryStorage.config.totalCostLimit` instead.")
  open var maxMemoryCost: Swift.Int {
    get
    set
  }
  @available(*, deprecated, message: "Not needed anymore.")
  final public class func defaultDiskCachePathClosure(path: Swift.String?, cacheName: Swift.String) -> Swift.String
  @available(*, deprecated, renamed: "diskStorage.config.pathExtension", message: "Use `diskStorage.config.pathExtension` instead.")
  open var pathExtension: Swift.String? {
    get
    set
  }
  @available(*, deprecated, renamed: "diskStorage.directoryURL.absoluteString", message: "Use `diskStorage.directoryURL.absoluteString` instead.")
  public var diskCachePath: Swift.String {
    get
  }
  @available(*, deprecated, renamed: "diskStorage.config.sizeLimit", message: "Use `diskStorage.config.sizeLimit` instead.")
  open var maxDiskCacheSize: Swift.UInt {
    get
    set
  }
  @available(*, deprecated, renamed: "diskStorage.cacheFileURL(forKey:)", message: "Use `diskStorage.cacheFileURL(forKey:).path` instead.")
  open func cachePath(forComputedKey key: Swift.String) -> Swift.String
  @available(*, deprecated, renamed: "retrieveImageInDiskCache(forKey:options:callbackQueue:completionHandler:)", message: "Use `Result` based `retrieveImageInDiskCache(forKey:options:callbackQueue:completionHandler:)` instead.")
  open func retrieveImageInDiskCache(forKey key: Swift.String, options: Kingfisher.KingfisherOptionsInfo? = nil) -> Kingfisher.KFCrossPlatformImage?
  @available(*, deprecated, renamed: "retrieveImage(forKey:options:callbackQueue:completionHandler:)", message: "Use `Result` based `retrieveImage(forKey:options:callbackQueue:completionHandler:)` instead.")
  open func retrieveImage(forKey key: Swift.String, options: Kingfisher.KingfisherOptionsInfo?, completionHandler: ((Kingfisher.KFCrossPlatformImage?, Kingfisher.CacheType) -> Swift.Void)?)
  @available(*, deprecated, message: "Deprecated. Use `diskStorage.config.expiration` instead")
  open var maxCachePeriodInSecond: Foundation.TimeInterval {
    get
    set
  }
  @available(*, deprecated, message: "Use `Result` based callback instead.")
  open func store(_ image: Kingfisher.KFCrossPlatformImage, original: Foundation.Data? = nil, forKey key: Swift.String, processorIdentifier identifier: Swift.String = "", cacheSerializer serializer: Kingfisher.CacheSerializer = DefaultCacheSerializer.default, toDisk: Swift.Bool = true, completionHandler: (() -> Swift.Void)?)
  @available(*, deprecated, message: "Use the `Result`-based `calculateDiskStorageSize` instead.")
  open func calculateDiskCacheSize(completion handler: @escaping ((_ size: Swift.UInt) -> Swift.Void))
}
extension Swift.Collection where Self.Element == Kingfisher.KingfisherOptionsInfoItem {
  @available(*, deprecated, renamed: "callbackQueue", message: "Use `callbackQueue` instead.")
  public var callbackDispatchQueue: Dispatch.DispatchQueue {
    get
  }
}
@available(*, deprecated, renamed: "KingfisherError.domain", message: "Use `KingfisherError.domain` instead.")
public let KingfisherErrorDomain: Swift.String
@available(*, unavailable, message: "Use `.invalidHTTPStatusCode` or `isInvalidResponseStatusCode` of `KingfisherError` instead for the status code.")
public let KingfisherErrorStatusCodeKey: Swift.String
extension Swift.Collection where Self.Element == Kingfisher.KingfisherOptionsInfoItem {
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `targetCache` instead.")
  public var targetCache: Kingfisher.ImageCache? {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `originalCache` instead.")
  public var originalCache: Kingfisher.ImageCache? {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `downloader` instead.")
  public var downloader: Kingfisher.ImageDownloader? {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `transition` instead.")
  public var transition: Kingfisher.ImageTransition {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `downloadPriority` instead.")
  public var downloadPriority: Swift.Float {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `forceRefresh` instead.")
  public var forceRefresh: Swift.Bool {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `fromMemoryCacheOrRefresh` instead.")
  public var fromMemoryCacheOrRefresh: Swift.Bool {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `forceTransition` instead.")
  public var forceTransition: Swift.Bool {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `cacheMemoryOnly` instead.")
  public var cacheMemoryOnly: Swift.Bool {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `waitForCache` instead.")
  public var waitForCache: Swift.Bool {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `onlyFromCache` instead.")
  public var onlyFromCache: Swift.Bool {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `backgroundDecode` instead.")
  public var backgroundDecode: Swift.Bool {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `preloadAllAnimationData` instead.")
  public var preloadAllAnimationData: Swift.Bool {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `callbackQueue` instead.")
  public var callbackQueue: Kingfisher.CallbackQueue {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `scaleFactor` instead.")
  public var scaleFactor: CoreGraphics.CGFloat {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `requestModifier` instead.")
  public var modifier: Kingfisher.ImageDownloadRequestModifier? {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `processor` instead.")
  public var processor: Kingfisher.ImageProcessor {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `imageModifier` instead.")
  public var imageModifier: Kingfisher.ImageModifier? {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `cacheSerializer` instead.")
  public var cacheSerializer: Kingfisher.CacheSerializer {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `keepCurrentImageWhileLoading` instead.")
  public var keepCurrentImageWhileLoading: Swift.Bool {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `onlyLoadFirstFrame` instead.")
  public var onlyLoadFirstFrame: Swift.Bool {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `cacheOriginalImage` instead.")
  public var cacheOriginalImage: Swift.Bool {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `onFailureImage` instead.")
  public var onFailureImage: Swift.Optional<Kingfisher.KFCrossPlatformImage?> {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `alsoPrefetchToMemory` instead.")
  public var alsoPrefetchToMemory: Swift.Bool {
    get
  }
  @available(*, deprecated, message: "Create a `KingfisherParsedOptionsInfo` from `KingfisherOptionsInfo` and use `loadDiskFileSynchronously` instead.")
  public var loadDiskFileSynchronously: Swift.Bool {
    get
  }
}
@available(*, deprecated, message: "Use `nil` in KingfisherOptionsInfo to indicate no modifier.")
public struct DefaultImageModifier : Kingfisher.ImageModifier {
  public static let `default`: Kingfisher.DefaultImageModifier
  public func modify(_ image: Kingfisher.KFCrossPlatformImage) -> Kingfisher.KFCrossPlatformImage
}
@available(*, deprecated, message: "Use `KFCrossPlatformImage` instead.")
public typealias Image = Kingfisher.KFCrossPlatformImage
@available(*, deprecated, message: "Use `KFCrossPlatformColor` instead.")
public typealias Color = Kingfisher.KFCrossPlatformColor
@available(*, deprecated, message: "Use `KFCrossPlatformImageView` instead.")
public typealias ImageView = Kingfisher.KFCrossPlatformImageView
@available(*, deprecated, message: "Use `KFCrossPlatformView` instead.")
public typealias View = Kingfisher.KFCrossPlatformView
@available(*, deprecated, message: "Use `KFCrossPlatformButton` instead.")
public typealias Button = Kingfisher.KFCrossPlatformButton
public protocol AuthenticationChallengeResponsable : AnyObject {
  func downloader(_ downloader: Kingfisher.ImageDownloader, didReceive challenge: Foundation.URLAuthenticationChallenge, completionHandler: @escaping (Foundation.URLSession.AuthChallengeDisposition, Foundation.URLCredential?) -> Swift.Void)
  func downloader(_ downloader: Kingfisher.ImageDownloader, task: Foundation.URLSessionTask, didReceive challenge: Foundation.URLAuthenticationChallenge, completionHandler: @escaping (Foundation.URLSession.AuthChallengeDisposition, Foundation.URLCredential?) -> Swift.Void)
}
extension Kingfisher.AuthenticationChallengeResponsable {
  public func downloader(_ downloader: Kingfisher.ImageDownloader, didReceive challenge: Foundation.URLAuthenticationChallenge, completionHandler: @escaping (Foundation.URLSession.AuthChallengeDisposition, Foundation.URLCredential?) -> Swift.Void)
  public func downloader(_ downloader: Kingfisher.ImageDownloader, task: Foundation.URLSessionTask, didReceive challenge: Foundation.URLAuthenticationChallenge, completionHandler: @escaping (Foundation.URLSession.AuthChallengeDisposition, Foundation.URLCredential?) -> Swift.Void)
}
public typealias KingfisherOptionsInfo = [Kingfisher.KingfisherOptionsInfoItem]
public enum KingfisherOptionsInfoItem {
  case targetCache(Kingfisher.ImageCache)
  case originalCache(Kingfisher.ImageCache)
  case downloader(Kingfisher.ImageDownloader)
  case transition(Kingfisher.ImageTransition)
  case downloadPriority(Swift.Float)
  case forceRefresh
  case fromMemoryCacheOrRefresh
  case forceTransition
  case cacheMemoryOnly
  case waitForCache
  case onlyFromCache
  case backgroundDecode
  @available(*, deprecated, message: "Use `.callbackQueue(CallbackQueue)` instead.")
  case callbackDispatchQueue(Dispatch.DispatchQueue?)
  case callbackQueue(Kingfisher.CallbackQueue)
  case scaleFactor(CoreGraphics.CGFloat)
  case preloadAllAnimationData
  case requestModifier(Kingfisher.ImageDownloadRequestModifier)
  case redirectHandler(Kingfisher.ImageDownloadRedirectHandler)
  case processor(Kingfisher.ImageProcessor)
  case cacheSerializer(Kingfisher.CacheSerializer)
  case imageModifier(Kingfisher.ImageModifier)
  case keepCurrentImageWhileLoading
  case onlyLoadFirstFrame
  case cacheOriginalImage
  case onFailureImage(Kingfisher.KFCrossPlatformImage?)
  case alsoPrefetchToMemory
  case loadDiskFileSynchronously
  case memoryCacheExpiration(Kingfisher.StorageExpiration)
  case memoryCacheAccessExtendingExpiration(Kingfisher.ExpirationExtending)
  case diskCacheExpiration(Kingfisher.StorageExpiration)
  case diskCacheAccessExtendingExpiration(Kingfisher.ExpirationExtending)
  case processingQueue(Kingfisher.CallbackQueue)
  case progressiveJPEG(Kingfisher.ImageProgressive)
  case alternativeSources([Kingfisher.Source])
  case retryStrategy(Kingfisher.RetryStrategy)
}
public struct KingfisherParsedOptionsInfo {
  public var targetCache: Kingfisher.ImageCache?
  public var originalCache: Kingfisher.ImageCache?
  public var downloader: Kingfisher.ImageDownloader?
  public var transition: Kingfisher.ImageTransition
  public var downloadPriority: Swift.Float
  public var forceRefresh: Swift.Bool
  public var fromMemoryCacheOrRefresh: Swift.Bool
  public var forceTransition: Swift.Bool
  public var cacheMemoryOnly: Swift.Bool
  public var waitForCache: Swift.Bool
  public var onlyFromCache: Swift.Bool
  public var backgroundDecode: Swift.Bool
  public var preloadAllAnimationData: Swift.Bool
  public var callbackQueue: Kingfisher.CallbackQueue
  public var scaleFactor: CoreGraphics.CGFloat
  public var requestModifier: Kingfisher.ImageDownloadRequestModifier?
  public var redirectHandler: Kingfisher.ImageDownloadRedirectHandler?
  public var processor: Kingfisher.ImageProcessor
  public var imageModifier: Kingfisher.ImageModifier?
  public var cacheSerializer: Kingfisher.CacheSerializer
  public var keepCurrentImageWhileLoading: Swift.Bool
  public var onlyLoadFirstFrame: Swift.Bool
  public var cacheOriginalImage: Swift.Bool
  public var onFailureImage: Kingfisher.KFCrossPlatformImage??
  public var alsoPrefetchToMemory: Swift.Bool
  public var loadDiskFileSynchronously: Swift.Bool
  public var memoryCacheExpiration: Kingfisher.StorageExpiration?
  public var memoryCacheAccessExtendingExpiration: Kingfisher.ExpirationExtending
  public var diskCacheExpiration: Kingfisher.StorageExpiration?
  public var diskCacheAccessExtendingExpiration: Kingfisher.ExpirationExtending
  public var processingQueue: Kingfisher.CallbackQueue?
  public var progressiveJPEG: Kingfisher.ImageProgressive?
  public var alternativeSources: [Kingfisher.Source]?
  public var retryStrategy: Kingfisher.RetryStrategy?
  public init(_ info: Kingfisher.KingfisherOptionsInfo?)
}
extension Kingfisher.ContentMode : Swift.Equatable {}
extension Kingfisher.ContentMode : Swift.Hashable {}
extension Kingfisher.ImageFormat : Swift.Equatable {}
extension Kingfisher.ImageFormat : Swift.Hashable {}
extension Kingfisher.CacheType : Swift.Equatable {}
extension Kingfisher.CacheType : Swift.Hashable {}
