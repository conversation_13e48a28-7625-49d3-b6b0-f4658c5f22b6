---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Documents/tingliteproject/tingLite/DerivedData/tingLite/Build/Intermediates.noindex/ArchiveIntermediates/tingLite/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/FDFullscreenPopGesture.framework/FDFullscreenPopGesture'
relocations:
  - { offset: 0x58E54, size: 0x8, addend: 0x0, symName: _FDFullscreenPopGestureVersionString, symObjAddr: 0x0, symBinAddr: 0x4FA0, symSize: 0x0 }
  - { offset: 0x58E89, size: 0x8, addend: 0x0, symName: _FDFullscreenPopGestureVersionNumber, symObjAddr: 0x38, symBinAddr: 0x4FD8, symSize: 0x0 }
  - { offset: 0x58EC6, size: 0x8, addend: 0x0, symName: '-[_FDFullscreenPopGestureRecognizerDelegate gestureRecognizerShouldBegin:]', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x174 }
  - { offset: 0x58F04, size: 0x8, addend: 0x0, symName: '-[_FDFullscreenPopGestureRecognizerDelegate gestureRecognizerShouldBegin:]', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x174 }
  - { offset: 0x58F6B, size: 0x8, addend: 0x0, symName: '-[_FDFullscreenPopGestureRecognizerDelegate navigationController]', symObjAddr: 0x174, symBinAddr: 0x4174, symSize: 0x18 }
  - { offset: 0x58FA2, size: 0x8, addend: 0x0, symName: '-[_FDFullscreenPopGestureRecognizerDelegate setNavigationController:]', symObjAddr: 0x18C, symBinAddr: 0x418C, symSize: 0xC }
  - { offset: 0x58FE3, size: 0x8, addend: 0x0, symName: '-[_FDFullscreenPopGestureRecognizerDelegate .cxx_destruct]', symObjAddr: 0x198, symBinAddr: 0x4198, symSize: 0x8 }
  - { offset: 0x59016, size: 0x8, addend: 0x0, symName: '+[UIViewController(FDFullscreenPopGesturePrivate) load]', symObjAddr: 0x1A0, symBinAddr: 0x41A0, symSize: 0x44 }
  - { offset: 0x590F6, size: 0x8, addend: 0x0, symName: '-[UIViewController(FDFullscreenPopGesturePrivate) fd_viewWillAppear:]', symObjAddr: 0x1E4, symBinAddr: 0x41E4, symSize: 0x80 }
  - { offset: 0x59154, size: 0x8, addend: 0x0, symName: '-[UIViewController(FDFullscreenPopGesturePrivate) fd_willAppearInjectBlock]', symObjAddr: 0x264, symBinAddr: 0x4264, symSize: 0x4 }
  - { offset: 0x5918E, size: 0x8, addend: 0x0, symName: '-[UIViewController(FDFullscreenPopGesturePrivate) fd_willAppearInjectBlock]', symObjAddr: 0x264, symBinAddr: 0x4264, symSize: 0x4 }
  - { offset: 0x591E7, size: 0x8, addend: 0x0, symName: '-[UIViewController(FDFullscreenPopGesturePrivate) setFd_willAppearInjectBlock:]', symObjAddr: 0x268, symBinAddr: 0x4268, symSize: 0x50 }
  - { offset: 0x59272, size: 0x8, addend: 0x0, symName: '+[UINavigationController(FDFullscreenPopGesture) load]', symObjAddr: 0x2B8, symBinAddr: 0x42B8, symSize: 0x44 }
  - { offset: 0x59300, size: 0x8, addend: 0x0, symName: '-[UINavigationController(FDFullscreenPopGesture) fd_pushViewController:animated:]', symObjAddr: 0x2FC, symBinAddr: 0x42FC, symSize: 0x250 }
  - { offset: 0x593B8, size: 0x8, addend: 0x0, symName: '-[UINavigationController(FDFullscreenPopGesture) fd_setupViewControllerBasedNavigationBarAppearanceIfNeeded:]', symObjAddr: 0x54C, symBinAddr: 0x454C, symSize: 0x150 }
  - { offset: 0x5942B, size: 0x8, addend: 0x0, symName: '___109-[UINavigationController(FDFullscreenPopGesture) fd_setupViewControllerBasedNavigationBarAppearanceIfNeeded:]_block_invoke', symObjAddr: 0x69C, symBinAddr: 0x469C, symSize: 0x68 }
  - { offset: 0x594A5, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32w, symObjAddr: 0x704, symBinAddr: 0x4704, symSize: 0xC }
  - { offset: 0x594CE, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32w, symObjAddr: 0x710, symBinAddr: 0x4710, symSize: 0x8 }
  - { offset: 0x594ED, size: 0x8, addend: 0x0, symName: '-[UINavigationController(FDFullscreenPopGesture) fd_popGestureRecognizerDelegate]', symObjAddr: 0x718, symBinAddr: 0x4718, symSize: 0x70 }
  - { offset: 0x59581, size: 0x8, addend: 0x0, symName: '-[UINavigationController(FDFullscreenPopGesture) fd_fullscreenPopGestureRecognizer]', symObjAddr: 0x788, symBinAddr: 0x4788, symSize: 0x70 }
  - { offset: 0x59615, size: 0x8, addend: 0x0, symName: '-[UINavigationController(FDFullscreenPopGesture) fd_viewControllerBasedNavigationBarAppearanceEnabled]', symObjAddr: 0x7F8, symBinAddr: 0x47F8, symSize: 0x64 }
  - { offset: 0x5967F, size: 0x8, addend: 0x0, symName: '-[UINavigationController(FDFullscreenPopGesture) setFd_viewControllerBasedNavigationBarAppearanceEnabled:]', symObjAddr: 0x85C, symBinAddr: 0x485C, symSize: 0x5C }
  - { offset: 0x596FC, size: 0x8, addend: 0x0, symName: '-[UIViewController(FDFullscreenPopGesture) fd_interactivePopDisabled]', symObjAddr: 0x8B8, symBinAddr: 0x48B8, symSize: 0x3C }
  - { offset: 0x59756, size: 0x8, addend: 0x0, symName: '-[UIViewController(FDFullscreenPopGesture) setFd_interactivePopDisabled:]', symObjAddr: 0x8F4, symBinAddr: 0x48F4, symSize: 0x5C }
  - { offset: 0x597C3, size: 0x8, addend: 0x0, symName: '-[UIViewController(FDFullscreenPopGesture) fd_prefersNavigationBarHidden]', symObjAddr: 0x950, symBinAddr: 0x4950, symSize: 0x3C }
  - { offset: 0x5981D, size: 0x8, addend: 0x0, symName: '-[UIViewController(FDFullscreenPopGesture) setFd_prefersNavigationBarHidden:]', symObjAddr: 0x98C, symBinAddr: 0x498C, symSize: 0x5C }
...
