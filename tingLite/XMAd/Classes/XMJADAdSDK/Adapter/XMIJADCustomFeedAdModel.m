//
//  XMIJADCustomFeedAdModel.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/4/1.
//

#import "XMIJADCustomFeedAdModel.h"
#import <JADYun/JADNativeSize.h>
#import <JADYun/JADNativeAdSlot.h>
#import <JADYun/JADNativeAd.h>
#import "XMIAdMacro.h"
#import "XMIGDTAdSDKManager.h"
#import "JADNativeAd+dspSDKReport.h"

@interface XMIJADCustomFeedAdModel ()<JADNativeAdDelegate>

@property (nonatomic, strong) JADNativeAd *adManager;

@property (nonatomic, copy) NSArray<JADNativeAdData *> *nativeAdDataObjects;

@end

@implementation XMIJADCustomFeedAdModel

- (void)loadAdData {
    if (self.loadingStatus != XMIFeedAdModelLoadingStatusInit) {
        return;
    }
    self.loadingStatus = XMIFeedAdModelLoadingStatusLoading;
    [super loadAdData];
    if (!self.adManager) {
        [self setupAdManager];
    }
    [self.adManager loadAdData];
}

/**
 private methods
 */
- (void)setupAdManager {
    JADNativeSize *size = [[JADNativeSize alloc] init];
    size.width = 1280;
    size.height = 720;
    
    JADNativeAdSlot *slot = [[JADNativeAdSlot alloc] init];
    slot.slotID = self.relatedData.dspPositionId;
    slot.imgSize = size;
//    if (self.relatedData.showstyle == XMIAdStyleMyPageFeedBannerVideo) {
//        slot.type = JADSlotTypeFeedVideo;
//    } else {
        slot.type = JADSlotTypeFeed;
//    }
    
    self.adManager = [[JADNativeAd alloc] initWithSlot:slot];
    self.adManager.delegate = self;
    self.adManager.rootViewController = self.rootViewController;
}

- (void)win:(nullable NSNumber *)auctionBidToWin {
    // JAD doesn't support win/loss notification
}

- (void)loss:(nullable NSNumber *)auctionPrice {
    // JAD doesn't support win/loss notification
}

#pragma mark - JADNativeAdDelegate
- (void)jadNativeAdDidLoadSuccess:(JADNativeAd *)nativeAd {
    XMILog(@"jadNativeAdDidLoadSuccess");
    if (!nativeAd.data || nativeAd.data.count == 0) {
        [self loadFailed:[NSError errorWithDomain:@"XMIJADCustomFeedAdModel" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"Empty ad data"}]];
        return;
    }
    
    self.nativeAdDataObjects = nativeAd.data;
    self.relatedData.originData = nativeAd;
    if (self.relatedData.isMobileRtb) {
        // 转换价格，单位为元，原始为分
        float originPrice = [self nativeAdDataObject].adPrice;
        self.relatedData.price = originPrice * 1.0 / 100;
    }
    [self loadSuccess];
}

- (void)jadNativeAdDidLoadFailure:(JADNativeAd *)nativeAd error:(NSError *)error {
    [self loadFailed:error];
}

- (void)jadNativeAdDidExposure:(JADNativeAd *)nativeAd {
    //在jad view里实现
}

- (void)jadNativeAdDidClick:(JADNativeAd *)nativeAd withView:(UIView *)view {
    //在jad view里实现
}

- (void)jadNativeAdDidCloseOtherController:(JADNativeAd *)nativeAd interactionType:(JADInteractionType)interactionType {
    //在jad view里实现
}

- (void)jadNativeAdDidClose:(JADNativeAd *)nativeAd withView:(UIView *)view {
    //在jad view里实现
}

- (JADNativeAdData *)nativeAdDataObject {
    if (self.nativeAdDataObjects.count > 0) {
        return [self.nativeAdDataObjects firstObject];
    }
    return nil;
}

- (NSString *)adTitle {
    return [[self nativeAdDataObject] adTitle];
}

- (NSString *)description {
    return [[self nativeAdDataObject] adDescription];
}

- (NSString *)adDescription {
    return [[self nativeAdDataObject] adDescription];
}

- (NSString *)adButtonText {
    return @"";
}

- (NSArray<NSString *> *)adImageURLs {
    return [self nativeAdDataObject].adImages;
}

- (NSString *)adVideoURL {
    return [self nativeAdDataObject].adVideoUrl;
}

- (id)adData {
    return self.adManager;
}

- (NSString *)iconUrl {
    return @"";
}

@end

