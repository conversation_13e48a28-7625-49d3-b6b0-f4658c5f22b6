//
//  XMIJADFindFeedNativeAdSocialView.m
//  XMAd
//
//  Created by xiaod<PERSON>2.zhang on 2025/4/1.
//

#import "XMIJADFindFeedNativeAdSocialView.h"
#import <JADYun/JADNativeAd.h>
#import <JADYun/JADNativeAdData.h>
#import "XMIAdError.h"
#import "XMICommonUtils.h"
#import "XMIAdNativeLogger.h"
#import "UIView+XMIUtils.h"
#import "XMIAdMacro.h"
#import "XMIAdVideoPlayer.h"

@interface XMIJADFindFeedNativeAdSocialView ()<JADNativeAdDelegate, XMIAdVideoPlayerDelegate>

@property (nonatomic, strong) JADNativeAd *adData;
@property (nonatomic, weak) UIViewController *rootViewController;
@property (nonatomic, strong) XMIAdVideoPlayer *videoPlayer;
@property (nonatomic, strong) XMIAdVideoView *videoView;
@property (nonatomic, strong) NSNumber *retryTag;
@property (nonatomic, assign) BOOL shouldPlayWhenEnterForeground;

@end

@implementation XMIJADFindFeedNativeAdSocialView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didReceiveForegroundNotification:) name:UIApplicationWillEnterForegroundNotification object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didReceiveBackgroundNotification:) name:UIApplicationDidEnterBackgroundNotification object:nil];
    }
    return self;
}

- (void)dealloc {
    [self.adData unregisterView];
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (XMIAdVideoPlayer *)videoPlayer {
    if (!_videoPlayer) {
        _videoPlayer = [[XMIAdVideoPlayer alloc] init];
        _videoPlayer.delegate = self;
        _videoPlayer.volume = 0.0;
        _videoPlayer.repeat = YES;
        _videoPlayer.view.alpha = 0.0;
    }
    return _videoPlayer;
}

- (XMIAdVideoView *)videoView {
    if (!_videoView) {
        _videoView = [[XMIAdVideoView alloc] init];
        _videoView.backgroundColor = [UIColor clearColor];
        [_videoView advanceCreatePlayer];
        _videoView.hidden = YES;
    }
    return _videoView;
}

#pragma mark - adview protocol

- (void)updateRootViewController:(UIViewController *)rootViewController {
    self.rootViewController = rootViewController;
}

- (void)customRenderWithAdData:(id)adData {
    if (!adData || ![adData isKindOfClass:[JADNativeAd class]]) {
        self.adData = nil;
        [self failRenderWithError:[XMIAdError emptyDataError]];
        return;
    }
    if ([self.adData isEqual:adData]) {
        return;
    }
    self.adData = adData;
    self.adData.delegate = self;
    [self doCustomRender];
}

- (void)doCustomRender {
    [self.adData unregisterView];
    self.coverImageView.hidden = NO;
    [self.adData registerContainer:self.contentView withClickableViews:@[self.coverImageView] withClosableViews:nil];
    [self.adMarkButtonView updateFeedAdType:XMIAdMarkAdTypeJAD];
}

- (void)updateVideoURL:(NSString *)videoURL indentifier:(NSString *)identifier {
    if (videoURL.length == 0) {
        [_videoPlayer stop];
        _videoView.hidden = YES;
        return;
    }
    self.videoView.hidden = NO;
    
    if (![self.videoPlayer.identifier isEqualToString:identifier]) {
        self.videoPlayer.identifier = identifier;
        [self loadVideoWithURL:[NSURL URLWithString:videoURL]];
    }
    self.videoView.frame = self.coverImageView.bounds;
}

- (void)loadVideoWithURL:(NSURL *)url {
    if (_videoView) {
        [_videoView removeFromSuperview];
    }
    self.retryTag = nil;
    [self.videoPlayer startPlayWithURL:url playerView:self.videoView];
}

- (void)sizeToFit {
    [super sizeToFit];
    self.videoView.frame = self.coverImageView.bounds;
}

- (void)play {
    if (!self.videoPlayer.isPlaying) {
        [self.videoPlayer play];
    }
}

- (void)pause {
    [self.videoPlayer pause];
}

- (void)replay {
    [self.videoPlayer replay];
}

- (void)stop {
    [self.videoPlayer stop];
}

- (BOOL)isPlaying {
    return self.videoPlayer.isPlaying;
}

#pragma mark - player delegate

- (void)player:(XMIAdVideoPlayer *)player playStateDidChanged:(XMIAdPlayerPlayState)state {
    [self playerStateChanged:(XMIPlayerPlayState)state];
    if (!self.videoView.superview && state == XMIAdPlayerStatePlaying) {
        [self.coverImageView insertSubview:self.videoView atIndex:0];
        self.videoView.frame = self.coverImageView.bounds;
    }
}

- (void)player:(XMIAdVideoPlayer *)player failWithError:(NSError *)error {
    BOOL shouldRetry = NO;
    if ([self.delegate respondsToSelector:@selector(feedAdViewShouldRetryVideo:)]) {
        shouldRetry = [self.delegate feedAdViewShouldRetryVideo:self];
    }
    if (shouldRetry) {
        if (self.retryTag) {
            self.retryTag = [NSNumber numberWithInteger:self.retryTag.integerValue + 1];
        } else {
            NSNumber *retryTag = [NSNumber numberWithInteger:arc4random()];
            self.retryTag = retryTag;
        }
        NSNumber *retryTag = self.retryTag;
        @weakify(self)
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            @strongify(self);
            if (!self
                || !self.retryTag || ![self.retryTag isEqual:retryTag]) {
                return ;
            }
            if (([UIApplication sharedApplication].applicationState == UIApplicationStateActive) && self.window && [self xmi_isExposed:self.window radio:0.01f]) {
                [self loadVideoWithURL:self.videoPlayer.currentURL];
            }
        });
    } else {
        [_videoView removeFromSuperview];
        [self failRenderWithError:error];
    }
}

- (void)didReceiveBackgroundNotification:(NSNotification *)notification {
    if (self.videoPlayer.isPlaying) {
        self.shouldPlayWhenEnterForeground = YES;
        [self pause];
    }
}

- (void)didReceiveForegroundNotification:(NSNotification *)notification {
    if (self.shouldPlayWhenEnterForeground) {
        [self play];
        self.shouldPlayWhenEnterForeground = NO;
    }
}

#pragma mark - jad delegate

- (void)jadNativeAdDidLoadSuccess:(JADNativeAd *)nativeAd {
    //在XMIJADCustomFeedAdModel里实现
}

- (void)jadNativeAdDidLoadFailure:(JADNativeAd *)nativeAd error:(NSError *__nullable)error {
    //在XMIJADCustomFeedAdModel里实现
}

- (void)jadNativeAdDidExposure:(JADNativeAd *)nativeAd {
    XMILogNativeAdInfo(@"jadNativeAdDidExposure");
    [self didExposeDspView];
}

- (void)jadNativeAdDidClick:(JADNativeAd *)nativeAd withView:(UIView *)view {
    [self clickAdView:@{}];
}

- (void)jadNativeAdDidCloseOtherController:(JADNativeAd *)nativeAd interactionType:(JADInteractionType)interactionType {
    [self didCloseAdDetail];
}

- (void)jadNativeAdDidClose:(JADNativeAd *)nativeAd withView:(UIView *)view {
    [self didCloseAdDetail];
}

@end
