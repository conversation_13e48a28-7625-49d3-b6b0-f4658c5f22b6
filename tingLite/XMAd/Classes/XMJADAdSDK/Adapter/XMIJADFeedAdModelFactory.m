//
//  XMIJADFeedAdModelFactory.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/4/1.
//

#import "XMIJADFeedAdModelFactory.h"
#import "XMIJADCustomFeedAdModel.h"

@implementation XMIJADFeedAdModelFactory

+ (XMIFeedAdModel *)adModelWithRelatedData:(XMIAdRelatedData *)relatedData
{
    XMIJADCustomFeedAdModel *model = [[XMIJADCustomFeedAdModel alloc] init];
    model.relatedData = relatedData;
    return model;
}

@end
