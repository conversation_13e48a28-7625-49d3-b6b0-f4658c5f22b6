//
//  XMIJADFeedAdViewFactory.m
//  XMAd
//
//  Created by xiaodong2.zhang on 2025/4/2.
//

#import "XMIJADFeedAdViewFactory.h"
#import "XMIJADFindFeedNativeAdSocialView.h"
#import "XMIJADFindFeedNativeAdView.h"

@implementation XMIJADFeedAdViewFactory

+ (Class)feedAdViewClasslWithShowStyle:(XMIAdShowStyle)showstyle
                                adType:(XMIAdType)adType
                          subShowStyle:(XMIAdShowSubStyle)subShowStyle {
    if (adType != XMIAdTypeJAD) {
        return nil;
    }
    switch (showstyle) {
        case XMIAdStyleHomeLargeImage:
            if (subShowStyle == XMIAdSubShowSubStyleSocial) {
                return [XMIJADFindFeedNativeAdSocialView class];
            }
            return [XMIJADFindFeedNativeAdView class];
            break;
            
        default:
            break;
    }
    return nil;
}

@end
