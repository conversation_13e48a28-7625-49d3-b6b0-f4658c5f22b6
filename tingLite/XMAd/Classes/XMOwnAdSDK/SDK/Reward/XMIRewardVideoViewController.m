//
//  XMIRewardVideoViewController.m
//  hpple
//
//  Created by cuiyuanzhe on 2022/6/13.
//

#import "XMIRewardVideoViewController.h"
#import "XMIAdNewVideoPlayer.h"
#import "XMIAdError.h"
#import "XMIAdMacro.h"
#import "XMIAdRelatedData.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import "XMICommonUtils.h"
#import "UIView+XMIUtils.h"
#import "XMIAdRewardVideoHeaderView.h"
#import "XMIOwnJumpManager.h"
#import "XMIAdReporter+AD.h"
#import "XMIAdManager.h"
#import <XMCategories/UIImage+XMCommon.h>
#import "XMIAdClickDetectView.h"
#import "XMIAdDataCenter.h"
#import "XMIAdAlertView.h"
#import "XMIAdSourceLabel.h"

static inline BOOL isIPhoneXSeries() {
    if (@available(iOS 11.0, *)) {
        UIWindow *mainWindow = [[[UIApplication sharedApplication] delegate] window];
        if (mainWindow.safeAreaInsets.bottom > 0.0) {
            return YES;
        }
    }
    return NO;
}

#define RewardBottomHeight 134
#define RewardBottomInterval 16
#define RewardBottomCoverLeft 12
#define RewardBottomCoverTop 12
#define RewardBottomCoverHeight 60
#define RewardBottomTitleLeft 12
#define RewardBottomTitleTop 10
#define RewardBottomTitleHeight 20
#define RewardBottomSubtitleTopSpace 8
#define RewardBottomSubtitleRightSpaceMin 14
#define RewardBottomButtonInterval 28
#define RewardBottomButtonHeight 36
#define RewardBottomButtonBottom 12
#define RewardBottomTitleRightSpaceMin 36
#define RewardBottomTagInterval 6
#define RewardBottomCloseInterval 11

#define RewardEndcardCoverWidth 70
#define RewardEndcardTitleTopSpace 20
#define RewardEndcardSubtitleTopSpace 11
#define RewardEndcardButtonTopSpace 32
#define RewardEndcardButtonWidth 251
#define RewardEndcardButtonHeight 42
#define RewardReplayButtonWidth 90
#define RewardReplayButtonHeight 19
#define RewardReplayButtonTop 36
#define RewardReplayButtonInterval 4.5f

typedef NS_ENUM(NSInteger, XMIRewardVideoBottomStyle) {
    XMIRewardVideoBottomStyleUnKnown = -1,
    XMIRewardVideoBottomStyleNone    = 0,
    XMIRewardVideoBottomStyleAlbum   = 1,
};

@interface XMIRewardVideoBottomModel:NSObject

@property(nonatomic, strong)NSURL *coverUrl;
@property(nonatomic, strong)NSString *name;
@property(nonatomic, strong)NSString *intro;
@property(nonatomic, strong)NSString *actionButtonText;
@property(nonatomic, assign)BOOL isFavorite;
@property(nonatomic, assign)XMIRewardVideoBottomStyle bottomStyle;

@end

@implementation XMIRewardVideoBottomModel

- (instancetype)initWithCoverUrl:(NSURL *)coverUrl
                            name:(NSString *)name
                           intro:(NSString *)intro
                actionButtonText:(NSString *)actionButtonText
                     bottomStyle:(XMIRewardVideoBottomStyle)bottomStyle
{
    self = [super init];
    if (self) {
        self.coverUrl = coverUrl;
        self.name = name;
        self.intro = intro;
        self.actionButtonText = actionButtonText;
        self.bottomStyle = bottomStyle;
    }
    return self;
}


@end

@interface XMIRewardVideoViewController ()<XMIAdVideoPlayerDelegate, XMIAdRewardVideoHeaderViewDelegate, XMIJumpManagerDelegate>

@property (strong, nonatomic) UIView *videoView;

@property (strong, nonatomic) XMIAdNewVideoPlayer *player;

@property (strong, nonatomic) XMIAdRewardVideoHeaderView *headerView;

@property (strong, nonatomic) UIView *bottomView;

@property (strong, nonatomic) UIView *bottomBigView;

@property (strong, nonatomic) UIView *endCardView;

@property (strong, nonatomic) UIButton *playButton;

@property (strong, nonatomic) UIButton *volumeButton;

@property (strong, nonatomic) UIImageView *backgrounndView;

@property (strong, nonatomic) UIVisualEffectView *effectview;

@property (weak, nonatomic) UIViewController *fromVc;

@property (nonatomic, strong) UIButton *subscribeButton;

@property (nonatomic, strong) UIButton *endSubscribeButton;

@property (nonatomic, assign) BOOL videoFinished;

@property (nonatomic, assign) BOOL viewAppeared;

@property (nonatomic, strong) NSTimer *timeoutTimer;

@property (nonatomic, assign) BOOL timeout;

@property (nonatomic, assign) BOOL playEnded;

@property (nonatomic, strong) XMIRewardVideoBottomModel *bottomViewModel;

@property (nonatomic, strong) XMIOwnJumpManager *jumpManager;

@property (nonatomic, assign) BOOL hadReportLandingClick;

@property (nonatomic, assign) BOOL hadReportManualClick;

@property (nonatomic, assign) BOOL hadReportAutoClick;

@property (nonatomic, assign) CGSize videoSize;

@property (nonatomic, assign) BOOL playerReady;

@property(nonatomic, strong) XMIAdAlertView *closeAlert;

@property (nonatomic, assign) BOOL replaying;

@end

@implementation XMIRewardVideoViewController
{
    NSInteger _retryCount;
}

#pragma mark - life circle

- (instancetype)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil
{
    self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil];
    if (self) {
        _retryCount = 3;
        _timeoutLimit = 10.0f;
        _timeout = NO;
    }
    return self;
}

- (void)dealloc
{
    [self.timeoutTimer invalidate];
    self.timeoutTimer  = nil;
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)setRelatedData:(XMIAdRelatedData *)relatedData
{
    _relatedData = relatedData;
    self.bottomViewModel = [[XMIRewardVideoBottomModel alloc] initWithCoverUrl:[NSURL URLWithString:relatedData.iconUrl] name:relatedData.name intro:relatedData.adDescription actionButtonText:[self adActionButtonText] bottomStyle:0];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupViews];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(willEnterForeground:) name:UIApplicationDidBecomeActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(willResignActive:) name:UIApplicationWillResignActiveNotification object:nil];
    // Do any additional setup after loading the view.
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    self.viewAppeared = YES;
    if ([self.delegate respondsToSelector:@selector(rewardVideoViewControllerWillAppear:)]) {
        [self.delegate rewardVideoViewControllerWillAppear:self];
    }
    [self autoPlayIfNeeded];
    [self.navigationController setNavigationBarHidden:YES animated:animated];
    [self showBottomView];
    [self.headerView viewAppeared];
    if (self.hadReportAutoClick) {
        [self closeVC:YES];
    }
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    if ([self.delegate respondsToSelector:@selector(rewardVideoViewControllerDidAppear:)]) {
        [self.delegate rewardVideoViewControllerDidAppear:self];
    }
}

- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
    self.viewAppeared = NO;
    if ([self.delegate respondsToSelector:@selector(rewardVideoViewControllerWillDisappear:)]) {
        [self.delegate rewardVideoViewControllerWillDisappear:self];
    }
    [self autoPauseIfNeeded];
    [self.player removeActiveControl];
    [self.headerView viewDisappeared];
}

- (void)viewDidDisappear:(BOOL)animated
{
    [super viewDidDisappear:animated];
    if ([self.delegate respondsToSelector:@selector(rewardVideoViewControllerDidDisappear:)]) {
        [self.delegate rewardVideoViewControllerDidDisappear:self];
    }
    [self.timeoutTimer invalidate];
    self.timeoutTimer  = nil;
    UIViewController *landingVC = nil;
    if (self.navigationController) {
        if (self.navigationController.presentedViewController) {
            landingVC = self.navigationController.presentedViewController;
        } else {
            landingVC = [self.navigationController.viewControllers lastObject];
        }
    }
    
    if ([landingVC isKindOfClass:[UINavigationController class]]) {
        landingVC = [[(UINavigationController *)landingVC viewControllers] lastObject];
    }
    if (landingVC.childViewControllers.count > 0) {
        landingVC = [[landingVC childViewControllers] lastObject];
    }
    if (landingVC == self) {
        return;
    }
    XMIAdClickDetectView *view = [[XMIAdClickDetectView alloc] initWithFrame:CGRectMake(0, landingVC.view.xmi_height - 1, 1, 1)];
    view.backgroundColor = [UIColor clearColor];
    @weakify(landingVC)
    @weakify(self)
    [view setPointInsideAction:^(UIView * _Nonnull view, CGPoint point) {
        @strongify(landingVC)
        @strongify(self)
        if (landingVC.navigationController && !landingVC.navigationController.navigationBarHidden) {
            CGPoint navPoint = [landingVC.navigationController.navigationBar convertPoint:point fromView:view];
            if (CGRectContainsPoint(landingVC.navigationController.navigationBar.bounds, navPoint)) {
                return;
            }
        }
        [self reportLandingClickIfNeeded];
    }];
    [landingVC.view addSubview:view];
    
}

- (void)startLoadVideo
{
    if (!self.relatedData.videoUrl) {
        if ([self.delegate respondsToSelector:@selector(rewardVideoViewController:playerDidFailWithError:)]) {
            [self.delegate rewardVideoViewController:self playerDidFailWithError:[XMIAdError emptyDataError]];
        }
        return;
    }
    if (self.player) {
        return;
    }
    self.player = [XMIAdNewVideoPlayer createVideoPlayer];
    self.player.volume = self.mute ? 0 : 1;
    self.player.repeat = NO;
    self.player.delegate = self;
    [self.player startPlayWithURL:[NSURL URLWithString:self.relatedData.videoUrl] playerView:self.videoView];
    if (self.timeoutLimit > 0) {
        self.timeoutTimer = [NSTimer timerWithTimeInterval:self.timeoutLimit target:self selector:@selector(onTimeout:) userInfo:nil repeats:NO];
        [[NSRunLoop mainRunLoop] addTimer:self.timeoutTimer forMode:NSRunLoopCommonModes];
    }
}

- (void)onTimeout:(NSTimer *)timer
{
    [self.timeoutTimer invalidate];
    self.timeoutTimer = nil;
    self.timeout = YES;
    if ([self.delegate respondsToSelector:@selector(rewardVideoViewController:playerDidFailWithError:)]) {
        [self.delegate rewardVideoViewController:self playerDidFailWithError:[XMIAdError timeoutError]];
    }
}

- (UIView *)videoView {
    if (!_videoView) {
        _videoView = [XMIAdNewVideoView createVideoView];
    }
    return _videoView;
}


#pragma mark - video player delegate

- (void)player:(XMIAdNewVideoPlayer *)player playStateDidChanged:(XMIAdPlayerPlayState)state
{
    switch (state) {
        case XMIAdPlayerStateBuffering:
            [self showLoadingButton];
            break;
        case XMIAdPlayerStateReady:
            if (self.timeout) {
                return;
            }
            if ([self.delegate respondsToSelector:@selector(rewardVideoViewControllerPlayerDidReady:)]) {
                [self.delegate rewardVideoViewControllerPlayerDidReady:self];
            }
            [self.timeoutTimer invalidate];
            self.timeoutTimer = nil;
            [self showPlayButton];
            [self autoPlayIfNeeded];
            if (self.isViewLoaded) {
                if (self.effectview) {
                    [self.view insertSubview:self.videoView aboveSubview:self.effectview];
                } else {
                    [self.view insertSubview:self.videoView atIndex:0];
                }
            }
            self.videoView.hidden = NO;
            self.playerReady = YES;
            [self updateVideoViewFrame];
            break;
        case XMIAdPlayerStatePlaying:
            [self hiddenPlayButton];
            if (CGSizeEqualToSize(self.videoSize, CGSizeZero)) {
                [self.player getVideoSize];
                [self updateVideoViewFrame];
            }
            break;
        case XMIAdPlayerStateStopped:
            [self showEndCard];
            [self.headerView finishCountDown];
            break;
        default:
            [self showPlayButton];
            break;
    }
}

- (void)updateVideoViewFrame {
    self.videoSize = [self.player getMediaSize];
    CGFloat width = self.videoSize.width;
    CGFloat height = self.videoSize.height;
    CGRect frame = self.view.bounds;
    if (width > 1
        && height > 1) {
        
        // 视频的高度很高时
        if (height/width > self.view.xmi_height/self.view.xmi_width) {
            frame.size.width = self.view.xmi_height*width/height;
            frame.origin.x = (self.view.xmi_width - frame.size.width)/2;
        }
        else {
            frame.size.height = self.view.xmi_width*height/width;
            
            // 横幅视频垂直居中
//            if (width > height) {
//                frame.origin.y = (self.view.height - frame.size.height)/2;
//            }
            frame.origin.y = (self.view.xmi_height - frame.size.height)/2;

        }
        if ([self isHorizonal]) {
            frame.origin.y -= 50;
        }
    }
    
    self.videoView.frame = frame;
}

- (void)tryReload {
    if (_timeout) {
        return;
    }
    
    if (_retryCount <= 0) {
        [self playFailed];
        return;
    }
    
    @weakify(self);
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        @strongify(self);
        if (!self
            || self->_timeout) {
            return ;
        }
        
        self->_retryCount--;
        self.player.delegate = nil;
        [self.player stop];
        self.player = nil;
        [self startLoadVideo];
    });
}

- (void)playFailed {
}

- (void)showPlayButton {
    [self.playButton.layer removeAllAnimations];
    [self.playButton setImage:[XMICommonUtils imageNamed:@"reward_play_btn"] forState:(UIControlStateNormal)];
}

- (void)showLoadingButton {
    CAAnimation *rollanimation = [self.playButton.layer animationForKey:@"roll"];
    if (rollanimation) {
        return;
    }
    
    [self.playButton setImage:[XMICommonUtils imageNamed:@"reward_play_loading"] forState:(UIControlStateNormal)];
    CABasicAnimation *animate = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];
    animate.byValue = [NSNumber numberWithFloat:M_PI * 2.0];
    animate.duration = 2;
    animate.repeatCount = HUGE_VALF;
    animate.removedOnCompletion = NO;
    [self.playButton.layer addAnimation:animate forKey:@"roll"];
}

- (void)hiddenPlayButton {
    [self.playButton.layer removeAllAnimations];
    self.playButton.hidden = YES;
}


- (void)showBottomView {
    if (self->_endCardView && !self->_endCardView.hidden) {
        return;
    }
    if (_bottomView && !_bottomBigView.hidden) {
        return;
    }
    if (!_bottomView || _bottomView.alpha == 0) {
        [self bottomView];
        self.bottomView.alpha = 0.5f;
        self.bottomView.hidden = NO;
        self.bottomView.xmi_top = self.view.xmi_height;
        [UIView animateWithDuration:0.2f delay:0 options:(UIViewAnimationOptionCurveEaseInOut) animations:^{
            self.bottomView.xmi_bottom = self.view.xmi_height - 36;
            self.bottomView.alpha = 1.0f;
        } completion:^(BOOL finished) {
            
        }];
    }
}

- (void)showEndCard {
    BOOL shouldReport = NO;
    if (!self.endCardView) {
        [self endCardView];
        shouldReport = YES;
    }
    self.endCardView.alpha = 0;
    self.endCardView.hidden = NO;
    [UIView animateWithDuration:0.25 animations:^{
        self.endCardView.alpha = 1;
        self.bottomView.xmi_top = self.view.xmi_height;
        self.bottomView.alpha = 0;
        self->_bottomBigView.alpha = 0;
    } completion:^(BOOL finished) {
        if (self.bottomViewModel.bottomStyle == XMIRewardVideoBottomStyleNone && [self isJumpAd]) {
            [self.jumpManager doJumpWithAd:self.relatedData];
            if (!self.hadReportAutoClick) {
                self.hadReportAutoClick = YES;
                [XMIAdReporter clickReportWithAd:self.relatedData andView:self.view andUserInfo:@{@"extraParams" : @{@"autoPull" : @(2)}}];
            }
            self.navigationController.interactivePopGestureRecognizer.enabled = NO;
            return;
        }
        CAKeyframeAnimation *anim = [CAKeyframeAnimation animationWithKeyPath:@"transform.rotation.z"];
        anim.values = @[@(0), @(-M_PI / 36.0f), @(M_PI / 36.0f), @(-M_PI / 36.0f), @(M_PI / 36.0f), @(0), @(0)];
        anim.duration = 1.0f;
        anim.repeatCount = 2;
        anim.keyTimes = @[@(0), @(0.08f), @(0.24f), @(0.4f), @(0.56f), @(0.64f), @(1)];
        anim.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear];
        anim.removedOnCompletion = YES;
        [self.endSubscribeButton.layer addAnimation:anim forKey:@"shake"];
    }];
    [self.headerView dismissAlertView];
}
- (void)player:(XMIAdNewVideoPlayer *)player failWithError:(NSError *)error
{
    _retryCount -= 1;
    if (_retryCount == 0 || self.timeout) {
        if ([self.delegate respondsToSelector:@selector(rewardVideoViewController:playerDidFailWithError:)]) {
            [self.delegate rewardVideoViewController:self playerDidFailWithError:error];
        }
        return;
    }
    @weakify(self)
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        @strongify(self)
        if (self && self.timeoutTimer) {
            [self.player startPlayWithURL:[NSURL URLWithString:self.relatedData.videoUrl] playerView:self.videoView];
        }
    });
   
}

- (void)player:(XMIAdNewVideoPlayer *)player playDidFinish:(NSError *)error
{
    [self.closeAlert dismiss];
    self.closeAlert = nil;
    self.replaying = NO;
    if (self.playEnded) {
        return;
    }
    self.playEnded = YES;
    if ([self.delegate respondsToSelector:@selector(rewardVideoViewControllerDidFinishPlay:)]) {
        [self.delegate rewardVideoViewControllerDidFinishPlay:self];
    }
}

- (void)player:(XMIAdNewVideoPlayer *)player getPlayerSize:(CGSize)playerSize
{
    //todo；播放器尺寸
}



- (void)willEnterForeground:(NSNotification *)notification
{
    [self autoPlayIfNeeded];
}

- (void)willResignActive:(NSNotification *)notification
{
    [self autoPauseIfNeeded];
}

- (void)autoPlayIfNeeded {
    if (!self.viewAppeared || [UIApplication sharedApplication].applicationState != UIApplicationStateActive || (self.playEnded && !self.replaying) || self.timeout) {
        return;
    }
    
    [self.player play];;
}

- (void)autoPauseIfNeeded {
    if ([self.player isPlaying]) {
        [self.player pause];
    }
}

#pragma mark - UI

- (void)setupViews
{
    if ([self isHorizonal]) {
        self.view.backgroundColor = XMI_COLOR_RGB(0x010002);
        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(horizonalFullScreenTapped:)];
        [self.view addGestureRecognizer:tapGesture];
    } else {
    self.view.backgroundColor = [UIColor whiteColor];
    
    // 背景
    self.backgrounndView = [[UIImageView alloc]initWithFrame:self.view.bounds];
    [self.view addSubview:self.backgrounndView];
    [self.backgrounndView sd_setImageWithURL:[NSURL URLWithString:self.relatedData.cover]];
    
    UIBlurEffect *blur = [UIBlurEffect effectWithStyle:UIBlurEffectStyleDark];
    UIVisualEffectView *effectview = [[UIVisualEffectView alloc] initWithEffect:blur];
    self.effectview = effectview;
    effectview.frame = self.view.bounds;
    [self.view addSubview:effectview];
    }
    
    
    
    // 播放按钮
//    self.playButton = [UIButton buttonWithType:(UIButtonTypeCustom)];
//    [self.view addSubview:self.playButton];
//    self.playButton.userInteractionEnabled = NO;
//    self.playButton.xmi_size = CGSizeMake(60, 60);
//    self.playButton.center = CGPointMake(self.view.xmi_width/2, self.view.xmi_height/2);
    NSTimeInterval unlockTime = self.player.duration > 0 ? self.player.duration : self.relatedData.unlockTime;
    // 静音按钮
    XMIAdRewardVideoHeaderView *header = [[XMIAdRewardVideoHeaderView alloc] initWithFrame:CGRectMake(0, 0, XMI_SCREEN_WIDTH, IS_REWARD_SMALL_SCREEN ? 72 : 91) totalTime:self.player.duration skipTime:unlockTime isResume:self.resume];
    [self.view addSubview:header];
    header.tag = 10000;
    header.delegate = self;
    self.headerView = header;
    // 静音按钮
    self.volumeButton = [UIButton buttonWithType:(UIButtonTypeCustom)];
    [self.volumeButton setImage:[XMICommonUtils imageNamed:@"reward_voice_off"] forState:(UIControlStateNormal)];
    [self.volumeButton setImage:[XMICommonUtils imageNamed:@"reward_voice_on"] forState:(UIControlStateSelected)];
    [header addSubview:self.volumeButton];
    self.volumeButton.xmi_size = CGSizeMake(26, 26);
    self.volumeButton.xmi_right = header.xmi_width - 54;
    self.volumeButton.xmi_top = IS_REWARD_SMALL_SCREEN ? 38 : 57;
    [self.volumeButton addTarget:self action:@selector(volumeButtonClick:) forControlEvents:(UIControlEventTouchUpInside)];
    self.volumeButton.selected = !self.mute;
    self.headerView = header;
    if (self.playerReady && !self.videoView.superview) {
        if (self.effectview) {
            [self.view insertSubview:self.videoView aboveSubview:self.effectview];
        } else {
            [self.view insertSubview:self.videoView atIndex:0];
        }
        [self updateVideoViewFrame];
    }
}

- (void)volumeButtonClick:(UIButton *)btn {
    btn.selected = !btn.isSelected;
    self.player.volume = btn.selected ? 1 : 0;
}

- (void)setMute:(BOOL)mute
{
    _mute = mute;
    self.volumeButton.selected = !mute;
    self.player.volume = self.volumeButton.selected ? 1 : 0;
}

- (UIView *)bottomView
{
    if (!_bottomView) {
        
        _bottomView = [[UIView alloc] initWithFrame:CGRectMake(RewardBottomInterval, self.view.xmi_height, self.view.xmi_width - 2 * RewardBottomInterval, RewardBottomHeight)];
        [self.view addSubview:_bottomView];
        _bottomView.backgroundColor = [UIColor clearColor];
        _bottomView.clipsToBounds = YES;
        _bottomView.backgroundColor = XMI_COLOR_RGB(0xFFFFFF);
        _bottomView.layer.cornerRadius = 8;
        _bottomView.layer.masksToBounds = YES;
        [self.view bringSubviewToFront:_bottomView];
        
        // cover
        UIImageView *cover = [[UIImageView alloc] init];
        cover.backgroundColor = XMI_COLOR_RGB(0xD8D8D8);
        cover.layer.cornerRadius     = 8;
        cover.layer.masksToBounds    = YES;
        [_bottomView addSubview:cover];
        [cover sd_setImageWithURL:self.bottomViewModel.coverUrl placeholderImage:nil];
        cover.xmi_size = CGSizeMake(RewardBottomCoverHeight, RewardBottomCoverHeight);
        cover.xmi_left = RewardBottomCoverLeft;
        cover.xmi_top = RewardBottomCoverTop;
        
        XMIAdSourceLabel *sourceLabel = [XMIAdSourceLabel labelWithSoundPatchStyle];
        [cover addSubview:sourceLabel];
        [sourceLabel updateWithInScreenSource:self.relatedData.inScreenSource materialProvideSource:self.relatedData.materialProvideSource];
        sourceLabel.xmi_bottom = cover.height;
        
        // button
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        button.titleLabel.font = XMI_AD_PingFangFont(14);
        [_bottomView addSubview:button];
        button.layer.masksToBounds    = YES;
        button.layer.cornerRadius     = RewardBottomButtonHeight * 0.5f;
        [button setBackgroundImage:[self buttonImage] forState:(UIControlStateNormal)];
        [button setTitleColor:XMI_COLOR_RGB(0xFFFFFF) forState:(UIControlStateNormal)];
        button.userInteractionEnabled = NO;
        [button setTitle:self.relatedData.buttonText forState:UIControlStateNormal];
        button.xmi_size = CGSizeMake(_bottomView.xmi_width - 2 * RewardBottomButtonInterval , RewardBottomButtonHeight);
        button.xmi_centerX = _bottomView.xmi_width * 0.5f;
        button.xmi_bottom = _bottomView.xmi_height - RewardBottomButtonBottom;
        self.subscribeButton = button;
        if (self.bottomViewModel.isFavorite) {
            [button setBackgroundImage:[self buttonImage] forState:(UIControlStateNormal)];
            [button setTitle:@"立即查看" forState:UIControlStateNormal];
        }else{
            if (self.relatedData.clickType == 23) {
                [button setBackgroundImage:[self subscribeButtonImage] forState:(UIControlStateNormal)];
            }else{
                [button setBackgroundImage:[self buttonImage] forState:(UIControlStateNormal)];
            }
            [button setTitle:[self adActionButtonText] forState:UIControlStateNormal];
        }

        // title
        UILabel *title = [[UILabel alloc] init];
        [_bottomView addSubview:title];
        [title setFont:XMI_AD_PingFangMediumFont(16)];
        title.textColor = XMI_COLOR_RGB(0x111111);

        title.text = self.bottomViewModel.name;
        [title sizeToFit];
        CGFloat titleMaxWidth = _bottomView.xmi_width - RewardBottomTitleRightSpaceMin - cover.xmi_right - RewardBottomTitleLeft;
        title.xmi_width = MIN(title.xmi_width, titleMaxWidth);
        title.xmi_left = cover.xmi_right + RewardBottomTitleLeft;
        title.xmi_height = RewardBottomTitleHeight;
        title.xmi_top = RewardBottomTitleTop;

        // subtitle
        CGFloat subMaxWidth = _bottomView.xmi_width - RewardBottomSubtitleRightSpaceMin - cover.xmi_right - RewardBottomTitleLeft;
        UILabel *subtitle = [[UILabel alloc] init];
        [_bottomView addSubview:subtitle];
        subtitle.font = XMI_AD_PingFangFont(13);
        subtitle.textColor = XMI_COLOR_RGB(0x2f2f2f);
        NSMutableParagraphStyle *ps = [[NSMutableParagraphStyle alloc] init];
        ps.minimumLineHeight = 18;
        ps.alignment = NSTextAlignmentLeft;
        ps.lineBreakMode = NSLineBreakByCharWrapping;
        subtitle.attributedText = [[NSAttributedString alloc] initWithString:self.bottomViewModel.intro attributes:@{NSFontAttributeName : XMI_AD_PingFangFont(13), NSForegroundColorAttributeName : XMI_COLOR_RGB(0x2f2f2f), NSParagraphStyleAttributeName : ps}];
        subtitle.numberOfLines = 2;
        [subtitle sizeToFit];
        if (title.text.length <= 0) {
            subtitle.xmi_top = title.xmi_top;
            subtitle.xmi_width = MIN(subtitle.xmi_width, titleMaxWidth);
            subtitle.xmi_left = title.xmi_left;
        }
        else {
            subtitle.xmi_top = title.xmi_bottom + RewardBottomSubtitleTopSpace;
            subtitle.xmi_width = MIN(subtitle.xmi_width, subMaxWidth);
            subtitle.xmi_left = title.xmi_left;
        }

        UIImageView *tagImageView = [[UIImageView alloc] initWithImage:[XMICommonUtils imageNamed:@"pic_ad_mark_8"]];
        [_bottomView addSubview:tagImageView];
        [tagImageView sizeToFit];
        tagImageView.xmi_right = _bottomView.xmi_width - RewardBottomTagInterval;
        tagImageView.xmi_bottom = _bottomView.xmi_height - RewardBottomTagInterval;
        UIButton *closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [closeButton addTarget:self action:@selector(closeBottomViewClick:) forControlEvents:UIControlEventTouchUpInside];
        [closeButton setImage:[XMICommonUtils imageNamed:@"reward_bottom_close"] forState:UIControlStateNormal];
        [_bottomView addSubview:closeButton];
        [closeButton sizeToFit];
        closeButton.xmi_right = _bottomView.xmi_width - RewardBottomCloseInterval;
        closeButton.xmi_centerY = RewardBottomCloseInterval + 7;

        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(actionButtonClick)];
        [_bottomView addGestureRecognizer:tapGesture];
    }
    return _bottomView;
}


- (UIView *)bottomBigView
{
    if (!_bottomBigView) {
        _bottomBigView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.view.xmi_width, 262)];
        CAGradientLayer *layer = [CAGradientLayer layer];
        layer.frame = _bottomBigView.bounds;
        layer.colors = @[(__bridge id)(XMI_COLOR_RGBA(0x000000, 0).CGColor), (__bridge id)(XMI_COLOR_RGBA(0x000000, 0.5f).CGColor)];
        layer.startPoint = CGPointMake(0.5f, 0);
        layer.endPoint = CGPointMake(0.5f, 1);
        [_bottomBigView.layer addSublayer:layer];
        [self.view addSubview:_bottomBigView];
        
        UIImageView *cover = [[UIImageView alloc] init];
        cover.backgroundColor = XMI_COLOR_RGB(0xD8D8D8);
        cover.layer.cornerRadius = 8;
        cover.layer.masksToBounds = YES;
        [_bottomBigView addSubview:cover];
         [cover sd_setImageWithURL:self.bottomViewModel.coverUrl placeholderImage:nil];
        cover.frame = CGRectMake(16, 94, 50, 50);
        
        CGFloat titleMaxWidth = _bottomBigView.xmi_width - 20 - 12 - cover.xmi_right;
        UILabel *titleLabel = [[UILabel alloc] init];
        [_bottomBigView addSubview:titleLabel];
        titleLabel.font = XMI_AD_PingFangMediumFont(16);
        titleLabel.textColor = XMI_COLOR_RGB(0xFFFFFF);
        titleLabel.textAlignment = NSTextAlignmentLeft;
        titleLabel.text = self.bottomViewModel.name;
        titleLabel.xmi_size = [titleLabel sizeThatFits:CGSizeMake(titleMaxWidth, 20)];
        titleLabel.xmi_height = 20;
        titleLabel.xmi_top = cover.xmi_top - 2;
        titleLabel.xmi_left = cover.xmi_right + 12;
        
        
        UILabel *subtitleLabel = [[UILabel alloc] init];
        [_bottomBigView addSubview:subtitleLabel];
        subtitleLabel.numberOfLines = 2;
        NSMutableParagraphStyle *ps = [[NSMutableParagraphStyle alloc] init];
        ps.minimumLineHeight = 18;
        ps.alignment = NSTextAlignmentLeft;
        ps.lineBreakMode = NSLineBreakByCharWrapping;
        subtitleLabel.attributedText = [[NSAttributedString alloc] initWithString:self.bottomViewModel.intro attributes:@{NSFontAttributeName : XMI_AD_PingFangFont(13), NSForegroundColorAttributeName : XMI_COLOR_RGB(0xFFFFFF), NSParagraphStyleAttributeName : ps}];
        [subtitleLabel sizeToFit];
        
        if (titleLabel.text.length <= 0) {
            subtitleLabel.xmi_top = titleLabel.xmi_top;
            subtitleLabel.xmi_width = MIN(subtitleLabel.xmi_width, titleMaxWidth);
            subtitleLabel.xmi_left = titleLabel.xmi_left;
        }
        else {
            subtitleLabel.xmi_top = titleLabel.xmi_bottom + 10;
            subtitleLabel.xmi_width = MIN(subtitleLabel.xmi_width, titleMaxWidth);
            subtitleLabel.xmi_left = titleLabel.xmi_left;
        }
        
        UIButton *actionButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_bottomBigView addSubview:actionButton];
        [actionButton addTarget:self action:@selector(actionButtonClick) forControlEvents:UIControlEventTouchUpInside];
        actionButton.xmi_size = CGSizeMake(287, 36);
        actionButton.xmi_top = subtitleLabel.xmi_bottom + 20;
        actionButton.xmi_centerX = _bottomBigView.xmi_width * 0.5f;
        actionButton.layer.cornerRadius = actionButton.xmi_height * 0.5f;
        actionButton.layer.masksToBounds = YES;
        [actionButton setTitle:self.bottomViewModel.actionButtonText forState:UIControlStateNormal];
        [actionButton setTitleColor:XMI_COLOR_RGB(0xFFFFFF) forState:UIControlStateNormal];
        actionButton.titleLabel.font = XMI_AD_PingFangFont(14);
        self.subscribeButton = actionButton;
        if (self.bottomViewModel.isFavorite) {
            [actionButton setBackgroundImage:[self buttonImage] forState:(UIControlStateNormal)];
            [actionButton setTitle:@"立即查看" forState:UIControlStateNormal];
        }else{
            if (self.relatedData.clickType == 23) {
                [actionButton setBackgroundImage:[self subscribeButtonImage] forState:(UIControlStateNormal)];
            }else{
                [actionButton setBackgroundImage:[self buttonImage] forState:(UIControlStateNormal)];
            }
            [actionButton setTitle:[self adActionButtonText] forState:UIControlStateNormal];
        }
        UIImageView *adMarkView = [[UIImageView alloc] initWithImage:[XMICommonUtils imageNamed:@"pic_ad_mark_9"]];
        [_bottomBigView addSubview:adMarkView];
        adMarkView.xmi_top = actionButton.xmi_bottom + 6;
        adMarkView.xmi_right = _bottomBigView.xmi_width - 16;
        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(actionButtonClick)];
        [_bottomBigView addGestureRecognizer:tapGesture];
    }
    return _bottomBigView;
}

- (UIView *)endCardView
{
    if (!_endCardView) {
        _endCardView = [[UIView alloc] initWithFrame:self.view.bounds];
        [self.view insertSubview:_endCardView belowSubview:self.headerView];
        _endCardView.backgroundColor = XMI_COLOR_RGBA(0x000000, 0.6f);
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(endCardClick:)];
        [self.endCardView addGestureRecognizer:tap];
        
        // container
        UIView *container = [[UIView alloc] initWithFrame:_endCardView.bounds];
        [_endCardView addSubview:container];
        container.xmi_width = _endCardView.xmi_width;
        
        // cover
        UIImageView *cover = [[UIImageView alloc] init];
        cover.backgroundColor = XMI_COLOR_RGB(0xD8D8D8);
        cover.layer.cornerRadius = 8;
        cover.layer.masksToBounds = YES;
        [container addSubview:cover];
        [cover sd_setImageWithURL:self.bottomViewModel.coverUrl];
        cover.xmi_size = CGSizeMake(RewardEndcardCoverWidth, RewardEndcardCoverWidth);
        cover.xmi_centerX = container.xmi_width/2;
        
        // title
        UILabel *title = [[UILabel alloc] init];
        [container addSubview:title];
        title.font = XMI_AD_PingFangMediumFont(18);
        title.textColor = XMI_COLOR_RGB(0xFFFFFF);
        title.text = self.relatedData.name;
        [title sizeToFit];
        CGFloat titleMaxWidth = container.xmi_width - 106;
        title.xmi_width = MIN(title.xmi_width, titleMaxWidth);
        title.xmi_height = 22;
        title.xmi_centerX = cover.xmi_centerX;
        title.xmi_top = cover.xmi_bottom + RewardEndcardTitleTopSpace;

        // subtitle
        UILabel *subtitle = [[UILabel alloc] init];
        [container addSubview:subtitle];
        subtitle.numberOfLines = 2;
        subtitle.textAlignment = NSTextAlignmentCenter;
        subtitle.font = XMI_AD_PingFangFont(14);
        subtitle.textColor = XMI_COLOR_RGB(0xFFFFFF);
       // subtitle.text = self.relatedData.intro;
        subtitle.xmi_size = [subtitle textRectForBounds:(CGRectMake(0, 0, titleMaxWidth, 100)) limitedToNumberOfLines:subtitle.numberOfLines].size;
        subtitle.xmi_centerX = title.xmi_centerX;
        
        if (title.text.length <= 0) {
            subtitle.xmi_top = title.xmi_top;
        }
        else {
            subtitle.xmi_top = title.xmi_bottom + RewardEndcardSubtitleTopSpace;
        }
        
        // button
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        [container addSubview:button];
        button.layer.masksToBounds    = YES;
        button.layer.cornerRadius     = RewardEndcardButtonHeight/2;
        [button setBackgroundImage:[self buttonImage] forState:(UIControlStateNormal)];
        [button setTitleColor:XMI_COLOR_RGB(0xFFFFFF) forState:(UIControlStateNormal)];
        button.titleLabel.font = XMI_AD_PingFangFont(14);
        [button addTarget:self action:@selector(actionButtonClick) forControlEvents:(UIControlEventTouchUpInside)];
    //    [button setTitle:self.adItem.actionButtonText ?: @"下载" forState:UIControlStateNormal];
        button.xmi_size = CGSizeMake(RewardEndcardButtonWidth, RewardEndcardButtonHeight);
        button.xmi_centerX = cover.xmi_centerX;
        button.xmi_top = subtitle.xmi_bottom + RewardEndcardButtonTopSpace;
        self.endSubscribeButton = button;
        if (self.bottomViewModel.isFavorite) {
            [button setBackgroundImage:[self buttonImage] forState:(UIControlStateNormal)];
            [button setTitle:@"立即查看" forState:UIControlStateNormal];
        }else{
            if (self.relatedData.clickType == 23) {
                [button setBackgroundImage:[self subscribeButtonImage] forState:(UIControlStateNormal)];
            }else{
                [button setBackgroundImage:[self buttonImage] forState:(UIControlStateNormal)];
            }
            [button setTitle:[self adActionButtonText] forState:UIControlStateNormal];
        }
        UIButton *replayButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [container addSubview:replayButton];
        replayButton.titleLabel.font = XMI_AD_PingFangFont(14);
        [replayButton setTitleColor:XMI_COLOR_RGB(0xFFFFFF) forState:(UIControlStateNormal)];
        [replayButton setImage:[XMICommonUtils imageNamed:@"reward_icon_replay"] forState:UIControlStateNormal];
        [replayButton setTitle:@"点击重播" forState:UIControlStateNormal];
        replayButton.titleEdgeInsets = UIEdgeInsetsMake(0, RewardReplayButtonInterval, 0, -RewardReplayButtonInterval);
        replayButton.imageEdgeInsets = UIEdgeInsetsMake(0, -RewardReplayButtonInterval, 0, RewardReplayButtonInterval);
        [replayButton addTarget:self action:@selector(replayButtonClick:) forControlEvents:(UIControlEventTouchUpInside)];
        replayButton.xmi_size = CGSizeMake(RewardReplayButtonWidth, RewardReplayButtonHeight);
        replayButton.xmi_centerX = container.xmi_width * 0.5f;
        replayButton.xmi_top = button.xmi_bottom + RewardReplayButtonTop;
        container.xmi_height = replayButton.xmi_bottom;
        container.xmi_centerY = self.endCardView.xmi_height*0.5f;
    }
    return _endCardView;
}

- (UIImage *)buttonImage {
    return [UIImage xm_imageWithColor:XMI_COLOR_RGB(0xFF4444) imageSize:CGSizeMake(1, 1)];
}

- (UIImage *)subscribeButtonImage {
    return [UIImage xm_imageWithColor:XMI_COLOR_RGB(0xFF4444) imageSize:CGSizeMake(1, 1)];
}

- (NSString *)adActionButtonText
{
    return self.relatedData.buttonText ?: (self.relatedData.clickType == 18 ? @"立即下载" : @"查看详情");
}

#pragma mark - click events

- (void)actionButtonClick{
    [self actionClickAction:YES];
}

- (void)actionClickAction:(BOOL)isBottomView
{
    [self.jumpManager doJumpWithAd:self.relatedData];
    if ([self.delegate respondsToSelector:@selector(rewardVideoViewControllerDidClick:)] ) {
        [self.delegate rewardVideoViewControllerDidClick:self];
    }
    if (!self.hadReportManualClick) {
        self.hadReportManualClick = YES;
        [XMIAdReporter clickReportWithAd:self.relatedData andView:self.view andUserInfo:@{@"extraParams" : @{@"autoPull" : @(1), @"clickAreaType" :  @(isBottomView ?  2 : 1)}}];
    }
}

- (void)endCardClick:(id)sender
{
    [self actionClickAction:YES];
}

- (void)replayButtonClick:(id)sender
{
    [self replay];
}


- (void)replay
{
    self.endCardView.hidden = YES;
    self.bottomBigView.hidden = YES;
    self.bottomView.hidden = NO;
    self.replaying = YES;
    [self showBottomView];
    [self.player play];
}

- (void)closeBottomViewClick:(id)sender
{
    self.bottomBigView.alpha = 0;
    self.bottomBigView.hidden = NO;
    self.bottomBigView.xmi_bottom = self.view.xmi_height + 48;
    [UIView animateWithDuration:0.2f animations:^{
            self.bottomView.alpha = 0;
        } completion:^(BOOL finished) {
            self.bottomView.hidden = YES;
            [UIView animateWithDuration:0.2f animations:^{
                self.bottomBigView.alpha = 1.0f;
                self.bottomBigView.xmi_bottom = self.view.xmi_height;
            }];
        }];
}

- (XMIOwnJumpManager *)jumpManager {
    if (_jumpManager == nil) {
        _jumpManager = [[XMIOwnJumpManager alloc] init];
        _jumpManager.delegate = self;
    }
    _jumpManager.rootViewController = self;
    return _jumpManager;
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

- (BOOL)isHorizonal
{
    return self.relatedData.showstyle == 8503 || self.relatedData.showstyle == 53;
}

#pragma mark - header delegate

- (void)headerViewDidClose:(XMIAdRewardVideoHeaderView *)headerView
{
    // linktype=105跳激励视频，不需要挽留弹窗
    if (![self needShowCloseAlert]) {
        [self closeVC];
        return;
    }
    
    if (!headerView.rewardFinished) {
        @weakify(self)
        self.closeAlert = [XMIAdAlertView alertWithType:XMIAdAlertViewTypeCertain contentWidth:275 contentInfo:@"观看完整视频才能获得奖励" cancelButtonTitle:@"放弃奖励" certainButtonTitle:@"继续观看" certainBlock:^{
        }];
        self.closeAlert.cancelBlock = ^{
            @strongify(self)
            if ([self.delegate respondsToSelector:@selector(rewardVideoViewControllerDidSkip:)]) {
                [self.delegate rewardVideoViewControllerDidSkip:self];
            }
            [self closeVC];
        };
        [self.closeAlert show];
        return;
    }
    [self closeVC];
}

- (void)closeVC
{
    [self closeVC:YES];
}

- (void)closeVC:(BOOL)animated
{
    if ([self.delegate respondsToSelector:@selector(rewardVideoViewControllerWillClose:)]) {
        [self.delegate rewardVideoViewControllerWillClose:self];
    }
    [self.presentingViewController dismissViewControllerAnimated:animated completion:^{
        if ([self.delegate respondsToSelector:@selector(rewardVideoViewControllerDidClose:)]) {
            [self.delegate rewardVideoViewControllerDidClose:self];
        }
    }];
}

- (void)headerViewDidFinishCountdown:(XMIAdRewardVideoHeaderView *)headerView
{
    if ([self.delegate respondsToSelector:@selector(rewardVideoViewControllerDidFinishReward:)]) {
        [self.delegate rewardVideoViewControllerDidFinishReward:self];
    }
}

//是否自动跳转
- (BOOL)isJumpAd
{
    if (!self.relatedData.videoAutoJump) {
        return NO;
    }
    //服务端控制
    return YES;
//    /*
//     易经的激励视频广告，广告主投放方式一直是跳转类型填落地页，跳转地址会自动拉起小程序
//     正常情况下拉起小程序的clickType是17不是1
//     所以这里做特殊处理，防止跳转小程序的落地页也会在完播的时候自动拉起
//     */
//    if (self.relatedData.realLink.length > 0) {
//        NSURL *url = [NSURL URLWithString:self.relatedData.realLink];
//        if ([[url.host lowercaseString] containsString:@"youshu"]) {
//            return NO;
//        }
//    }
//    return (self.relatedData.clickType == 1 && self.relatedData.openlinkType == 0 && self.relatedData.linkType != 2 && self.relatedData.dpRealLink.length == 0);
}

- (void)reportLandingClickIfNeeded
{
    if (self.hadReportLandingClick) {
        return;
    }
    self.hadReportLandingClick = YES;
    [XMIAdReporter clickReportWithAd:self.relatedData andView:self.view andUserInfo:@{@"extraParams" : @{@"autoPull" : @(3)}}];
}

- (void)horizonalFullScreenTapped:(UIGestureRecognizer *)gest
{
    if (!self.videoView.superview || CGRectContainsPoint(self.videoView.frame, [gest locationInView:self.view])) {
        return;
    }
    [self actionClickAction:NO];
}


- (BOOL)needShowCloseAlert {
    if (self.relatedData.positionId == 94) {
        return NO;
    }
    return YES;
}
@end
