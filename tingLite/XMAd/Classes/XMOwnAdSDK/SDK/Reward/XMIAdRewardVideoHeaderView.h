//
//  XMIAdRewardVideoHeaderView.h
//  XMAd
//
//  Created by cuiyuanz<PERSON> on 2022/9/8.
//

#import <UIKit/UIKit.h>

#define IS_REWARD_SMALL_SCREEN (XMI_SCREEN_HEIGHT < 700)

NS_ASSUME_NONNULL_BEGIN

@class XMIAdRewardVideoHeaderView;

@protocol XMIAdRewardVideoHeaderViewDelegate <NSObject>

- (void)headerViewDidClose:(XMIAdRewardVideoHeaderView *)headerView;

- (void)headerViewDidFinishCountdown:(XMIAdRewardVideoHeaderView *)headerView;

@end

@interface XMIAdRewardVideoHeaderView : UIView

@property (nonatomic, weak) id<XMIAdRewardVideoHeaderViewDelegate> delegate;

@property (nonatomic, assign) BOOL rewardFinished;

- (instancetype)initWithFrame:(CGRect)frame totalTime:(NSTimeInterval)totalTime skipTime:(NSTimeInterval)skipTime isResume:(BOOL)isResume;

- (void)dismissAlertView;

- (void)finishCountDown;

- (void)viewAppeared;

- (void)viewDisappeared;

@end

NS_ASSUME_NONNULL_END
