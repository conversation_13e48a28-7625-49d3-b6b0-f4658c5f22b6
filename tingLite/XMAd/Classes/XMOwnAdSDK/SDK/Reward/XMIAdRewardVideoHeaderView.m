//
//  XMIAdRewardVideoHeaderView.m
//  XMAd
//
//  Created by cuiyuanzhe on 2022/9/8.
//

#import "XMIAdRewardVideoHeaderView.h"
#import "XMIAdAlertView.h"
#import "XMIAdMacro.h"
#import "UIView+XMIUtils.h"
#import "XMICommonUtils.h"

static NSTimeInterval kXMIRewardAdSkipTime = 0;

@interface XMIAdRewardVideoHeaderView ()

@property (nonatomic, assign)NSTimeInterval lastTime;

@property (strong, nonatomic) NSTimer *timer;

@property (nonatomic, assign) NSTimeInterval totalTime;

@property (nonatomic, assign) NSTimeInterval timerRemainTime;

@property (nonatomic, assign) NSTimeInterval timerLastTime;

@property (nonatomic, strong) UILabel *tipLabel;

@property (nonatomic, strong) UILabel *countdownLabel;

@property (nonatomic, strong) UIButton* closeBtn;

@property (strong, nonatomic) UIButton *vipButton;

@property (nonatomic, strong) XMIAdAlertView *closeAlert;

@end

@implementation XMIAdRewardVideoHeaderView

- (instancetype)initWithFrame:(CGRect)frame totalTime:(NSTimeInterval)totalTime skipTime:(NSTimeInterval)skipTime isResume:(BOOL)isResume
{
    self = [self initWithFrame:frame];
    if (self) {
        if (totalTime > 0) {
            self.totalTime = MIN(totalTime, skipTime);
        } else {
            self.totalTime = skipTime;
        }
        if (isResume) {
            self.totalTime = kXMIRewardAdSkipTime > 0 ? kXMIRewardAdSkipTime : self.totalTime;
        }
        self.timerRemainTime = self.totalTime;
        [self setupUI];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appDidBecomeActive:) name:UIApplicationDidBecomeActiveNotification object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appWillResignActive:) name:UIApplicationWillResignActiveNotification object:nil];
    }
    return self;
}

- (void)viewAppeared
{
    if (!self.rewardFinished) {
        self.lastTime = [[NSDate date] timeIntervalSince1970];
        [self start:YES];
    }
}

- (void)viewDisappeared
{
    if (!self.rewardFinished) {
        [self start:NO];
    }
}

- (void)removeFromSuperview {
    [self.timer invalidate];
    self.timer = nil;
    [super removeFromSuperview];
}

- (void)start:(BOOL)start {
    [self.timer invalidate];
    self.timer = nil;
    if (start && self.timerRemainTime > 0) {
        self.timerLastTime = [[NSDate date] timeIntervalSince1970];
        self.timer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(timerFired:) userInfo:nil repeats:YES];
        [self.timer fire];
    }
}

- (void)timerFired:(NSTimer *)timer {
    [self.superview bringSubviewToFront:self];
    if (self.rewardFinished) {
        return;
    }
    NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970];
    NSTimeInterval interval = currentTime - self.timerLastTime;
    self.timerRemainTime -= interval;
    self.timerLastTime = currentTime;
    kXMIRewardAdSkipTime = self.timerRemainTime;
    [self updateUI];
    
}

- (void)setupUI
{
    self.backgroundColor = XMI_COLOR_RGB(0x010002);
    
    CGFloat space = 16;
    CGFloat interval = 12;

    self.tipLabel = [[UILabel alloc] init];
    [self addSubview:self.tipLabel];
    self.tipLabel.font = XMI_AD_PingFangFont(12);
    self.tipLabel.textColor = XMI_COLOR_RGB(0xFFFFFF);
    self.tipLabel.textAlignment = NSTextAlignmentCenter;
    self.tipLabel.text = @"看完本条视频广告，即可免费解锁本条付费声音";
    [self.tipLabel sizeToFit];
    self.tipLabel.xmi_height = 16;

    self.tipLabel.xmi_left = space;
    self.tipLabel.xmi_top = IS_REWARD_SMALL_SCREEN ? 14 : 33;
    self.tipLabel.hidden = YES;

    self.closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [self addSubview:self.closeBtn];
    [self.closeBtn addTarget:self action:@selector(didClickClose) forControlEvents:UIControlEventTouchUpInside];
    self.closeBtn.frame = CGRectMake(space, self.tipLabel.xmi_bottom + 8 , 26, 26);
    UIImage *image = [XMICommonUtils imageNamed:@"reward_video_close"];
    if (image) {
        [self.closeBtn setImage:image forState:UIControlStateNormal];
    } else {
        [self.closeBtn setTitle:@"关闭" forState:UIControlStateNormal];
        [self.closeBtn setTitleColor:XMI_COLOR_RGB(0xFFFFFF) forState:UIControlStateNormal];
        self.closeBtn.titleLabel.font = XMI_AD_PingFangFont(12);
        [self.closeBtn sizeToFit];
    }
    
    
    self.countdownLabel = [[UILabel alloc] init];
    [self addSubview:self.countdownLabel];
    self.countdownLabel.font = self.tipLabel.font;
    self.countdownLabel.textColor = self.tipLabel.textColor;
    self.countdownLabel.textAlignment = NSTextAlignmentCenter;
    self.countdownLabel.text = [NSString stringWithFormat:@"%zd", (NSInteger)self.timerRemainTime];
    self.countdownLabel.backgroundColor = XMI_COLOR_RGBA(0x000000, 0.7f);
    self.countdownLabel.layer.cornerRadius = 13;
    self.countdownLabel.layer.borderColor = XMI_COLOR_RGBA(0xFFFFFF, 0.3f).CGColor;
    self.countdownLabel.layer.borderWidth = 0.5f;
    self.countdownLabel.layer.masksToBounds = YES;
    self.countdownLabel.xmi_size = CGSizeMake(26, 26);
    self.countdownLabel.xmi_centerY = self.closeBtn.xmi_centerY;
    self.countdownLabel.xmi_right = self.xmi_width - space;
    self.countdownLabel.autoresizingMask = UIViewAutoresizingFlexibleLeftMargin;

    //跳转vip
//    self.vipButton = [UIButton buttonWithType:UIButtonTypeCustom];
//    [self addSubview:self.vipButton];
//    [self.vipButton addTarget:self action:@selector(vipButtonClick:) forControlEvents:UIControlEventTouchUpInside];
//    [self.vipButton setTitle:@"开通会员 免广告畅听" forState:UIControlStateNormal];
//    [self.vipButton setTitleColor:colorFromRGB(0xF9C660) forState:UIControlStateNormal];
//    self.vipButton.titleLabel.font = kPingFangSCFont(11);
//    self.vipButton.size = CGSizeMake(127, 26);
//    self.vipButton.left = self.closeBtn.right + interval;
//    self.vipButton.centerY = self.closeBtn.centerY;
//    self.vipButton.backgroundColor = colorFromRGBA(0x000000,0.6f);
//    self.vipButton.layer.cornerRadius = 13;
//    self.vipButton.layer.borderWidth = 0.5f;
//    self.vipButton.layer.borderColor = colorFromRGBA(0xFFFFFF, 0.3f).CGColor;
}

- (void)updateUI
{
    if (self.timerRemainTime <= 0) {
        self.timerRemainTime = 0;
        [self.timer invalidate];
        self.timer = nil;
        self.countdownLabel.hidden = YES;
        self.tipLabel.text = @"已获得免费解锁特权";
        [self.tipLabel sizeToFit];
        self.tipLabel.xmi_height = 16;
        if (!self.rewardFinished && [self.delegate respondsToSelector:@selector(headerViewDidFinishCountdown:)]) {
            [self.delegate headerViewDidFinishCountdown:self];
        }
        self.rewardFinished = YES;
        return;
    }
    self.countdownLabel.text = [NSString stringWithFormat:@"%zd", (NSUInteger)ceil(self.timerRemainTime)];
}

- (void)didClickClose {
    if ([self.delegate respondsToSelector:@selector(headerViewDidClose:)]) {
        [self.delegate headerViewDidClose:self];
    }
}


- (void)dismissAlertView
{
    [self.closeAlert dismiss];
    [self.timer invalidate];
    self.timer = nil;
}

- (void)dealloc
{
    [self.timer invalidate];
    self.timer = nil;
}



//- (void)vipButtonClick:(id)sender
//{
//    if (![[XMConfigCenter sharedConfigCenter] getBoolValueWithGroup:@"ad" andItem:@"rewardVideoSupportVip" defaultValue:YES]) {
//        return;
//    }
//    if (self.toVipBlock) {
//        self.toVipBlock();
//    }
//}


- (void)appDidBecomeActive:(NSNotification *)notification
{
    if (!self.superview) {
        return;
    }
    [self start:YES];
}

- (void)appWillResignActive:(NSNotification *)notification
{
    [self start:NO];
}

- (void)finishCountDown
{
    [self start:NO];
    self.timerRemainTime = 0;
    kXMIRewardAdSkipTime = 0;
    [self updateUI];
    [self dismissAlertView];
}
/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
