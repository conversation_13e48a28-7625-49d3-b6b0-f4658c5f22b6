//
//  XMIOwnRewardAdFactory.m
//  XMAd
//
//  Created by cu<PERSON>yuanz<PERSON> on 2022/9/21.
//

#import "XMIOwnRewardAdFactory.h"
#import "XMIOwnRewardAd.h""

@implementation XMIOwnRewardAdFactory

+ (XMIRewardAd *)rewardAdWithRelatedData:(XMIAdRelatedData *)relatedData
{
    XMIRewardAd *rewardAd = [[XMIOwnRewardAd alloc] init];
    rewardAd.relatedData = relatedData;
    return rewardAd;
}


@end
