//
//  XMIOwnRewardAd.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/14.
//

#import "XMIOwnRewardAd.h"
#import "XMIRewardVideoViewController.h"

@interface XMIOwnRewardAd ()<XMIRewardVideoViewControllerDelegate>

@property (nonatomic, strong) XMIRewardVideoViewController *rewardVideoViewController;


@end

@implementation XMIOwnRewardAd
{
    BOOL _mute;
}
@dynamic mute;


- (void)startLoadAd
{
    if (self.rewardVideoViewController) {
        return;
    }
    self.rewardVideoViewController = [[XMIRewardVideoViewController alloc] init];
    self.rewardVideoViewController.delegate = self;
    if (self.relatedData.videoUrl) {
        self.rewardVideoViewController.relatedData = self.relatedData;
    }
    [self.rewardVideoViewController startLoadVideo];
}

- (void)showFromRootViewController:(UIViewController *)rootViewController
{
    [self controllerWillShow];
    [rootViewController presentViewController:self.rewardVideoViewController animated:YES completion:^{
        [self controllerDidShow];
    }];
}

- (void)setMute:(BOOL)mute
{
    _mute = mute;
    self.rewardVideoViewController.mute = mute;
}

- (BOOL)mute
{
    return _mute;
}

#pragma mark - reward delegate;

- (void)rewardVideoViewControllerDidFinishReward:(XMIRewardVideoViewController *)controller {
    [self rewardSuccess:@{}];
}

- (void)rewardVideoViewController:(nonnull XMIRewardVideoViewController *)controller playerDidFailWithError:(nonnull NSError *)error {
    [self loadFail:error];
}


- (void)rewardVideoViewControllerDidClick:(nonnull XMIRewardVideoViewController *)controller {
    [self didClickAd];
}

- (void)rewardVideoViewControllerDidFinishPlay:(nonnull XMIRewardVideoViewController *)controller {
    [self playFinished];
}

- (void)rewardVideoViewControllerDidSkip:(nonnull XMIRewardVideoViewController *)controller {
    [self didClickSkip];
}

- (void)rewardVideoViewControllerPlayerDidReady:(nonnull XMIRewardVideoViewController *)controller {
    [self loadSuccess];
    [self loadVideoSuccess];
}

- (void)rewardVideoViewControllerWillClose:(XMIRewardVideoViewController *)controller
{
    [self controllerWillClose];
}

- (void)rewardVideoViewControllerDidClose:(XMIRewardVideoViewController *)controller
{
    [self controllerDidHide];
}


@end
