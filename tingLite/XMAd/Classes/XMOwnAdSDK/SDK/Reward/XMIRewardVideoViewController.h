//
//  XMIRewardVideoViewController.h
//  hpple
//
//  Created by cuiyuanzhe on 2022/6/13.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class XMIRewardVideoViewController, XMIAdRelatedData;

@protocol XMIRewardVideoViewControllerDelegate <NSObject>

@optional

- (void)rewardVideoViewControllerPlayerDidReady:(XMIRewardVideoViewController *)controller;

- (void)rewardVideoViewController:(XMIRewardVideoViewController *)controller playerDidFailWithError:(NSError *)error;

- (void)rewardVideoViewControllerWillAppear:(XMIRewardVideoViewController *)controller;

- (void)rewardVideoViewControllerWillDisappear:(XMIRewardVideoViewController *)controller;

- (void)rewardVideoViewControllerDidAppear:(XMIRewardVideoViewController *)controller;

- (void)rewardVideoViewControllerDidDisappear:(XMIRewardVideoViewController *)controller;

- (void)rewardVideoViewControllerDidFinishReward:(XMIRewardVideoViewController *)controller;

- (void)rewardVideoViewControllerDidClick:(XMIRewardVideoViewController *)controller;

- (void)rewardVideoViewControllerDidSkip:(XMIRewardVideoViewController *)controller;

- (void)rewardVideoViewControllerWillClose:(XMIRewardVideoViewController *)controller;

- (void)rewardVideoViewControllerDidClose:(XMIRewardVideoViewController *)controller;

- (void)rewardVideoViewControllerDidFinishPlay:(XMIRewardVideoViewController *)controller;

@end

@interface XMIRewardVideoViewController : UIViewController

@property (nonatomic, strong) XMIAdRelatedData *relatedData;

@property (nonatomic, assign) NSTimeInterval countdownTotalTime;

@property (nonatomic, weak) id<XMIRewardVideoViewControllerDelegate> delegate;

@property (nonatomic, assign) NSTimeInterval timeoutLimit;

@property (nonatomic, assign) BOOL resume;

@property (nonatomic, assign) BOOL mute;

- (void)startLoadVideo;

@end

NS_ASSUME_NONNULL_END
