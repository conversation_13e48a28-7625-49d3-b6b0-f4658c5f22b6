//
//  XMIOwnExpressAdManager.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/26.
//

#import "XMIOwnExpressAdManager.h"
#import "XMIAdError.h"

@interface XMIOwnExpressAdManager ()

@property (nonatomic, strong) XMIAdSlot *adSlot;

@end

@implementation XMIOwnExpressAdManager

- (instancetype)initWithSlot:(XMIAdSlot *)adSlot {
    self = [super init];
    if (self) {
        self.adSlot = adSlot;
    }
    
    return self;
}

- (void)loadAdData {
    // 为保持接口一致，模拟异步
    
//    @weakify(self)
//    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
//        @strongify(self)
//        if (!self) {
//            return;
//        }
//
//        dispatch_async(dispatch_get_main_queue(), ^{
//            [self didLoadData];
//        });
//    });
    [self didLoadData];
}

- (void)didLoadData {
    if (self.adSlot.relatedData == nil) {
        if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdManager:didLoadFailWithError:)]) {
            [self.delegate expressAdManager:self didLoadFailWithError:[XMIAdError emptyDataError]];
        }
        return;
    }
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdManager:didLoadData:)]) {
        [self.delegate expressAdManager:self didLoadData:self.adSlot.relatedData];
    }
}

@end
