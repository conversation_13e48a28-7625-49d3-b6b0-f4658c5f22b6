//
//  XMIOwnExpressAdManager.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/26.
//

#import <Foundation/Foundation.h>
#import "XMIAdDefines.h"
#import "XMIAdSlot.h"

NS_ASSUME_NONNULL_BEGIN

@class XMIOwnExpressAdManager;
@class XMIExpressAdView;

@protocol XMIOwnExpressAdDelegate <NSObject>

@optional
- (void)expressAdManager:(XMIOwnExpressAdManager *)manager didLoadData:(XMIAdRelatedData *)relatedData;
- (void)expressAdManager:(XMIOwnExpressAdManager *)manager didLoadFailWithError:(NSError *)error;
- (void)expressAdManager:(XMIOwnExpressAdManager *)manager adViewDidRender:(XMIExpressAdView *)adView hasNeedRefresh:(BOOL)isNeedRefresh;
- (void)expressAdManager:(XMIOwnExpressAdManager *)manager adView:(XMIExpressAdView *)adView didRenderFailWithError:(NSError *)error;
- (void)expressAdManager:(XMIOwnExpressAdManager *)manager adViewWillShow:(UIView *)aView;
- (void)expressAdManager:(XMIOwnExpressAdManager *)manager adViewDidClick:(UIView *)aView withUserInfo:(nullable NSDictionary *)userInfo;
- (void)expressAdManager:(XMIOwnExpressAdManager *)manager sdkNotSupportWithAdView:(XMIExpressAdView *)adView adViewDidClick:(UIView *)aView withUserInfo:(nullable NSDictionary *)userInfo;
- (void)expressAdManager:(XMIOwnExpressAdManager *)manager adView:(XMIExpressAdView *)adView playerStateChanged:(XMIPlayerPlayState)state;
- (void)expressAdManager:(XMIOwnExpressAdManager *)manager adView:(XMIExpressAdView *)adView playerDidPlayFinish:(nullable NSError *)error;
- (void)expressAdManager:(XMIOwnExpressAdManager *)manager adViewPlayerDidBecomeInvisible:(XMIExpressAdView *)adView;
- (void)expressAdManager:(XMIOwnExpressAdManager *)manager adViewWillPresentScreen:(UIView *)aView;
- (void)expressAdManager:(XMIOwnExpressAdManager *)manager adViewDidPresentScreen:(UIView *)aView;
- (void)expressAdManager:(XMIOwnExpressAdManager *)manager adViewDetailControllerDidClosed:(UIView *)aView;
- (void)expressAdManager:(XMIOwnExpressAdManager *)manager adViewDidClickedClose:(UIView *)aView;
@end


@interface XMIOwnExpressAdManager : NSObject

@property (nonatomic, weak) id<XMIOwnExpressAdDelegate> delegate;
@property (nonatomic, weak) UIViewController *rootViewController;

- (instancetype)initWithSlot:(XMIAdSlot *)adSlot;

- (void)loadAdData;

@end

NS_ASSUME_NONNULL_END
