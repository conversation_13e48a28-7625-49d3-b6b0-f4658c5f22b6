//
//  XMIOwnExpressAdDoubleView.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/12/4.
//

#import "XMIExpressAdDoubleView.h"
#import "XMIOwnExpressAdViewDelegate.h"

NS_ASSUME_NONNULL_BEGIN
@class XMIAdNewVideoPlayer, XMIAdVideoView, XMAdDownloadMediaManager;
@interface XMIOwnExpressAdDoubleView : XMIExpressAdDoubleView

@property(nonatomic, strong, readonly)UIView *audioCoverView;
@property (nonatomic, strong, nullable) XMIAdNewVideoPlayer *videoPlayer;
@property (nonatomic, strong) UIView *videoView;
@property (nonatomic, strong) XMIAdRelatedData *adData;
@property (nonatomic, strong) XMAdDownloadMediaManager *downLoadManager;

/**
 曝光状态，用于视频播放暂停
 */
@property (nonatomic, assign) BOOL lastExposed;

- (void)updatePlayerWith:(XMIAdNewVideoPlayer *)videoPlayer
                 getSize:(CGSize)playerSize;
@end

NS_ASSUME_NONNULL_END
