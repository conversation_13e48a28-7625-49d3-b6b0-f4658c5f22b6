//
//  XMIOwnExpressAdView+ShopWindow.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/2.
//

#import "XMIOwnExpressAdView+ShopWindow.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import "XMIAdMacro.h"
#import "XMICommonUtils.h"
#import "XMIAdRelatedData.h"
#import "XMIAdDataCenter.h"
#import "XMIAdViewProtocol.h"

@class XMIShopView;

@protocol XMIShopViewDelegate <NSObject>

@optional
- (void)shopView:(XMIShopView *)shopView didTap:(UITapGestureRecognizer *)tap;

@end

@interface XMIShopView : UIView<XMIAdViewProtocol>

@property (nonatomic, strong) XMIAdRelatedData *adData;
@property (nonatomic, weak) id delegate;

@end

@implementation XMIShopView

- (instancetype)initWithFrame:(CGRect)frame andRelatedAd:(XMIAdRelatedData *)relatedData {
    self = [super initWithFrame:frame];
    if (self) {
        [self commonInit:relatedData];
        [self setupWithRelatedAd:relatedData];
    }
    
    return self;
}

- (instancetype)initMoreWithFrame:(CGRect)frame andRelatedAd:(XMIAdRelatedData *)relatedData {
    self = [super initWithFrame:frame];
    if (self) {
        [self commonInit:relatedData];
        [self setupMoreView];
    }
    
    return self;
}

- (void)commonInit:(XMIAdRelatedData *)relatedData {
    self.adData = relatedData;
    self.backgroundColor = [UIColor whiteColor];
    self.layer.cornerRadius = 4;
    self.layer.masksToBounds = YES;
    
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleViewTap:)];
    [self addGestureRecognizer:tap];
}

- (void)setupWithRelatedAd:(XMIAdRelatedData *)relatedData {
    CGFloat itemWidth = self.frame.size.width;
    UIFont *itemFont = XMI_AD_PingFangFont(13);
    // image
    UIImageView *imageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, itemWidth, itemWidth)];
    if (relatedData.cover != nil) {
        [imageView sd_setImageWithURL:[NSURL URLWithString:relatedData.cover] placeholderImage:[XMICommonUtils imageNamed:@"ad_bkg_default"]];
    } else {
        imageView.image = [XMICommonUtils imageNamed:@"ad_bkg_default"];
    }
    [self addSubview:imageView];
    // detail
    UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(5, itemWidth + 14, itemWidth - 5 * 2, itemFont.lineHeight)];
    label.font = itemFont;
    label.textColor = XMI_COLOR_RGB(0x333333);
    label.backgroundColor = [UIColor clearColor];
    label.lineBreakMode = NSLineBreakByTruncatingTail;
    label.text = relatedData.name;
    [self addSubview:label];
}

/**
 查看更多 特殊视图
 */
- (void)setupMoreView {
    CGFloat itemWidth = self.frame.size.width;
    CGFloat itemHeight = self.frame.size.height;
    UIFont *itemFont = XMI_AD_PingFangFont(12);
    // 查看更多
    UILabel *moreLabel1 = [[UILabel alloc] initWithFrame:CGRectMake(0, itemHeight / 2 - itemFont.lineHeight - 3, itemWidth, itemFont.lineHeight)];
    moreLabel1.font = itemFont;
    moreLabel1.textColor = XMI_COLOR_RGB(0x666666);
    moreLabel1.textAlignment = NSTextAlignmentCenter;
    moreLabel1.text = @"查看更多";
    [self addSubview:moreLabel1];
    // 分割线
    CGFloat lineWidth = itemWidth * 0.6;
    CGFloat lineHeight = 1 / [UIScreen mainScreen].scale;
    UIView *lineView = [[UIView alloc] initWithFrame:CGRectMake((itemWidth - lineWidth) / 2, itemHeight / 2, lineWidth, lineHeight)];
    lineView.backgroundColor = XMI_COLOR_RGB(0x666666);
    [self addSubview:lineView];
    // more
    UILabel *moreLabel2 = [[UILabel alloc] initWithFrame:CGRectMake(0, itemHeight / 2 + 3, itemWidth, itemFont.lineHeight)];
    moreLabel2.font = itemFont;
    moreLabel2.textColor = XMI_COLOR_RGB(0x666666);
    moreLabel2.textAlignment = NSTextAlignmentCenter;
    moreLabel2.text = @"more";
    [self addSubview:moreLabel2];
}

- (void)handleViewTap:(UITapGestureRecognizer *)tap {
    if (self.delegate && [self.delegate respondsToSelector:@selector(shopView:didTap:)]) {
        [self.delegate shopView:self didTap:tap];
    }
}

- (void)willMoveToWindow:(UIWindow *)newWindow {
    [super willMoveToWindow:newWindow];
}

- (void)didMoveToWindow {
    [super didMoveToWindow];
}

#pragma mark - XMIAdViewProtocol
/**
 获取关联广告数据
 橱窗样式内包含很多个广告，获取广告数据需要特殊处理，上报需要
 */
- (XMIAdRelatedData *)getRelatedAdData {
    return self.adData;
}
/**
 获取关联的XMIExpressAdView
 */
- (XMIExpressAdView *)getExpressAdView {
    // shopView - scrollView - contentView - expressAdView
    return (XMIExpressAdView *)self.superview.superview.superview;
}

@end

@implementation XMIOwnExpressAdView (ShopWindow)

- (UIView *)buildShopWindowView:(XMIAdRelatedData *)relatedData {
    // scrollview布局 左16，间隔10. 一屏显示3.5个元素
    CGFloat itemWidth = [self scrollItemWidth];
    CGRect frame = CGRectMake(0, 0, XMI_SCREEN_WIDTH, [self shopWindowHeight]);
    UIScrollView *scrollView = [[UIScrollView alloc] initWithFrame:frame];
    scrollView.showsHorizontalScrollIndicator = NO;
    scrollView.alwaysBounceHorizontal = YES;
    scrollView.backgroundColor = XMI_COLOR_RGB(0xf0f0f0);
    // 渐变色
    [self makeViewGradient:scrollView];
    // 元素
    NSArray<XMIAdRelatedData *> *rDataArray = [self getRelatedAdData:relatedData];
    CGRect rect = CGRectMake(16, 0, itemWidth, frame.size.height - 6);
    for (int i = 0; i < rDataArray.count; i++) {
        XMIAdRelatedData *rData = rDataArray[i];
        XMIShopView *itemView;
        if (i == rDataArray.count - 1) {
            itemView = [[XMIShopView alloc] initMoreWithFrame:rect andRelatedAd:rData];
        } else {
            itemView = [[XMIShopView alloc] initWithFrame:rect andRelatedAd:rData];
        }
        itemView.delegate = self;
        [scrollView addSubview:itemView];
        //
        rect = CGRectOffset(rect, rect.size.width + 10, 0);
    }
    scrollView.contentSize = CGSizeMake(16 * 2 + rDataArray.count * itemWidth + (rDataArray.count - 1) * 10, scrollView.frame.size.height);
    
    return scrollView;
}

- (CGFloat)scrollItemWidth {
    // 一屏显示3.5个元素，图片是正方形
    return (XMI_SCREEN_WIDTH - 16 - 3 * 10) / 3.5;
}

- (CGFloat)shopWindowHeight {
    UIFont *itemFont = XMI_AD_PingFangFont(13);
    return [self scrollItemWidth] + 14 * 2 + itemFont.lineHeight + 6;
}

- (void)makeViewGradient:(UIView *)aView {
    CAGradientLayer *layer = [CAGradientLayer layer];
    layer.colors = @[(__bridge  id)[UIColor whiteColor].CGColor, (__bridge  id)XMI_COLOR_RGB(0xf0f0f0).CGColor];
    layer.startPoint = CGPointMake(0, 0);
    layer.endPoint = CGPointMake(0, 1.0);
    layer.frame = aView.bounds;
    [aView.layer addSublayer:layer];
}

/**
 橱窗样式特殊处理，获取showstyle为20的若干个广告
 */
- (NSArray<XMIAdRelatedData *> *)getRelatedAdData:(XMIAdRelatedData *)relatedData {
    return [XMIAdDataCenter getShopWindowAdData:relatedData.responseId withSlotId:relatedData.slotId];
}

#pragma mark - XMIShopViewDelegate
- (void)shopView:(XMIShopView *)shopView didTap:(UITapGestureRecognizer *)tap {
    [self adViewDidTap:tap];
}

@end
