//
//  XMIOwnExpressAdDoubleView+video.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/12/4.
//

#import "XMIOwnExpressAdDoubleView+video.h"
#import "XMIAdDefines.h"
#import "XMIAdMacro.h"
#import "XMIAdError.h"
#import "UIView+XMIUtils.h"
#import "XMIAdRelatedData.h"
#import "XMIAdReportHelper.h"
#import "XMIAnimatedImageView.h"
#import "XMIAdHelper.h"

@interface XMIOwnExpressAdDoubleView ()<XMIAdVideoPlayerDelegate>

@end

@implementation XMIOwnExpressAdDoubleView (video)

- (XMIAdNewVideoPlayer *)buildVideoPlayer {
    
    XMIAdNewVideoPlayer *videoPlayer = [XMIAdNewVideoPlayer createVideoPlayer];
    videoPlayer.delegate = self;
    videoPlayer.volume = 0.0;
    videoPlayer.repeat = YES;
    videoPlayer.view.alpha = 0.0;
    NSString *identifier = [self.adData getIdentifier];
    videoPlayer.identifier = identifier;
    return videoPlayer;
}

#pragma mark - XMIAdVideoPlayerDelegate
- (void)player:(XMIAdNewVideoPlayer *)player playStateDidChanged:(XMIAdPlayerPlayState)state {
    NSString *identifier = [self.adData getIdentifier];
    if (![player.identifier isEqualToString:identifier]) {
        return;
    }
    if (state == XMIAdPlayerStateBuffering) {
        /// 此时可以获取到视频的宽高等
        if (self.adData.loadingStatus != XMIAdRelatedLoadingStatusHasSureSizeRadio) {
            [self.videoPlayer getVideoSize];
        }
    }
    if (state == XMIAdPlayerStatePlaying || state == XMIAdPlayerStateReady) {
        player.view.alpha = 1.0;
    }
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdView:playerStateChanged:)]) {
        [self.delegate expressAdView:self playerStateChanged:(XMIPlayerPlayState)state];
    }
}

- (void)player:(XMIAdNewVideoPlayer *)player getPlayerSize:(CGSize)playerSize
{
    [self updatePlayerWith:player getSize:playerSize];
}

- (void)player:(XMIAdNewVideoPlayer *)player failWithError:(NSError *)error {
    NSString *identifier = [self.adData getIdentifier];
    if (![player.identifier isEqualToString:identifier]) {
        return;
    }
    [self.videoPlayer stop];
    self.videoView.hidden = YES;
    XMILog(@"-- play fail, %@", error);
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdView:playerDidPlayFinish:)]) {
        [self.delegate expressAdView:self playerDidPlayFinish:error];
    }
}

- (void)player:(XMIAdNewVideoPlayer *)player playTimeDidChanged:(CGFloat)currentTime {
    NSString *identifier = [self.adData getIdentifier];
    if (![player.identifier isEqualToString:identifier]) {
        return;
    }
//    XMILog(@"-- progress %.2f", currentTime);
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdView:playTimeDidChanged:)]) {
        [self.delegate expressAdView:self playTimeDidChanged:currentTime];
    }
}

- (void)player:(XMIAdNewVideoPlayer *)player playDidFinish:(NSError *)error {
    NSString *identifier = [self.adData getIdentifier];
    if (![player.identifier isEqualToString:identifier]) {
        return;
    }
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdView:playerDidPlayFinish:)]) {
        [self.delegate expressAdView:self playerDidPlayFinish:error];
    }
}

//- (void)scrollViewDidScroll {
//    [super scrollViewDidScroll];
//
//}

///**
// 通知滑动停止了
// */
- (void)scrollViewDidEndScroll
{
    [super scrollViewDidEndScroll];

    if (self.adData.showstyle == XMIAdStyleHomeDoubleRowVerticalVideo) {
        // 视频广告需要反复检测曝光状态，以控制播放暂停
        NSString *videoUrl = self.adData.videoUrl;
        if (!videoUrl || ![NSURL URLWithString:videoUrl]) {
            if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdView:playerDidPlayFinish:)]) {
                [self.delegate expressAdView:self playerDidPlayFinish:[XMIAdError emptyDataError]];
            }
            return;
        }
        
    }
}


- (int)getVideoPlayTime {
    if (self.videoPlayer == nil) {
        return 0;
    }
    return (int)(self.videoPlayer.currentTime * 1000);
}

- (int)getVideoDuration {
    if (self.videoPlayer == nil) {
        return 0;
    }
    return (int)(self.videoPlayer.duration * 1000);
}


- (void)startVideoToPlay:(XMIAdRelatedData *)relatedData
{

    if (!relatedData.videoUrl) {
        return;
    }
    [XMIAdHelper showDebugMessageWhenPlayVideo:relatedData];
    // 在配置项adAlwaysPlayVideo为false，且(当前在流量下，且无视频缓存时)，不去播放视频
    if (![XMIAdHelper canPlayVideoOnWIFIOrCached:relatedData]) {
        return;
    }
    
    self.videoView.hidden = NO;
    
    NSString *identifier = [relatedData getIdentifier];
    if (![self.videoPlayer.identifier isEqualToString:identifier]) {
        self.videoPlayer.identifier = identifier;
        [self.videoPlayer startPlayWithURL:[NSURL URLWithString:relatedData.videoUrl] playerView:self.videoView];
    }
    self.videoView.frame = self.coverImageView.bounds;
    if (!self.videoPlayer.isPlaying) {
        [self.videoPlayer play];
    }
}

- (void)cleanVideoPlayer
{
    [self.videoPlayer stop];
    self.videoView.hidden = YES;
    self.videoPlayer = nil;
}

/**
 overwrite super methods
 */
- (void)didMoveToWindow {
    [super didMoveToWindow];
    
    if (self.adData.showstyle == XMIAdStyleHomeDoubleRowVerticalVideo) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self doDetectOwnExpose];
        });
    }
}

- (void)scrollViewDidScroll {
    [super scrollViewDidScroll];
    // 背景板模式图片随滚动改变位置
    if (self.adData.showstyle == XMIAdStyleHomeDoubleRowVerticalVideo) {
        // 视频广告需要反复检测曝光状态，以控制播放暂停
        [self doDetectOwnExpose];
    }
}

- (void)viewExposeChanged:(BOOL)isExposed {
    XMILog(@"--> %d", isExposed);
    // 曝光了，视频广告才开始播放
    if (self.adData.showstyle == XMIAdStyleHomeDoubleRowVerticalVideo) {
        if (isExposed) {
            if (![self.videoPlayer isPlaying]) {
                [self.videoPlayer play];
            }
        } else {
            if (self.videoPlayer.isPlaying) {
                [self.videoPlayer pause];
            }
        }
    }
}

- (void)doDetectOwnExpose {
    BOOL isExposed = [self xmi_isExposed:self.rootViewController.view radio:0.01];
    if (isExposed != self.lastExposed) {
        self.lastExposed = isExposed;
        if (!isExposed) {
            [self playerDidDisappear];
        }
    }
    [self viewExposeChanged:isExposed];
    
}

- (void)playerDidDisappear
{
    if (self.adData.showstyle == XMIAdStyleHomeDoubleRowVerticalVideo) {
        if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdView:relatedData:andPlayStatusChange:)]) {
            [self.delegate expressAdView:self relatedData:self.adData andPlayStatusChange:XMIAdPlayStatusInvisible];
        }
    }
}

/// 广告按钮播放
- (void)adViewDidCloseClickedResult
{
    [self playerIsPlayingDidClose];
    
}

- (void)playerIsPlayingDidClose
{
    if (!self.videoPlayer.isPlaying) {
        return;
    }
    [self.videoPlayer stop];
    self.videoView.hidden = YES;
    if (self.adData.showstyle == XMIAdStyleHomeDoubleRowVerticalVideo) {
        if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdView:relatedData:andPlayStatusChange:)]) {
            [self.delegate expressAdView:self relatedData:self.adData andPlayStatusChange:XMIAdPlayStatusManualClose];
        }
    }
}



@end
