//
//  XMIExpressAdView+Video.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/29.
//

#import "XMIOwnExpressAdView+Video.h"
#import "XMIAdMacro.h"

@interface XMIOwnExpressAdView ()<XMIAdVideoPlayerDelegate>

@end

@implementation XMIOwnExpressAdView (Video)

- (XMIAdVideoPlayer *)buildVideoPlayer:(NSString *)videoUrl {
    if (videoUrl == nil) {
        return nil;
    }
    
    XMIAdVideoPlayer *videoPlayer = [[XMIAdVideoPlayer alloc] init];
    videoPlayer.delegate = self;
    videoPlayer.volume = 0;
    videoPlayer.repeat = YES;
    [videoPlayer setURL:[NSURL URLWithString:videoUrl]];
    videoPlayer.view.alpha = 0.0;
    
    return videoPlayer;
}

#pragma mark - XMIAdVideoPlayerDelegate
- (void)player:(XMIAdVideoPlayer *)player playStateDidChanged:(XMIAdPlayerPlayState)state {
    XMILog(@"state change %ld", (long)state);
    if (state == XMIAdPlayerStatePlaying || state == XMIAdPlayerStateReady) {
        player.view.alpha = 1.0;
    }
    if (self.ownDelegate && [self.ownDelegate respondsToSelector:@selector(expressAdView:playerStateChanged:)]) {
        [self.ownDelegate expressAdView:self playerStateChanged:state];
    }
}

- (void)player:(XMIAdVideoPlayer *)player failWithError:(NSError *)error {
    XMILog(@"-- play fail, %@", error);
}

- (void)player:(XMIAdVideoPlayer *)player playTimeDidChanged:(CGFloat)currentTime {
//    XMILog(@"-- progress %.2f", currentTime);
    if (self.ownDelegate && [self.ownDelegate respondsToSelector:@selector(expressAdView:playTimeDidChanged:)]) {
        [self.ownDelegate expressAdView:self playTimeDidChanged:currentTime];
    }
}

- (void)player:(XMIAdVideoPlayer *)player playDidFinish:(NSError *)error {
    if (self.ownDelegate && [self.ownDelegate respondsToSelector:@selector(expressAdView:playerDidPlayFinish:)]) {
        [self.ownDelegate expressAdView:self playerDidPlayFinish:error];
    }
}

@end
