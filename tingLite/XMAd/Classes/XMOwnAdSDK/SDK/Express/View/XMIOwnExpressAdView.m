//
//  XMIOwnExpressAdView.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/26.
//

#import "XMIOwnExpressAdView.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import "XMIAdDefines.h"
#import "XMIAdRelatedData.h"
#import "XMIAdMacro.h"
#import "XMIAdError.h"
#import "XMICommonUtils.h"
#import "XMIAdViewProtocol.h"
#import "UIView+XMIUtils.h"
#import "XMIAdConverter.h"
#import "XMIExpressAdSingleView+Internal.h"
#import "XMIOwnExpressAdView+Video.h"
#import "XMIOwnExpressAdView+ShopWindow.h"

@interface XMIOwnExpressAdView ()<XMIAdViewProtocol>

@property (nonatomic, strong) XMIAdRelatedData *adData;
@property (nonatomic, strong) XMIAdVideoPlayer *videoPlayer;

/**
 曝光状态，用于视频播放暂停
 */
@property (nonatomic, assign) BOOL lastExposed;

@end

@implementation XMIOwnExpressAdView
@synthesize isCustom = _isCustom;
@synthesize contentView = _contentView;
@synthesize logoImageView = _logoImageView;
@synthesize titleLabel = _titleLabel;
@synthesize descLabel = _descLabel;
@synthesize videoView = _videoView;
@synthesize bottomView = _bottomView;
@synthesize actionView = _actionView;
@synthesize imgViewArray = _imgViewArray;

- (instancetype)initWithOriginData:(id)adData {
    self = [super init];
    if (self) {
        [self setupWithOriginData:(XMIAdRelatedData *)adData];
    }
    
    return self;
}

- (void)setupWithOriginData:(XMIAdRelatedData *)adData {
    _isCustom = YES;
    if (adData == nil) {
        return;
    }
    self.adData = adData;
    // 给一个初始值，容错
    self.scrollFrame = [UIScreen mainScreen].bounds;
}

- (void)render:(CGSize)size {
    BOOL ret = [self renderCustom];
    
    if (ret) {
        if (self.ownDelegate && [self.ownDelegate respondsToSelector:@selector(expressAdViewDidRender:hasNeedRefresh:)]) {
            [self.ownDelegate expressAdViewDidRender:self hasNeedRefresh:NO];
        }
    } else {
        if (self.ownDelegate && [self.ownDelegate respondsToSelector:@selector(expressAdView:didRenderFailWithError:)]) {
            [self.ownDelegate expressAdView:self didRenderFailWithError:[XMIAdError emptyDataError]];
        }
    }
}

/**
 自渲染
 */
- (BOOL)renderCustom {
    if (self.adData == nil) {
        return NO;
    }
    
    if ([XMIAdConverter isVirtualAD:self.adData.adid]) {
        [self renderVirtual];
        return YES;
    }
    
    // 保持广告内容宽高16/9，留白左右16
    self.frame = CGRectMake(0, 0, [self adWidth], [self adHeight]);
    CGFloat contentWidth = [self contentWidth];
    CGFloat contentHeight = [self contentHeight];
    // content view
    UIView *contentView = [self defaultContentView];
    [self addSubview:contentView];
    _contentView = contentView;
    
    // logo 主站拷贝的宽高
    UIImageView *logoImageView = [[UIImageView alloc] initWithFrame:CGRectMake(contentWidth - 24 - 6, 6, 24, 14)];
    if (self.adData.adMark == nil) {
        logoImageView.image = [XMICommonUtils imageNamed:@"pic_ad_mark_2"];
    } else {
        [logoImageView sd_setImageWithURL:[NSURL URLWithString:self.adData.adMark] placeholderImage:[XMICommonUtils imageNamed:@"pic_ad_mark_2"]];
    }
    [contentView addSubview:logoImageView];
    _logoImageView = logoImageView;
    
    // bottom
    UIView *bottomView = [self defaultBottomView];
    [_contentView addSubview:bottomView];
    _bottomView = bottomView;
    // action
    UIView *actionView = [self defaultActionViewWithText:self.adData.buttonText];
    actionView.frame = CGRectMake(contentWidth - actionView.frame.size.width - 10, contentHeight - 26 - 10, actionView.frame.size.width, actionView.frame.size.height);
    [_contentView addSubview:actionView];
    _actionView = actionView;
    // title
    UILabel *titleLabel = [self defaultTitleViewWithTitle:self.adData.name];
    [_bottomView addSubview:titleLabel];
    _titleLabel = titleLabel;
    // desc
    UILabel *descLabel = [self defaultDescViewWithDesc:self.adData.adDescription];
    [_contentView addSubview:descLabel];
    _descLabel = descLabel;
    
    // ad content
    [self renderAdContent:CGSizeMake(contentWidth, contentHeight)];
    
    return YES;
}
/**
 广告实际内容
 */
- (void)renderAdContent:(CGSize)size {
    switch (self.adData.showstyle) {
        case XMIAdStyleHomeLargeImage:
            [self renderHomeLargeImage:size];
            break;
        case XMIAdStyleHomeVideo:
            [self renderHomeVideo:size];
            break;
        case XMIAdStyleHomeShopWindow:
            [self renderHomeShopWindow:size];
            break;
        case XMIAdStyleHomeBackgroundImage:
            [self renderHomeBackgroundImage:size];
            break;
            
        default:
            break;
    }
}

/**
 大图
 */
- (void)renderHomeLargeImage:(CGSize)size {
    UIImageView *bigImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, size.width, size.height)];
    if (self.adData.cover != nil) {
        [bigImageView sd_setImageWithURL:[NSURL URLWithString:self.adData.cover] placeholderImage:[XMICommonUtils imageNamed:@"ad_bkg_default"]];
    } else {
        bigImageView.image = [XMICommonUtils imageNamed:@"ad_bkg_default"];
    }
    [self.contentView insertSubview:bigImageView atIndex:0];
    _imgViewArray = [[NSArray alloc] initWithObjects:bigImageView, nil];
    
    [self registerClickableViews:@[self.contentView, bigImageView, self.bottomView]];
}

/**
 视频
 */
- (void)renderHomeVideo:(CGSize)size {
    // 备胎图
    UIImageView *bigImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, size.width, size.height)];
    if (self.adData.cover != nil) {
        [bigImageView sd_setImageWithURL:[NSURL URLWithString:self.adData.cover] placeholderImage:[XMICommonUtils imageNamed:@"ad_bkg_default"]];
    } else {
        bigImageView.image = [XMICommonUtils imageNamed:@"ad_bkg_default"];
    }
    [self.contentView insertSubview:bigImageView atIndex:0];
    
    XMIAdVideoPlayer *player = [self buildVideoPlayer:self.adData.videoUrl];
    if (player == nil) {
        return;
    }
    
    player.view.frame = CGRectMake(0, 0, size.width, size.height);
    [self.contentView insertSubview:player.view atIndex:1];
    _videoView = player.view;
    self.videoPlayer = player;
    
    [self registerClickableViews:@[self.contentView, self.videoView, self.bottomView]];
}

/**
 橱窗
 */
- (void)renderHomeShopWindow:(CGSize)size {
    // 橱窗无左右padding、无button
    self.contentView.frame = CGRectMake(0, 0, [self adWidth], [self adHeight]);
    self.contentView.layer.cornerRadius = 0;
    //
    self.actionView.frame = CGRectZero;
    UIFont *titleFont = XMI_AD_PingFangSemiboldFont(18);
    self.bottomView.frame = CGRectMake(0, 0, self.contentView.frame.size.width, 20 * 2 + titleFont.lineHeight);
    self.bottomView.layer.cornerRadius = 0;
    self.bottomView.backgroundColor = [UIColor clearColor];
    UIImage *logoImage = [XMICommonUtils imageNamed:@"pic_ad_mark_1"];
    self.titleLabel.frame = CGRectMake(16, 20, self.contentView.frame.size.width - 16 * 2 - logoImage.size.width, titleFont.lineHeight);
    self.titleLabel.font = titleFont;
    self.titleLabel.textColor = XMI_COLOR_RGB(0x333333);
    // 与title垂直居中
    self.logoImageView.frame = CGRectMake(self.contentView.frame.size.width - 12 - logoImage.size.width, self.titleLabel.frame.origin.y + (self.titleLabel.frame.size.height - logoImage.size.height) / 2, logoImage.size.width, logoImage.size.height);
    if (self.adData.adMark == nil) {
        self.logoImageView.image = logoImage;
    } else {
        [self.logoImageView sd_setImageWithURL:[NSURL URLWithString:self.adData.adMark] placeholderImage:logoImage];
    }
    
    UIView *shopView = [self buildShopWindowView:self.adData];
    shopView.frame = CGRectMake(0, self.bottomView.frame.size.height, shopView.frame.size.width, shopView.frame.size.height);
    [self.contentView addSubview:shopView];
}

/**
 背景板
 */
- (void)renderHomeBackgroundImage:(CGSize)size {
    // 背景板无左右padding、无button、无bottom
    self.contentView.frame = CGRectMake(0, 0, [self adWidth], [self adHeight]);
    self.contentView.layer.cornerRadius = 0;
    //
    self.actionView.frame = CGRectZero;
    self.bottomView.frame = CGRectZero;
    self.contentView.clipsToBounds = YES;
    self.logoImageView.frame = CGRectMake(self.contentView.frame.size.width - self.logoImageView.frame.size.width - 5, self.contentView.frame.size.height - self.logoImageView.frame.size.height - 5, self.logoImageView.frame.size.width, self.logoImageView.frame.size.height);
    // 背景图
    UIImageView *bgImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, self.scrollFrame.size.width, self.scrollFrame.size.height)];
    if (self.adData.cover != nil) {
        [bgImageView sd_setImageWithURL:[NSURL URLWithString:self.adData.cover] placeholderImage:[XMICommonUtils imageNamed:@"ad_bkg_default"]];
    } else {
        bgImageView.image = [XMICommonUtils imageNamed:@"ad_bkg_default"];
    }
    [self.contentView insertSubview:bgImageView atIndex:0];
    _imgViewArray = [[NSArray alloc] initWithObjects:bgImageView, nil];
    
    [self registerClickableViews:@[self.contentView, bgImageView]];
}
/**
 滚动后，图片y坐标发生变化，视觉上看起来图片内容在视窗内滚动
 */
- (void)homeBgImageDidScroll {
    if (!self.window) {
        return;
    }
    if (!_imgViewArray || _imgViewArray.count < 1) {
        return;
    }
    
    CGRect rect = [self convertRect:self.frame toView:nil];
    if (!CGRectIntersectsRect(rect, [UIScreen mainScreen].bounds)) {
        return;
    }
    CGFloat y = 0;
    if (rect.origin.y > self.scrollFrame.origin.y) {
        y = self.scrollFrame.origin.y - rect.origin.y;
    }
    UIImageView *bgImageView = [_imgViewArray firstObject];
    bgImageView.frame = CGRectMake(bgImageView.frame.origin.x, y, self.scrollFrame.size.width, self.scrollFrame.size.height);
}

/**
 虚拟广告
 */
- (void)renderVirtual {
    self.frame = CGRectMake(0, 0, 1, 1);
    self.backgroundColor = [UIColor clearColor];
}

- (CGFloat)adHeight {
    // 虚拟广告高度为0
    if ([XMIAdConverter isVirtualAD:self.adData.adid]) {
        return 0;
    }
    // 橱窗模式高度不一样
    if (self.adData.showstyle == XMIAdStyleHomeShopWindow) {
        CGFloat shopHeight = [self shopWindowHeight];
        UIFont *titleFont = XMI_AD_PingFangSemiboldFont(18);
        return shopHeight + 20 * 2 + titleFont.lineHeight;
    }
    
    return [super adHeight];
}

- (void)scrollViewDidScroll {
    [super scrollViewDidScroll];
    // 背景板模式图片随滚动改变位置
    if (self.adData.showstyle == XMIAdStyleHomeBackgroundImage) {
        [self homeBgImageDidScroll];
    } else if (self.adData.showstyle == XMIAdStyleHomeVideo) {
        // 视频广告需要反复检测曝光状态，以控制播放暂停
        [self doDetectOwnExpose];
    }
}

- (void)viewExposeChanged:(BOOL)isExposed {
    XMILog(@"--> %d", isExposed);
    // 曝光了，视频广告才开始播放
    if (self.adData.showstyle == XMIAdStyleHomeVideo) {
        if (isExposed) {
            if (![self.videoPlayer isPlaying]) {
                [self.videoPlayer play];
            }
        } else {
            [self.videoPlayer pause];
            
            if (self.ownDelegate && [self.ownDelegate respondsToSelector:@selector(expressAdViewPlayerDidBecomeInvisible:)]) {
                [self.ownDelegate expressAdViewPlayerDidBecomeInvisible:self];
            }
        }
    }
}

- (void)doDetectOwnExpose {
    BOOL isExposed = [self xmi_isExposed:self.rootViewController.view radio:0.01];
    if (isExposed != self.lastExposed) {
        self.lastExposed = isExposed;
        [self viewExposeChanged:isExposed];
    }
}

/**
 overwrite super methods
 */
- (void)didMoveToWindow {
    [super didMoveToWindow];
    
    // 背景板首次展示更新图片位置
    if (self.adData.showstyle == XMIAdStyleHomeBackgroundImage) {
        // 下一个runloop，视图位置才真正确定
        dispatch_async(dispatch_get_main_queue(), ^{
            [self homeBgImageDidScroll];
        });
    } else if (self.adData.showstyle == XMIAdStyleHomeVideo) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self doDetectOwnExpose];
        });
    }
}

/**
 增加及处理点击事件
 */
- (void)registerClickableViews:(NSArray *)views {
    for (UIView *aView in views) {
        aView.userInteractionEnabled = YES;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleViewClick:)];
        [aView addGestureRecognizer:tap];
    }
}
- (void)handleViewClick:(UITapGestureRecognizer *)tap {
    [self adViewDidTap:tap];
}
- (void)adViewDidTap:(UITapGestureRecognizer *)tap {
    XMILog(@"*** xm adView click");
    if (self.ownDelegate && [self.ownDelegate respondsToSelector:@selector(expressAdView:adViewDidClick:withUserInfo:)]) {
        NSMutableDictionary *dic = [[NSMutableDictionary alloc] init];
        // 防止除0问题
        if (self.frame.size.width > 0 && self.frame.size.height > 0) {
            CGPoint point = [tap locationInView:self];
            NSString *absX = [NSString stringWithFormat:@"%d", (int)point.x];
            NSString *absY = [NSString stringWithFormat:@"%d", (int)point.y];
            NSString *x = [NSString stringWithFormat:@"%.2f", point.x / self.frame.size.width];
            NSString *y = [NSString stringWithFormat:@"%.2f", point.y / self.frame.size.height];
            dic[@"absX"] = absX;
            dic[@"absY"] = absY;
            dic[@"x"] = x;
            dic[@"y"] = y;
        }
        [self.ownDelegate expressAdView:self adViewDidClick:tap.view withUserInfo:dic];
    }
}

/**
 显示类型
 */
- (int)getAdShowType {
    XMIAdShowType showType = XMIAdShowTypeImage;
    if (self.adData == nil) {
        return showType;
    }
    switch (self.adData.showstyle) {
        case XMIAdStyleHomeLargeImage:
        case XMIAdStyleHomeShopWindow:
        case XMIAdStyleHomeBackgroundImage:
            showType = XMIAdShowTypeImage;
            break;
        case XMIAdStyleHomeVideo:
            showType = XMIAdShowTypeVideo;
            break;
        default:
            break;
    }
    return showType;
}

- (int)getVideoPlayTime {
    if (self.videoPlayer == nil) {
        return 0;
    }
    return (int)(self.videoPlayer.currentTime * 1000);
}

- (int)getVideoDuration {
    if (self.videoPlayer == nil) {
        return 0;
    }
    return (int)(self.videoPlayer.duration * 1000);
}

@end
