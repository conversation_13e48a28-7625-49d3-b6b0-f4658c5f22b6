//
//  XMIOwnExpressAdDoubleView+audio.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/12/4.
//

#import "XMIOwnExpressAdDoubleView+audio.h"
#import "XMIAdRelatedData.h"
#import "XMIExpressAdBusniessExtraModel.h"
#import "NSString+XMIUtils.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import "XMICommonUtils.h"
#import <Masonry/Masonry.h>
#import "XMAdDownloadMediaManager.h"
#import "NSURL+XMICommon.h"
#import "XMIAnimatedImageView.h"
#import <XMCategories/XMCategory.h>

@implementation XMIOwnExpressAdDoubleView (audio)

- (void)setAudioContrains
{
    [self.audioCoverView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.coverImageView);
    }];
    [self.tagImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(self.audioCoverView);
        make.height.mas_equalTo(kXMIExpressAdDoubleVideoViewTagImageHeight);
        make.width.mas_equalTo(0);
    }];
    
    [self.anchorTapView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.bottom.equalTo(self.coverMaskView);
        make.right.equalTo(self.playTagIcon.mas_left);
    }];
    
    [self.anchorImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.audioCoverView.mas_left).offset(8);
        make.bottom.equalTo(self.audioCoverView.mas_bottom).offset(-8);
        make.size.mas_equalTo(CGSizeMake(kXMIExpressAdDoubleVideoViewAnchorHeight, kXMIExpressAdDoubleVideoViewAnchorHeight));
    }];
    
    [self.anchorNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.anchorImageView.mas_right).offset(4);
        make.centerY.equalTo(self.anchorImageView.mas_centerY);
        make.right.lessThanOrEqualTo(self.playTagIcon.mas_left).offset(-10);
    }];
    
    [self.playCountLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.audioCoverView.mas_right).offset(-8);
        make.bottom.equalTo(self.audioCoverView.mas_bottom).offset(-8);
    }];
    
    [self.playTagIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.playCountLabel.mas_left).offset(-3);
        make.centerY.equalTo(self.playCountLabel.mas_centerY);
    }];
    
//    [self.recLabel mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.left.mas_equalTo(kXMIExpressAdDoubleVideoViewHoriznalMargin);
//        make.bottom.equalTo(self.contentView.mas_bottom).offset(-30);
//        make.right.mas_equalTo(-kXMIExpressAdDoubleVideoViewHoriznalMargin);
//    }];
    
    [self.coverMaskView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.bottom.right.equalTo(self.coverImageView);
        make.height.mas_equalTo(XMIExpressAdDoubleViewCoverMaskHeight);
    }];

    [self.scoreImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView.mas_left).offset(7);
        make.bottom.equalTo(self.contentView.mas_bottom).offset(-7);
        make.size.mas_equalTo(CGSizeMake(16, 16));
    }];

    [self.scoreLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.scoreImgV.mas_right);
        make.centerY.equalTo(self.scoreImgV.mas_centerY);
    }];
    
}

- (void)startAudioFrameWithRelatedData:(XMIAdRelatedData *)relatedData
{
    id mm = relatedData.businessExtraModel;
    if ([mm isKindOfClass:XMIExpressAdBusniessExtraModel.class]) {
        XMIExpressAdBusniessExtraModel *extraModel = (XMIExpressAdBusniessExtraModel *)mm;
        self.recLabel.text = extraModel.recommendTags;
        [self p_setSubscribleCountWithModel:extraModel];
        [self p_setPraiseScoreWithModel:extraModel];
        self.anchorNameLabel.text = extraModel.broadcasterName;
        CGFloat imageWidth = relatedData.adWidth;
        NSURL *URL = [NSURL URLWithString:extraModel.cover];
        self.downLoadManager.identifier = [relatedData getIdentifier];
        self.downLoadManager.url = [URL xmi_webpURLWithSize:CGSizeMake(imageWidth*2, imageWidth*2)];
        UIImage *placeImage = [XMICommonUtils imageNamed:@"ad_bkg_default"];
        __weak typeof(self) weakSelf = self;
        [self.downLoadManager setImageWithImageView:self.coverImageView placeholderImage:placeImage complete:^(XMAdDownloadMediaManager * _Nonnull manager) {
            NSString * identifier = [weakSelf.adData getIdentifier];
            if ([identifier isEqualToString:manager.identifier]) {
                if (!manager.isSuccess) {
                    weakSelf.coverImageView.image = nil;
                }
            }
        }];
        
        [self.anchorImageView setImageWithURL:[NSURL URLWithString:extraModel.broadcasterAvata] placeholderImage:[XMICommonUtils imageNamed:@"avatar_default_rectangle"]];
        [self p_setTagImageWithModel:extraModel];
        
        self.coverImageView.frame = CGRectMake(0, 0, self.bounds.size.width, self.bounds.size.width);
        CGFloat titleHeight = relatedData.titleTextLayout.textBoundingSize.height;
        self.titleLabel.frame = CGRectMake(kXMIExpressAdDoubleVideoViewHoriznalMargin, self.coverImageView.frame.size.height + kXMIExpressAdDoubleVideoViewCoverTitleSpace, self.bounds.size.width - kXMIExpressAdDoubleVideoViewHoriznalMargin*2, titleHeight);
        CGFloat recTitleSpace = self.titleLabel.frame.origin.y + self.titleLabel.frame.size.height + kXMIExpressAdDoubleVideoViewTitleRecSpace;
        CGFloat recLabelWidth = self.bounds.size.width - kXMIExpressAdDoubleVideoViewHoriznalMargin*2;
        CGFloat recLabelHeight = [self.recLabel.font lineHeight];
        self.recLabel.frame = CGRectMake(kXMIExpressAdDoubleVideoViewHoriznalMargin, recTitleSpace, recLabelWidth, recLabelHeight);
        
    }
}



- (void)p_setTagImageWithModel:(XMIExpressAdBusniessExtraModel *)extraModel
{
    self.tagImageView.hidden = extraModel.albumCornerMark.length > 0 ? NO : YES;
    [self.tagImageView sd_setImageWithURL:[NSURL URLWithString:extraModel.albumCornerMark]];
    NSURL *tagUrl = [NSURL URLWithString:extraModel.albumCornerMark];
    __weak typeof(self) weakSelf = self;
    [self.tagImageView sd_setImageWithURL:tagUrl placeholderImage:nil completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
        if ([tagUrl.absoluteString isEqualToString:extraModel.albumCornerMark] && image) {
            self.tagImageView.hidden = NO;
            weakSelf.tagImageView.image = image;
            CGFloat width = kXMIExpressAdDoubleVideoViewTagImageHeight * image.size.width/image.size.height;
            [weakSelf.tagImageView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.width.mas_equalTo(width);
            }];
        }else{
            self.tagImageView.hidden = YES;
        }
    }];
}

- (void)p_setSubscribleCountWithModel:(XMIExpressAdBusniessExtraModel *)extraModel
{
    long long subscribleCount = [extraModel.subscribleCount ?: @"0" longLongValue];
    
    if (subscribleCount <= 100) {
        self.playCountLabel.hidden = YES;
        self.playTagIcon.hidden = YES;
    }else{
        self.playCountLabel.hidden = NO;
        self.playTagIcon.hidden = NO;
        self.playCountLabel.text = [extraModel.subscribleCount ?: @"0" formatBigNumber];
    }
    
}

- (void)p_setPraiseScoreWithModel:(XMIExpressAdBusniessExtraModel *)extraModel
{
    CGFloat score = [extraModel.evaluateScore floatValue];
    if (score <= 0) {
        self.scoreImgV.hidden = YES;
        self.scoreLabel.hidden = YES;
    }else{
        self.scoreImgV.hidden = NO;
        self.scoreLabel.hidden = NO;
        self.scoreLabel.text = [NSString stringWithFormat:@"%.1f分", score];
    }
}


@end
