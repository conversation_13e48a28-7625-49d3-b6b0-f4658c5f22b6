//
//  XMIOwnExpressAdDoubleView.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/12/4.
//

#import "XMIOwnExpressAdDoubleView.h"
#import "XMIAdRelatedData.h"
#import "XMICommonUtils.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import "XMIAdMacro.h"
#import "XMIOwnExpressAdDoubleView+audio.h"
#import "XMIAdNewVideoPlayer.h"
#import "XMIOwnExpressAdDoubleView+video.h"
#import "XMAdDownloadMediaManager.h"
#import "XMIOwnJumpManager.h"
#import "NSURL+XMICommon.h"
#import "XMIAnimatedImageView.h"

@interface XMIOwnExpressAdDoubleView()<XMIOwnJumpManagerDelegate>

@property (nonatomic, strong, readwrite) UIView *audioCoverView;
@property (nonatomic, strong) XMIOwnJumpManager *jumpManager;


@end

@implementation XMIOwnExpressAdDoubleView

- (void)dealloc
{
    [self cleanVideoPlayer];
}

- (UIView *)audioCoverView
{
    if (!_audioCoverView) {
        _audioCoverView = [[UIView alloc] initWithFrame:CGRectZero];
        _audioCoverView.userInteractionEnabled = NO;
        _audioCoverView.hidden = YES;
    }
    return _audioCoverView;
}

- (UIView *)videoView {
    if (!_videoView) {
        _videoView = [XMIAdNewVideoView createVideoView];
    }
    return _videoView;
}

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.clipsToBounds = YES;
        self.videoPlayer = [self buildVideoPlayer];
        self.downLoadManager = [XMAdDownloadMediaManager new];
        [self p_setUI];
        [self setAudioContrains];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(ad_applicationDidEnterBackground:) name:UIApplicationDidEnterBackgroundNotification object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(ad_applicationDidBecomeActive:) name:UIApplicationDidBecomeActiveNotification object:nil];
    }
    return self;
}

- (void)ad_applicationDidEnterBackground:(NSNotification *)notification
{
    if (self.videoPlayer.isPlaying) {
        [self.videoPlayer stop];
    }
}

- (void)ad_applicationDidBecomeActive:(NSNotification *)notification
{
    [self doDetectOwnExpose];
}


- (void)p_setUI
{
    [self addSubview:self.placeHolderView];
    self.placeHolderView.frame = self.bounds;
    self.placeHolderView.hidden = NO;
    [self addSubview:self.contentView];
    [self.contentView addSubview:self.contentMaskView];
    [self.contentView addSubview:self.coverImageView];
    [self.coverImageView addSubview:self.videoView];
    [self.contentView addSubview:self.adMark];
    [self.contentView addSubview:self.titleMaskView];
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.statusButton];
    [self.contentView addSubview:self.closeButton];
    
    [self.coverImageView addSubview:self.audioCoverView];
    [self.audioCoverView addSubview:self.coverMaskView];
    [self.audioCoverView addSubview:self.anchorTapView];
    [self.audioCoverView addSubview:self.tagImageView];
    [self.audioCoverView addSubview:self.anchorImageView];
    [self.audioCoverView addSubview:self.anchorNameLabel];
    [self.audioCoverView addSubview:self.playTagIcon];
    [self.audioCoverView addSubview:self.playCountLabel];
    self.audioCoverView.userInteractionEnabled = YES;
    self.coverImageView.userInteractionEnabled = YES;
    
    [self.contentView addSubview:self.recLabel];
    [self.contentView addSubview:self.scoreImgV];
    [self.contentView addSubview:self.scoreLabel];
    
    [self registerOwnClickableViews:@[self.contentView]];
}

- (void)refreshWithData:(XMIAdRelatedData *)adData {
    [super refreshWithData:adData];
    if (adData.loadingStatus == XMIAdRelatedLoadingStatusNormal) {
        return;
    }
    self.placeHolderView.hidden = YES;
    self.contentView.hidden = NO;
    self.frame = CGRectMake(0, 0, adData.adWidth, adData.adHeight);
    self.adData = adData;
    [self startSetUpUIWithModel:adData];
//    [self setStatusButtonColorAnimation];
}



- (void)layoutSubviews
{
    [super layoutSubviews];
    
}

- (void)startSetUpUIWithModel:(id)model
{
    XMIAdRelatedData *relatedData = nil;
    if ([model isKindOfClass:XMIAdRelatedData.class]) {
        relatedData = (XMIAdRelatedData *)model;
    }
    if (!relatedData) {
        return;
    }
    self.adData = relatedData;
    self.titleLabel.textLayout = relatedData.titleTextLayout;
    [self startSetFrame];
    
    UIImage *adMarkImage = [XMICommonUtils imageNamed:@"pic_ad_mark_3"];
    [self p_setAdMark:adMarkImage];
    NSURL *markUrl = [NSURL URLWithString:relatedData.adMark];
    __weak typeof(self) weakSelf = self;
    [self.adMark sd_setImageWithURL:markUrl placeholderImage:adMarkImage completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
        if ([imageURL.absoluteString isEqualToString:weakSelf.relatedData.adMark]) {
            if (image) {
                [weakSelf p_setAdMark:image];
            }else{
                [weakSelf p_setAdMark:adMarkImage];
            }
        }
    }];
    
}

- (void)p_setAdMark:(UIImage *)image
{
    if (!image) {
        return;
    }
    self.adMark.image = image;
    CGFloat width = 14 * image.size.width/image.size.height;
    self.adMark.frame = CGRectMake(self.bounds.size.width - width, 0, width, 14);
}





- (void)startSetFrame
{
    self.coverImageView.backgroundColor = XMI_COLOR_RGB(0xEDEDED);
    self.closeButton.frame = CGRectMake(self.bounds.size.width - 23, self.bounds.size.height - 23, 16, 16);
    
    self.titleLabel.hidden = NO;
    self.contentMaskView.hidden = NO;
    self.audioCoverView.hidden = YES;
    self.scoreLabel.hidden = YES;
    self.scoreImgV.hidden = YES;
    self.recLabel.hidden = YES;
    self.coverImageView.hidden = NO;
    self.videoView.hidden = YES;
    
    self.contentView.frame = self.bounds;
    self.contentMaskView.frame = self.bounds;
    self.contentBlackLayer.frame = self.bounds;
    
    XMIAdRelatedData *relatedData = self.adData;
    self.coverImageView.image = nil;
    if (relatedData.showstyle == XMIAdStyleHomeDoubleRowVerticalImage) {
        self.coverImageView.frame = self.bounds;
        self.titleMaskView.frame = CGRectMake(0, self.bounds.size.height - 105, self.bounds.size.width, 105);
        [self p_configStatusButton:self.adData];
        [self p_setTitleLabelFrame];
        [self p_setCoverImage:relatedData];
    }else if (relatedData.showstyle == XMIAdStyleHomeDoubleRowVideo){
        CGFloat coverHeight = self.bounds.size.width * 9/16.0;
        self.coverImageView.frame = CGRectMake(0, kXMIExpressAdDoubleVideoViewCoverTopMargin, self.bounds.size.width, coverHeight);
        CGFloat titleHeight = relatedData.titleTextLayout.textBoundingSize.height;
        CGFloat titleBottomMargin = 36;
        self.titleLabel.frame = CGRectMake(kXMIExpressAdDoubleVideoViewHoriznalMargin, self.bounds.size.height - titleHeight - titleBottomMargin, self.bounds.size.width - kXMIExpressAdDoubleVideoViewHoriznalMargin*2, titleHeight);
        [self p_configStatusButton:self.adData];
        [self.contentMaskView sd_setImageWithURL:[NSURL URLWithString:relatedData.cover] placeholderImage:nil];
        [self.coverImageView sd_setImageWithURL:[NSURL URLWithString:relatedData.cover] placeholderImage:nil completed:nil];
        self.titleMaskView.hidden = YES;
    }else if (relatedData.showstyle == XMIAdStyleHomeDoubleRowAudio){
        self.audioCoverView.hidden = NO;
        self.statusButton.hidden = YES;
        self.titleMaskView.hidden = YES;
        self.contentMaskView.hidden = YES;
        self.recLabel.hidden = NO;
        self.scoreLabel.hidden = NO;
        self.scoreImgV.hidden = NO;
        [self startAudioFrameWithRelatedData:relatedData];
    }else if (relatedData.showstyle == XMIAdStyleHomeDoubleRowVerticalVideo){
        self.contentMaskView.frame = self.bounds;
        self.coverImageView.frame = self.bounds;
        self.titleMaskView.frame = CGRectMake(0, self.bounds.size.height - 105, self.bounds.size.width, 105);
        [self p_configStatusButton:self.adData];
        [self p_setTitleLabelFrame];
        [self p_setCoverImage:relatedData];
        [self startVideoToPlay:relatedData];
    }
    
    
}

- (void)p_setCoverImage:(XMIAdRelatedData *)relatedData
{
    [self.contentMaskView sd_setImageWithURL:[NSURL URLWithString:relatedData.cover] placeholderImage:nil];
    __weak typeof(self) weakSelf = self;
    CGFloat imageWidth = relatedData.adWidth;
    NSURL *coverURL = [NSURL URLWithString:relatedData.cover];
    NSURL *URL = [coverURL xmi_webpURLWithSize:CGSizeMake(imageWidth*2, 0)];
    self.downLoadManager.identifier = [relatedData getIdentifier];
    self.downLoadManager.url = URL;
    UIImage *placeImage = [XMICommonUtils imageNamed:@"ad_bkg_default"];
    [self.downLoadManager setImageWithImageView:self.coverImageView placeholderImage:placeImage complete:^(XMAdDownloadMediaManager * _Nonnull manager) {
        NSString * identifier = [weakSelf.adData getIdentifier];
        if ([identifier isEqualToString:manager.identifier]) {
            if (!manager.isSuccess) {
                weakSelf.coverImageView.image = nil;
            }
            if (manager.isSuccess) {
                [weakSelf didRenderResultWithModel:weakSelf.adData mediaSize:manager.image.size];
            }
        }
    }];
}

- (void)updatePlayerWith:(XMIAdNewVideoPlayer *)videoPlayer
                 getSize:(CGSize)playerSize
{
    NSURL *currentURL = videoPlayer.currentURL;
    NSString *identifer = [self.adData getIdentifier];
    if ([self.adData.videoCover isEqualToString:currentURL.absoluteString] && [videoPlayer.identifier isEqualToString:identifer]) {
        CGSize playerSize = [videoPlayer getMediaSize];
        XMILog(@"state change size=%@", [NSValue valueWithCGSize:playerSize]);
        
        [self didRenderResultWithModel:self.adData mediaSize:playerSize];
    }
}

- (void)p_configStatusButton:(XMIAdRelatedData *)model
{
    CGFloat statusHeight = 18;
    if (model.clickTitle.length) {
        [self.statusButton setTitle:model.clickTitle forState:UIControlStateNormal];
        self.statusButton.hidden = NO;
        if (model.clickTitle.length == 2) {
            self.statusButton.frame = CGRectMake(kXMIExpressAdDoubleVideoViewHoriznalMargin, self.bounds.size.height - statusHeight - 10, 42, statusHeight);
        }else{
            self.statusButton.frame = CGRectMake(kXMIExpressAdDoubleVideoViewHoriznalMargin, self.bounds.size.height - statusHeight - 10, 54, statusHeight);
        }
        
//        if (model.linkType == 2 || model.clickType == 18) {
//            /// 下载
//            self.statusButton.backgroundColor = XMI_COLOR_RGB(0x1295FF);
//        }else{
//            self.statusButton.backgroundColor = XMI_COLOR_RGB(0xFF4646);
//        }
        
    }else{
        self.statusButton.hidden = YES;
    }
    
    if (!self.adData.titleTextLayout && self.statusButton.hidden) {
        self.titleMaskView.hidden = YES;
    }else{
        self.titleMaskView.hidden = NO;
    }
    
}

/// 获取是否是下载
- (BOOL)getAdIsDownloadApp
{
    return self.adData.linkType == 2 || self.adData.clickType == 18;
}

- (void)p_setTitleLabelFrame
{
    if (!self.statusButton.hidden) {
        CGFloat titleHeight = self.adData.titleTextLayout.textBoundingSize.height;
        CGFloat titleBottomMargin = 36;
        self.titleLabel.frame = CGRectMake(kXMIExpressAdDoubleVideoViewHoriznalMargin, self.bounds.size.height - titleHeight - titleBottomMargin, self.bounds.size.width - kXMIExpressAdDoubleVideoViewHoriznalMargin*2, titleHeight);
        
    }else{
        
        CGFloat titleHeight = self.adData.titleTextLayout.textBoundingSize.height;
        CGFloat titleBottomMargin = 8;
        self.titleLabel.frame = CGRectMake(kXMIExpressAdDoubleVideoViewHoriznalMargin, self.bounds.size.height - titleHeight - titleBottomMargin, self.bounds.size.width - kXMIExpressAdDoubleVideoViewHoriznalMargin - kXMIExpressAdDoubleVideoViewBigHoriznalMargin, titleHeight);
    }
    
    if (self.statusButton.hidden && !self.adData.titleTextLayout) {
        self.titleMaskView.hidden = YES;
    }else{
        self.titleMaskView.hidden = NO;
    }
}


- (void)didRenderResultWithModel:(XMIAdRelatedData *)model
                       mediaSize:(CGSize)mediaSize
{
    if (mediaSize.width > 0) {
        CGFloat sourceRadio = mediaSize.height/mediaSize.width;
        
        model.isNeedRefresh = YES;
        if (sourceRadio > 0 && sourceRadio < 1.4) {
            sourceRadio = 1.4;
        }
        
        if (sourceRadio > 1.8) {
            sourceRadio = 1.8;
        }
        model.sizeRadio = sourceRadio;
        
        /// 此种情况开始出现
        self.placeHolderView.hidden = YES;
        self.contentView.hidden = NO;
    }
    
    if (mediaSize.width > 0) {
        CGFloat originalSourceRadio = mediaSize.height/mediaSize.width;
        CGFloat videoTopMargin = 0;
        CGFloat videoHeight = self.bounds.size.width*originalSourceRadio;
        CGFloat adHeight = [XMIExpressAdDoubleView getAdViewHeightWithRelatedData:self.adData];
        if (fabs(self.adData.adHeight - adHeight) > 5) {
            self.relatedData.loadingStatus = XMIAdRelatedLoadingStatusNormal;
        }
        model.adHeight = adHeight;
        videoTopMargin = (adHeight - videoHeight)/2.0;
        self.coverImageView.frame = CGRectMake(0, videoTopMargin, self.frame.size.width, videoHeight);
        if (!self.videoView.hidden) {
            self.videoView.frame = CGRectMake(0, 0, self.frame.size.width, videoHeight);
        }
    }
    model.isDidRender = YES;
    BOOL isNeedRefresh = self.adData.loadingStatus != XMIAdRelatedLoadingStatusHasSureSizeRadio;
    self.adData.loadingStatus = XMIAdRelatedLoadingStatusHasSureSizeRadio;
    [self adViewDidRenderHasNeedRefresh:isNeedRefresh];
    
}


/**
 获取显示类型 0-静态图 1-git 2-视频 3-备胎图
 */
- (int)getAdShowType
{
    XMIAdShowStyle showStyle = self.relatedData.showstyle;
    if (showStyle == XMIAdStyleHomeDoubleRowVerticalVideo) {
        return XMIAdShowTypeVideo;
    }else if (showStyle == XMIAdStyleHomeDoubleRowVerticalImage){
        return XMIAdShowTypeImage;
    }
    return XMIAdShowTypeImage;
}


#pragma mark 点击
- (void)adViewDidTap:(UITapGestureRecognizer *)tap {
    NSMutableDictionary *dic = [[NSMutableDictionary alloc] init];
    // 防止除0问题
    if (self.frame.size.width > 0 && self.frame.size.height > 0) {
        CGPoint point = [tap locationInView:self];
        NSString *absX = [NSString stringWithFormat:@"%d", (int)point.x];
        NSString *absY = [NSString stringWithFormat:@"%d", (int)point.y];
        NSString *x = [NSString stringWithFormat:@"%.2f", point.x / self.frame.size.width];
        NSString *y = [NSString stringWithFormat:@"%.2f", point.y / self.frame.size.height];
        dic[@"absX"] = absX;
        dic[@"absY"] = absY;
        dic[@"x"] = x;
        dic[@"y"] = y;
    }
    BOOL jumpSupport = [self.jumpManager doJumpWithAd:self.adData];
    dic[kUserInfoJumpNotSupport] = @(!jumpSupport);
    [self adViewDidClick:tap.view withUserInfo:dic];
}

- (void)closeButtonAction:(UIButton *)button
{
    [self adViewDidClickedClose:button];
}

/**
 跳转处理
 */
- (XMIOwnJumpManager *)jumpManager {
    if (_jumpManager == nil) {
        _jumpManager = [[XMIOwnJumpManager alloc] init];
        _jumpManager.delegate = self;
    }
    _jumpManager.rootViewController = self.rootViewController;
    return _jumpManager;
}

#pragma mark - XMIOwnJumpManagerDelegate
- (void)jumpManager:(XMIOwnJumpManager *)expressAd adWillPresentScreen:(XMIAdRelatedData *)adData {
    [self adViewWillPresentScreen];
}
- (void)jumpManager:(XMIOwnJumpManager *)expressAd adDidPresentScreen:(XMIAdRelatedData *)adData {
    [self adViewDidPresentScreen];
}
- (void)jumpManager:(XMIOwnJumpManager *)expressAd adDetailControllerDidClosed:(XMIAdRelatedData *)adData {
    [self adViewDetailControllerDidClosed];
}

@end
