//
//  XMIOwnFeedAdModel.m
//  XMAd
//
//  Created by cuiyuanzhe on 2022/3/4.
//

#import "XMIOwnFeedAdModel.h"

@implementation XMIOwnFeedAdModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.loadingStatus = XMIFeedAdModelLoadingStatusLoadSuccess;
    }
    return self;
}

- (void)loadAdData
{
    if (self.loadingStatus != XMIFeedAdModelLoadingStatusInit) {
        return;
    }
    [self loadAdData];
    [self loadSuccess];
}

@end
