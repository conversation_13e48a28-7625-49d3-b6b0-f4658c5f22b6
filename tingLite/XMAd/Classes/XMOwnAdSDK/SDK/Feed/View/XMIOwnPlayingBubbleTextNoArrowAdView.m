//
//  XMIOwnPlayingBubbleTextNoArrowAdView.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/5/18.
//

#define IS_SOUND_SMALL_SCREEN (XMI_SCREEN_HEIGHT <= 667.0f)

#import "XMIOwnPlayingBubbleTextNoArrowAdView.h"
#import "XMIAdMacro.h"
#import "UIView+XMIUtils.h"
#import "XMICommonUtils.h"
#import <XMWebImage/UIImageView+WebCache.h>

@interface XMIOwnPlayingBubbleTextNoArrowAdView ()

@property (nonatomic, strong) UIImageView *avatarImageView;

@end

@implementation XMIOwnPlayingBubbleTextNoArrowAdView

{
    UIView *_bubbleView;
}

- (UIView *)bubbleView
{
    if (!_bubbleView) {
        _bubbleView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 200, 100)];
        _bubbleView.layer.cornerRadius = 6;
        [self.contentView insertSubview:_bubbleView atIndex:0];
        _bubbleView.frame = self.contentView.bounds;
        _bubbleView.backgroundColor = XMI_COLOR_RGBA(0x000000, 0.25f);
        
    }
    return _bubbleView;
}

- (UIImageView *)avatarImageView
{
    if (!_avatarImageView) {
        _avatarImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
        [self.contentView addSubview:_avatarImageView];
        _avatarImageView.layer.cornerRadius = 12;
        _avatarImageView.layer.masksToBounds = YES;
        _avatarImageView.layer.borderColor = XMI_COLOR_RGBA(0xF4F5FD, 0.9f).CGColor;
        _avatarImageView.layer.borderWidth = 1;
    }
    return _avatarImageView;
}


- (void)layoutSubviews
{
    [super layoutSubviews];
    if (self.contentView.layer.animationKeys.count == 0 && CGAffineTransformIsIdentity(self.contentView.transform)) {
        self.contentView.frame = self.bounds;
    }
    self.avatarImageView.xmi_left = 5;
    self.avatarImageView.xmi_centerY = self.contentView.bounds.size.height * 0.5f;
    CGFloat closeBtnMargin = 6;
    self.closeButton.xmi_top = closeBtnMargin;
    self.closeButton.xmi_right = self.contentView.bounds.size.width - closeBtnMargin;
    self.btnLabel.xmi_right = self.contentView.bounds.size.width - 32;
    self.btnLabel.xmi_centerY = self.xmi_height * 0.5f;
    self.recommendLabel.xmi_left = self.avatarImageView.xmi_right + 5;
    self.recommendLabel.xmi_centerY = self.xmi_height * 0.5f;
    if (self.recommendLabel.hidden) {
        self.titleContainer.frame = CGRectMake(self.avatarImageView.xmi_right + 5, 0, self.btnLabel.xmi_left - 4 - self.avatarImageView.xmi_right - 5, self.xmi_height);
    } else {
        self.titleContainer.frame = CGRectMake(self.recommendLabel.xmi_right + 4, 0, self.btnLabel.xmi_left - 4 - self.recommendLabel.xmi_right - 4, self.xmi_height);
    }
    self.adMarkView.center = CGPointMake(self.contentView.bounds.size.width - 3 - self.adMarkView.bounds.size.width * 0.5f, self.contentView.bounds.size.height - 3 - self.adMarkView.bounds.size.height * 0.5f);
    [self updateBubbleAndTitle];
}

- (BOOL)shouldPlaySpreadAnimation
{
    return YES;
}

- (void)updateArtistAvatar:(NSString *)avatarURL
{
    if (avatarURL.length == 0) {
        self.avatarImageView.image = [XMICommonUtils imageNamed:@"avatar_default_rectangle"];
    } else {
        [self.avatarImageView sd_setImageWithURL:[NSURL URLWithString:avatarURL] placeholderImage:[XMICommonUtils imageNamed:@"avatar_default_rectangle"]];
    }
}

- (CGFloat)contentWidth
{
    return self.xmi_width;
}

- (void)didExposeAdView
{
    [super didExposeAdView];
    self.adMarkView.center = CGPointMake(self.contentView.bounds.size.width - 3 - self.adMarkView.bounds.size.width * 0.5f, self.contentView.bounds.size.height - 3 - self.adMarkView.bounds.size.height * 0.5f);
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
