//
//  XMIOwnPlayingMBubbleTextAdView.m
//  XMAd
//
//  Created by cuiyuanz<PERSON> on 2022/12/9.
//

#import "XMIOwnPlayingMBubbleTextAdView.h"
#import <XMAd/UIView+XMIUtils.h>
#import <XMAd/XMIAdMacro.h>
#import <XMCategories/XMUIScaleMng.h>
#import "XMICommonUtils.h"
#import <XMWebImage/UIImageView+WebCache.h>

@interface XMIOwnPlayingMBubbleTextAdView ()

@property (nonatomic, copy) NSString *btnLabelText;

@property (nonatomic, strong) UIImageView *ctaView;

@property (nonatomic, strong) UIImageView *recommendView;

@property (nonatomic, strong) UIImageView *avatarImageView;

@end

@implementation XMIOwnPlayingMBubbleTextAdView

{
    UIView *_bubbleView;
}

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self customSubviews];
    }
    return self;
}


- (UIView *)bubbleView
{
    if (!_bubbleView) {
        _bubbleView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 200, 100)];
        _bubbleView.layer.cornerRadius = xmUIPic(16);
        [self.contentView insertSubview:_bubbleView atIndex:0];
        _bubbleView.frame = self.contentView.bounds;
        _bubbleView.backgroundColor = XMI_COLOR_RGBA(0x000000, 0.08f);
        
    }
    return _bubbleView;
}

- (UIImageView *)avatarImageView
{
    if (!_avatarImageView) {
        _avatarImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, xmUIPic(24), xmUIPic(24))];
        [self.contentView addSubview:_avatarImageView];
        _avatarImageView.layer.cornerRadius = xmUIPic(12);
        _avatarImageView.layer.masksToBounds = YES;
//        _avatarImageView.layer.borderColor = XMI_COLOR_RGBA(0xF4F5FD, 0.9f).CGColor;
//        _avatarImageView.layer.borderWidth = 1;
    }
    return _avatarImageView;
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    if (self.contentView.layer.animationKeys.count == 0 && CGAffineTransformIsIdentity(self.contentView.transform)) {
        self.contentView.frame = self.bounds;
    } else {
        self.contentView.bounds = self.bounds;
        self.contentView.center = CGPointMake(self.xmi_width * 0.5f, self.xmi_height * 0.5f);
    }
    self.avatarImageView.xmi_left = xmUIHSpace(4);
    self.avatarImageView.xmi_centerY = self.contentView.bounds.size.height * 0.5f;
    self.closeButton.xmi_top = xmUIVSpace(9);
    self.closeButton.xmi_right = self.contentView.bounds.size.width - xmUIHSpace(10);
    self.ctaView.xmi_right = self.contentView.bounds.size.width - xmUIHSpace(28);
    self.ctaView.xmi_centerY = self.contentView.bounds.size.height * 0.5f;
    if (self.avatarImageView.hidden) {
        self.recommendView.xmi_left = xmUIHSpace(8);
    } else {
        self.recommendView.xmi_left = self.avatarImageView.xmi_right + xmUIHSpace(6);
    }
    self.recommendView.xmi_centerY = self.xmi_height * 0.5f;
    CGFloat contentWidth = self.titleScrollView.contentSize.width - self.titleContainer.xmi_width;
    if (self.recommendView.hidden) {
        if (self.avatarImageView.hidden) {
            self.titleContainer.frame = CGRectMake(xmUIHSpace(8), 0, self.ctaView.xmi_left - xmUIHSpace(8) - xmUIHSpace(5), self.xmi_height);
        } else {
            self.titleContainer.frame = CGRectMake(self.avatarImageView.xmi_right + xmUIHSpace(5), 0, self.ctaView.xmi_left - xmUIHSpace(5) - self.avatarImageView.xmi_right - xmUIHSpace(5), self.xmi_height);
        }
       
    } else {
        self.titleContainer.frame = CGRectMake(self.recommendView.xmi_right + xmUIHSpace(4), 0, self.ctaView.xmi_left - xmUIHSpace(4) - self.recommendView.xmi_right - xmUIHSpace(4), self.xmi_height);
    }
    
    [self updateBubbleAndTitle];
    if (self.titleScrollView.layer.animationKeys.count > 0) {
        self.titleLabel.xmi_width = contentWidth + self.titleContainer.xmi_width;
        self.titleScrollView.contentSize = CGSizeMake(contentWidth + self.titleContainer.xmi_width, self.titleContainer.xmi_height);
    } else {
        self.titleScrollView.contentSize = self.titleContainer.xmi_size;
        self.titleLabel.xmi_width = self.titleContainer.xmi_width;
    }
    self.adMarkView.xmi_right = self.contentView.bounds.size.width - xmUIHSpace(9);
    self.adMarkView.xmi_bottom = self.contentView.bounds.size.height - xmUIVSpace(5);
}

- (void)customSubviews
{
    self.btnLabel.hidden = YES;
    self.recommendLabel.alpha = 0;
    self.titleLabel.font = XMI_AD_PingFangFont(xmUIFont(14));
    self.titleLabel.textColor = XMI_COLOR_RGBA(0xFFFFFF, 0.8f);
}

- (BOOL)shouldPlaySpreadAnimation
{
    return NO;
}

- (void)updateBtnText:(NSString *)btnText
{
    btnText = @"查看详情";
    if ([self.btnLabelText isEqualToString:btnText]) {
        return;
    }
    self.btnLabelText = btnText;
    UIImage *image = [self imageWithText:btnText fontSize:11 interval:6 height:20 conerRadius:10 alpha:1];
    self.ctaView.image = image;
    self.ctaView.xmi_size = image.size;
}

- (void)customRenderWithAdData:(id)adData
{
    [super customRenderWithAdData:adData];
    self.recommendView.hidden = self.recommendLabel.hidden;
}


- (UIImage *)imageWithText:(NSString *)text fontSize:(CGFloat)fontSize interval:(CGFloat)interval height:(CGFloat)height conerRadius:(CGFloat)cornerRadius alpha:(CGFloat)alpha
{
    NSDictionary *attributes = @{NSFontAttributeName : XMI_AD_PingFangFont(xmUIFont(fontSize)), NSForegroundColorAttributeName : XMI_COLOR_RGB(0x000000)};
    CGSize textSize = [text boundingRectWithSize:CGSizeMake(MAXFLOAT, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading attributes:attributes context:nil].size;
    CGRect textFrame = CGRectMake(xmUIPic(interval) , xmUIPic(height * 0.5f) - textSize.height * 0.5f, textSize.width, textSize.height);
    CGSize fullSize = CGSizeMake(xmUIPic(interval * 2.0f) + textSize.width, xmUIPic(height));
    UIGraphicsBeginImageContextWithOptions(fullSize, NO, [UIScreen mainScreen].scale);
    [text drawInRect:textFrame withAttributes:attributes];
    UIBezierPath *path = [UIBezierPath bezierPathWithRoundedRect:CGRectMake(0, 0, fullSize.width, fullSize.height) cornerRadius:xmUIPic(cornerRadius)];
    [XMI_COLOR_RGB(0xFFFFFF) set];
    [path fillWithBlendMode:kCGBlendModeSourceOut alpha:alpha];
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return image;
}

- (UIImageView *)recommendView
{
    if (!_recommendView) {
        _recommendView = [[UIImageView alloc] initWithImage:[self imageWithText:@"推荐" fontSize:9 interval:2 height:12 conerRadius:2 alpha:0.4f]];
        [self.contentView addSubview:_recommendView];
    }
    return _recommendView;
}

- (UIImageView *)ctaView
{
    if (!_ctaView) {
        _ctaView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, xmUIPic(56), xmUIPic(20))];
        [self.contentView addSubview:_ctaView];
    }
    return _ctaView;
}

- (void)updateArtistAvatar:(NSString *)avatarURL
{
    if (avatarURL.length == 0) {
        self.avatarImageView.image = [XMICommonUtils imageNamed:@"avatar_default_rectangle"];
    } else {
        [self.avatarImageView sd_setImageWithURL:[NSURL URLWithString:avatarURL] placeholderImage:[XMICommonUtils imageNamed:@"avatar_default_rectangle"]];
    }
}

- (void)hideAvatar:(BOOL)hide
{
    self.avatarImageView.hidden = hide;
    [self setNeedsLayout];
    [self layoutIfNeeded];
}

- (CGFloat)contentWidth
{
    return self.xmi_width;
}

- (void)updateAdMark:(NSString *)adMark
{
    self.adMarkView.limitedHeight = -1;
    @weakify(self);
    UIImage *placeholder = [XMICommonUtils imageNamed:@"pic_ad_mark_10"];
    [self.adMarkView sd_setImageWithURL:[NSURL URLWithString:adMark] placeholderImage:placeholder completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
        @strongify(self);
        if (self && !error) {
            self.adMarkView.xmi_right = self.contentView.bounds.size.width - xmUIHSpace(9);
            self.adMarkView.xmi_bottom = self.contentView.bounds.size.height - xmUIVSpace(5);
        }
    }];
}

- (void)didExposeAdView
{
    [super didExposeAdView];
    self.adMarkView.xmi_right = self.contentView.bounds.size.width - xmUIHSpace(9);
    self.adMarkView.xmi_bottom = self.contentView.bounds.size.height - xmUIVSpace(5);
}



/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
