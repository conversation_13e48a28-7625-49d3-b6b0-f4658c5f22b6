//
//  XMIOwnFindFeedNativeImageListAdView.m
//  XMAd
//
//  Created by xiaodong2.zhang on 2024/7/10.
//

#import "XMIOwnFindFeedNativeImageListAdView.h"
#import "UIView+XMIUtils.h"
#import "XMIAdMacro.h"
#import "XMICommonUtils.h"
#import "XMIAdDataCenter.h"
#import "XMIAdRelatedData.h"
#import "XMIAdHelper.h"
#import "XMIFindFeedNativeAdItemCell.h"
#import "XMIAdRespData.h"

@implementation XMIOwnFindFeedNativeImageListAdView

+ (CGFloat)calAdHeight:(id)adData withAdWidth:(CGFloat)adWidth {
    if (![adData isKindOfClass:[XMIAdRelatedData class]]) {
        return 0;
    }
    CGFloat height = kHomeRatioSize(54) + [self itemCellSizeWithStyle:((XMIAdRelatedData *)adData).showstyle].height + kHomeRatioSize(10);
    return height;
}

+ (CGSize)itemCellSizeWithStyle:(NSInteger)style {
    if (style == XMIAdStyleHomeListImage3_4) {
        return CGSizeMake(kHomeRatioSize(136), kHomeRatioSize(185));
    } else if (style == XMIAdStyleHomeListImage9_16) {
        return CGSizeMake(kHomeRatioSize(106), kHomeRatioSize(189));
    } else if (style == XMIAdStyleHomeListImage1_1) {
        return CGSizeMake(kHomeRatioSize(136), kHomeRatioSize(136) + kHomeRatioSize(20) + 11);
    } else {
        return CGSizeZero;
    }
}

#pragma mark - XMIFindFeedNativeAdImageListBaseView

- (CGSize)itemCellSize {
    return [XMIOwnFindFeedNativeImageListAdView itemCellSizeWithStyle:self.adData.showstyle];
}

- (Class)itemCellClass {
    return [XMIFindFeedNativeAdItemCell class];
}

- (NSMutableArray *)itemCellArray {
    return (NSMutableArray *)self.adData.composite;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    XMIAdBeanComposite *item = [self.adData.composite objectAtIndex:indexPath.item];
    if (item.realLink.length || item.dpLink.length) {
        XMIAdRelatedData *realClickItem = [self.adData copy];
        if (item.realLink.length) {
            realClickItem.realLink = item.realLink;
        }
        realClickItem.dpRealLink = item.dpLink;
        [self tapAdViewWithPoint:CGPointZero userInfo:@{@"realClickItem" : realClickItem}];
    } else {
        [self tapAdViewWithPoint:CGPointZero userInfo:nil];
    }
}

@end
