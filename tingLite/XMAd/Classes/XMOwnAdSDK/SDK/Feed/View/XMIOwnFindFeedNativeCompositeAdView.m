//
//  XMIOwnFindFeedNativeCompositeAdView.m
//  XMAd
//
//  Created by xia<PERSON><PERSON>2.zhang on 2024/7/24.
//

#import "XMIOwnFindFeedNativeCompositeAdView.h"
#import "UIView+XMIUtils.h"
#import "XMIAdMacro.h"
#import "XMICommonUtils.h"
#import "XMIAdRelatedData.h"
#import "XMIAdHelper.h"
#import "XMIFindFeedNativeAdItemCell.h"
#import "XMIBounceMoreView.h"

@implementation XMIOwnFindFeedNativeCompositeAdView
@synthesize adData = _adData;

+ (CGFloat)calAdHeight:(id)adData withAdWidth:(CGFloat)adWidth {
    if (![adData isKindOfClass:[XMIAdRelatedData class]]) {
        return 0;
    }
    CGFloat height = kHomeRatioSize(54) + [self itemCellSizeWithStyle:((XMIAdRelatedData *)adData).showstyle].height + kHomeRatioSize(10);
    return height;
}

+ (CGSize)itemCellSizeWithStyle:(NSInteger)style {
    if (style == XMIAdStyleHomeCompositeBanner3_4) {
        return CGSizeMake(kHomeRatioSize(136), kHomeRatioSize(185));
    } else if (style == XMIAdStyleHomeCompositeBanner9_16) {
        return CGSizeMake(kHomeRatioSize(106), kHomeRatioSize(189));
    } else if (style == XMIAdStyleHomeCompositeBanner1_1) {
        return CGSizeMake(kHomeRatioSize(136), kHomeRatioSize(136) + kHomeRatioSize(20) + 11);
    } else {
        return CGSizeZero;
    }
}

#pragma mark - XMIFindFeedNativeAdImageListBaseView

- (CGSize)itemCellSize {
    return [XMIOwnFindFeedNativeCompositeAdView itemCellSizeWithStyle:self.adData.showstyle];
}

- (Class)itemCellClass {
    return [XMIFindFeedNativeAdItemCell class];
}

- (NSMutableArray *)itemCellArray {
    return self.adData.currentCompositeAds;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    XMIAdRelatedData *item = [self.adData.currentCompositeAds objectAtIndex:indexPath.item];
    if (item) {
        [self tapAdViewWithPoint:CGPointZero userInfo:@{@"realClickItem" : item}];
    }
}

- (void)setAdData:(XMIAdRelatedData *)adData {
    _adData = adData;
    [self setMoreViewHidden:!(adData.realLink.length || adData.dpRealLink.length)];
}

//混投容器不需要曝光
- (BOOL)supportRealExpose {
    return NO;
}

@end
