//
//  XMIOwnFindFeedNativeSocialImageAdView.m
//  XMAd
//
//  Created by 张晓东 on 2024/3/7.
//

#import "XMIOwnFindFeedNativeSocialImageAdView.h"
#import "UIView+XMIUtils.h"
#import <XMWebImage/SDWebImageManager.h>
#import "XMIAdMacro.h"
#import "XMICommonUtils.h"
#import "XMIAdDataCenter.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import "XMIAdRelatedData.h"
#import "XMIAdHelper.h"

@interface XMIOwnFindFeedNativeSocialImageAdView ()

@end

@implementation XMIOwnFindFeedNativeSocialImageAdView

#pragma mark - adview protocol

- (NSArray<UIView *> *)clickableViews
{
    return @[self.contentView];
}

- (void)updateCustomUI
{
    [super updateCustomUI];
}

- (void)layoutSubviews
{
    [super layoutSubviews];
}


#pragma mark - XMIFeedAdViewCustomRenderProtocol

- (void)customRenderWithAdData:(id)adData
{
    if (![adData isKindOfClass:[XMIAdRelatedData class]]) {
        return;
    }
    XMIAdRelatedData *data = (XMIAdRelatedData *)adData;
    if ([XMIAdHelper adDataIsOperation:data]) {
        [self.adMarkButtonView updateAdMarkText:@""];
    } else {
        if ([[data.adUserType uppercaseString] isEqualToString:@"RTB"]) {
            [self.adMarkButtonView updateAdMarkText:@"推广"];
        } else {
            [self.adMarkButtonView updateAdMarkText:@"广告"];
        }
    }
}

#pragma mark - base

- (BOOL)isVideoAdView
{
    return NO;
}

@end
