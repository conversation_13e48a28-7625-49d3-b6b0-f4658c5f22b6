//
//  XMIOwnPlayingBubbleTextAdBaseView.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/5/17.
//

#import "XMIOwnPlayingBubbleTextAdBaseView.h"
#import "XMIAdMarkView.h"
#import "XMICommonUtils.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import "UIView+XMIUtils.h"
#import "XMIAdMacro.h"


@interface XMIOwnPlayingBubbleTextAdBaseView ()

@end

@implementation XMIOwnPlayingBubbleTextAdBaseView

- (XMIAdMarkView *)adMarkView
{
    if (!_adMarkView) {
        _adMarkView = [[XMIAdMarkView alloc] initWithImage:[XMICommonUtils imageNamed:@"pic_ad_mark_6"]];
        _adMarkView.limitedHeight = -1;
        [_adMarkView sizeToFit];
        [self.contentView addSubview:_adMarkView];
    }
    return _adMarkView;
}

- (void)updateAdMark:(NSString *)adMark
{
    @weakify(self);
    UIImage *placeholder = [XMICommonUtils imageNamed:@"pic_ad_mark_6"];
    [self.adMarkView sd_setImageWithURL:[NSURL URLWithString:adMark] placeholderImage:placeholder completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
        @strongify(self);
        if (self && !error) {
            self.adMarkView.center = CGPointMake(self.contentView.bounds.size.width - 3 - self.adMarkView.bounds.size.width * 0.5f, self.contentView.bounds.size.height - 3 - self.adMarkView.bounds.size.height * 0.5f);
        }
    }];
}

@end
