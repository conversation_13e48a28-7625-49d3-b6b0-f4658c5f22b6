//
//  XMIOwnFindFeedNativeAdABTestImageView.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/3/1.
//

#import "XMIOwnFindFeedNativeAdABTestImageView.h"
#import "XMIAdMarkView.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import "UIView+XMIUtils.h"
#import "XMICommonUtils.h"
#import "XMIAdMacro.h"
#import "XMIAdRelatedData.h"
#import "XMIAdHelper.h"

@interface XMIOwnFindFeedNativeAdABTestImageView ()

@end

@implementation XMIOwnFindFeedNativeAdABTestImageView

- (BOOL)isVideoAdView
{
    return NO;
}

- (void)customRenderWithAdData:(id)adData
{
    if (![adData isKindOfClass:[XMIAdRelatedData class]]) {
        return;
    }
    XMIAdRelatedData *data = (XMIAdRelatedData *)adData;
    if ([XMIAdHelper adDataIsOperation:data]) {
        [self.adMarkButtonView updateAdMarkText:@""];
    } else {
        if ([[data.adUserType uppercaseString] isEqualToString:@"RTB"]) {
            [self.adMarkButtonView updateAdMarkText:@"推广"];
        } else {
            [self.adMarkButtonView updateAdMarkText:@"广告"];
        }
    }
}

@end
