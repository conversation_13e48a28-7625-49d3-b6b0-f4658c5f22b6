//
//  XMIOwnFindFeedNativeImageAdView.m
//  XMAd
//
//  Created by <PERSON><PERSON>yuanz<PERSON> on 2022/3/1.
//

#import "XMIOwnFindFeedNativeImageAdView.h"
#import "UIView+XMIUtils.h"
#import <XMWebImage/SDWebImageManager.h>
#import "XMIAdMacro.h"
#import "XMICommonUtils.h"
#import "XMIAdDataCenter.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import "XMIAdRelatedData.h"
#import "XMIAdHelper.h"

@interface XMIOwnFindFeedNativeImageAdView ()

@end

@implementation XMIOwnFindFeedNativeImageAdView

#pragma mark - adview protocol

- (NSArray<UIView *> *)clickableViews
{
    return @[self.contentView];
}

- (void)updateCustomUI
{
    [super updateCustomUI];
}

- (void)updateNeedRender:(CGFloat)needRender renderSize:(CGSize)renderSize
{
    self.coverImageView.contentMode = UIViewContentModeScaleAspectFill;
}


- (void)layoutSubviews
{
    [super layoutSubviews];
}

- (UIImage *)resizeImage:(UIImage *)image toSize:(CGSize)reSize
{
    UIGraphicsBeginImageContext(CGSizeMake(reSize.width, reSize.height));
    [image drawInRect:CGRectMake(0, 0, reSize.width, reSize.height)];
    UIImage *reSizeImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    return reSizeImage;
}

#pragma mark - XMIFeedAdViewCustomRenderProtocol

- (void)customRenderWithAdData:(id)adData
{
    if (![adData isKindOfClass:[XMIAdRelatedData class]]) {
        return;
    }
    XMIAdRelatedData *data = (XMIAdRelatedData *)adData;
    if ([XMIAdHelper adDataIsOperation:data]) {
        [self.adMarkButtonView updateAdMarkText:@""];
    } else {
        if ([[data.adUserType uppercaseString] isEqualToString:@"RTB"]) {
            [self.adMarkButtonView updateAdMarkText:@"推广"];
        } else {
            [self.adMarkButtonView updateAdMarkText:@"广告"];
        }
    }
}

#pragma mark - reload

- (void)coverImageViewDidLoad
{
    [super coverImageViewDidLoad];

}

- (CGFloat)backgroundHeightWithImageWidth:(CGFloat)width
{
    if (self.coverImageView.image.size.height > 1) {
        return [self imageHeightWithImageWidth:width];
    }
    return [super backgroundHeightWithImageWidth:width];
}

- (BOOL)isVideoAdView
{
    return NO;
}

+ (CGFloat)calAdHeight:(id)adData withAdWidth:(CGFloat)adWidth
{
    UIImage *image = nil;
    if (![adData isKindOfClass:[XMIAdRelatedData class]]) {
        return CGFLOAT_MIN;
    }
    XMIAdRelatedData *relatedData = (XMIAdRelatedData *)adData;
    if (relatedData.cover.length > 0) {
        NSString *key = [[SDWebImageManager sharedManager] cacheKeyForURL:[NSURL URLWithString:relatedData.cover]];
        image = [[SDWebImageManager sharedManager].imageCache imageFromCacheForKey:key];
    }
    
    CGFloat imageHeight = [self imageHeightWithImageWidth:adWidth];

    return imageHeight + kContentViewEdgeTop * 2;
}

@end
