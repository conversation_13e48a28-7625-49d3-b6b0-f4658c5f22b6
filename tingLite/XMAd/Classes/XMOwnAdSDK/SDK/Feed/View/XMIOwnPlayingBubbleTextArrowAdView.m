//
//  XMIOwnPlayingBubbleTextArrowAdView.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/8/11.
//

#define IS_SOUND_SMALL_SCREEN (XMI_SCREEN_HEIGHT <= 667.0f)

#import "XMIOwnPlayingBubbleTextArrowAdView.h"
#import "XMIAdMacro.h"
#import "UIView+XMIUtils.h"
#import "XMICommonUtils.h"
#import <XMWebImage/UIImageView+WebCache.h>

@interface XMIOwnPlayingBubbleTextArrowAdView ()

@property (nonatomic, strong) UIImageView *avatarImageView;

@property (nonatomic, strong) CAShapeLayer *arrowLayer;

@property (nonatomic, weak) UIView *lowerView;

@end

@implementation XMIOwnPlayingBubbleTextArrowAdView


{
    UIView *_bubbleView;
}

- (UIView *)bubbleView
{
    if (!_bubbleView) {
        _bubbleView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 200, 100)];
        UIView *lowerView = [[UIView alloc] initWithFrame:CGRectMake(0, 7, 200, 93)];
        [_bubbleView addSubview:lowerView];
        lowerView.layer.cornerRadius = 6;
        lowerView.backgroundColor = XMI_COLOR_RGBA(0x000000, 0.25f);
        _lowerView = lowerView;
        _arrowLayer = [CAShapeLayer layer];
        [_bubbleView.layer addSublayer:_arrowLayer];
        _arrowLayer.bounds = CGRectMake(0, 0, 16, 7);
        _arrowLayer.position = CGPointMake(self.arrowCenterX, 3.5);
        _arrowLayer.fillColor = lowerView.backgroundColor.CGColor;
        UIBezierPath *bezierPath = [UIBezierPath bezierPath];
        [bezierPath moveToPoint:CGPointMake(0, 7)];
        [bezierPath addLineToPoint:CGPointMake(8, 0)];
        [bezierPath addLineToPoint:CGPointMake(16, 7)];
        [bezierPath addLineToPoint:CGPointMake(0, 7)];
        [bezierPath closePath];
        _arrowLayer.path = bezierPath.CGPath;
        [self.contentView insertSubview:_bubbleView atIndex:0];
        _bubbleView.frame = self.contentView.bounds;
        _bubbleView.backgroundColor = [UIColor clearColor];
    }
    return _bubbleView;
}

- (UIImageView *)avatarImageView
{
    if (!_avatarImageView) {
        _avatarImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
        [self.contentView addSubview:_avatarImageView];
        _avatarImageView.layer.cornerRadius = 12;
        _avatarImageView.layer.masksToBounds = YES;
        _avatarImageView.layer.borderColor = XMI_COLOR_RGBA(0xF4F5FD, 0.9f).CGColor;
        _avatarImageView.layer.borderWidth = 1;
    }
    return _avatarImageView;
}


- (void)layoutSubviews
{
    [super layoutSubviews];
    if (self.contentView.layer.animationKeys.count == 0 && CGAffineTransformIsIdentity(self.contentView.transform)) {
        self.contentView.frame = self.bounds;
    }
    CGFloat arrowHeight = 7;
    self.avatarImageView.xmi_left = 5;
    self.avatarImageView.xmi_centerY = self.contentView.bounds.size.height * 0.5f + arrowHeight * 0.5f;
    CGFloat closeBtnMargin = 6;
    self.closeButton.xmi_top = closeBtnMargin + arrowHeight;
    self.closeButton.xmi_right = self.contentView.bounds.size.width - closeBtnMargin;
    self.btnLabel.xmi_right = self.contentView.bounds.size.width - 32;
    self.btnLabel.xmi_centerY = self.avatarImageView.xmi_centerY;
    self.recommendLabel.xmi_left = self.avatarImageView.xmi_right + 5;
    self.recommendLabel.xmi_centerY = self.avatarImageView.xmi_centerY;
    if (self.recommendLabel.hidden) {
        self.titleContainer.frame = CGRectMake(self.avatarImageView.xmi_right + 5, arrowHeight, self.btnLabel.xmi_left - 4 - self.avatarImageView.xmi_right - 5, self.xmi_height - arrowHeight);
    } else {
        self.titleContainer.frame = CGRectMake(self.recommendLabel.xmi_right + 4, arrowHeight, self.btnLabel.xmi_left - 4 - self.recommendLabel.xmi_right - 4, self.xmi_height - arrowHeight);
    }

    [self updateBubbleAndTitle];
    _lowerView.frame = CGRectMake(0, arrowHeight, self.bubbleView.xmi_width, self.bubbleView.xmi_height - arrowHeight);
    self.adMarkView.center = CGPointMake(self.contentView.bounds.size.width - 3 - self.adMarkView.bounds.size.width * 0.5f, self.contentView.bounds.size.height - 3 - self.adMarkView.bounds.size.height * 0.5f);
}

- (BOOL)shouldPlaySpreadAnimation
{
    return YES;
}

- (void)updateArtistAvatar:(NSString *)avatarURL
{
    if (avatarURL.length == 0) {
        self.avatarImageView.image = [XMICommonUtils imageNamed:@"avatar_default_rectangle"];
    } else {
        [self.avatarImageView sd_setImageWithURL:[NSURL URLWithString:avatarURL] placeholderImage:[XMICommonUtils imageNamed:@"avatar_default_rectangle"]];
    }
}

- (CGFloat)contentWidth
{
    return self.xmi_width;
}

- (void)setArrowCenterX:(CGFloat)arrowCenterX
{
    _arrowCenterX = arrowCenterX;
    _arrowLayer.position = CGPointMake(self.arrowCenterX, 3.5);
}

- (CGFloat)arrowHeight
{
    return 7.0f;
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
