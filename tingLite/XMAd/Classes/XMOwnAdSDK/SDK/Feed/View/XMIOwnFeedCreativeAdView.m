//
//  XMIOwnFeedCreativeAdView.m
//  XMAd
//
//  Created by cuiyuanz<PERSON> on 2022/3/8.
//

#import "XMIOwnFeedCreativeAdView.h"
#import "XMIAdMarkView.h"
#import "XMICommonUtils.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import "UIView+XMIUtils.h"
#import "XMIAdMacro.h"

@interface XMIOwnFeedCreativeAdView ()

@property (strong, nonatomic) XMIAdMarkView *adMark;

@end

@implementation XMIOwnFeedCreativeAdView

- (XMIAdMarkView *)adMark
{
    if (!_adMark) {
        _adMark = [[XMIAdMarkView alloc] init];
        [self.contentView addSubview:_adMark];
    }
    return _adMark;
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    self.adMark.xmi_right = self.xmi_width - 4;
    self.adMark.xmi_bottom = self.xmi_height - 4;
}

- (void)updateAdMark:(NSString *)adMark
{
    @weakify(self);
    UIImage *placeholder = [XMICommonUtils imageNamed:@"pic_ad_mark_2"];
    if (adMark) {
        [self.adMark sd_setImageWithURL:[NSURL URLWithString:adMark] placeholderImage:placeholder completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
            @strongify(self);
            if (self && !error) {
                self.adMark.xmi_size = CGSizeMake(24, 14);
                self.adMark.xmi_right = self.xmi_width - 4;
                self.adMark.xmi_bottom = self.xmi_height - 4;
            }
        }];
    } else {
        self.adMark.image = placeholder;
        self.adMark.xmi_size = CGSizeMake(24, 14);
        self.adMark.xmi_right = self.xmi_width - 4;
        self.adMark.xmi_bottom = self.xmi_height - 4;
    }
}

+ (CGFloat)calAdHeight:(id)adData withAdWidth:(CGFloat)adWidth
{
    CGFloat imageTop = 10;
    CGFloat imageLeft = 15;
    CGFloat imageBottomSpace = 10;
    CGFloat titleBottomSpace = 6;
    CGFloat descriptionBottomSpace = 10;
    CGFloat imageWidth = XMI_SCREEN_WIDTH - imageLeft * 2;
    CGFloat imageHeight = [self imageHeightWithImageWidth:imageWidth];
    CGFloat titleHeight = 15;
    CGFloat descHeight = 14;
    CGFloat height = imageTop + imageHeight + imageBottomSpace + titleHeight + titleBottomSpace + descHeight + descriptionBottomSpace;
    return height;
}

+ (CGFloat)imageHeightWithImageWidth:(CGFloat)imageWidth
{
    CGFloat imageHeight = 0;
    // 1496 * 400
    if([XMICommonUtils isDeviceIPad])
    {
        imageHeight = imageWidth * (400.0 / 1496);
    }
    // 1182 * 426
    else
    {
        imageHeight = imageWidth * (9 / 16.0);
    }
    return imageHeight;
}
/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
