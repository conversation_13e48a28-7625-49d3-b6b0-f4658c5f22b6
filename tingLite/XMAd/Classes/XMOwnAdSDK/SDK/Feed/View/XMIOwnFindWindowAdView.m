//
//  XMIOwnFindWindowAdView.m
//  XMAd
//
//  Created by cuiyuanzhe on 2022/3/8.
//

#import "XMIOwnFindWindowAdView.h"
#import "XMIAdMarkView.h"
#import "XMICommonUtils.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import "UIView+XMIUtils.h"

@interface XMIOwnFindWindowAdView ()

@property (nonatomic, strong) XMIAdMarkView *adMarkView;

@end

@implementation XMIOwnFindWindowAdView

- (XMIAdMarkView *)adMarkView
{
    if (!_adMarkView) {
        _adMarkView = [[XMIAdMarkView alloc] init];
        [self addSubview:_adMarkView];
        _adMarkView.xmi_width = 28;
        _adMarkView.xmi_height = 16;
        _adMarkView.xmi_right = self.xmi_width - 38;
        _adMarkView.xmi_centerY = self.closeButton.xmi_centerY;
    }
    return _adMarkView;
}

- (void)updateAdMark:(NSString *)adMark
{
    UIImage *adMarkPlaceholder = [XMICommonUtils imageNamed:@"pic_ad_mark_1"];
    self.adMarkView.limitedHeight = adMarkPlaceholder.size.height;
    [self.adMarkView sd_setImageWithURL:[NSURL URLWithString:adMark] placeholderImage:adMarkPlaceholder];
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    _adMarkView.xmi_width = 28;
    _adMarkView.xmi_height = 16;
    _adMarkView.xmi_right = self.xmi_width - 38;
    _adMarkView.xmi_centerY = self.closeButton.xmi_centerY;
}

+ (CGFloat)calAdHeight:(id)adData withAdWidth:(CGFloat)adWidth
{
    return 181;
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/
@end
