//
//  XMIOwnFindFeedGXAdView.m
//  XMAd
//
//  Created by cuiyuanzhe on 2025/4/7.
//

#import "XMIOwnFindFeedGXAdView.h"
#import <XMAdGaiaX/XMAdGXCommonDef.h>
#import <GaiaXiOS/GaiaXiOS.h>

@interface XMIOwnFindFeedGXAdView ()

@property (nonatomic, assign) CGPoint tapPoint;

@end

@implementation XMIOwnFindFeedGXAdView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didReceiveClickNotification:) name:XMAdGXSlideViewDidClickNotification object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didReceiveCloseNotification:) name:XMAdGXSlideViewDidClickCloseNotification object:nil];
        self.tapPoint = CGPointZero;
    }
    return self;
}



- (void)didReceiveClickNotification:(NSNotification *)notification
{
    UIView *view = notification.object;
    if (view.gx_rootView == self.gxAdView) {
        NSMutableDictionary *dic = [NSMutableDictionary dictionary];
        id index = [[notification userInfo] objectForKey:XMAdGXSlideViewIndexKey];
        if (index) {
            dic[@"index"] = index;
        }
        [self tapAdViewWithPoint:self.tapPoint userInfo:nil];
    }
}

- (void)didReceiveCloseNotification:(NSNotification *)notification
{
    UIView *view = notification.object;
    if (view.gx_rootView == self.gxAdView) {
        NSMutableDictionary *dic = [NSMutableDictionary dictionary];
        id index = [[notification userInfo] objectForKey:XMAdGXSlideViewIndexKey];
        if (index) {
            dic[@"index"] = index;
        }
        [self closeAdViewClickWithUserInfo:dic];
    }
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (UIView *)hitTest:(CGPoint)point withEvent:(UIEvent *)event
{
    if (event.type == UIEventTypeTouches) {
        self.tapPoint = point;
    }
    return [super hitTest:point withEvent:event];
}


@end
