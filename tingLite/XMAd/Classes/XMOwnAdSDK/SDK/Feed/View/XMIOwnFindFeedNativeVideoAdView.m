//
//  XMIOwnFindFeedNativeVideoAdView.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/2.
//

#import "XMIOwnFindFeedNativeVideoAdView.h"
#import "XMIAdVideoPlayer.h"
#import "UIView+XMIUtils.h"
#import "XMIAdMacro.h"
#import "XMIAdRelatedData.h"
#import "XMIAdReporter+AD.h"
@interface XMIOwnFindFeedNativeVideoAdView ()<XMIAdVideoPlayerDelegate>

@property (nonatomic, strong) XMIAdVideoPlayer *videoPlayer;

@property (nonatomic, strong) XMIAdVideoView *videoView;

@property (nonatomic, strong) NSNumber *retryTag;

@property (nonatomic, assign) BOOL shouldPlayWhenEnterForeground;

@property (nonatomic, strong) XMIAdRelatedData *tempData;

@end

@implementation XMIOwnFindFeedNativeVideoAdView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didReceiveForegroundNotification:) name:UIApplicationWillEnterForegroundNotification object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didReceiveBackgroundNotification:) name:UIApplicationDidEnterBackgroundNotification object:nil];
    }
    return self;
}

- (XMIAdVideoPlayer *)videoPlayer
{
    if (!_videoPlayer) {
        _videoPlayer = [[XMIAdVideoPlayer alloc] init];
        _videoPlayer.delegate = self;
        _videoPlayer.volume = 0.0;
        _videoPlayer.repeat = YES;
        _videoPlayer.view.alpha = 0.0;
    }
    return _videoPlayer;
}

- (XMIAdVideoView *)videoView {
    if (!_videoView) {
        _videoView = [[XMIAdVideoView alloc] init];
        _videoView.backgroundColor = [UIColor clearColor];
        [_videoView advanceCreatePlayer];
        _videoView.hidden = YES;
    }
    return _videoView;
}

- (void)play
{
    if (!self.videoPlayer.isPlaying) {
        [self.videoPlayer play];
    }
}

- (void)pause
{
    [self.videoPlayer pause];
}

- (void)replay
{
    [self.videoPlayer replay];
}

- (void)stop
{
    [self.videoPlayer stop];
}

- (BOOL)isPlaying
{
    return self.videoPlayer.isPlaying;
}

- (void)updateVideoURL:(NSString *)videoURL indentifier:(NSString *)identifier
{
    if (videoURL.length == 0) {
        [_videoPlayer stop];
        _videoView.hidden = YES;
        return;
    }
    self.videoView.hidden = NO;
    
    if (![self.videoPlayer.identifier isEqualToString:identifier]) {
        self.videoPlayer.identifier = identifier;
        [self loadVideoWithURL:[NSURL URLWithString:videoURL]];
    }
    self.videoView.frame = self.coverImageView.bounds;
}

- (void)sizeToFit
{
    [super sizeToFit];
    self.videoView.frame = self.coverImageView.bounds;
}

- (BOOL)isVideoAdView
{
    return YES;
}

- (void)customRenderWithAdData:(id)adData {
    [super customRenderWithAdData:adData];
    XMIAdRelatedData *data = XMTypedValue(XMIAdRelatedData, adData);
    self.tempData = data;
}

#pragma mark - player delegate

- (void)player:(XMIAdVideoPlayer *)player playStateDidChanged:(XMIAdPlayerPlayState)state
{
    [self playerStateChanged:(XMIPlayerPlayState)state];
    if (!self.videoView.superview && state == XMIAdPlayerStatePlaying) {
        [self.coverImageView insertSubview:self.videoView atIndex:0];
        self.videoView.frame = self.coverImageView.bounds;
    }
}

- (void)player:(XMIAdNewVideoPlayer *)player playTimeDidChanged:(CGFloat)currentTime {
    if ([self.delegate respondsToSelector:@selector(feedAdView:playTimeDidChanged:)]) {
        [self.delegate feedAdView:self playTimeDidChanged:currentTime];
    }
    
    if (self.tempData && self.tempData.businessReportClickTime > 0 && !self.tempData.businessReportedClickTime) {
        if (currentTime >= self.tempData.businessReportClickTime) {
            [XMIAdReporter clickReportWithAd:self.tempData andView:self andUserInfo:@{@"extraParams" : @{@"virtualClickReportType": @(1)}}];
            self.tempData.businessReportedClickTime = YES;
        }
    }
}

- (void)player:(XMIAdVideoPlayer *)player failWithError:(NSError *)error
{
    BOOL shouldRetry = NO;
    if ([self.delegate respondsToSelector:@selector(feedAdViewShouldRetryVideo:)]) {
        shouldRetry = [self.delegate feedAdViewShouldRetryVideo:self];
    }
    if (shouldRetry) {
        if (self.retryTag) {
            self.retryTag = [NSNumber numberWithInteger:self.retryTag.integerValue + 1];
        } else {
            NSNumber *retryTag = [NSNumber numberWithInteger:arc4random()];
            self.retryTag = retryTag;
        }
        NSNumber *retryTag = self.retryTag;
        @weakify(self)
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            @strongify(self);
            if (!self
                || !self.retryTag || ![self.retryTag isEqual:retryTag]) {
                return ;
            }
            if (([UIApplication sharedApplication].applicationState == UIApplicationStateActive) && self.window && [self xmi_isExposed:self.window radio:0.01f]) {
                [self loadVideoWithURL:self.videoPlayer.currentURL];
            }
        });
       
    } else {
        [_videoView removeFromSuperview];
        [self failRenderWithError:error];
    }
}

- (void)loadVideoWithURL:(NSURL *)url
{
    if (_videoView) {
        [_videoView removeFromSuperview];
    }
    self.retryTag = nil;
    [self.videoPlayer startPlayWithURL:url playerView:self.videoView];
}

- (void)didReceiveBackgroundNotification:(NSNotification *)notification
{
    if (self.videoPlayer.isPlaying) {
        self.shouldPlayWhenEnterForeground = YES;
        [self pause];
    }
}

- (void)didReceiveForegroundNotification:(NSNotification *)notification
{
    if (self.shouldPlayWhenEnterForeground) {
        [self play];
        self.shouldPlayWhenEnterForeground = NO;
    }
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end
