//
//  XMIOwnSimpleCardAdImageView.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/2/24.
//

#import "XMIOwnSimpleCardAdImageView.h"
#import "XMIAdMarkView.h"
#import "XMICommonUtils.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import "UIView+XMIUtils.h"
#import "XMIAdMacro.h"

@interface XMIOwnSimpleCardAdImageView ()

@property (nonatomic, strong) XMIAdMarkView *adMarkView;

@end

@implementation XMIOwnSimpleCardAdImageView


- (XMIAdMarkView *)adMarkView
{
    if (!_adMarkView) {
        _adMarkView = [[XMIAdMarkView alloc] init];
        _adMarkView.limitedHeight = 14;
        [self.coverImageView addSubview:_adMarkView];
    }
    return _adMarkView;
}

- (void)updateAdMark:(NSString *)adMark
{
    @weakify(self);
    UIImage *placeholder = [XMICommonUtils imageNamed:@"pic_ad_mark_2"];
    [self.adMarkView sd_setImageWithURL:[NSURL URLWithString:adMark] placeholderImage:placeholder completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
        @strongify(self);
        if (self && !error) {
            self.adMarkView.xmi_left = 4;
            self.adMarkView.xmi_bottom =  self.coverImageView.xmi_height - 4;
        }
    }];
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    self.adMarkView.xmi_left = 4;
    self.adMarkView.xmi_bottom =  self.coverImageView.xmi_height - 4;
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
