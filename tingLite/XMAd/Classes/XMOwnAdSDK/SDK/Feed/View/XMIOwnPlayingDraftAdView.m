//
//  XMIOwnPlayingDraftAdView.m
//  XMAd
//
//  Created by cuiyuanzhe on 2022/3/8.
//

#import "XMIOwnPlayingDraftAdView.h"
#import "XMIAdMarkView.h"
#import "XMICommonUtils.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import "UIView+XMIUtils.h"

@interface XMIOwnPlayingDraftAdView ()

@property (nonatomic, strong) XMIAdMarkView *adMarkView;

@end

@implementation XMIOwnPlayingDraftAdView

- (XMIAdMarkView *)adMarkView
{
    if (!_adMarkView) {
        _adMarkView = [[XMIAdMarkView alloc] initWithImage:[XMICommonUtils imageNamed:@"pic_ad_mark_4"]];
        [self.coverView addSubview:_adMarkView];
        [_adMarkView sizeToFit];
        _adMarkView.xmi_left = 3;
        _adMarkView.xmi_bottom = self.coverView.xmi_height - 3;
    }
    return _adMarkView;
}

- (void)updateAdMark:(NSString *)adMark
{
    UIImage *adMarkPlaceholder = [XMICommonUtils imageNamed:@"pic_ad_mark_4"];
    self.adMarkView.image = adMarkPlaceholder;
    self.adMarkView.limitedHeight = adMarkPlaceholder.size.height;
    [self.adMarkView sd_setImageWithURL:[NSURL URLWithString:adMark] placeholderImage:adMarkPlaceholder];
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    _adMarkView.xmi_left = 3;
    _adMarkView.xmi_bottom = self.coverView.xmi_height - 3;
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
