//
//  XMIOwnFindGuessYouLikeAdView.m
//  XMAd
//
//  Created by xmly on 2022/12/14.
//

#import "XMIOwnFindGuessYouLikeAdView.h"
#import "XMIAnimatedImageView.h"
#import "UIView+XMIUtils.h"
#import <YYText/YYText.h>
#import "XMIAdMacro.h"
#import "XMIAdModel.h"
#import "XMIAdReporter.h"
#import "XMIAdButton.h"
#import "XMICommonUtils.h"
#import <XMCategories/UIImage+XMDynamic.h>
#import <XMWebImage/UIImageView+WebCache.h>
#import "XMIOwnMixFeedCardAdView.h"
#import "XMIExpressAdTrackInfoMapModel.h"
#import "NSObject+XMIModel.h"
#import "XMIOwnJumpManager.h"
#import "XMIAdReporter+AD.h"
#import <XMCategories/XMCategory.h>
#import "XMIExpressAdContainer.h"
#import "XMIAdHelper.h"


#define kCoverTop_B kHomeRatioSize(16)
#define kHomeSingleStyleMargins_B 16

#define kPlayIconSize kHomeRatioSize(26)
#define kCoverSize kSingleCoverSize

#define kSingleCloseColor XMI_COLOR_DynamicFromRGB(0xB3B3B3, 0x444444)

#define kDislikeSize kHomeRatioSize(20)
#define kDislikeContainerW (kDislikeSize + kHomeSingleStyleMargins_B * 2)
#define kDislikeContainerH (kDislikeSize + 12)

#define kSingleTitleFont_B XMI_AD_PingFangFont(kHomeRatioSize(16))
#define kSingleTitleColor_B XMI_COLOR_DynamicFromRGB(0x131313, 0xDCDCDC)

#define kHomeTitle2Cover_B kHomeRatioSize(12)
#define kTitleRightMargin_B kHomeRatioSize(21)
#define kHomeSingleTitleX_B (kHomeSingleStyleMargins_B + kCoverSize + kHomeTitle2Cover_B)

#define kHomeInfoTop_B kHomeRatioSize(4)
#define kHomeInfoHeight_B kHomeRatioSize(18)
#define kHomeInfoFont_B XMI_AD_PingFangFont(kHomeRatioSize(12))
#define kHomeInfoColor_B XMI_COLOR_RGB(0xF97373)

#define kReasonTop_B kHomeRatioSize(4)
#define kReasonHeight_B kHomeRatioSize(18)

#define kHomeAvatarSize kHomeRatioSize(16)
#define kHomeAvatarTopSpace_B kHomeRatioSize(7)
#define kHomeAvatarBottomSpace_B kHomeRatioSize(16)

#define kHomeInfoIconSize_B kHomeRatioSize(12)

#define kSingleIntroFont_B XMI_AD_PingFangFont(kHomeRatioSize(12))
#define kSingleIntroColor_B XMI_COLOR_DynamicFromRGB(0x8F8F8F, 0x66666B)

#define kHomeInfoIcon2Text_B kHomeRatioSize(3)

@interface XMIOwnFindGuessYouLikeAdView()<XMIFeedAdViewCustomRenderProtocol>
@property (nonatomic, strong) XMIAdButton *closeButton;
@property (nonatomic, strong) UIButton *userButton;

@property (nonatomic, strong) XMIAnimatedImageView *coverView;
@property (nonatomic, strong) XMIAnimatedImageView *avatarView;

@property (nonatomic, strong) UIView *playIcon;
@property (nonatomic, strong) UIImageView *playIconImageView;

@property (nonatomic, strong) YYLabel *titleLabel;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) UILabel *subTitleLabel;
@property (nonatomic, strong) UILabel *subTitleLabel2;
@property (nonatomic, strong) UIView *divideLine;
@property (nonatomic, strong) UIView *scoreDivideLine;
@property (nonatomic, strong) UIImageView *tagImageView;

@property (nonatomic, strong) UIImageView *scoreImageView;
@property (nonatomic, strong) UILabel *scoreLabel;

@property (nonatomic, strong) UIImageView *adMark;

@property (nonatomic, strong) UIImageView *closeImageView;

@property (nonatomic, weak) XMIAdRelatedData *adData;

@property (nonatomic, assign) long long playerPromoteId;
@property (nonatomic, assign) BOOL isPlaying;
@end

@implementation XMIOwnFindGuessYouLikeAdView
#pragma mark - Init
- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = XMI_COLOR_DynamicFromRGB(0xffffff, 0x131313);
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(playerStatusDidChange:) name:XMIExpressAdContainerControllerPlayerStatusDidChange object:nil];
    }
    return self;
}

- (YYLabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [YYLabel new];
        _titleLabel.isAccessibilityElement = NO;
        _titleLabel.numberOfLines = 2;
        [self.contentView addSubview:_titleLabel];
    }
    return _titleLabel;
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        UILabel *label = [UILabel new];
        label.textColor = XMI_COLOR_DynamicFromRGB(0x666666, 0x8D8D91);
        label.font = XMI_AD_PingFangFont(11);
        label.isAccessibilityElement = NO;
        _nameLabel = label;
        [self.contentView addSubview:label];
    }
    return _nameLabel;
}
 
- (XMIAnimatedImageView *)avatarView {
    if (!_avatarView) {
        XMIAnimatedImageView *view = [XMIAnimatedImageView new];
        view.contentMode = UIViewContentModeScaleAspectFill;
        view.layer.cornerRadius = kHomeAvatarSize / 2;
        view.layer.masksToBounds = YES;
        _avatarView = view;
        [self.contentView addSubview:view];
    }
    return _avatarView;
}

- (XMIAnimatedImageView *)coverView {
    if (!_coverView) {
        XMIAnimatedImageView *cView = [[XMIAnimatedImageView alloc] init];
        cView.contentMode = UIViewContentModeScaleAspectFill;
        cView.layer.cornerRadius = 4;
        cView.layer.masksToBounds = YES;
        cView.backgroundColor = XMI_COLOR_DynamicFromRGB(0xF0F0F0, 0x333333);
        [cView addSubview:self.tagImageView];
        _coverView = cView;
        
        
        [cView addSubview:self.playIcon];
        CGFloat playIconW = kPlayIconSize;
        CGFloat playIconH = kPlayIconSize;
        CGFloat playIconX = kCoverSize - playIconW - kHomeRatioSize(6);
        CGFloat playIconY = kCoverSize - playIconH - kHomeRatioSize(6);
        self.playIcon.frame = CGRectMake(playIconX, playIconY, playIconW, playIconH);
        [self.contentView addSubview:cView];
    }
    return _coverView;
}

- (UIImageView *)tagImageView {
    if (!_tagImageView) {
        _tagImageView = [UIImageView new];
    }
    return _tagImageView;
}

- (UIButton *)userButton {
    if (!_userButton) {
        _userButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_userButton addTarget:self action:@selector(anchorItemClicked:) forControlEvents:UIControlEventTouchUpInside];
        _userButton.backgroundColor = UIColor.clearColor;
        _userButton.adjustsImageWhenHighlighted = NO;
        [self.contentView addSubview:_userButton];
    }
    return _userButton;
}

- (UILabel *)subTitleLabel {
    if (!_subTitleLabel) {
        UILabel *label = [UILabel new];
        label.isAccessibilityElement = NO;
        _subTitleLabel = label;
        [self.contentView addSubview:label];
    }
    return _subTitleLabel;
}

- (UILabel *)subTitleLabel2 {
    if (!_subTitleLabel2) {
        UILabel *label = [UILabel new];
        label.isAccessibilityElement = NO;
        _subTitleLabel2 = label;
        [self.contentView addSubview:_subTitleLabel2];
    }
    return _subTitleLabel2;
}

- (UIView *)divideLine {
    if (!_divideLine) {
        _divideLine = [UIView new];
        _divideLine.backgroundColor = XMI_COLOR_DynamicFromRGBA(0xEEEEEE, 1, 0x000000, 1);
        [self.contentView addSubview:_divideLine];
    }
    return _divideLine;
}

- (UIView *)scoreDivideLine
{
    if (!_scoreDivideLine) {
        _scoreDivideLine = [UIView new];
        _scoreDivideLine.alpha = 0.6f;
        [self.contentView addSubview:_scoreDivideLine];
    }
    return _scoreDivideLine;
}

/// 关闭按钮
- (UIButton *)closeButton
{
    if (!_closeButton) {
        _closeButton = [[XMIAdButton alloc] initWithFrame:CGRectZero];
        UIImage *normalImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        UIImage *darkImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close_dark"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        [_closeButton setImage:[UIImage xm_imageWithLight:normalImage dark:darkImage] forState:UIControlStateNormal];
        _closeButton.imageView.tintColor = kSingleCloseColor;
        [_closeButton addTarget:self action:@selector(closeButtonAction:) forControlEvents:UIControlEventTouchUpInside];
        _closeButton.accessibilityLabel = @"关闭";
        [self.contentView addSubview:_closeButton];
    }
    return _closeButton;
}

- (UIImageView *)scoreImageView {
    if (!_scoreImageView) {
        _scoreImageView = [[UIImageView alloc] initWithFrame:CGRectZero];
        _scoreImageView.contentMode = UIViewContentModeScaleAspectFit;
        [self.contentView addSubview:_scoreImageView];
    }
    return _scoreImageView;
}

- (UILabel *)scoreLabel {
    if (!_scoreLabel) {
        _scoreLabel = [[UILabel alloc] init];
        [self.contentView addSubview:_scoreLabel];
    }
    return _scoreLabel;
}

- (UIImageView *)adMark
{
    if (!_adMark) {
        _adMark = [[UIImageView alloc] initWithFrame:CGRectZero];
        _adMark.image = [XMICommonUtils imageNamed:@"pic_ad_mark_5"];
        _adMark.contentMode = UIViewContentModeScaleAspectFit;
        [self.contentView addSubview:_adMark];
    }
    return _adMark;
}

- (UIView *)playIcon {
    if (!_playIcon) {
        _playIcon = [UIView new];
        _playIcon.backgroundColor = UIColor.clearColor;
        
        UIBlurEffect *effect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleLight];
        UIVisualEffectView *effectView = [[UIVisualEffectView alloc] initWithEffect:effect];
        effectView.layer.cornerRadius = kPlayIconSize / 2;
        effectView.layer.masksToBounds = YES;
        [_playIcon addSubview:effectView];
        effectView.frame = CGRectMake(0, 0, kPlayIconSize, kPlayIconSize);
        
        UIImage *image = [XMICommonUtils imageNamed:@"icon_mix_feed_track_play"];
        UIImageView *play = [[UIImageView alloc] initWithImage:image];
        play.contentMode = UIViewContentModeScaleAspectFit;
        _playIconImageView = play;
        [_playIcon addSubview:play];

        CGFloat playW = image.size.width;
        CGFloat playH = image.size.height;
        CGFloat playX = (kPlayIconSize - playW) / 2;
        CGFloat playY = (kPlayIconSize - playH) / 2;
        play.frame = CGRectMake(playX, playY, playW, playH);
    }
    return _playIcon;
}

- (UIImageView *)closeImageView
{
    if (!_closeImageView) {
        UIImage *normalImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        UIImage *darkImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close_dark"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        _closeImageView = [[UIImageView alloc] initWithImage:[UIImage xm_imageWithLight:normalImage dark:darkImage]];
        _closeImageView.tintColor = kSingleCloseColor;;
        [self.closeButton addSubview:_closeImageView];
        [self.closeButton setImage:nil forState:UIControlStateNormal];
    }
    return _closeImageView;
}

- (void)sizeToFit
{
    UIEdgeInsets edge = self.edge;
    CGFloat imgWidth = self.xmi_width - edge.left - edge.right;
    self.contentView.frame = CGRectMake(edge.left, edge.top, imgWidth, self.xmi_height);
}

#pragma mark - Action
- (void)playerStatusDidChange:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    id trackInfo = self.adData.trackInfoMapModel;
    if ([trackInfo isKindOfClass:[XMIExpressAdTrackInfoMapModel class]]) {
        long long trackId = [[userInfo valueForKey:@"trackId"] longLongValue];
        BOOL playing = [[userInfo valueForKey:@"playing"] boolValue];

        XMIExpressAdTrackInfoMapModel *trackInfoModel = (XMIExpressAdTrackInfoMapModel *)trackInfo;
        long long promoteId = [trackInfoModel.promoteId longLongValue];
        self.playerPromoteId = trackId;
        self.isPlaying = playing;
        if (trackId != promoteId) {
            return;
        }
        if (playing) {
            self.playIconImageView.image = [XMICommonUtils imageNamed:@"icon_mix_feed_track_pause"];
        } else {
            self.playIconImageView.image = [XMICommonUtils imageNamed:@"icon_mix_feed_track_play"];
        }
    }
}

- (void)closeButtonAction:(UIButton *)button
{
    [self closeAdViewClick];
}

- (void)anchorItemClicked:(UIButton *)button
{
    if (self.delegate && [self.delegate respondsToSelector:@selector(anchorItemClicked:anchorId:)]) {
        NSString *uid;
        id trackInfo = self.adData.trackInfoMapModel;
        if ([trackInfo isKindOfClass:[XMIExpressAdTrackInfoMapModel class]]) {
            XMIExpressAdTrackInfoMapModel *trackInfoModel = (XMIExpressAdTrackInfoMapModel *)trackInfo;
            uid = trackInfoModel.broadcasterId;
        }
        if (uid.length) {
            [self.delegate anchorItemClicked:self anchorId:uid];
        }
    }
}

#pragma mark - XMIFeedAdViewProtocol
- (void)didExposeAdView
{
    if (!self.adData.isExposed) {
        if (self.adData) {
            // 混排猜你喜欢，需要在展示时上报tingshow，请求时不上报tingshow
            [XMIAdReporter exposeReportValidAds:@[self.adData]];
        }
    }
    [super didExposeAdView];
}

+ (CGFloat)calAdHeight:(id)relatedData withAdWidth:(CGFloat)adWidth {
    if (![relatedData isKindOfClass:[XMIAdRelatedData class]]) {
        return 0;
    }
    XMIAdRelatedData *adData = (XMIAdRelatedData *)relatedData;
    
    id trackInfo = adData.trackInfoMapModel;
    XMIExpressAdTrackInfoMapModel *trackInfoModel;
    if ([trackInfo isKindOfClass:[XMIExpressAdTrackInfoMapModel class]]) {
        trackInfoModel = (XMIExpressAdTrackInfoMapModel *)trackInfo;
    }
    
    if (!adData.titleTextLayout) {
        adData.titleTextLayout = [self getTitleTextLayout:trackInfoModel.title];
    }
    
    CGFloat titleHeight = ceilf(adData.titleTextLayout.textBoundingSize.height);
    
    CGFloat calHeight = kCoverTop_B + titleHeight;
    if (trackInfoModel.subtitle.length > 0 || ([trackInfoModel.promoteType isEqualToString:@"ALBUM"] && trackInfoModel.albumScore.length)) {
        calHeight = calHeight + kReasonTop_B + kReasonHeight_B;
    }
    
    BOOL displaySub2 = NO;
    if ([trackInfoModel.promoteType isEqualToString:@"TRACK"]) {
        if (trackInfoModel.playCount.length || trackInfoModel.duration.length) {
            displaySub2 = YES;
        }
    } else if ([trackInfoModel.promoteType isEqualToString:@"LIVE"]) {
        if (trackInfoModel.hotness.length) {
            displaySub2 = YES;
        }
    }
    
    if (displaySub2) {
        calHeight = calHeight + kHomeInfoTop_B + kHomeInfoHeight_B;
    }
    calHeight = calHeight + kHomeAvatarTopSpace_B + kHomeAvatarSize + kHomeAvatarBottomSpace_B;
    CGFloat height = MAX(kCoverTop_B * 2 + kCoverSize, calHeight);
    
    return height;
}

- (void)customRenderWithAdData:(id)adData
{
    if (![adData isKindOfClass:[XMIAdRelatedData class]]) {
        return;
    }
    XMIAdRelatedData *relatedData = (XMIAdRelatedData *)adData;
    self.adData = adData;
    id trackInfo = relatedData.trackInfoMapModel;
    if ([trackInfo isKindOfClass:[XMIExpressAdTrackInfoMapModel class]]) {
        XMIExpressAdTrackInfoMapModel *trackInfoModel = (XMIExpressAdTrackInfoMapModel *)trackInfo;
        
        
        self.titleLabel.textLayout = relatedData.titleTextLayout;
        self.subTitleLabel.text = trackInfoModel.subtitle;
        self.subTitleLabel.hidden = !trackInfoModel.subtitle.length;

        NSURL *imgUrl = [[NSURL URLWithString:trackInfoModel.broadcasterPic] xm_webpURLWithSize:CGSizeMake(kHomeAvatarSize * UIScreen.mainScreen.scale, kHomeAvatarSize * UIScreen.mainScreen.scale)];
        [self.avatarView sd_setImageWithURL:imgUrl placeholderImage:[XMICommonUtils imageNamed:@"avatar_default_rectangle"] completed:nil];
        self.nameLabel.text = trackInfoModel.broadcasterName;
        
        self.playIcon.hidden = YES;
        self.scoreImageView.hidden = self.scoreLabel.hidden = YES;
        self.scoreDivideLine.hidden = YES;
        self.subTitleLabel2.hidden = YES;
        if ([trackInfoModel.promoteType isEqualToString:@"TRACK"]) {
            long long promoteId = [trackInfoModel.promoteId longLongValue];
            if (promoteId == self.playerPromoteId && self.isPlaying) {
                self.playIconImageView.image = [XMICommonUtils imageNamed:@"icon_mix_feed_track_pause"];
            } else {
                self.playIconImageView.image = [XMICommonUtils imageNamed:@"icon_mix_feed_track_play"];
            }
            NSMutableAttributedString *subtitle2 = [[NSMutableAttributedString alloc] init];
            if (trackInfoModel.playCount.length) {
                [subtitle2 appendAttributedString:[[NSAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@次播放", [trackInfoModel.playCount formatBigNumber]] attributes:@{NSForegroundColorAttributeName : self.subTitleLabel2.textColor}]];

            }
            if (trackInfoModel.duration.length > 0) {
                if (subtitle2.length) {
                    [subtitle2 appendAttributedString:[[NSAttributedString alloc] initWithString:@" · " attributes:@{NSForegroundColorAttributeName : colorDynamicFromRGBA(0xd8d8d8, 1.0f, 0x8D8D91, 1.0f)}]];
                }
                [subtitle2 appendAttributedString:[[NSAttributedString alloc] initWithString:[NSString stringWithFormat:@"总时长%@", [XMIAdHelper stringForSeconds2:[trackInfoModel.duration longLongValue]]] attributes:@{NSForegroundColorAttributeName : self.subTitleLabel2.textColor}]];
            }
            if (subtitle2.length) {
                [subtitle2 addAttribute:NSFontAttributeName value:self.subTitleLabel.font range:NSMakeRange(0, subtitle2.length)];
                self.subTitleLabel2.text = nil;
                self.subTitleLabel2.attributedText = subtitle2;
                self.subTitleLabel2.hidden = NO;
            }
            self.playIcon.hidden = NO;
        } else if ([trackInfoModel.promoteType isEqualToString:@"ALBUM"]) {
            if (trackInfoModel.albumScore.length > 0) {
                self.scoreImageView.hidden = self.scoreLabel.hidden = YES;
                self.scoreDivideLine.hidden = YES;
            }
        } else if ([trackInfoModel.promoteType isEqualToString:@"LIVE"]) {
            if (trackInfoModel.hotness.length) {
                self.subTitleLabel2.hidden = NO;
                self.subTitleLabel2.attributedText = nil;
                self.subTitleLabel2.text = [NSString stringWithFormat:@"热度%@", [trackInfoModel.hotness formatBigNumber]];
            }
        }
    }
    
    self.userButton.accessibilityLabel = [NSString stringWithFormat:@"主播%@",self.nameLabel.text];
    
    UIImage *adMarkImage = [XMICommonUtils imageNamed:@"pic_ad_mark_5"];
    UIImage *adMarkDarkImage = [XMICommonUtils imageNamed:@"pic_ad_mark_5_dark"];
    NSURL *markUrl = [NSURL URLWithString:relatedData.adMark];
    [self.adMark sd_setImageWithURL:markUrl placeholderImage:[UIImage xm_imageWithLight:adMarkImage dark:adMarkDarkImage]];
    
    self.divideLine.hidden = relatedData.hideDivideLine;
    
    [self p_setupCoverImageWithData:relatedData];
    [self p_setCloseButtonBackImage:relatedData];
    
    
    CGFloat titleLabelY = kCoverTop_B;
    CGFloat titleLabelX = kHomeSingleTitleX_B;
    
    CGFloat titleLabelW = MAX(
                              ceilf(relatedData.titleTextLayout.textBoundingSize.width),
                              XMSCREEN_WIDTH - kHomeSingleTitleX_B - kTitleRightMargin_B
                              );;
    CGFloat titleLabelH = relatedData.titleTextLayout.textBoundingSize.height;
    self.titleLabel.frame = CGRectMake(titleLabelX, titleLabelY  - kHomeRatioSize(3), titleLabelW, titleLabelH);
    
    CGFloat scoreY = titleLabelY + titleLabelH + kHomeInfoTop_B;
    CGFloat avatarViewY = titleLabelY + titleLabelH + kHomeAvatarTopSpace_B;

    if (!self.subTitleLabel.hidden) {
        CGFloat subTitleLabelX = titleLabelX;
        CGFloat subTitleLabelY = titleLabelY + titleLabelH + kReasonTop_B;
        CGFloat subTitleLabelW = XMI_SCREEN_WIDTH - subTitleLabelX - kHomeTitle2Cover_B;
        CGFloat subTitleLabelH = kReasonHeight_B;
        self.subTitleLabel.frame = CGRectMake(subTitleLabelX, subTitleLabelY, subTitleLabelW, subTitleLabelH);
        
        scoreY = subTitleLabelY + subTitleLabelH + kHomeInfoTop_B;
        avatarViewY = subTitleLabelY + subTitleLabelH + kHomeAvatarTopSpace_B;
    }
    
    if (!self.scoreImageView.hidden) {
        CGFloat subTitleLabelY = titleLabelY + titleLabelH + kReasonTop_B;
        CGFloat subTitleLabelH = kReasonHeight_B;
        CGFloat scoreImageViewW = kHomeInfoIconSize_B;
        self.scoreImageView.xmi_size = CGSizeMake(scoreImageViewW, scoreImageViewW);
        self.scoreImageView.xmi_left = titleLabelX;
        self.scoreImageView.xmi_centerY = subTitleLabelY + subTitleLabelH * 0.5f;
        [self.scoreLabel sizeToFit];
        self.scoreLabel.xmi_left = self.scoreImageView.xmi_right + kHomeRatioSize(3.0f);
        self.scoreLabel.xmi_centerY = self.scoreImageView.xmi_centerY;
        self.scoreDivideLine.xmi_size = CGSizeMake(1, kHomeRatioSize(8));
        self.scoreDivideLine.xmi_left = self.scoreLabel.xmi_right + kHomeRatioSize(8);
        self.scoreDivideLine.xmi_centerY = self.scoreLabel.xmi_centerY;
        if (!self.subTitleLabel.hidden) {
            CGFloat subTitleLabelX = self.scoreDivideLine.xmi_right + kHomeRatioSize(8);
            CGFloat subTitleLabelW = XMI_SCREEN_WIDTH - subTitleLabelX - kHomeTitle2Cover_B;
            self.subTitleLabel.xmi_left = subTitleLabelX;
            self.subTitleLabel.xmi_width = subTitleLabelW;
        }
    }
    if (!self.subTitleLabel2.hidden) {
        CGFloat subTitleLabelX = titleLabelX;
        CGFloat subTitleLabelY = self.subTitleLabel.hidden ? (titleLabelY + titleLabelH + kReasonTop_B) : avatarViewY;
        CGFloat subTitleLabelW = XMI_SCREEN_WIDTH - subTitleLabelX - kHomeTitle2Cover_B;
        CGFloat subTitleLabelH = kReasonHeight_B;
        self.subTitleLabel2.frame = CGRectMake(subTitleLabelX, subTitleLabelY, subTitleLabelW, subTitleLabelH);
        avatarViewY = subTitleLabelY + subTitleLabelH + kHomeAvatarTopSpace_B;
    }
    
    CGFloat avatarViewX = titleLabelX;
    CGFloat avatarViewW = kHomeAvatarSize;
    CGFloat avatarViewH = kHomeAvatarSize;
    avatarViewY = MAX(avatarViewY, self.xmi_height - kHomeAvatarBottomSpace_B - avatarViewH);
    self.avatarView.frame = CGRectMake(avatarViewX, avatarViewY, avatarViewW, avatarViewH);
    
    CGFloat closeButtonW = kDislikeContainerW;
    CGFloat closeButtonH = kDislikeContainerH;
    CGFloat closeButtonX = self.xmi_width - closeButtonW;
    CGFloat closeButtonY = avatarViewY + (avatarViewH - closeButtonH) / 2;
    self.closeButton.frame = CGRectMake(closeButtonX, closeButtonY, closeButtonW, closeButtonH);
    self.closeImageView.size = CGSizeMake(kHomeRatioSize(8), kHomeRatioSize(8));
    self.closeImageView.center = CGPointMake(closeButtonW * 0.5f, closeButtonH * 0.5f);
   
    [self.nameLabel sizeToFit];
    CGFloat nameLabelX = avatarViewX + avatarViewW + 4;
    CGFloat nameLabelY = avatarViewY;
    CGFloat nameLabelW = MIN(self.nameLabel.xmi_width + 20, closeButtonX - nameLabelX - 10);
    CGFloat nameLabelH = avatarViewH;
    self.nameLabel.frame = CGRectMake(nameLabelX, nameLabelY, nameLabelW, nameLabelH);
    
    CGFloat userButtonX = avatarViewX;
    CGFloat userButtonY = avatarViewY - 2;
    CGFloat userButtonW = nameLabelX + nameLabelW - userButtonX;
    CGFloat userButtonH = avatarViewH + 4;
    self.userButton.frame = CGRectMake(userButtonX, userButtonY, userButtonW, userButtonH);
    
    CGFloat adMarkW = kHomeRatioSize(28);
    CGFloat adMarkH = kHomeRatioSize(16);
    
    self.adMark.xmi_size = CGSizeMake(adMarkW, adMarkH);
    self.adMark.xmi_right = self.closeButton.xmi_centerX - kHomeRatioSize(15);
    self.adMark.xmi_centerY = self.closeButton.xmi_centerY;
    
    CGFloat lineX = titleLabelX;
    CGFloat lineH = kOnePixelsLineHeight;
    CGFloat lineY = self.xmi_height - lineH;
    CGFloat lineW = XMI_SCREEN_WIDTH - lineX;
    self.divideLine.frame = CGRectMake(lineX, lineY, lineW, lineH);
}

- (NSArray<UIView *> *)clickableViews
{
    return @[self.contentView];
}

- (void)updateCustomUI
{
    [super updateCustomUI];
    
    self.nameLabel.textColor = XMI_COLOR_DynamicFromRGB(0x666666, 0x8D8D91);
    self.nameLabel.font = XMI_AD_PingFangFont(kHomeRatioSize(12));
    self.subTitleLabel.font = kSingleIntroFont_B;
    self.subTitleLabel.textColor = kSingleIntroColor_B;
    self.subTitleLabel2.font = kSingleIntroFont_B;
    self.subTitleLabel2.textColor = kSingleIntroColor_B;
    self.scoreDivideLine.backgroundColor = kSingleIntroColor_B;
    self.scoreLabel.font = kHomeInfoFont_B;
    self.scoreLabel.textColor = kHomeInfoColor_B;
    self.closeButton.imageView.tintColor = kSingleCloseColor;
    
    CGFloat coverViewY = kCoverTop_B;
    CGFloat coverViewW = kCoverSize;
    CGFloat coverViewH = kCoverSize;
    CGFloat coverViewX = kHomeSingleStyleMargins_B;
    self.coverView.frame = CGRectMake(coverViewX, coverViewY, coverViewW, coverViewH);
}

#pragma mark - UI
+ (YYTextLayout *)getTitleTextLayout:(NSString *)title
{
    NSMutableAttributedString *str = [[NSMutableAttributedString alloc] initWithString:title?:@""];
    str.yy_font = kSingleTitleFont_B;
    str.yy_color = kSingleTitleColor_B;
    
    CGFloat maxWidth = XMI_SCREEN_WIDTH - kHomeSingleTitleX_B - kTitleRightMargin_B;
    YYTextContainer *titleContainer      = [YYTextContainer containerWithSize:(CGSizeMake(maxWidth, 999))];
    titleContainer.maximumNumberOfRows   = 2;
    [titleContainer setTruncationType:(YYTextTruncationTypeEnd)];
    YYTextLayout *layout = [YYTextLayout layoutWithContainer:titleContainer text:str];
    return layout;
}

- (void)p_setCloseButtonBackImage:(XMIAdRelatedData *)adData
{
    UIImage *normalImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
    UIImage *darkImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close_dark"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
    [self.closeButton setImage:[UIImage xm_imageWithLight:normalImage dark:darkImage] forState:UIControlStateNormal];
    self.closeButton.imageView.tintColor = kSingleCloseColor;
    self.closeButton.hitTestEdgeOutsets = [adData closeAreaPaddingWithDefaultPadding:UIEdgeInsetsMake(7, 7, 7, 7)];
}

- (void)p_setupCoverImageWithData:(XMIAdRelatedData *)relatedData {
    id trackInfo = relatedData.trackInfoMapModel;
    if ([trackInfo isKindOfClass:[XMIExpressAdTrackInfoMapModel class]]) {
        XMIExpressAdTrackInfoMapModel *trackInfoModel = (XMIExpressAdTrackInfoMapModel *)trackInfo;
        UIImage *normalImage = [XMICommonUtils imageNamed:@"bkg_mix_feed"];
        UIImage *darkImage = [XMICommonUtils imageNamed:@"bkg_mix_feed_dark"];
        UIImage *placeholderImage = [UIImage xm_imageWithLight:normalImage dark:darkImage];
        
        NSURL *imgUrl = [[NSURL URLWithString:trackInfoModel.iconUrl] xm_webpURLWithSize:CGSizeMake(kCoverSize * UIScreen.mainScreen.scale, kCoverSize * UIScreen.mainScreen.scale)];
        [self.coverView sd_setImageWithURL:imgUrl placeholderImage:placeholderImage completed:nil];
    }
}

- (void)traitCollectionDidChange:(UITraitCollection *)previousTraitCollection
{
    [super traitCollectionDidChange:previousTraitCollection];
    self.titleLabel.textLayout = self.titleLabel.textLayout;
    
    [self p_setCloseButtonBackImage:self.adData];
    [self p_setupCoverImageWithData:self.adData];
}


@end
