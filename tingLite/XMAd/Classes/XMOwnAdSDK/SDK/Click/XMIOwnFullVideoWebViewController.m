//
//  XMIOwnFullVideoWebViewController.m
//  XMAd
//
//  Created by xmly on 2022/11/16.
//

#import "XMIOwnFullVideoWebViewController.h"
#import "XMIAdDataCenter.h"
#import <Masonry/Masonry.h>
#import "XMIAnimatedImageView.h"
#import "XMIAdLabel.h"
#import "XMIAdButton.h"
#import "XMICommonUtils.h"
#import "XMIAdMacro.h"
#import "UIView+XMIUtils.h"
#import "XMIAdRelatedData.h"
#import "XMIOwnAdHelper.h"
#import <XMCategories/NSObject+XMCommon.h>
#import <XMWebImage/UIImageView+WebCache.h>
#import <XMCategories/XMMacro.h>
#import "XMIAdVideoPlayer.h"
#import "XMIExpressAdTrackInfoMapModel.h"
#import "XMIAdManager.h"
#import "XMIWebVCProtocol.h"
#import "XMIAdReporter+AD.h"
#import "XMIADVideoProgressBar.h"
#import "XMISlider.h"
@interface XMIOwnFullVideoWebViewController ()<XMIAdVideoPlayerDelegate>
@property (nonatomic, strong) UIView *bottomView;
@property (nonatomic, strong) XMIAdLabel *jumpLabel;
@property (nonatomic, strong) XMIAnimatedImageView *imageView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *subTitleLabel;

@property (nonatomic, strong) UIView *webContainerView;
@property (nonatomic, strong) UILabel *webTitle;

@property (nonatomic, strong) UIButton *playButton;

@property (nonatomic, assign) BOOL hasGetVideoSize;
@end

@implementation XMIOwnFullVideoWebViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = UIColor.blackColor;
    [self addNavTitleViews];
    [self addBottomJumpView];
    
    [self buildWebView];
    
    [self setupUIWithData];
    
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(xmplayerPlay:)]) {
        [manager.delegate xmplayerPlay:NO];
    }
    
    //增加监听，当键盘出现或改变时收出消息
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillChangeFrame:) name:UIKeyboardWillChangeFrameNotification object:nil];
    
    // XMWebService -> XMHAdInfoCapability.m 发送通知关闭webview，基于jssdk
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(closeButtonAction:) name:@"AdFullVideoWebViewHide" object:nil];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:YES animated:animated];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [self.navigationController setNavigationBarHidden:NO animated:animated];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    BOOL beingDismissed = self.navigationController.beingDismissed;
    
    if (beingDismissed) {
        [self cancelDelayOperation];
        [self.adPlayer stop];
        XMIAdManager *manager = [XMIAdManager sharedInstance];
        if (manager.delegate && [manager.delegate respondsToSelector:@selector(xmplayerPlay:)]) {
            [manager.delegate xmplayerPlay:YES];
        }
    }
}

- (void)setupPlayerFrame {
    [self.videoView mas_makeConstraints:^(MASConstraintMaker *make) {
        if (@available(iOS 11.0, *)) {
            make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop).mas_offset(44);
        } else {
            make.top.equalTo(self.view);
        }
        make.left.right.bottom.equalTo(self.view);
    }];
    self.adPlayer.repeat = YES;
    self.adPlayer.volume = 1;
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(videoViewClick)];
    [self.videoView addGestureRecognizer:tap];
}

- (void)buildControl {
    UIButton *playButton = [[UIButton alloc] initWithFrame:CGRectZero];
    playButton.userInteractionEnabled = NO;
    _playButton = playButton;
    playButton.hidden = YES;
    [self.videoView addSubview:playButton];
    CGFloat playWidth = 60;
    [playButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.videoView);
        make.width.height.mas_equalTo(playWidth);
    }];
    [playButton setImage:[XMICommonUtils imageNamed:@"icon_video_play"] forState:UIControlStateNormal];
}

- (void)addNavTitleViews {
    UIView *superView = self.view;
    
    UILabel *navLabel = [UILabel new];
    navLabel.text = @"广告";
    navLabel.font = XMI_AD_PingFangMediumFont(17);
    navLabel.textColor = XMI_COLOR_RGBA(0xffffff, 1);
    [superView addSubview:navLabel];
    [navLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(superView);
        make.centerY.equalTo(self.backBtn);
    }];
}

- (void)addBottomJumpView {
    UIView *superView = self.view;
    
    CGFloat viewLeftMargin = 16;
    CGFloat viewBottomMargin = 40;
    CGFloat viewHeight = 84;
    UIView *bottomView = [UIView new];
    _bottomView = bottomView;
    bottomView.layer.cornerRadius = 8;
    bottomView.layer.masksToBounds = YES;
    [superView addSubview:bottomView];
    bottomView.backgroundColor = XMI_COLOR_RGBA(0xffffff, 0.95);;
    [bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(superView.mas_left).mas_offset(viewLeftMargin);
        make.right.equalTo(superView.mas_right).mas_offset(-viewLeftMargin);
        make.bottom.equalTo(superView.mas_bottom).mas_offset(-viewBottomMargin);
        make.height.mas_equalTo(viewHeight);
    }];
    
    
    superView = bottomView;
    
    CGFloat imageLeft = 12;
    XMIAnimatedImageView *imageView = [[XMIAnimatedImageView alloc] init];
    _imageView = imageView;
    imageView.layer.cornerRadius = 4;
    imageView.layer.masksToBounds = YES;
    [superView addSubview:imageView];
    [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(superView).mas_offset(imageLeft);
        make.top.equalTo(superView.mas_top).mas_offset(imageLeft);
        make.bottom.equalTo(superView.mas_bottom).mas_offset(-imageLeft);
        make.width.mas_equalTo(imageView.mas_height);
    }];
    
    XMIAdLabel *jumpLabel = [XMIAdLabel new];
    _jumpLabel = jumpLabel;
    jumpLabel.font = XMI_AD_PingFangMediumFont(14);
    jumpLabel.textColor = XMI_COLOR_RGBA(0xffffff, 1);
    jumpLabel.textAlignment = NSTextAlignmentCenter;
    jumpLabel.backgroundColor = XMI_COLOR_RGBA(0xff4444, 1);
    jumpLabel.textEdgeInsets = UIEdgeInsetsMake(8, 12, 8, 12);
    [superView addSubview:jumpLabel];
    CGFloat jumpLabelRight = 12;
    [jumpLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(superView.mas_right).mas_offset(-jumpLabelRight);
        make.centerY.equalTo(superView);
    }];
    
    
    CGFloat titleLeft = 8;
    UILabel *titleLabel = [UILabel new];
    _titleLabel = titleLabel;
    titleLabel.numberOfLines = 2;
    titleLabel.font = XMI_AD_PingFangMediumFont(15);
    titleLabel.textColor = XMI_COLOR_RGBA(0x333333, 1);
    [superView addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(imageView.mas_top);
        make.left.equalTo(imageView.mas_right).mas_offset(titleLeft);
        make.right.equalTo(jumpLabel.mas_left).mas_offset(-titleLeft);
    }];
    
    CGFloat titleBottom = 7;
    UILabel *subTitleLabel = [UILabel new];
    _subTitleLabel = subTitleLabel;
    subTitleLabel.numberOfLines = 1;
    subTitleLabel.font = XMI_AD_PingFangFont(13);
    subTitleLabel.textColor = XMI_COLOR_RGBA(0x666666, 1);
    [superView addSubview:subTitleLabel];
    [subTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(titleLabel.mas_bottom).mas_offset(titleBottom);
        make.left.right.equalTo(titleLabel);
    }];
    
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(bottomViewClick)];
    [bottomView addGestureRecognizer:tap];
    

    NSTimeInterval autoOpenTime = [XMIAdDataCenter fullVideoWebPageAutoOpenTime];
    if (autoOpenTime > 0) {
        if (autoOpenTime <= 0.15) autoOpenTime = 0.15;
        XMWeakObject(self);
        [self delayWithTimeInterval:autoOpenTime andOperation:^{
            [weak_self bottomViewClick];
        }];
    }
}

- (void)buildWebView {
    NSURL *url = [NSURL URLWithString:self.adData.realLink];
    UIViewController<XMIWebVCProtocol> *webVC = [XMIOwnAdHelper adWebControllerWithURL:url];
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wundeclared-selector"
    if ([webVC respondsToSelector:@selector(setHeightOffset:)]) {
        [webVC performSelector:@selector(setHeightOffset:) withObject:@(0)];
    }
    if ([webVC respondsToSelector:@selector(setHiddenTabbar:)]) {
        [webVC performSelector:@selector(setHiddenTabbar:) withObject:@(1)];
    }
#pragma clang diagnostic pop
    webVC.hiddenNavigationBar = YES;
    UIView *webContainerView = [UIView new];
    webContainerView.layer.cornerRadius = 8;
    webContainerView.layer.masksToBounds = YES;
    self.webContainerView = webContainerView;
    [self.view addSubview:webContainerView];
    CGFloat webHeight = 450;
    [webContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.bottom.equalTo(self.view.mas_bottom).mas_offset(webHeight);
        make.height.mas_equalTo(webHeight);
    }];
    
    [self addChildViewController:webVC];
    
    UIView *adWebView = webVC.view;
    [webContainerView addSubview:adWebView];
    [adWebView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(webContainerView);
    }];
    
    
    XMIAdButton *closeButton = [[XMIAdButton alloc] initWithFrame:CGRectZero];
    closeButton.hitTestEdgeOutsets = [self.adData closeAreaPaddingWithDefaultPadding:UIEdgeInsetsMake(7, 7, 7, 7)];;
    UIImage *normalImage = [[XMICommonUtils imageNamed:@"icon_video_web_close"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    [closeButton setImage:normalImage forState:UIControlStateNormal];
    [closeButton addTarget:self action:@selector(closeButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    [webContainerView addSubview:closeButton];
    
    CGFloat closeTop = 25;
    CGFloat closeRight = 17;
    CGFloat closeWidth = 16;
    [closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(webContainerView.mas_top).mas_offset(closeTop);
        make.right.equalTo(webContainerView.mas_right).mas_offset(-closeRight);
        make.width.height.mas_equalTo(closeWidth);
    }];
    
    __block long long startTime = [XMICommonUtils currentTimestamp];
    @weakify(self)
    [webVC setFinishLoadingHandler:^(BOOL success, NSError * _Nullable error) {
        @strongify(self)
        XMILog(@"finishLoad %@, %@", self.adData.realLink, error);
        if (!self) {
            return;
        }
        [XMIAdReporter webFinishLoading:success withAdData:self.adData startTimestamp:startTime];
    }];
}

- (void)setupUIWithData {
    NSString *coverUrl = self.adData.cover;
    NSString *name = self.adData.name;
    NSString *adDescription = self.adData.adDescription;
    NSString *clickTile = self.adData.clickTitle.length ? self.adData.clickTitle : self.adData.buttonText;
    if (!clickTile.length) clickTile = @"了解详情";
    
    
    id trackInfo = self.adData.trackInfoMapModel;
    if ([trackInfo isKindOfClass:[XMIExpressAdTrackInfoMapModel class]]) {
        XMIExpressAdTrackInfoMapModel *trackInfoModel = (XMIExpressAdTrackInfoMapModel *)trackInfo;
        coverUrl = trackInfoModel.iconUrl.length ? trackInfoModel.iconUrl : coverUrl;
        name = trackInfoModel.title.length ? trackInfoModel.title : name;
        adDescription = trackInfoModel.subtitle.length ? trackInfoModel.subtitle : adDescription;
    }
    
    [self.imageView sd_setImageWithURL:[NSURL URLWithString:coverUrl] placeholderImage:nil completed:nil];
    self.titleLabel.text = name;
    self.subTitleLabel.text = adDescription;
    self.jumpLabel.text = clickTile;
    self.webTitle.text = self.adData.name;
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    [self.jumpLabel.superview setNeedsLayout];
    [self.jumpLabel.superview layoutIfNeeded];
    self.jumpLabel.layer.cornerRadius = self.jumpLabel.xmi_height * 0.5;
    self.jumpLabel.layer.masksToBounds = YES;
}

- (void)closeButtonAction:(XMIAdButton *)closeButton {
    [self.view endEditing:YES];
    [UIView animateWithDuration:0.3 animations:^{
        [self.webContainerView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.view.mas_bottom).mas_offset(self.webContainerView.xmi_height);
        }];
        [self.webContainerView.superview layoutIfNeeded];
    } completion:^(BOOL finished) {
        
    }];
}

- (void)bottomViewClick {
    [self cancelDelayOperation];
    [UIView animateWithDuration:0.3 animations:^{
        [self showPopReminderView];
        [self.webContainerView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.view.mas_bottom);
        }];
        [self.webContainerView.superview layoutIfNeeded];
    } completion:^(BOOL finished) {
        
    }];
}

- (void)videoViewClick {
    if (!self.adPlayer.isPlaying) {
        [self.adPlayer play];
        self.playButton.hidden = YES;
    } else {
        [self.adPlayer pause];
        self.playButton.hidden = NO;
    }
}

- (void)keyboardWillChangeFrame:(NSNotification*)notification{
    CGRect keyboardFrame = [notification.userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    CGFloat keyboardHeight = UIScreen.mainScreen.bounds.size.height - keyboardFrame.origin.y;
    [UIView animateWithDuration:0.3 animations:^{
        [self.webContainerView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.view.mas_bottom).mas_offset(-keyboardHeight);
        }];
        [self.webContainerView.superview layoutIfNeeded];
    } completion:^(BOOL finished) {
        
    }];
}

#pragma mark - XMIAdVideoPlayerDelegate
- (void)player:(XMIAdNewVideoPlayer *)player playStateDidChanged:(XMIAdPlayerPlayState)state {
    if (state == XMIAdPlayerStatePlaying || state == XMIAdPlayerStateReady) {
        CGSize playerSize = [player getMediaSize];
        if (!self.hasGetVideoSize && !CGSizeEqualToSize(CGSizeZero, playerSize)) {
            if (playerSize.width > playerSize.height) {
                [self.videoView mas_remakeConstraints:^(MASConstraintMaker *make) {
                    if (@available(iOS 11.0, *)) {
                        make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop).mas_offset(44);
                    } else {
                        make.top.equalTo(self.view);
                    }
                    make.left.right.equalTo(self.view);
                    make.bottom.equalTo(self.bottomView.mas_top);
                }];
                [self.videoView.superview layoutIfNeeded];
            }
            self.hasGetVideoSize = YES;
        }
    }
}

@end
