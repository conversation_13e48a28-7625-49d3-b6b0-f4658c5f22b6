//
//  XMIOwnVideoStoreViewController.h
//  XMAd
//  视频+商店半拼接
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/23.
//

#import "XMIOwnVideoViewController.h"

NS_ASSUME_NONNULL_BEGIN

@protocol XMIOwnVideoStoreViewControllerDelegate;

@interface XMIOwnVideoStoreViewController : XMIOwnVideoViewController

@property (nonatomic, weak) id<XMIOwnVideoStoreViewControllerDelegate> storeDelegate;
@property (nonatomic, copy) NSString *productId;

@end

@protocol XMIOwnVideoStoreViewControllerDelegate <NSObject>

@optional
- (void)ownVideoStore:(XMIOwnVideoStoreViewController *)controller loadProductFailWithError:(NSError *)error;

@end

NS_ASSUME_NONNULL_END
