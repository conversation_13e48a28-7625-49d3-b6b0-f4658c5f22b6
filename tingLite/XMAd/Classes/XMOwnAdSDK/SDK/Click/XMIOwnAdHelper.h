//
//  XMIOwnAdHelper.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/25.
//

#import <Foundation/Foundation.h>
#import "XMIOwnStoreViewController.h"
#import "XMIOwnVideoStoreViewController.h"
#import "XMIOwnVideoWebViewController.h"
#import "XMIOwnFullVideoWebViewController.h"

NS_ASSUME_NONNULL_BEGIN

@class XMIAdRelatedData;
@protocol XMIWebVCProtocol;

@interface XMIOwnAdHelper : NSObject

/**
 app内部应用商店
 */
+ (void)storeControllerAsyncWithAd:(XMIAdRelatedData *)adData completion:(void(^)(SKStoreProductViewController *vc))completion;

+ (UIViewController *)storeControllerWithAd:(XMIAdRelatedData *)adData andDelegate:(id<XMIOwnStoreViewControllerDelegate>)delegate;
/**
 视频半拼接应用商店
 */
+ (UIViewController *)videoStoreControllerWithAd:(XMIAdRelatedData *)adData andDelegate:(id<XMIOwnVideoViewControllerDelegate, XMIOwnVideoStoreViewControllerDelegate>)delegate;
/**
 视频半拼接web
 */
+ (UIViewController *)videoWebControllerWithAd:(XMIAdRelatedData *)adData andDelegate:(id<XMIOwnVideoViewControllerDelegate>)delegate;

/**
 全屏视频半拼接web，线索表单
 */
+ (UIViewController *)fullVideoWebControllerWithAd:(XMIAdRelatedData *)adData andDelegate:(id<XMIOwnVideoViewControllerDelegate>)delegate;
/**
 创建web容器
 外部实现了代理用外部的，没实现用默认内置的
 */
+ (UIViewController<XMIWebVCProtocol> *)adWebControllerWithURL:(NSURL *)url;

/**
 全屏视频半拼接web-B，沉浸式视频落地页
 */
+ (UIViewController *)fullVideoWebControllerBWithAd:(XMIAdRelatedData *)adData andDelegate:(id<XMIOwnVideoViewControllerDelegate>)delegate;
@end

NS_ASSUME_NONNULL_END
