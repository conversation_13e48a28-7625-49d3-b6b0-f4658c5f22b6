//
//  XMIOwnVideoViewController.h
//  XMAd
//  视频半拼接落地页
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/25.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@protocol XMIOwnVideoViewControllerDelegate;
@class XMIAdRelatedData, XMIAdNewVideoPlayer;

@interface XMIOwnVideoViewController : UIViewController

@property (nonatomic, weak) id<XMIOwnVideoViewControllerDelegate> delegate;
@property (nonatomic, strong) XMIAdRelatedData *adData;

@property (nonatomic, strong, readonly) UIView *videoView;
@property (nonatomic, strong, readonly) UIButton *backBtn;
@property (nonatomic, strong, readonly) XMIAdNewVideoPlayer *adPlayer;
// 如果需要重设播放器的frame，重写该方法
- (void)setupPlayerFrame;
/**
 viewController关闭
 */
- (void)didClosed;
// 展示播音合规类弹窗
- (void)showPopReminderView;

@end

@protocol XMIOwnVideoViewControllerDelegate <NSObject>

@optional
- (void)ownVideoViewWillAppear:(XMIOwnVideoViewController *)controller;
- (void)ownVideoViewDidAppear:(XMIOwnVideoViewController *)controller;
- (void)ownVideoViewWillDisappear:(XMIOwnVideoViewController *)controller;
- (void)ownVideoViewDidDisappear:(XMIOwnVideoViewController *)controller;
- (void)ownVideoDidClosed:(XMIOwnVideoViewController *)controller;

@end

NS_ASSUME_NONNULL_END
