//
//  XMIOwnStoreViewController.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/23.
//

#import <StoreKit/StoreKit.h>

NS_ASSUME_NONNULL_BEGIN

@class XMIAdRelatedData;
@protocol XMIOwnStoreViewControllerDelegate;

@interface XMIOwnStoreViewController : SKStoreProductViewController

@property (nonatomic, weak) id<XMIOwnStoreViewControllerDelegate> storeDelegate;
@property (nonatomic, copy) NSString *productId;
@property (nonatomic, strong) XMIAdRelatedData *adData;

@end

@protocol XMIOwnStoreViewControllerDelegate <NSObject>

@optional
- (void)ownStore:(XMIOwnStoreViewController *)controller loadProductFailWithError:(NSError *)error;
- (void)ownStoreViewWillAppear:(XMIOwnStoreViewController *)controller;
- (void)ownStoreViewDidAppear:(XMIOwnStoreViewController *)controller;
- (void)ownStoreViewWillDisappear:(XMIOwnStoreViewController *)controller;
- (void)ownStoreViewDidDisappear:(XMIOwnStoreViewController *)controller;
- (void)ownStoreDidClosed:(XMIOwnStoreViewController *)controller;

@end

NS_ASSUME_NONNULL_END
