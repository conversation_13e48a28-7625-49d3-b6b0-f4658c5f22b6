//
//  XMIOwnJumpManager.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/19.
//

#import <Foundation/Foundation.h>
#import "XMIJumpManagerProtocol.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, XMIAdJumpType) {
    XMIAdJumpTypeNone = 0,      // 不跳转
    XMIAdJumpTypeWeb = 1,       // 应用内webview
    XMIAdJumpTypeBrowser = 2,   // 系统浏览器打开
    XMIAdJumpTypeCall = 3,      // 打电话
    XMIAdJumpTypeDeeplink = 4,  // Deeplink，跳转应用内页面
    XMIAdJumpTypeWeixin = 5,    // 微信小程序
    XMIAdJumpTypeStore = 6,     // 应用内app商店 SKStoreProductViewController
    XMIAdJumpTypeVideoStore = 7,// 带视频拼接半屏应用内商店
    XMIAdJumpTypeVideoWeb = 8,  // 视频拼接半屏H5
    XMIAdJumpTypeGame = 9,      // 游戏中心
    XMIAdJumpTypeMall = 10,     // 商城
    XMIAdJumpTypeVideoFullWeb = 11, // 带视频全屏，linktype=102
    XMIAdJumpTypeCustom = 12    // 跳转自定义页面，自己实现跳转逻辑 linktype=105
};

@class XMIAdRelatedData;
@class XMIOwnJumpManager;

@protocol XMIOwnJumpManagerDelegate < XMIJumpManagerDelegate>

@end

@interface XMIOwnJumpManager : NSObject<XMIJumpManagerProtocol>

@property (nonatomic, weak) id<XMIOwnJumpManagerDelegate> delegate;
@property (nonatomic, weak) UIViewController *rootViewController;

@end

NS_ASSUME_NONNULL_END
