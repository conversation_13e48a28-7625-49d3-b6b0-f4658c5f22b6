//
//  XMIOwnAdHelper.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/25.
//

#import "XMIOwnAdHelper.h"
#import "XMIAdRelatedData.h"
#import "XMIWebVCProtocol.h"
#import "XMIAdManager.h"
#import "XMIAdWebViewController.h"
#import "XMIOwnFullVideoWebViewControllerB.h"
#import "XMIOwnFullVideoStoreViewController.h"
@implementation XMIOwnAdHelper

+ (void)storeControllerAsyncWithAd:(XMIAdRelatedData *)adData completion:(void(^)(SKStoreProductViewController *vc))completion {
    NSString *productId = [self productIdFromUrl:adData.realLink];
    if (productId == nil) {
        if (completion) {
            completion(nil);
        }
        return;
    }
    SKStoreProductViewController * storeVC = [[SKStoreProductViewController alloc] init];
    NSDictionary *dic = [NSDictionary dictionaryWithObject:[NSNumber numberWithLong:[productId integerValue]] forKey:SKStoreProductParameterITunesItemIdentifier];

    [storeVC loadProductWithParameters:dic completionBlock:^(BOOL result, NSError * _Nullable error) {
        if (completion) {
            if (result) {
                completion(storeVC);
            } else {
                completion(nil);
            }
        }
    }];
}

+ (XMIOwnStoreViewController *)storeControllerWithAd:(XMIAdRelatedData *)adData andDelegate:(id<XMIOwnStoreViewControllerDelegate>)delegate {
    NSString *productId = [self productIdFromUrl:adData.realLink];
    if (productId == nil) {
        return nil;
    }
    
    XMIOwnStoreViewController * storeVC = [[XMIOwnStoreViewController alloc] init];
    storeVC.modalPresentationStyle = UIModalPresentationFullScreen;
    storeVC.productId = productId;
    storeVC.adData = adData;
    storeVC.storeDelegate = delegate;
    
    return storeVC;
}

+ (UIViewController *)videoStoreControllerWithAd:(XMIAdRelatedData *)adData andDelegate:(id<XMIOwnVideoViewControllerDelegate, XMIOwnVideoStoreViewControllerDelegate>)delegate {
    NSString *productId = [self productIdFromUrl:adData.realLink];
    if (productId == nil) {
        return nil;
    }
    
    UIViewController *vc;
    if (adData.linkType == 103) {
        XMIOwnFullVideoStoreViewController * storeVC = [[XMIOwnFullVideoStoreViewController alloc] init];
        storeVC.modalPresentationStyle = UIModalPresentationFullScreen;
        storeVC.productId = productId;
        storeVC.adData = adData;
        vc = storeVC;
    } else {
        XMIOwnVideoStoreViewController * storeVC = [[XMIOwnVideoStoreViewController alloc] init];
        storeVC.modalPresentationStyle = UIModalPresentationFullScreen;
        storeVC.productId = productId;
        storeVC.adData = adData;
        storeVC.delegate = delegate;
        storeVC.storeDelegate = delegate;
        vc = storeVC;
    }
    return vc;
}

+ (XMIOwnVideoWebViewController *)videoWebControllerWithAd:(XMIAdRelatedData *)adData andDelegate:(id<XMIOwnVideoViewControllerDelegate>)delegate {
    XMIOwnVideoWebViewController *webVC = [[XMIOwnVideoWebViewController alloc] init];
    webVC.modalPresentationStyle = UIModalPresentationFullScreen;
    webVC.adData = adData;
    webVC.delegate = delegate;
    
    return webVC;
}

+ (UIViewController *)fullVideoWebControllerWithAd:(XMIAdRelatedData *)adData andDelegate:(id<XMIOwnVideoViewControllerDelegate>)delegate {
    XMIOwnFullVideoWebViewController *webVC = [[XMIOwnFullVideoWebViewController alloc] init];
    webVC.modalPresentationStyle = UIModalPresentationFullScreen;
    webVC.adData = adData;
    webVC.delegate = delegate;
    
    return webVC;
}

+ (UIViewController *)fullVideoWebControllerBWithAd:(XMIAdRelatedData *)adData andDelegate:(id<XMIOwnVideoViewControllerDelegate>)delegate {
    XMIOwnFullVideoWebViewControllerB *webVC = [[XMIOwnFullVideoWebViewControllerB alloc] init];
    webVC.modalPresentationStyle = UIModalPresentationFullScreen;
    webVC.adData = adData;
    webVC.delegate = delegate;
    
    return webVC;
}

+ (UIViewController<XMIWebVCProtocol> *)adWebControllerWithURL:(NSURL *)url {
    UIViewController<XMIWebVCProtocol> *webVC;
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerGetCustomWebVCClass)]) {
        Class cls = [manager.delegate managerGetCustomWebVCClass];
        webVC = [((UIViewController<XMIWebVCProtocol> *)[cls alloc]) initWithURL:url];
    } else {
        webVC = [[XMIAdWebViewController alloc] initWithURL:url];
    }
    return webVC;
}

/**
 从url中获取appstore商品id
 */
+ (NSString *)productIdFromUrl:(NSString *)urlStr {
    NSString *productId = nil;
    NSRange productUrlRange = [urlStr rangeOfString:@"\\.apple\\.com.*/id[0-9]+" options:NSRegularExpressionSearch];
    if (productUrlRange.location != NSNotFound) {
        NSRange range = [urlStr rangeOfString:@"id[0-9]+" options:NSRegularExpressionSearch];
        if (range.location != NSNotFound) {
            productId = [urlStr substringWithRange:NSMakeRange(range.location + 2, range.length - 2)];
        }
    }
    return productId;
}

@end
