//
//  XMIOwnStoreViewController.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/23.
//

#import "XMIOwnStoreViewController.h"
#import "XMIAdMacro.h"
#import "XMIAdError.h"

@interface XMIOwnStoreViewController ()<SKStoreProductViewControllerDelegate>

@end

@implementation XMIOwnStoreViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    if (!self.productId) {
        if (self.storeDelegate && [self.storeDelegate respondsToSelector:@selector(ownStore:loadProductFailWithError:)]) {
            [self.storeDelegate ownStore:self loadProductFailWithError:[XMIAdError emptyProductIDError]];
        }
        // 未加载会无法关闭，自动关闭
        @weakify(self)
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, 1 * NSEC_PER_SEC), dispatch_get_main_queue(), ^{
            @strongify(self)
            if (!self) {
                return;
            }
            
            [self dismissViewControllerAnimated:YES completion:nil];
        });
        return;
    }
    
    self.delegate = self;
    NSDictionary *dic = [NSDictionary dictionaryWithObject:self.productId forKey:SKStoreProductParameterITunesItemIdentifier];
    [self loadProductWithParameters:dic completionBlock:^(BOOL result, NSError * _Nullable error) {
        if (!result) {
            XMILog(@"SKStoreProduct load fail, %@", error);
            if (self.storeDelegate && [self.storeDelegate respondsToSelector:@selector(ownStore:loadProductFailWithError:)]) {
                [self.storeDelegate ownStore:self loadProductFailWithError:error];
            }
        }
    }];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    if (self.storeDelegate && [self.storeDelegate respondsToSelector:@selector(ownStoreViewWillAppear:)]) {
        [self.storeDelegate ownStoreViewWillAppear:self];
    }
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    
    if (self.storeDelegate && [self.storeDelegate respondsToSelector:@selector(ownStoreViewDidAppear:)]) {
        [self.storeDelegate ownStoreViewDidAppear:self];
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    if (self.storeDelegate && [self.storeDelegate respondsToSelector:@selector(ownStoreViewWillDisappear:)]) {
        [self.storeDelegate ownStoreViewWillDisappear:self];
    }
    
    [super viewWillDisappear:animated];
}

- (void)viewDidDisappear:(BOOL)animated {
    if (self.storeDelegate && [self.storeDelegate respondsToSelector:@selector(ownStoreViewDidDisappear:)]) {
        [self.storeDelegate ownStoreViewDidDisappear:self];
    }
    
    [super viewDidDisappear:animated];
}

/**
 productstore关闭处理
 */
- (void)didClosed {
    if (self.storeDelegate && [self.storeDelegate respondsToSelector:@selector(ownStoreDidClosed:)]) {
        [self.storeDelegate ownStoreDidClosed:self];
    }
}

#pragma mark - SKStoreProductViewControllerDelegate
- (void)productViewControllerDidFinish:(SKStoreProductViewController *)viewController {
    [self dismissViewControllerAnimated:YES completion:^{
        [self didClosed];
    }];
}

@end
