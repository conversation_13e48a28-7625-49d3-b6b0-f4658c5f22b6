//
//  XMIOwnFullVideoBaseController.m
//  XMAd
//
//  Created by xmly on 2022/12/21.
//

#import "XMIOwnFullVideoBaseController.h"
#import "XMIAdDataCenter.h"
#import <Masonry/Masonry.h>
#import "XMIAnimatedImageView.h"
#import "XMIAdLabel.h"
#import "XMIAdButton.h"
#import "XMICommonUtils.h"
#import "XMIAdMacro.h"
#import "UIView+XMIUtils.h"
#import "XMIAdRelatedData.h"
#import "XMIOwnAdHelper.h"
#import <XMCategories/NSObject+XMCommon.h>
#import <XMWebImage/UIImageView+WebCache.h>
#import <XMCategories/XMMacro.h>
#import "XMIAdVideoPlayer.h"
#import "XMIAdManager.h"
#import "XMIWebVCProtocol.h"
#import "XMIAdReporter+AD.h"
#import "XMIADVideoProgressBar.h"
#import "XMISlider.h"
@interface XMIOwnFullVideoBaseController ()<XMIADVideoProgressBarDelegate>
@property (nonatomic, strong) XMIADVideoProgressBar *progressBar;


@property (nonatomic, strong) UIButton *playButton;

@property (nonatomic, assign) BOOL hasGetVideoSize;


@end

@implementation XMIOwnFullVideoBaseController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = UIColor.blackColor;
    [self addNavTitleViews];
    [self addProgressBarView];
    
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(xmplayerPlay:)]) {
        [manager.delegate xmplayerPlay:NO];
    }
    
    //增加监听，当键盘出现或改变时收出消息
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillChangeFrame:) name:UIKeyboardWillChangeFrameNotification object:nil];
    
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:YES animated:animated];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [self.navigationController setNavigationBarHidden:NO animated:animated];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    BOOL beingDismissed = self.navigationController.beingDismissed || self.beingDismissed;
    
    if (beingDismissed) {
        [self.adPlayer stop];
        XMIAdManager *manager = [XMIAdManager sharedInstance];
        if (manager.delegate && [manager.delegate respondsToSelector:@selector(xmplayerPlay:)]) {
            [manager.delegate xmplayerPlay:YES];
        }
    }
}

- (void)setupPlayerFrame {
    [self.videoView mas_makeConstraints:^(MASConstraintMaker *make) {
        if (@available(iOS 11.0, *)) {
            make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop).mas_offset(44);
        } else {
            make.top.equalTo(self.view);
        }
        make.left.right.bottom.equalTo(self.view);
    }];
    self.adPlayer.repeat = YES;
    self.adPlayer.volume = 1;
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(videoViewClick)];
    [self.videoView addGestureRecognizer:tap];
}

- (void)buildControl {
    UIButton *playButton = [[UIButton alloc] initWithFrame:CGRectZero];
    playButton.userInteractionEnabled = NO;
    _playButton = playButton;
    playButton.hidden = YES;
    [self.videoView addSubview:playButton];
    CGFloat playWidth = 60;
    [playButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.videoView);
        make.width.height.mas_equalTo(playWidth);
    }];
    [playButton setImage:[XMICommonUtils imageNamed:@"icon_video_play"] forState:UIControlStateNormal];
}

- (void)addNavTitleViews {
    UIView *superView = self.view;
    
    UIButton *voiceButton = [[UIButton alloc] initWithFrame:CGRectZero];
    [voiceButton addTarget:self action:@selector(voiceButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    [superView addSubview:voiceButton];
    CGFloat voiceWidth = 26;
    CGFloat voiceRight = 16;
    [voiceButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(superView.mas_right).mas_offset(-voiceRight);
        make.centerY.equalTo(self.backBtn);
        make.width.height.mas_equalTo(voiceWidth);
    }];
    [voiceButton setImage:[XMICommonUtils imageNamed:@"icon_voice_on"] forState:UIControlStateNormal];
    [voiceButton setImage:[XMICommonUtils imageNamed:@"icon_voice_off"] forState:UIControlStateSelected];
    
    
    CGFloat labelMarign = 16;
    UILabel *navLabel = [UILabel new];
    navLabel.text = self.adData.name;
    navLabel.font = XMI_AD_PingFangFont(15);
    navLabel.textColor = XMI_COLOR_RGBA(0xffffff, 1);
    navLabel.textAlignment = NSTextAlignmentCenter;
    [superView addSubview:navLabel];
    [navLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(superView);
        make.centerY.equalTo(self.backBtn);
        make.left.equalTo(self.backBtn.mas_right).mas_offset(labelMarign);
        make.right.equalTo(voiceButton.mas_left).mas_offset(-labelMarign);
    }];
}

- (void)addProgressBarView {
    UIButton *screenSwitchButton = [[UIButton alloc] initWithFrame:CGRectZero];
    self.screenSwitchButton = screenSwitchButton;
    [screenSwitchButton addTarget:self action:@selector(screenSwitchButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    [self.videoView addSubview:screenSwitchButton];
    CGFloat switchWidth = 24;
    CGFloat switchRight = 16;
    CGFloat switchBottom = 100;
    [screenSwitchButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(self.videoView.mas_right).mas_offset(-switchRight);
        make.bottom.equalTo(self.videoView.mas_bottom).mas_offset(-switchBottom);
        make.width.height.mas_equalTo(switchWidth);
    }];
    [screenSwitchButton setImage:[XMICommonUtils imageNamed:@"icon_video_screen_switch"] forState:UIControlStateNormal];
    
    [self.videoView addSubview:self.progressBar];
    CGFloat leftMargin = 12;
    CGFloat progressHeight = 15;
    [self.progressBar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.videoView.mas_left).mas_offset(leftMargin);
        make.right.equalTo(screenSwitchButton.mas_left).mas_offset(-leftMargin);
        make.height.mas_equalTo(progressHeight);
        make.centerY.equalTo(screenSwitchButton);
    }];
}


- (XMIADVideoProgressBar *)progressBar {
    if (!_progressBar) {
        _progressBar = [[XMIADVideoProgressBar alloc] init];
        _progressBar.delegate = self;
    }
    return _progressBar;
}

- (void)videoViewClick {
    if (!self.adPlayer.isPlaying) {
        [self.adPlayer play];
        self.playButton.hidden = YES;
    } else {
        [self.adPlayer pause];
        self.playButton.hidden = NO;
    }
}


- (void)voiceButtonClick:(UIButton *)sender {
    sender.selected = !sender.selected;
    self.adPlayer.volume = sender.selected ? 0 : 1;
}

- (void)screenSwitchButtonClick:(UIButton *)sender {
    
}

#pragma mark - XMIAdVideoPlayerDelegate
- (void)player:(XMIAdNewVideoPlayer *)player playStateDidChanged:(XMIAdPlayerPlayState)state {
    if (state == XMIAdPlayerStatePlaying || state == XMIAdPlayerStateReady) {
        self.progressBar.totalTime = player.duration;
    }
}

- (void)player:(XMIAdNewVideoPlayer *)player playTimeDidChanged:(CGFloat)currentTime {
    NSTimeInterval duration = player.duration;
    self.progressBar.currentTime = currentTime;
    self.progressBar.totalTime = duration;
    self.progressBar.progressSr.value = currentTime/duration;
}

#pragma mark - XMADVideoProgressBarDelegate
- (void)didBeginTouchSlider:(XMISlider *)slider
{
    if ([self.adPlayer isPlaying]) {
        [self.adPlayer pause];
    }
}

- (void)didEndTouchSlider:(XMISlider *)slider
{
    if (!self.adPlayer.isPlaying) {
        [self.adPlayer play];
    }
}

- (void)didMoveSlide:(XMISlider *)slider
{
    NSTimeInterval duration = self.adPlayer.duration;
    self.progressBar.currentTime = duration * slider.value;
    [self.adPlayer seek:self.progressBar.currentTime];
}

- (void)keyboardWillChangeFrame:(NSNotification*)notification{

}
@end
