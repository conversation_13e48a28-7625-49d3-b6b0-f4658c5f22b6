//
//  XMIOwnFullVideoWebViewControllerB.m
//  XMAd
//
//  Created by xmly on 2022/12/21.
//

#import "XMIOwnFullVideoWebViewControllerB.h"
#import "XMIAdRelatedData.h"
#import "XMIOwnAdHelper.h"
#import "XMIWebVCProtocol.h"
#import "XMIAdMacro.h"
#import "UIView+XMIUtils.h"
#import <XMCategories/NSObject+XMCommon.h>
#import <Masonry/Masonry.h>
#import <KVOController/KVOController.h>
#import "XMICommonUtils.h"
#import "XMIAdReporter+AD.h"
#import "XMIAdVideoPlayer.h"
@interface XMIOwnFullVideoWebViewControllerB ()
@property (nonatomic, strong) UIView *webContainerView;
@property (nonatomic, assign) BOOL showedWebView;
@end

@implementation XMIOwnFullVideoWebViewControllerB

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self buildWebView];
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    if (CGRectEqualToRect(CGRectZero, self.webContainerView.frame)) {
        CGFloat webHeight = self.view.xmi_height - (self.view.xmi_width * 9 / 16) - self.videoView.xmi_top;
        [self.webContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.view);
            make.bottom.equalTo(self.view.mas_bottom).mas_offset(webHeight);
            make.height.mas_equalTo(webHeight);
        }];
    }
}

- (void)buildWebView {
    NSURL *url = [NSURL URLWithString:self.adData.realLink];
    UIViewController<XMIWebVCProtocol> *webVC = [XMIOwnAdHelper adWebControllerWithURL:url];
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wundeclared-selector"
    if ([webVC respondsToSelector:@selector(setHeightOffset:)]) {
        [webVC performSelector:@selector(setHeightOffset:) withObject:@(0)];
    }
    if ([webVC respondsToSelector:@selector(setHiddenTabbar:)]) {
        [webVC performSelector:@selector(setHiddenTabbar:) withObject:@(1)];
    }
#pragma clang diagnostic pop
    webVC.hiddenNavigationBar = YES;
    UIView *webContainerView = [UIView new];
    self.webContainerView = webContainerView;
    [self.view addSubview:webContainerView];
    [self addChildViewController:webVC];
    
    
    UIView *webTitleView = [UIView new];
    webTitleView.backgroundColor = XMI_COLOR_RGBA(0xffffff, 1);
    [webContainerView addSubview:webTitleView];
    CGFloat webTitleHeight = 48;
    [webTitleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.equalTo(webContainerView);
        make.height.mas_equalTo(webTitleHeight);
    }];
    
    UILabel *webTitleLabel = [UILabel new];
    webTitleLabel.text = self.adData.name;
    webTitleLabel.font = XMI_AD_PingFangMediumFont(16);
    webTitleLabel.textColor = XMI_COLOR_RGBA(0x333333, 1);
    webTitleLabel.textAlignment = NSTextAlignmentCenter;
    [webTitleView addSubview:webTitleLabel];
    [webTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.centerY.equalTo(webTitleView);
    }];
    
    UIView *adWebView = webVC.view;
    [webContainerView addSubview:adWebView];
    [adWebView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(webContainerView);
        make.top.equalTo(webTitleView.mas_bottom);
    }];
    
    __block long long startTime = [XMICommonUtils currentTimestamp];
    @weakify(self)
    [webVC setFinishLoadingHandler:^(BOOL success, NSError * _Nullable error) {
        @strongify(self)
        XMILog(@"finishLoad %@, %@", self.adData.realLink, error);
        if (!self) {
            return;
        }
        [XMIAdReporter webFinishLoading:success withAdData:self.adData startTimestamp:startTime];
    }];
    
    
    [self.KVOController observe:webVC
                        keyPath:@"title"
                        options:(NSKeyValueObservingOptionNew)
                          block:^(id  _Nullable observer, id  _Nonnull object,
                                  NSDictionary<NSString *,id> * _Nonnull change) {
        @strongify(self);
        NSValue *value = change[NSKeyValueChangeNewKey];
        NSString *title = [value aktStringValue];
        webTitleLabel.text = title ?: self.adData.name;
        
    }];
}

- (void)screenSwitchButtonClick:(UIButton *)sender {
    if (self.videoView.xmi_height >= self.view.xmi_height * 0.5) {
        self.showedWebView = YES;
        [UIView animateWithDuration:0.3 animations:^{
            [self.videoView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(self.view).mas_offset(-self.webContainerView.xmi_height);
            }];
            [self.screenSwitchButton mas_updateConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(self.videoView.mas_bottom).mas_offset(-14);
            }];
            [self.webContainerView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(self.view.mas_bottom);
            }];
            [self.view layoutIfNeeded];
        }];
    } else {
        [UIView animateWithDuration:0.3 animations:^{
            [self.videoView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(self.view);
            }];
            [self.screenSwitchButton mas_updateConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(self.videoView.mas_bottom).mas_offset(-100);
            }];
            [self.webContainerView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(self.view.mas_bottom).mas_offset(self.webContainerView.xmi_height);
            }];
            [self.view layoutIfNeeded];
        }];
    }
}

#pragma mark - XMIAdVideoPlayerDelegate
- (void)player:(XMIAdNewVideoPlayer *)player playDidFinish:(NSError *)error {
    if (!self.showedWebView) {
        [self screenSwitchButtonClick:nil];
    }
}

- (void)keyboardWillChangeFrame:(NSNotification*)notification{
    CGRect keyboardFrame = [notification.userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    CGFloat keyboardHeight = UIScreen.mainScreen.bounds.size.height - keyboardFrame.origin.y;
    [UIView animateWithDuration:0.3 animations:^{
        [self.webContainerView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.view.mas_bottom).mas_offset(-keyboardHeight);
        }];
        [self.webContainerView.superview layoutIfNeeded];
    } completion:^(BOOL finished) {

    }];
}
@end
