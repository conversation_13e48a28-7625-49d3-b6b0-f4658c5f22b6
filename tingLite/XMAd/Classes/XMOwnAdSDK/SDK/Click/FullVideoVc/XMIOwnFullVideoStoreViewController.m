//
//  XMIOwnFullVideoStoreViewController.m
//  XMAd
//
//  Created by xmly on 2022/12/21.
//

#import "XMIOwnFullVideoStoreViewController.h"
#import "UIView+XMIUtils.h"
#import <Masonry/Masonry.h>
#import "XMIAdVideoPlayer.h"
#import <StoreKit/StoreKit.h>
@interface XMIOwnFullVideoStoreViewController ()<SKStoreProductViewControllerDelegate>
@property (nonatomic, assign) BOOL showedStoreView;
@property (nonatomic, assign) CGFloat storeHeight;

@property (nonatomic, strong) SKStoreProductViewController *storeVc;
@end

@implementation XMIOwnFullVideoStoreViewController


- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = UIColor.blackColor;
    
    [self buildStoreVc];
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    self.storeHeight = self.view.xmi_height - (self.view.xmi_width * 9 / 16) - self.videoView.xmi_top;
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    BOOL beingDismissed = self.navigationController.beingDismissed;
    
    if (beingDismissed) {
        self.storeVc = nil;
    }
}

- (void)buildStoreVc {
    SKStoreProductViewController *storeVc = [[SKStoreProductViewController alloc]init];
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    dic[SKStoreProductParameterITunesItemIdentifier] = self.productId;
    storeVc.delegate = self;
    [storeVc loadProductWithParameters:dic completionBlock:nil];
    self.storeVc = storeVc;
}

- (void)showStoreVc {
    if (!self.storeVc) {
        [self buildStoreVc];
    }
    UIViewController *storeVc = self.storeVc;
    [self presentViewController:storeVc animated:NO completion:^{
        storeVc.view.backgroundColor = UIColor.whiteColor;
        storeVc.view.superview.frame = CGRectMake(0, self.view.xmi_height - self.storeHeight, self.view.xmi_width, self.storeHeight);
        if (@available(iOS 13, *)) {
            UIViewController *subVC = storeVc.childViewControllers.firstObject;
            subVC.view.subviews.firstObject.frame = CGRectMake(0, -10, self.view.xmi_height, self.view.xmi_height-self.storeHeight + 10);
        }
    }];
}

- (void)screenSwitchButtonClick:(UIButton *)sender {
    if (self.videoView.xmi_height >= self.view.xmi_height * 0.5) {
        self.showedStoreView = YES;
        [UIView animateWithDuration:0.3 animations:^{
            [self.videoView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(self.view).mas_offset(-self.storeHeight);
            }];
            [self.screenSwitchButton mas_updateConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(self.videoView.mas_bottom).mas_offset(-14);
            }];
            
            [self.view layoutIfNeeded];
        } completion:^(BOOL finished) {
            [self showStoreVc];
        }];
    } else {
        [self dismissStoreControllerWithDismissContainerVc:NO];
    }
}

- (void)handleBackBtnClick:(id)sender {
    if (!self.storeVc) {
        [self dismissViewControllerAnimated:YES completion:^{
            [self didClosed];
        }];
    } else {
        [self dismissStoreControllerWithDismissContainerVc:YES];
    }
}

// 隐藏store页面时，是否也dismiss当前vc
- (void)dismissStoreControllerWithDismissContainerVc:(BOOL)dismissContainer {
    [self.storeVc dismissViewControllerAnimated:YES completion:^{
        [UIView animateWithDuration:0.3 animations:^{
            [self.videoView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(self.view);
            }];
            [self.screenSwitchButton mas_updateConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(self.videoView.mas_bottom).mas_offset(-100);
            }];
            [self.view layoutIfNeeded];
        } completion:^(BOOL finished) {
            self.storeVc = nil;
            if (dismissContainer) {
                [self dismissViewControllerAnimated:YES completion:^{
                    [self didClosed];
                }];
            }
        }];
    }];
}

#pragma mark - XMIAdVideoPlayerDelegate
- (void)player:(XMIAdNewVideoPlayer *)player playDidFinish:(NSError *)error {
    if (!self.showedStoreView) {
        [self screenSwitchButtonClick:nil];
    }
}

- (void)productViewControllerDidFinish:(SKStoreProductViewController *)viewController {
    [UIView animateWithDuration:0.3 animations:^{
        [self.videoView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.view);
        }];
        [self.screenSwitchButton mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.videoView.mas_bottom).mas_offset(-100);
        }];
        [self.view layoutIfNeeded];
    } completion:^(BOOL finished) {
        self.storeVc = nil;
    }];
}
@end
