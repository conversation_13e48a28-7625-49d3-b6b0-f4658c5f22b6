//
//  XMIOwnJumpManager.m
//  XMAd
//  跳转方式有以下这些情况
//  1.应用内浏览器打开 2.第三方浏览器打开 3.专辑详情页 4.声音页 5.视频页 6.主播页 7.专辑类听单
//  8.声音类听单 9.个人直播 10.喜马拉雅活动 11.跳转小程序 12.下载 13.游戏中心 14.游戏中心-锚点
//
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/19.
//

#import "XMIOwnJumpManager.h"
#import "XMIAdRelatedData.h"
#import "XMIAdManager.h"
#import "XMIWebVCProtocol.h"
#import "XMIAdMacro.h"
#import "XMICommonUtils.h"
#import "XMIOwnAdHelper.h"
#import "XMIAdReporter.h"
#import "XMIAdAlertView.h"
#import "XMIAdReporter+AD.h"
#import <XMCategories/NSObject+XMCommon.h>
#import "XMIAdABTest.h"
#import "XMIAdHelper.h"
@interface XMIOwnJumpManager ()<XMIOwnStoreViewControllerDelegate, XMIOwnVideoViewControllerDelegate, XMIOwnVideoStoreViewControllerDelegate, SKStoreProductViewControllerDelegate>

@end

@implementation XMIOwnJumpManager

/// 是否有对应的处理
- (BOOL)doJumpWithAd:(XMIAdRelatedData *)adData {
    if (adData == nil) {
        return YES;
    }
    id object = [self getObjectFromConfigCenterWithKey:@"jumpByAdadpiPositionIdMap"];
    if ([object isKindOfClass:[NSDictionary class]]) {
        NSDictionary *dict = (NSDictionary *)object;
        NSString *postionIdStr = [NSString stringWithFormat:@"%lld", adData.positionId];
        BOOL jumpByAdApi = [dict valueForKey:postionIdStr];
        if (jumpByAdApi) {
            return NO;
        }
    }
    
    [self settingCurrentAdData:adData];
    if ([adData.popReminderStyle isEqualToString:@"1"]) {// 播音合规弹窗，允许了才能跳转
        XMIAdJumpType jumpType = [self detectJumpType:adData];
        if (jumpType > XMIAdJumpTypeNone && jumpType <= XMIAdJumpTypeMall && jumpType !=  XMIAdJumpTypeWeixin) {
            //fix:微信也需要xmadapi跳转这边不出播音合规弹窗
            [[XMIAdAlertView alertWithType:XMIAdAlertViewTypeCertain contentWidth:275 contentInfo:adData.popReminderText cancelButtonTitle:@"返回" certainButtonTitle:@"确认" certainBlock:^{
                [self isHandleJumpWithAd:adData];
            }] show];
            return YES;
        } else {
            // xmad不支持的跳转通过xmadapi触发弹窗
            return NO;
        }
    } else {
        return [self isHandleJumpWithAd:adData];
    }
}

- (BOOL)isHandleJumpWithAd:(XMIAdRelatedData *)adData {
    BOOL isHandleJump = YES;
    XMIAdJumpType jumpType = [self detectJumpType:adData];
    switch (jumpType) {
        case XMIAdJumpTypeWeb:
            [self doJumpWeb:adData];
            break;
        case XMIAdJumpTypeBrowser:
            [self doJumpBrowser:adData];
            break;
        case XMIAdJumpTypeCall:
            [self doJumpPhoneCall:adData];
            break;
        case XMIAdJumpTypeDeeplink:
            [self doJumpDeeplink:adData];
            break;
        case XMIAdJumpTypeWeixin:
            [self doJumpWeixin:adData];
            break;
        case XMIAdJumpTypeStore:
            [self doJumpStore:adData];
            break;
        case XMIAdJumpTypeVideoStore:
            [self doJumpVideoStore:adData];
            break;
        case XMIAdJumpTypeVideoWeb:
        case XMIAdJumpTypeVideoFullWeb:
            [self doJumpVideoWeb:adData];
            break;
        case XMIAdJumpTypeGame:
            [self doJumpGame:adData];
            break;
        case XMIAdJumpTypeMall:
            [self doJumpMall:adData];
            break;
        case XMIAdJumpTypeCustom:
            isHandleJump = YES;
            break;
        default:
            isHandleJump = NO;
            break;
    }
    return isHandleJump;
}


/**
 跳转web容器
 */
- (void)doJumpWeb:(XMIAdRelatedData *)adData {
    if (adData.realLink == nil) {
        return;
    }
    if ([self doCanOpenWebApp:adData]) {
        return;
    }
    [self openUrlInWeb:adData.realLink withAd:adData];
    
    
}

/**
 跳转deeplink
 */
- (void)doJumpDeeplink:(XMIAdRelatedData *)adData {
    @weakify(self)
    [self openUrl:adData.dpRealLink completionHandler:^(BOOL success) {
        @strongify(self)
        if (!self) {
            return;
        }
        [self p_deeplinkClickedReport:adData success:success];
        
        if (!success) {
            // 失败处理
//            if (![adData.dpRealLink hasPrefix:@"http"] && ![adData.dpRealLink hasPrefix:@"https"]) {
//                return;
//            }
            
            if (adData.enableContinuePlay && [adData videoUrlVaild]) {
                [self doJumpVideoWeb:adData];
                return;
            }
            [self openUrlInWeb:adData.realLink withAd:adData];
        }
    }];
}

- (void)p_deeplinkClickedReport:(XMIAdRelatedData *)adData
                        success:(BOOL)success
{
    // 上报
    if (@available(iOS 10.0, *)) {
        BOOL isInstall = [self canOpenUrl:adData.dpRealLink];
        XMIAdDpReporter *reporter = [[XMIAdDpReporter alloc] init];
        [reporter fillWithAd:adData];
        reporter.dpCallStatus = isInstall ? (success ? XMIAdDpCallStatusSuccess : XMIAdDpCallStatusFail) : XMIAdDpCallStatusNotInstall;
        [[XMIAdReporter sharedInstance] addDpReport:reporter];
        
    }
}

/**
 跳转浏览器
 */
- (void)doJumpBrowser:(XMIAdRelatedData *)adData {
    [self openUrl:adData.realLink];
}

/**
 打电话
 */
- (void)doJumpPhoneCall:(XMIAdRelatedData *)adData {
    NSString *title = nil;
    // TODO:
    UIAlertController *alertVC = [UIAlertController alertControllerWithTitle:title message:adData.realLink preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *confirmAction = [UIAlertAction actionWithTitle:@"立即拨打" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        NSString *callUrl = [NSString stringWithFormat:@"telprompt://%@", adData.realLink];
        if ([self canOpenUrl:callUrl]) {
            [self openUrl:callUrl];
        } else {
            callUrl = [NSString stringWithFormat:@"tel:%@", adData.realLink];
            [self openUrl:callUrl];
        }
    }];
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"残忍取消" style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        
    }];
    [alertVC addAction:confirmAction];
    [alertVC addAction:cancelAction];
    [self.rootViewController presentViewController:alertVC animated:YES completion:nil];
}

- (id)getObjectFromConfigCenterWithKey:(NSString *)key
{
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(getConfigCenterWithKey:)]) {
        id object = [manager.delegate getConfigCenterWithKey:key];
        return object;
    }
    return nil;
}

/**
 跳微信
 */
- (void)doJumpWeixin:(XMIAdRelatedData *)adData {
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerJumpWeixin:fromController:)]) {
        [manager.delegate managerJumpWeixin:adData fromController:self.rootViewController];
    }
}


/// 跳转到 处理有关iting
- (BOOL)doCanOpenWebApp:(XMIAdRelatedData *)adData {
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerJumpWebInApp:)]) {
        return [manager.delegate managerJumpWebInApp:adData];
    }
    return NO;
}

/**
 跳应用内appstore
 */
- (void)doJumpStore:(XMIAdRelatedData *)adData {
    BOOL useStoreJumpV2 = [XMIAdABTest getBoolValueWithKey:@"useStoreJumpV2iOS" defaultValue:YES];
    if (useStoreJumpV2) {
        [XMIOwnAdHelper storeControllerAsyncWithAd:adData completion:^(SKStoreProductViewController * _Nonnull storeVC) {
            if (storeVC == nil) {
                [self doJumpWeb:adData];
                return;
            }
            if (self.delegate && [self.delegate respondsToSelector:@selector(jumpManager:adWillPresentScreen:)]) {
                [self.delegate jumpManager:self adWillPresentScreen:adData];
            }
            [storeVC aktTagObject:adData forKey:@"adData"];
            storeVC.delegate = self;
            [self.rootViewController presentViewController:storeVC animated:YES completion:^{
                if (self.delegate && [self.delegate respondsToSelector:@selector(jumpManager:adDidPresentScreen:)]) {
                    [self.delegate jumpManager:self adDidPresentScreen:adData];
                }
            }];
        }];
    } else {
        UIViewController *storeVC = [XMIOwnAdHelper storeControllerWithAd:adData andDelegate:self];
        if (storeVC == nil) {
            [self doJumpWeb:adData];
            return;
        }
        [self.rootViewController presentViewController:storeVC animated:YES completion:nil];
    }
}

/**
 跳拼接appstore
 */
- (void)doJumpVideoStore:(XMIAdRelatedData *)adData {
    UIViewController *videoStoreVC = [XMIOwnAdHelper videoStoreControllerWithAd:adData andDelegate:self];
    if (videoStoreVC == nil) {
        [self doJumpWeb:adData];
        return;
    }
    [self.rootViewController presentViewController:videoStoreVC animated:YES completion:nil];
}

/**
 跳拼接web
 */
- (void)doJumpVideoWeb:(XMIAdRelatedData *)adData {
    // https://docs.dingtalk.com/i/nodes/3KLw95QMzkb8gNbRlDdNJAjrymPeEN2q?iframeQuery=utm_source%3Dportal%26utm_medium%3Dportal_recent&corpId=ding51f195092fd77474&rnd=0.11598159125816498
    // 激励视频页的点击，直接跳全屏h5
    NSString *postionIdStr = [NSString stringWithFormat:@"%lld", adData.positionId];
    NSDictionary *rewardAdOpenFullWebviewDic = [self getObjectFromConfigCenterWithKey:@"rewardAdOpenFullWebview"];
    BOOL jumpWeb = [rewardAdOpenFullWebviewDic boolMaybeForKey:postionIdStr];
    if (jumpWeb) {
        //激励视频不跳拼接落地页
        [self doJumpWeb:adData];
    }
    else if(adData.linkType == 102) {
        UIViewController *videoWebVC = [XMIOwnAdHelper fullVideoWebControllerWithAd:adData andDelegate:self];
        UINavigationController *nav = [[UINavigationController alloc] initWithRootViewController:videoWebVC];
        [self.rootViewController presentViewController:nav animated:YES completion:nil];
    }
    else if (adData.linkType == 103) {
        UIViewController *videoWebVC = [XMIOwnAdHelper fullVideoWebControllerBWithAd:adData andDelegate:self];
        UINavigationController *nav = [[UINavigationController alloc] initWithRootViewController:videoWebVC];
        [self.rootViewController presentViewController:nav animated:YES completion:nil];
    }
    else {
        UIViewController *videoWebVC = [XMIOwnAdHelper videoWebControllerWithAd:adData andDelegate:self];
        UINavigationController *nav = [[UINavigationController alloc] initWithRootViewController:videoWebVC];
        [self.rootViewController presentViewController:nav animated:YES completion:nil];
    }
}

/**
 跳游戏中心
 */
- (void)doJumpGame:(XMIAdRelatedData *)adData {
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerJumpGameCenter:fromController:)]) {
        [manager.delegate managerJumpGameCenter:adData fromController:self.rootViewController];
    }
}

/**
 跳喜马商城
 */
- (void)doJumpMall:(XMIAdRelatedData *)adData {
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerJumpMall:fromController:)]) {
        [manager.delegate managerJumpMall:adData fromController:self.rootViewController];
    }
}

#pragma mark - XMIOwnStoreViewControllerDelegate
- (void)ownStore:(XMIOwnStoreViewController *)controller loadProductFailWithError:(NSError *)error {
    
}
- (void)ownStoreViewWillAppear:(XMIOwnStoreViewController *)controller {
    if (self.delegate && [self.delegate respondsToSelector:@selector(jumpManager:adWillPresentScreen:)]) {
        [self.delegate jumpManager:self adWillPresentScreen:controller.adData];
    }
}
- (void)ownStoreViewDidAppear:(XMIOwnStoreViewController *)controller {
    if (self.delegate && [self.delegate respondsToSelector:@selector(jumpManager:adDidPresentScreen:)]) {
        [self.delegate jumpManager:self adDidPresentScreen:controller.adData];
    }
}
- (void)ownStoreDidClosed:(XMIOwnStoreViewController *)controller {
    if (self.delegate && [self.delegate respondsToSelector:@selector(jumpManager:adDetailControllerDidClosed:)]) {
        [self.delegate jumpManager:self adDetailControllerDidClosed:controller.adData];
    }
}

- (void)productViewControllerDidFinish:(SKStoreProductViewController *)viewController
{
    if (self.delegate && [self.delegate respondsToSelector:@selector(jumpManager:adDetailControllerDidClosed:)]) {
        [self.delegate jumpManager:self adDetailControllerDidClosed:[viewController aktTagObjectWithKey:@"adData"]];
    }
}

#pragma mark - XMIOwnVideoStoreViewControllerDelegate
- (void)ownVideoStore:(XMIOwnStoreViewController *)controller loadProductFailWithError:(NSError *)error {
    
}
#pragma mark - XMIOwnVideoViewControllerDelegate
- (void)ownVideoViewWillAppear:(XMIOwnStoreViewController *)controller {
    if (self.delegate && [self.delegate respondsToSelector:@selector(jumpManager:adWillPresentScreen:)]) {
        [self.delegate jumpManager:self adWillPresentScreen:controller.adData];
    }
}
- (void)ownVideoViewDidAppear:(XMIOwnStoreViewController *)controller {
    if (self.delegate && [self.delegate respondsToSelector:@selector(jumpManager:adDidPresentScreen:)]) {
        [self.delegate jumpManager:self adDidPresentScreen:controller.adData];
    }
}
- (void)ownVideoDidClosed:(XMIOwnStoreViewController *)controller {
    if (self.delegate && [self.delegate respondsToSelector:@selector(jumpManager:adDetailControllerDidClosed:)]) {
        [self.delegate jumpManager:self adDetailControllerDidClosed:controller.adData];
    }
}

/**
 检测跳转类型
 */
- (XMIAdJumpType)detectJumpType:(XMIAdRelatedData *)adData {
    // click=2不跳转
    // 有deeplink优先跳deeplink
    // opinlinkType=4跳游戏中心
    // clickType=3 浏览器
    // clickType=16 打电话
    // clickType=17 跳转微信小程序
    // clickType=18 app内商店 商店分系统默认的、视频拼接半屏store两种
    //
    // clickType=1
    // 贴片广告跳webview web分直接web，视频拼接半屏H5两种
    // 其它广告 openlinkType 0-webview 3-打电话 1其它-浏览器
    
    if (adData.clickType == XMIAdClickTypeNone) {
        return XMIAdJumpTypeNone;
    }
    
    if (adData.linkType == 105) {
        [self detectOpenRewardAd:adData];
        return XMIAdJumpTypeCustom;
    }
    
    // 如果是内容推广，xmad不支持该种跳转，由xmadapi处理
    if ([self isContentAd:adData]) {
        return XMIAdJumpTypeNone;
    }
    
    
    if (adData.clickType == XMIAdClickTypeWeixin) {
        return XMIAdJumpTypeWeixin;
    } else if (adData.clickType == XMIAdClickTypeStore){
//        return XMIAdJumpTypeDeeplink;
    }
    
    if (adData.openlinkType == XMIAdOpenlinkTypeGame) {
        return XMIAdJumpTypeGame;
    }
    
    if (adData.dpRealLink && adData.dpRealLink.length > 0) {
        return XMIAdJumpTypeDeeplink;
    }
    
    XMIAdJumpType jumpType = XMIAdJumpTypeWeb;
    switch (adData.clickType) {
        case XMIAdClickTypeNone:
            jumpType = XMIAdJumpTypeNone;
            break;
        case XMIAdClickTypeBrowser:
            jumpType = XMIAdJumpTypeBrowser;
            break;
        case XMIAdClickTypeWeixin:
            jumpType = XMIAdJumpTypeWeixin;
            break;
        case XMIAdClickTypeStore:
            jumpType = [self detectSubStoreJumpType:adData];
            break;
        case XMIAdClickTypeWeb:
            jumpType = [self detectRealWebJumpType:adData];
            break;
        default:
            break;
    }
    
    return jumpType;
}
/**
 判断是直接appstore，还是半屏拼接
 */
- (XMIAdJumpType)detectSubStoreJumpType:(XMIAdRelatedData *)adData {
    if (adData.enableVideoJoinAppStore && adData.videoUrl && adData.videoUrl.length > 0) {
        return XMIAdJumpTypeVideoStore;
    } else if (adData.linkType == 103) {
        return XMIAdJumpTypeVideoStore;
    }
    return XMIAdJumpTypeStore;
}
/**
 clickType=1时，根据openlinkType进一步检测真实跳转类型
 */
- (XMIAdJumpType)detectRealWebJumpType:(XMIAdRelatedData *)adData {
    XMIAdJumpType jumpType = XMIAdJumpTypeWeb;
    // 分贴片 非贴片情况
    if (1) {
        switch (adData.openlinkType) {
            case XMIAdOpenlinkTypeWeb:
                jumpType = XMIAdJumpTypeWeb;
                break;
            case XMIAdOpenlinkTypeCall:
                jumpType = XMIAdJumpTypeCall;
                break;
            case XMIAdOpenlinkTypeBrowser:
            default:
                jumpType = XMIAdJumpTypeBrowser;
                break;
        }
    } else {
        
    }
    // 判断是直接跳webview，还是半屏拼接
    if (jumpType == XMIAdJumpTypeWeb && [adData videoUrlVaild]) {
        if (adData.linkType == 102) {
            jumpType = XMIAdJumpTypeVideoFullWeb;// 全屏视频
        } else if (adData.enableContinuePlay) {
            jumpType = XMIAdJumpTypeVideoWeb;// 半屏视频拼接
        }
    }
    
    return jumpType;
}


- (BOOL)isContentAd:(XMIAdRelatedData *)adData {
    NSString *realLink = adData.realLink;
    if ([realLink containsString:@"track_id"] && [realLink containsString:@"responseId"] && [realLink containsString:@"materialId"]) {
        return YES;
    }
    
    if (adData.linkType == 101 && adData.promoteTrackId > 0) {
        return YES;
    }
    
    if (adData.linkType == 100) {
        return YES;
    }
    // 新视频拼接落地页
    if (adData.linkType == 106) {
        return YES;
    }
    
    //全屏视频落地页样式优化
    //https://teambition.ximalaya.com/task/673c5a68434c39c21b29d4cb
    if (adData.linkType == 107) {
        return YES;
    }
    
    return NO;
}

- (void)detectOpenRewardAd:(XMIAdRelatedData *)adData {
    if ([[XMIAdManager sharedInstance].delegate respondsToSelector:@selector(loadRewardVideoAdIfLinkTypeIs105:completion:)]) {
        [[XMIAdManager sharedInstance].delegate loadRewardVideoAdIfLinkTypeIs105:adData completion:^(BOOL success) {
            if (!success) {
                adData.linkType = 1;
                [self doJumpWithAd:adData];
            }
        }];
    }
}

/**
 是否商城URL
 */
- (BOOL)isMallUrl:(NSString *)strUrl
{
    if (strUrl.length && ([strUrl containsString:@"youzan.com"]||[strUrl containsString:@"koudaitong.com"]||[strUrl containsString:@"kdt.im"])) {
        return YES;
    }
    return NO;
}

- (void)openUrlInWeb:(NSString *)urlString withAd:(XMIAdRelatedData *)adData {
    NSURL *url = [NSURL URLWithString:urlString];
    if (!url) url = [NSURL URLWithString:[urlString stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding]];
    if (url == nil) {
        XMILog(@"ad 跳转web时url是空的!");
        return;
    }
    // 站内scheme交由站内处理
    if ([[XMIAdManager sharedInstance].delegate respondsToSelector:@selector(managerSchemeManagerCanOpenURL:)]) {
        BOOL schemeOpenResult = [[XMIAdManager sharedInstance].delegate managerSchemeManagerCanOpenURL:url];
        if (schemeOpenResult) {
            return;
        }
    }
    
    UIViewController<XMIWebVCProtocol> *webVC = [XMIOwnAdHelper adWebControllerWithURL:url];
    if ([adData.popReminderStyle isEqualToString:@"2"]) {
        webVC.popReminderText = adData.popReminderText;
    } else if ([adData.popReminderStyle isEqualToString:@"3"]) {
        webVC.popReminderTextStyle3 = adData.popReminderText;
    } else if ([adData.popReminderStyle isEqualToString:@"4"]) {
        webVC.popReminderTextStyle4 = adData.popReminderText;
    }
    
    long long clickTime = [XMICommonUtils currentTimestamp];
    __block long long startTime = [XMICommonUtils currentTimestamp];
    __weak UIViewController *weakVC = webVC;
    
    [webVC setViewDidLoadHandler:^{
        XMILog(@"didLoad %@", urlString);
        startTime = [XMICommonUtils currentTimestamp];
        XMIAdLandingPageReporter *reporter = [[XMIAdLandingPageReporter alloc] init];
        [reporter fillWithAd:adData];
        reporter.clickTime = @(clickTime).stringValue;
        reporter.completeTime = @(startTime - clickTime).stringValue;
        reporter.realLinkStatus =  @"100";
        reporter.landingPageUrl = urlString ? : @"";
        [[XMIAdReporter sharedInstance] addLandingPageReport:reporter];
    }];
    [webVC setViewWillDealloc:^{
        long long closeTimeMs = NSDate.date.timeIntervalSince1970 * 1000;
        XMIAdLandingPageReporter *reporter = [[XMIAdLandingPageReporter alloc] init];
        [reporter fillWithAd:adData];
        reporter.clickTime = @(clickTime).stringValue;
        reporter.closeTime = @(closeTimeMs - clickTime).stringValue;
        reporter.realLinkStatus =  @"3";
        reporter.landingPageUrl = urlString ? : @"";
        [[XMIAdReporter sharedInstance] addLandingPageReport:reporter];
    }];
    @weakify(self)
    [webVC setFinishLoadingHandler:^(BOOL success, NSError * _Nullable error) {
        XMILog(@"finishLoad %@, %@", urlString, error);
        @strongify(self)
        if (!self) {
            return;
        }
        [XMIAdReporter webFinishLoading:success withAdData:adData startTimestamp:startTime];
        long long completeTime = [XMICommonUtils currentTimestamp];
        XMIAdLandingPageReporter *reporter = [[XMIAdLandingPageReporter alloc] init];
        [reporter fillWithAd:adData];
        reporter.clickTime = @(clickTime).stringValue;
        reporter.completeTime = @(completeTime - clickTime).stringValue;
        reporter.realLinkStatus =  success ? @"0" : @"1";
        reporter.landingPageUrl = urlString ? : @"";
        [[XMIAdReporter sharedInstance] addLandingPageReport:reporter];
    }];
    [webVC setFirstPaintHandler:^{
        XMILog(@"firstPaint %@", urlString);
        @strongify(self)
        if (!self) {
            return;
        }
        double pixelCheckTime = [[[[weakVC valueForKey:@"webProvider"] valueForKey:@"bitmapCheck"] valueForKey:@"pixelCheckTime"] aktDoubleValue];
        if (pixelCheckTime > 0) {
            XMIAdLandingPageReporter *reporter = [[XMIAdLandingPageReporter alloc] init];
            [reporter fillWithAd:adData];
            reporter.clickTime = @(clickTime).stringValue;
            reporter.firstCompleteTime = @((NSInteger)(pixelCheckTime * 1000)).stringValue;
            reporter.realLinkStatus =  @"2";
            reporter.landingPageUrl = urlString ? : @"";
            [[XMIAdReporter sharedInstance] addLandingPageReport:reporter];
        }
    }];
    if (self.rootViewController.navigationController != nil) {
        webVC.hidesBottomBarWhenPushed = YES;
        [self.rootViewController.navigationController pushViewController:webVC animated:YES];
    } else {
        webVC.modalPresentationStyle = UIModalPresentationFullScreen;
        UINavigationController *navController = [[UINavigationController alloc] initWithRootViewController:webVC];
        [self.rootViewController presentViewController:navController animated:YES completion:nil];
    }
}

- (BOOL)canOpenUrl:(NSString *)urlString {
    if (urlString == nil) {
        return NO;
    }
    NSURL *url = [NSURL URLWithString:urlString];
    if (!url) {
        url = [NSURL URLWithString:[urlString stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding]];
    }
    if (url == nil) {
        return NO;
    }
    return [[UIApplication sharedApplication] canOpenURL:url];
}
- (void)openUrl:(NSString *)urlString {
    [self openUrl:urlString completionHandler:nil];
}
- (void)openUrl:(NSString *)urlString completionHandler:(void (^ __nullable)(BOOL success))completion {
    if (urlString == nil) {
        completion ? completion(NO) : nil;
        return;
    }
    if ([urlString hasPrefix:@"iting://open?msg_type=330"]) {
        NSDictionary *URLQueryDictionary = [urlString URLQueryDictionary];
        if ([[URLQueryDictionary stringMaybeForKey:@"function"] isEqualToString:@"1"]) {
            urlString = [urlString stringByAppendingFormat:@"&ticket=%@", [XMIAdHelper getTicketWithBusiness:@"live" scene:@"red_packet_ad"]];
        }
    }
    //直播红包特殊处理
    NSURL *url = [NSURL URLWithString:urlString];
    if (!url) {
        url = [NSURL URLWithString:[urlString stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding]];
    }
    if (url == nil) {
        completion ? completion(NO) : nil;
        return;
    }
    
    if (@available(iOS 10.0, *)) {
        [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {
            completion ? completion(success) : nil;
        }];
    } else {
        // 由于10以下的系统没有提供用户授权弹窗结果的回调，当失败处理
        [[UIApplication sharedApplication] openURL:url];
        completion ? completion(NO) : nil;
    }
}

- (void)settingCurrentAdData:(XMIAdRelatedData *)adData {
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerSettingCurrentAdData:)]) {
        [manager.delegate managerSettingCurrentAdData:adData];
    }
}
@end
