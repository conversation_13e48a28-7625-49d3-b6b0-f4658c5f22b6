//
//  XMIOwnVideoWebViewController.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/25.
//

#import "XMIOwnVideoWebViewController.h"
#import <Masonry/Masonry.h>
#import "XMICommonUtils.h"
#import "XMIAdRelatedData.h"
#import "XMIOwnAdHelper.h"
#import "XMIAdAlertView.h"
#import "XMIAdManager.h"
#import "XMIWebVCProtocol.h"
#import "XMIAdReporter+AD.h"
#import <XMCategories/XMMacro.h>
#import "XMIAdMacro.h"
#import <XMAd/XMIAdHelper.h>

@interface XMIOwnVideoWebViewController ()

@property (nonatomic, weak) UIView *webView;

@end

@implementation XMIOwnVideoWebViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    
    [self showPopReminderView];
}

- (void)setupUI {
    // web
    [self buildWebView];
}

- (void)buildWebView {
    NSURL *url = [NSURL URLWithString:self.adData.realLink];
    UIViewController<XMIWebVCProtocol> *webVC = [XMIOwnAdHelper adWebControllerWithURL:url];
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wundeclared-selector"
    if ([webVC respondsToSelector:@selector(setHeightOffset:)]) {
        [webVC setValue:@(0) forKey:@"heightOffset"];
    }
    if ([webVC respondsToSelector:@selector(setHiddenNavigationBar:)]) {
        [webVC setValue:@(YES) forKey:@"hiddenNavigationBar"];
    }
    if ([webVC respondsToSelector:@selector(setHiddenTabbar:)]) {
        [webVC setValue:@(YES) forKey:@"hiddenTabbar"];
    }
#pragma clang diagnostic pop
    [self addChildViewController:webVC];
    [self.view addSubview:webVC.view];
    //
    UIView *aView = webVC.view;
    [aView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(self.view);
        make.top.equalTo(self.videoView.mas_bottom);
    }];
    
    __block long long startTime = [XMICommonUtils currentTimestamp];
    @weakify(self)
    [webVC setFinishLoadingHandler:^(BOOL success, NSError * _Nullable error) {
        XMILog(@"finishLoad %@, %@", self.adData.realLink, error);
        @strongify(self)
        if (!self) {
            return;
        }
        [XMIAdReporter webFinishLoading:success withAdData:self.adData startTimestamp:startTime];
    }];
    
    self.webView = aView;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    if (self.navigationController) {
        [self.navigationController setNavigationBarHidden:YES];
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    if (self.navigationController) {
        [self.navigationController setNavigationBarHidden:NO];
    }
}

@end
