//
//  XMIOwnVideoStoreViewController.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/23.
//

#import "XMIOwnVideoStoreViewController.h"
#import <StoreKit/StoreKit.h>
#import "XMIAdMacro.h"
#import "XMIAdError.h"

@interface XMIOwnVideoStoreViewController ()<SKStoreProductViewControllerDelegate>

@end

@implementation XMIOwnVideoStoreViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.backBtn.hidden = YES;
    [self setupUI];
}

- (void)setupUI {
    // store
    [self buildStore];
}

- (void)buildStore {
    if (!self.productId) {
        if (self.storeDelegate && [self.storeDelegate respondsToSelector:@selector(ownVideoStore:loadProductFailWithError:)]) {
            [self.storeDelegate ownVideoStore:self loadProductFailWithError:[XMIAdError emptyProductIDError]];
        }
        // 未加载会无法关闭，自动关闭
        @weakify(self)
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, 1 * NSEC_PER_SEC), dispatch_get_main_queue(), ^{
            @strongify(self)
            if (!self) {
                return;
            }
            
            [self dismissViewControllerAnimated:YES completion:nil];
        });
        return;
    }
    
    NSDictionary *dic = [NSDictionary dictionaryWithObject:self.productId forKey:SKStoreProductParameterITunesItemIdentifier];
    SKStoreProductViewController *storeVC = [[SKStoreProductViewController alloc] init];
    storeVC.delegate = self;
    [storeVC loadProductWithParameters:dic completionBlock:^(BOOL result, NSError * _Nullable error) {
        if (!result) {
            XMILog(@"Video+SKStoreProduct load fail, %@", error);
            if (self.storeDelegate && [self.storeDelegate respondsToSelector:@selector(ownVideoStore:loadProductFailWithError:)]) {
                [self.storeDelegate ownVideoStore:self loadProductFailWithError:error];
            }
        }
    }];
    [self presentViewController:storeVC animated:NO completion:^{
        storeVC.view.backgroundColor = [UIColor whiteColor];
        UIView *videoView = self.videoView;
        storeVC.view.superview.frame = CGRectMake(0, videoView.frame.origin.y + videoView.frame.size.height, self.view.frame.size.width, self.view.frame.size.height - videoView.frame.size.height);
        if (@available(iOS 13, *)) {
            UIViewController *subVC = storeVC.childViewControllers.firstObject;
            subVC.view.subviews.firstObject.frame = CGRectMake(0, -20, videoView.frame.size.width, self.view.frame.size.height - videoView.frame.size.height);
        }
    }];
}

#pragma mark - SKStoreProductViewControllerDelegate
- (void)productViewControllerDidFinish:(SKStoreProductViewController *)viewController {
    [viewController dismissViewControllerAnimated:NO completion:^{
        [self dismissViewControllerAnimated:YES completion:^{
            [self didClosed];
        }];
    }];
}

@end
