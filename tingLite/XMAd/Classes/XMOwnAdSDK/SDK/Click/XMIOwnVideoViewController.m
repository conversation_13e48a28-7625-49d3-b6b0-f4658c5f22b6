//
//  XMIOwnVideoViewController.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/25.
//

#import "XMIOwnVideoViewController.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import <Masonry/Masonry.h>
#import "XMIAdMacro.h"
#import "XMIAdRelatedData.h"
#import "XMICommonUtils.h"
#import "XMIAdVideoPlayer.h"
#import <XMAd/XMIAdHelper.h>
#import "XMIAdAlertView.h"
#import "XMIAdManager.h"

@interface XMIOwnVideoViewController ()<XMIAdVideoPlayerDelegate>

@property (nonatomic, strong) XMIAdNewVideoPlayer *adPlayer;
@property (nonatomic, strong) UIView *videoView;
@property (nonatomic, strong) UIButton *voiceBtn;
@property (nonatomic, strong) UIView *maskView;
@property (nonatomic, strong) UIButton *backBtn;

@property (nonatomic, assign) BOOL showedReminderView;
@end

@implementation XMIOwnVideoViewController

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self baseSetupUI];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didEnterBackground) name:UIApplicationDidEnterBackgroundNotification object:nil];
}

- (void)baseSetupUI {
    // player
    [self buildPlayer];
    [self setupPlayerFrame];
    
    // control
    [self buildControl];
    
    [self buildBackBtn];
    
    NSString *videoUrl = self.adData.videoUrl;
    if (!videoUrl.length) {
        videoUrl = self.adData.videoCover;
    }
    
    [self.adPlayer startPlayWithURL:[NSURL URLWithString:self.adData.videoUrl] playerView:self.videoView];
    [self.adPlayer play];
}

- (UIView *)videoView {
    if (!_videoView) {
        _videoView = [XMIAdNewVideoView createVideoView];
        _videoView.hidden = NO;
        [self.view addSubview:_videoView];
    }
    return _videoView;
}

- (void)buildPlayer {
    XMIAdNewVideoPlayer *player = [XMIAdNewVideoPlayer createVideoPlayer];
    player.scalingMode = XMIAdNewPlayerScalingModeAspectFit;
    player.delegate = self;
    player.volume = 0;
    self.adPlayer = player;
}

- (void)setupPlayerFrame {
    [self.videoView mas_makeConstraints:^(MASConstraintMaker *make) {
        if (@available(iOS 11.0, *)) {
            make.left.equalTo(self.view.mas_safeAreaLayoutGuideLeft);
            make.right.equalTo(self.view.mas_safeAreaLayoutGuideRight);
            make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop);
        } else {
            make.left.equalTo(self.view);
            make.right.equalTo(self.view);
            make.top.equalTo(self.view);
        }
        make.width.mas_equalTo(self.videoView.mas_height).multipliedBy(16.0/9.0);
    }];
}

- (void)buildBackBtn {
    UIButton *backBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    _backBtn = backBtn;
    [backBtn setImage:[XMICommonUtils imageNamed:@"icon_back_ad_video"] forState:UIControlStateNormal];
    [self.view addSubview:backBtn];
    [backBtn addTarget:self action:@selector(handleBackBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [backBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        if (@available(iOS 11.0, *)) {
            make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop).with.offset(16);
        } else {
            make.top.equalTo(self.view).with.offset(16);
        }
        make.left.equalTo(self.view).with.offset(16);
        make.width.mas_equalTo(24);
        make.height.mas_equalTo(24);
    }];
}
- (void)handleBackBtnClick:(id)sender {
    [self dismissViewControllerAnimated:YES completion:^{
        [self didClosed];
    }];
}

- (void)buildControl {
    UIView *videoView = self.videoView;
    // 1.voice
    UIImage *vOnImage = [XMICommonUtils imageNamed:@"icon_voice_on"];
    UIImage *vOffImage = [XMICommonUtils imageNamed:@"icon_voice_off"];
    UIButton *voiceBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [voiceBtn setImage:vOffImage forState:UIControlStateNormal];
    [voiceBtn setImage:vOnImage forState:UIControlStateSelected];
    [videoView addSubview:voiceBtn];
    [voiceBtn addTarget:self action:@selector(handleVoiceBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [voiceBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(videoView).with.offset(-16);
        make.top.equalTo(videoView).with.offset(16);
        make.width.mas_equalTo(30);
        make.height.mas_equalTo(voiceBtn.mas_width);
    }];
    self.voiceBtn = voiceBtn;
    // 2.mask view
    UIView *maskView = [[UIView alloc] initWithFrame:CGRectZero];
    maskView.backgroundColor = XMI_COLOR_RGBA(0x000000, 0.5);
    maskView.alpha = 0;
    [videoView addSubview:maskView];
    [maskView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(videoView);
    }];
    self.maskView = maskView;
    // 3.replay
    UIButton *replayBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [replayBtn setImage:[XMICommonUtils imageNamed:@"video_btn_replay"] forState:UIControlStateNormal];
    [replayBtn setTitle:@" 重播" forState:UIControlStateNormal];
    replayBtn.titleLabel.font = XMI_AD_PingFangFont(13);
    [maskView addSubview:replayBtn];
    [replayBtn addTarget:self action:@selector(handleReplayBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [replayBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(maskView).with.offset(-20);
        make.bottom.equalTo(maskView).with.offset(-20);
    }];
    // desc view 为了让logo title整体居中
    UIView *descView = [[UIView alloc] initWithFrame:CGRectZero];
    descView.backgroundColor = [UIColor clearColor];
    [maskView addSubview:descView];
    [descView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(maskView);
        make.width.equalTo(maskView);
    }];
    // 4.logo
    UIImageView *logoImageView = [[UIImageView alloc] initWithFrame:CGRectZero];
    logoImageView.layer.cornerRadius = 8.0;
    logoImageView.layer.masksToBounds = YES;
    [descView addSubview:logoImageView];
    [logoImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(descView);
        make.top.equalTo(descView);
        make.width.mas_equalTo(48);
        make.height.mas_equalTo(logoImageView.mas_width);
    }];
    // 5.title
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero];
    titleLabel.textAlignment = NSTextAlignmentCenter;
    titleLabel.lineBreakMode = NSLineBreakByTruncatingTail;
    titleLabel.textColor = XMI_COLOR_RGB(0xffffff);
    titleLabel.font = XMI_AD_PingFangFont(16);
    titleLabel.text = self.adData.downloadAppName.length > 0 ? self.adData.downloadAppName : self.adData.name;
    [descView addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(descView);
        make.top.equalTo(logoImageView.mas_bottom).with.offset(5);
        make.bottom.equalTo(descView);
        make.width.equalTo(descView);
    }];
    
}
- (void)handleVoiceBtnClick:(id)sender {
    self.voiceBtn.selected = !self.voiceBtn.selected;
    CGFloat volume = self.voiceBtn.selected ? 1 : 0;
    self.adPlayer.volume = volume;
}
- (void)handleReplayBtnClick:(id)sender {
    [self.adPlayer replay];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(ownVideoViewWillAppear:)]) {
        [self.delegate ownVideoViewWillAppear:self];
    }
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(ownVideoViewDidAppear:)]) {
        [self.delegate ownVideoViewDidAppear:self];
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    if (self.delegate && [self.delegate respondsToSelector:@selector(ownVideoViewWillDisappear:)]) {
        [self.delegate ownVideoViewWillDisappear:self];
    }
    
    [super viewWillDisappear:animated];
}

- (void)viewDidDisappear:(BOOL)animated {
    if (self.delegate && [self.delegate respondsToSelector:@selector(ownVideoViewDidDisappear:)]) {
        [self.delegate ownVideoViewDidDisappear:self];
    }
    
    [super viewDidDisappear:animated];
    
    BOOL beingDismissed = self.beingDismissed;
    if (beingDismissed) {
        [self.adPlayer stop];
    }
}

/**
 关闭处理
 */
- (void)didClosed {
    if (self.delegate && [self.delegate respondsToSelector:@selector(ownVideoDidClosed:)]) {
        [self.delegate ownVideoDidClosed:self];
    }
}

/**
 播放结束，显示maskview
 */
- (void)didPlayEnd {
    [UIView animateWithDuration:0.2 animations:^{
        self.maskView.alpha = 1;
        self.voiceBtn.alpha = 0;
    }];
}
- (void)didPlayStart {
    self.maskView.alpha = 0;
    self.voiceBtn.alpha = 1;
}

- (void)showPopReminderView {
    if (self.showedReminderView) {
        return;
    }
    self.showedReminderView = YES;
    if ([self.adData.popReminderStyle isEqualToString:@"2"]) {
        [[XMIAdAlertView alertWithType:XMIAdAlertViewTypeDefault contentWidth:280 contentInfo:self.adData.popReminderText cancelButtonTitle:@"" certainButtonTitle:@"确认" certainBlock:^{}] show];
    } else if ([self.adData.popReminderStyle isEqualToString:@"3"]) {
        XMIAdManager *manager = [XMIAdManager sharedInstance];
        if (manager.delegate && [manager.delegate respondsToSelector:@selector(showToastWithInfo:)]) {
            [manager.delegate showToastWithInfo:self.adData.popReminderText];
        }
    } else if ([self.adData.popReminderStyle isEqualToString:@"4"]) {
        [XMIAdHelper showTopRemindViewInView:self.view remindText:self.adData.popReminderText];
    }
}

#pragma mark - XMIAdVideoPlayerDelegate
- (void)player:(XMIAdVideoPlayer *)player playStateDidChanged:(XMIAdPlayerPlayState)state {
    if (state == XMIAdPlayerStateStopped) {
        [self didPlayEnd];
    } else {
        [self didPlayStart];
    }
}

- (void)didEnterBackground {
    if (self.adPlayer.isPlaying) {
        [self.adPlayer pause];
    }
}
@end
