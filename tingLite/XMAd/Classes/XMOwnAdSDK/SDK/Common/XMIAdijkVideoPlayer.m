//
//  XMIAdijkVideoPlayer.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/1/21.
//

#import "XMIAdijkVideoPlayer.h"
#import <XMCommonUtil/XMAppSession.h>
#import <XMAVKit/XMAVKit.h>
#import <XMBase/XMTimer.h>
#import <KVOController/KVOController.h>
#import <XMCategories/XMMacro.h>
#import <XMCategories/NSObject+Dealloc.h>
#import "XMIAdError.h"
#import "XMIAdManager.h"
#import "XMIAdHelper.h"
@implementation XMIAdijkVideoView

@end

@interface XMIAdijkVideoPlayer()<XMAppAudioAction>
{
    BOOL _playerDidLoad;
    BOOL _wantToPlay;         // 调用者在此时是否希望播放
}
@property (strong, nonatomic) XMAVPlayer          *aPlayer;
@property (strong, nonatomic) XMAVPlayerItem      *item;
@property (strong, nonatomic) XMAVPlayerView  *aPlayerView;
@property (nonatomic, strong) XMTimer *timer;
@property(nonatomic, assign)BOOL muteWhenInterrupt;
@property(nonatomic, assign)BOOL requestActive;
@property (nonatomic, assign) unsigned long currentEvent;
@end


@implementation XMIAdijkVideoPlayer

/**
 初始化
 @param URL :本地或远程链接
 */
- (instancetype)initWithURL:(NSURL *)URL
{
    if (self = [super initWithURL:URL]) {
        [self setURL:URL];
    }
    return self;
}

- (instancetype)initWithURL:(NSURL *)URL
                 playerView:(UIView *)playerView
{
    if (self = [super initWithURL:URL]) {
        [self setURL:URL];
    }
    return self;
    
}


- (void)addVideoPlayerKVO {
    [self.KVOController unobserve:_aPlayer keyPath:@"playbackStatus"];
    unsigned long eventCode = arc4random();
    self.currentEvent = eventCode;
    XMWeakObject(self);
    [self.KVOController observe:_aPlayer keyPath:@"playbackStatus" options:(NSKeyValueObservingOptionNew|NSKeyValueObservingOptionOld) block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        XMStrongObject(self);
        if (eventCode != self.currentEvent) return;
        XMAVPlayerPlaybackStatus status = [change[NSKeyValueChangeNewKey] integerValue];
        switch (status) {
            case XMAVPlayerPlaybackStatusStart:
            {
                [self changePlayStateTo:XMIAdPlayerStateInitial];
            }
                break;
            case XMAVPlayerPlaybackStatusPlaying:
            {
                [self changePlayStateTo:XMIAdPlayerStatePlaying];
                [self p_fireTimer];
            }
                break;
            case XMAVPlayerPlaybackStatusBuffer:
            {
                self->_currentTime = 0;
                [self changePlayStateTo:XMIAdPlayerStateBuffering];
            }
                break;
            case XMAVPlayerPlaybackStatusStop:
            {
                [self p_invalidateTimer];
            }
                break;
            case XMAVPlayerPlaybackStatusEnd:
            {
                if (self.repeat) {
                    [self playerDidPlayFinish:NO];
                    [self resetToStartPoint];
                } else {
                    [self playerDidPlayFinish];
                }
                [self p_invalidateTimer];
            }
                break;
            case XMAVPlayerPlaybackStatusUnknown:
                break;
            case XMAVPlayerPlaybackStatusPause:
            {
                [self changePlayStateTo:XMIAdPlayerStatePause];
            }
                break;
            case XMAVPlayerPlaybackStatusFailed:
            {
            }
                break;
            default:
                break;
        }
        NSLog(@"XMAVPlayerPlaybackStatus = %ld", (long)status);
    }];
    [self.KVOController observe:_aPlayer keyPath:@"status" options:(NSKeyValueObservingOptionNew|NSKeyValueObservingOptionOld) block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        XMStrongObject(self);
        
        XMAVPlayerStatus status = [change[NSKeyValueChangeNewKey] integerValue];
        
        switch (status) {
            case XMAVPlayerStatusFailed:
            case XMAVPlayerStatusUnknown:
            {
                [self playerDidPlayFail];
            }
                break;
            case XMAVPlayerStatusReadyToPlay:
            {
//                [self updateStatus:(XMAdVideoPlayerLayerPrepared)];
                [self playerDidReadyToPlay];
            }
                break;
            default:
                break;
        }
    }];
    
    [self listenForName:UIApplicationDidEnterBackgroundNotification object:nil queue:nil usingBlock:^(NSNotification *note) {
        XMStrongObject(self);
        [self pause];
    }];
}

- (void)p_invalidateTimer {
    if (!self.timer) return;
    [self.timer invalidate];
    self.timer = nil;
}

- (void)p_fireTimer {
    if (self.timer) return;
    self.timer =  [XMTimer scheduledTimerWithTimeInterval:0.5 target:self selector:@selector(p_refreshState) userInfo:nil repeats:YES];
}

- (void)p_refreshState {
    NSTimeInterval currentTime = self.aPlayer.currentTime;
    if (self.delegate && [self.delegate respondsToSelector:@selector(player:playTimeDidChanged:)]) {
        [self.delegate player:self playTimeDidChanged:currentTime];
    }
}

- (void)setURL:(NSURL *)url
    playerView:(UIView *)playerView
{
    if (![playerView isKindOfClass:XMAVPlayerView.class]) {
        return;
    }
    self.aPlayerView = (XMAVPlayerView *)playerView;
    
    [self setURL:url];
    self.aPlayerView.player = self.aPlayer;
}


- (void)startPlayWithURL:(NSURL *)url
              playerView:(UIView *)playerView
{
    [self setURL:url playerView:playerView];
}

- (void)getVideoSize
{
    
}

/**
 设置播放源，会自动停止当前播放的视频
 */
- (void)setURL:(NSURL *)url
{
    self.item = [XMAVPlayerItem playerItemWithURL:url mediaType:XMAVPlayerItemVideo];
    self.item.enableMixVoice = YES;
    XMAVPlayer *player = [XMAVPlayer playerWithPlayerItem:self.item];
    player.volume = self.volume;
    if (self.scalingMode == XMIAdNewPlayerScalingModeAspectFit) {
        player.scalingMode = XMAVPlayerScalingModeAspectFit;
    } else {
        player.scalingMode = XMAVPlayerScalingModeFill;
    }
    self.aPlayer = player;
    if (player.status == XMAVPlayerStatusReadyToPlay) {
        [self changePlayStateTo:XMIAdPlayerStateReady];
    }
    [self addVideoPlayerKVO];
}

static bool hasShowDebugToast = NO;
/**
 播放
 */
- (void)play
{
    _wantToPlay = YES;
    if (!_playerDidLoad) {
        return;
    }
    [self.aPlayer play];
    if (self.volume > 0.1) {
        [[XMAppSession appSession] requestActive:YES withAudioAction:self];
        self.requestActive = YES;
    }
    
    if ([XMIAdManager sharedInstance].isDebug && !hasShowDebugToast) {
        hasShowDebugToast = YES;
        NSString *message = @"广告-新版播放器-双排";
        [XMIAdHelper showAlertWithMessage:message delayTime:0];
    }
}
/**
 暂停
 */
- (void)pause
{
    _wantToPlay = NO;
    if (!_playerDidLoad) {
        return;
    }
    [self.aPlayer pause];
}
/**
 停止
 */
- (void)stop
{
    [self.aPlayer stop];
}

- (void)removeActiveControl {
    if (self.requestActive) {
        [[XMAppSession appSession] requestActive:NO withOption:XMAppSessionNotifyOptionsOtherResume audioAction:self];
        self.requestActive = NO;
    }
}

/**
 time 单位秒
 */
- (void)seek:(CGFloat)time
{
    if (!_playerDidLoad) {
        return;
    }
    [self.aPlayer seekToTime:time];
}
/**
 重播，等价于seek:0
 */
- (void)replay
{
    if (!_playerDidLoad) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self resetToStartPoint];
            return;
        });
        return;
    }
    [self resetToStartPoint];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self.aPlayer play];
    });
}
/**
 是否正在播放
 */
- (BOOL)isPlaying
{
    return self.aPlayer.isPlaying;
}

- (void)setVolume:(CGFloat)volume {
    if (volume < 0) {
        volume = 0;
    }
    else if (volume > 1) {
        volume = 1;
    }
    _volume             = volume;
    if (self.isPlaying
        && volume > 0.1) {
        [[XMAppSession appSession] requestActive:YES withAudioAction:self];
        self.requestActive = YES;
    }
    self.aPlayer.volume = volume;
}

/// 获取视频的size
- (CGSize)getMediaSize
{
    return CGSizeMake(self.aPlayer.currentResolustion.xmavr_width, self.aPlayer.currentResolustion.xmavr_height);
}


- (void)resetToStartPoint {
    _wantToPlay = YES;
    if (!_playerDidLoad) {
        return;
    }
    [self seek:0];
//    [self pause];
//    [self updateStatus:(XMAdVideoPlayerPlayEnd)];
    if (self.repeat) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self play];
        });
    } else {
        [self pause];
    }
}


/**
 player达到启播状态 初始化总时间等
 */
- (void)playerDidReadyToPlay {
    _duration = self.item.duration;
    _playerDidLoad = YES;
    [self changePlayStateTo:XMIAdPlayerStateReady];
    if (_wantToPlay) {
        [self.aPlayer play];
    }
}

/**
 播放失败处理
 */
- (void)playerDidPlayFail {
    [self changePlayStateTo:XMIAdPlayerStateFailed];
    if (self.delegate && [self.delegate respondsToSelector:@selector(player:failWithError:)]) {
        NSError *error = self.aPlayer.error;
        if (!error) {
            error = [XMIAdError emptyDataError];
        }
        [self.delegate player:self failWithError:error];
    }
}

/**
 播放结束处理
 */
- (void)playerDidPlayFinish {
    [self playerDidPlayFinish:YES];
}
- (void)playerDidPlayFinish:(BOOL)willStopped {
    if (willStopped) {
        [self changePlayStateTo:XMIAdPlayerStateStopped];
    }
    if (self.delegate && [self.delegate respondsToSelector:@selector(player:playDidFinish:)]) {
        [self.delegate player:self playDidFinish:nil];
    }
}


/**
 播放状态处理
 */
- (void)changePlayStateTo:(XMIAdPlayerPlayState)state {
    _state = state;
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(player:playStateDidChanged:)]) {
        [self.delegate player:self playStateDidChanged:state];
    }
}

- (void)cancelAll {
    [self resetCurrentPlayer];
}

- (void)resetCurrentPlayer {
    // aPlayer
    // 清除加载超时事件
    [self removeAllNotificationBlock];
    if (self.aPlayer) {
        [self.KVOController unobserveAll];
        [self pause];
        [self.aPlayer replaceCurrentItemWithPlayerItem:nil];
    }
    self.aPlayer        = nil;
    self.item           = nil;
    _playerDidLoad      = NO;
    _wantToPlay         = NO;
    [self p_invalidateTimer];
}

- (void)dealloc {
    [self cancelAll];
}


#pragma mark - XMAppAudioAction

-(BOOL)appSessionWasPreemptedByOther {
    [self pauseWhenInterrupt];
    return YES;
}

-(void)appSessionOnRouteChange:(XMAppSessionRouteChangeReason)routeChangeReason type:(XMAppSessionRouteType)routeType {
    if (routeChangeReason == XMAppSessionRouteChangeReasonOldDeviceUnavailable
        && routeType == XMAppSessionRouteTypeHeadphone) {
        [self pauseWhenInterrupt];
    }
}

-(void)appSessionOnInterruptted:(XMAppSessionInterrupt)intrrupt {
    if (intrrupt == XMAppSessionInterruptBegin) {
        [self pauseWhenInterrupt];
    }
}

- (void)pauseWhenInterrupt {
    if (self.muteWhenInterrupt) {
        self.volume = 0;
    }
    else {
        [self pause];
    }
}

@end
