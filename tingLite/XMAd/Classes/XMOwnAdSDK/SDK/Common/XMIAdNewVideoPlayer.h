//
//  XMIAdNewVideoPlayer.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/1/21.
//

#import <Foundation/Foundation.h>
#import <AVFoundation/AVFoundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 播放器状态
 */
typedef NS_ENUM(NSInteger, XMIAdPlayerPlayState) {
    XMIAdPlayerStateInitial   = 0,
    XMIAdPlayerStateReady     = 1,
    XMIAdPlayerStateBuffering = 2,
    XMIAdPlayerStatePlaying   = 3,
    XMIAdPlayerStateStopped   = 4,
    XMIAdPlayerStatePause     = 5,
    XMIAdPlayerStateFailed    = 6,
    XMIAdPlayerStateUnknown   = 99
};

/*!
 XMAVPlayer view scaling mode
 */
typedef NS_ENUM(NSInteger, XMIAdNewPlayerScalingMode) {
    XMIAdNewPlayerScalingModeNone,       // No scaling
    XMIAdNewPlayerScalingModeAspectFit,  // Uniform scale until one dimension fits
    XMIAdNewPlayerScalingModeAspectFill, // Uniform scale until the movie fills the visible bounds. One dimension may have clipped contents
    XMIAdNewPlayerScalingModeFill        // Non-uniform scale. Both render dimensions will exactly match the visible bounds
};

@protocol XMIAdVideoPlayerDelegate, XMIAdNewVideoPlayer;

@interface XMIAdNewVideoView : UIView

@property (nonatomic, strong, nullable) AVPlayerLayer *videoLayer;
- (void)advanceCreatePlayer;
- (void)preparedWithPlayer:(AVPlayer *)player;

+ (UIView *)createVideoView;

@end

@interface XMIAdNewVideoPlayer : NSObject
{
    XMIAdPlayerPlayState _state;
    CGFloat              _volume;
    CGFloat              _currentTime;
    CGFloat              _duration;
}


+ (XMIAdNewVideoPlayer *)createVideoPlayer;


/**
 播放UI
 */
@property (nonatomic, strong, readonly) UIView *view;
/**
 播放状态
 */
@property (nonatomic, assign) XMIAdPlayerPlayState state;
/**
 contentMode
 */
@property (nonatomic, assign) XMIAdNewPlayerScalingMode scalingMode;
/**
 视频时长，单位秒
 */
@property (nonatomic, assign, readonly) CGFloat duration;
/**
 当前播放时间
 */
@property (nonatomic, assign, readonly) CGFloat currentTime;
/**
 音量，范围(0-1)，默认0
 */
@property (nonatomic, assign) CGFloat volume;
/**
 是否循环播放，默认否
 */
@property (nonatomic, assign) BOOL repeat;
/**
 代理
 */
@property (nonatomic, weak) id<XMIAdVideoPlayerDelegate> delegate;

@property (nonatomic, strong, nullable) NSURL *currentURL;

/// 异步回调时解决多个任务的时机
@property (nonatomic, strong) NSString *identifier;

/**
 初始化
 @param URL :本地或远程链接
 */
- (instancetype)initWithURL:(NSURL *)URL;

- (instancetype)initWithURL:(NSURL *)URL
                 playerView:(UIView *)playerView;

- (void)setURL:(NSURL *)url
    playerView:(UIView *)playerView;

- (void)startPlayWithURL:(NSURL *)url
              playerView:(UIView *)playerView;

- (void)getVideoSize;

/**
 设置播放源，会自动停止当前播放的视频
 */
- (void)setURL:(NSURL *)url;
/**
 播放
 */
- (void)play;
/**
 暂停
 */
- (void)pause;
/**
 停止
 */
- (void)stop;
/**
 time 单位秒
 */
- (void)seek:(CGFloat)time;
/**
 重播，等价于seek:0
 */
- (void)replay;
/**
 是否正在播放
 */
- (BOOL)isPlaying;

/**
 注销XMAppSession
 */
- (void)removeActiveControl;

/// 获取视频的size
- (CGSize)getMediaSize;

@end

@protocol XMIAdVideoPlayerDelegate <NSObject>

@optional
- (void)player:(XMIAdNewVideoPlayer *)player playStateDidChanged:(XMIAdPlayerPlayState)state;
- (void)player:(XMIAdNewVideoPlayer *)player failWithError:(NSError *)error;
- (void)player:(XMIAdNewVideoPlayer *)player playTimeDidChanged:(CGFloat)currentTime;
- (void)player:(XMIAdNewVideoPlayer *)player playDidFinish:(nullable NSError *)error;
- (void)player:(XMIAdNewVideoPlayer *)player getPlayerSize:(CGSize)playerSize;

@end

NS_ASSUME_NONNULL_END
