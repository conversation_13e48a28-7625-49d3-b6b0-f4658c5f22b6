//
//  XMIAdVideoPlayer.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/26.
//

#import "XMIAdVideoPlayer.h"

#import "XMIAdMacro.h"
#import "XMIAdError.h"


@implementation XMIAdVideoView


- (void)advanceCreatePlayer
{
    AVPlayerLayer *layer = [[AVPlayerLayer alloc] init];
    layer.videoGravity = AVLayerVideoGravityResizeAspect;
    [self.layer addSublayer:layer];
    self.videoLayer = layer;
}



- (void)preparedWithPlayer:(AVPlayer *)player {
    if (player == nil) {
        return;
    }
    
    if (self.videoLayer != nil) {
        [self.videoLayer removeFromSuperlayer];
        self.videoLayer = nil;
    }
    AVPlayerLayer *layer = [[AVPlayerLayer alloc] init];
    layer.videoGravity = AVLayerVideoGravityResizeAspect;
    layer.player = player;
    [self.layer addSublayer:layer];
    self.videoLayer = layer;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    if (self.videoLayer != nil) {
        self.videoLayer.frame = self.bounds;
    }
}

@end


@interface XMIAdVideoPlayer ()

@property (nonatomic, strong) AVPlayer *player;
@property (nonatomic, strong) AVPlayerItem *currentItem;
@property (nonatomic, strong) XMIAdVideoView *videoView;
@property (nonatomic, strong) id playTimeObserver;
@property (nonatomic, assign) BOOL playWhenReady;
@property (nonatomic, assign) CGSize playerMediaSize;

@end

@implementation XMIAdVideoPlayer

- (instancetype)init {
    self = [super init];
    if (self) {
        [self commonInit];
    }
    
    return self;
}

- (instancetype)initWithURL:(NSURL *)URL {
    self = [super init];
    if (self) {
        [self commonInit];
        [self setURL:URL playerView:self.videoView];
    }
    return self;
}

- (instancetype)initWithURL:(NSURL *)URL
                 playerView:(XMIAdVideoView *)playerView
{
    self = [super init];
    if (self) {
        [self commonInit];
        [self setURL:URL playerView:playerView];
    }
    return self;
}






- (void)commonInit {
    self.volume = 0.0;
    self.playWhenReady = NO;
    self.repeat = NO;
    _state = XMIAdPlayerStateUnknown;
}

- (void)setURL:(NSURL *)url
    playerView:(UIView *)playerView
{
    [self createPlayerWithURL:url playerView:playerView];
}

- (void)setURL:(NSURL *)url {
    [self createPlayerWithURL:url];
}

- (void)play {
    if (self.player == nil) {
        return;
    }
    
    self.playWhenReady = YES;
    [self.player play];
}

- (void)pause {
    if (self.player == nil) {
        return;
    }
    
    self.playWhenReady = NO;
    [self.player pause];
}

- (void)stop {
    [self pause];
}

- (void)seek:(CGFloat)time {
    if (self.player == nil) {
        return;
    }
    if (time < 0 || time > self.duration) {
        return;
    }
    
    @try {
        [self.player seekToTime:CMTimeMake(time * 600, 600) toleranceBefore:kCMTimeZero toleranceAfter:kCMTimeZero];
        if (self.playWhenReady) {
            [self.player play];
        }
    } @catch (NSException *e) {
        XMILog(@"seek with exception!!!");
    }
}

- (void)replay {
    self.playWhenReady = YES;
    [self seek:0];
}

- (BOOL)isPlaying {
    if (self.player == nil) {
        return NO;
    }
    return self.player.rate > 0.1;
}

- (void)setVolume:(CGFloat)volume {
    if (volume < 0 || volume > 1.0) {
        return;
    }
    _volume = volume;
    if (self.player == nil) {
        return;
    }
    
    self.player.volume = volume;
}

- (UIView *)view {
    return (UIView *)self.videoView;
}

/**
 创建播放器，并进行相关初始化
 */
- (void)createPlayerWithURL:(NSURL *)url {
    [self createPlayerWithURL:url playerView:self.videoView];
}

/**
 创建播放器，并进行相关初始化
 */
- (void)createPlayerWithURL:(NSURL *)url
                 playerView:(UIView *)playerView
{
    if (![playerView isKindOfClass:XMIAdVideoView.class]) {
        return;
    }
    XMIAdVideoView *videoView = (XMIAdVideoView *)playerView;
    [self cleanIfNeed];
    self.currentURL = url;
    self.currentItem = [AVPlayerItem playerItemWithURL:url];
    self.player = [AVPlayer playerWithPlayerItem:self.currentItem];
    self.player.volume = self.volume;
    self.player.actionAtItemEnd = AVPlayerActionAtItemEndPause;
    [videoView preparedWithPlayer:self.player];
    [self addPlayerObserves];
}





- (void)startPlayWithURL:(NSURL *)url
              playerView:(XMIAdVideoView *)playerView
{
    [self cleanIfNeed];
    self.currentURL = url;
    self.currentItem = [AVPlayerItem playerItemWithURL:url];
    if (!self.currentItem) {
        return;
    }
    if (!self.player) {
        self.player = [AVPlayer playerWithPlayerItem:self.currentItem];
    }else{
        [self.player replaceCurrentItemWithPlayerItem:self.currentItem];
    }
    playerView.videoLayer.player = self.player;
    self.player.volume = self.volume;
    self.player.actionAtItemEnd = AVPlayerActionAtItemEndPause;
    if (self.player.status == AVPlayerStatusReadyToPlay) {
        [self changePlayStateTo:XMIAdPlayerStateReady];
    }
    [self addPlayerObserves];
}


- (void)getVideoSize
{
    __weak typeof(self) weakSelf = self;
    dispatch_async(dispatch_get_global_queue(0, 0), ^{
        NSArray *tracks = [weakSelf.currentItem.asset tracksWithMediaType:AVMediaTypeVideo];
        if (tracks.count) {
            AVAssetTrack *videoTrack = tracks.firstObject;
            weakSelf.playerMediaSize = videoTrack.naturalSize;
        }else{
            weakSelf.playerMediaSize = CGSizeZero;
        }
        dispatch_async(dispatch_get_main_queue(), ^{
            if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(player:getPlayerSize:)]) {
                [weakSelf.delegate player:weakSelf getPlayerSize:weakSelf.playerMediaSize];
            }
        });
    });
}

/// 获取视频的size
- (CGSize)getMediaSize
{
    return self.playerMediaSize;
}



- (void)cleanIfNeed {
    if (self.player != nil) {
        [self pause];
        [self.player replaceCurrentItemWithPlayerItem:nil];
    }
    [self removePlayerObserves];
    
    self.player = nil;
    self.currentItem = nil;
    self.currentURL = nil;
    _currentTime = 0;
    [self changePlayStateTo:XMIAdPlayerStateInitial];
}

- (XMIAdVideoView *)videoView {
    if (_videoView == nil) {
        _videoView = [[XMIAdVideoView alloc] init];
        _videoView.backgroundColor = [UIColor blackColor];
    }
    return _videoView;
}

/**
 player达到启播状态 初始化总时间等
 */
- (void)playerDidReadyToPlay {
    _duration = CMTimeGetSeconds(self.currentItem.duration);
    [self changePlayStateTo:XMIAdPlayerStateReady];
    if (self.playWhenReady) {
        [self.player play];
    }
}
/**
 播放失败处理
 */
- (void)playerDidPlayFail {
    [self changePlayStateTo:XMIAdPlayerStateFailed];
    if (self.delegate && [self.delegate respondsToSelector:@selector(player:failWithError:)]) {
        NSError *error = self.player.error;
        if (!error) {
            error = [XMIAdError emptyDataError];
        }
        [self.delegate player:self failWithError:error];
    }
}
/**
 播放结束处理
 */
- (void)playerDidPlayFinish {
    [self playerDidPlayFinish:YES];
}
- (void)playerDidPlayFinish:(BOOL)willStopped {
    if (willStopped) {
        [self changePlayStateTo:XMIAdPlayerStateStopped];
    }
    if (self.delegate && [self.delegate respondsToSelector:@selector(player:playDidFinish:)]) {
        [self.delegate player:self playDidFinish:nil];
    }
}

/**
 播放状态处理
 */
- (void)changePlayStateTo:(XMIAdPlayerPlayState)state {
    _state = state;
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(player:playStateDidChanged:)]) {
        [self.delegate player:self playStateDidChanged:state];
    }
}

/**
 播放进度处理，时间单位 秒
 */
- (void)changePlayTimeTo:(CGFloat)time {
    if (time < 0) {
        _currentTime = 0;
    } else if (time > self.duration) {
        _currentTime = self.duration;
    } else {
        _currentTime = time;
    }
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(player:playTimeDidChanged:)]) {
        [self.delegate player:self playTimeDidChanged:_currentTime];
    }
}

/**
 播放相关监听
 */
- (void)addPlayerObserves {
    // 1.播放状态
    [self.currentItem addObserver:self forKeyPath:@"status" options:NSKeyValueObservingOptionNew context:nil];
    // 2.缓冲情况
    [self.currentItem addObserver:self forKeyPath:@"playbackLikelyToKeepUp" options:NSKeyValueObservingOptionNew context:nil];
    
    @weakify(self)
    // 3.播放进度
    self.playTimeObserver = [self.player addPeriodicTimeObserverForInterval:CMTimeMake(1, 4) queue:dispatch_get_main_queue() usingBlock:^(CMTime time) {
        @strongify(self)
        if (!self) {
            return;
        }
        [self changePlayTimeTo:CMTimeGetSeconds(time)];
    }];
    // 4.播放rate
    [self.player addObserver:self forKeyPath:@"rate" options:NSKeyValueObservingOptionNew context:nil];
    
    // 5.播放结束
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handlePlayToEndNotification:) name:AVPlayerItemDidPlayToEndTimeNotification object:nil];
    // 6.播放失败
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleFailedPlayToEndNotification:) name:AVPlayerItemFailedToPlayToEndTimeNotification object:nil];
    // 7.播放中断
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handlePlayInterruptionNotification:) name:AVAudioSessionInterruptionNotification object:[AVAudioSession sharedInstance]];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handlePlayInterruptionNotification:) name:UIApplicationDidBecomeActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handlePlayInterruptionNotification:) name:UIApplicationWillResignActiveNotification object:nil];
}
- (void)removePlayerObserves {
    if (self.currentItem != nil) {
        // 1
        [self.currentItem removeObserver:self forKeyPath:@"status" context:nil];
        // 2
        [self.currentItem removeObserver:self forKeyPath:@"playbackLikelyToKeepUp" context:nil];
    }
    // 3
    if (self.playTimeObserver != nil) {
        [self.player removeTimeObserver:self.playTimeObserver];
        self.playTimeObserver = nil;
    }
    // 4
    if (self.player != nil) {
        [self.player removeObserver:self forKeyPath:@"rate" context:nil];
    }
    // 5
    [[NSNotificationCenter defaultCenter] removeObserver:self name:AVPlayerItemDidPlayToEndTimeNotification object:nil];
    // 6
    [[NSNotificationCenter defaultCenter] removeObserver:self name:AVPlayerItemFailedToPlayToEndTimeNotification object:nil];
    // 7
    [[NSNotificationCenter defaultCenter] removeObserver:self name:AVAudioSessionInterruptionNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIApplicationDidBecomeActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIApplicationWillResignActiveNotification object:nil];
}
/**
 监听回调
 */
- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSKeyValueChangeKey,id> *)change context:(void *)context {
    if ([object isKindOfClass:[AVPlayerItem class]]) {
        [self playerItemValueChange:change forKey:keyPath];
    } else if ([object isKindOfClass:[AVPlayer class]]) {
        [self playerValueChange:change forKey:keyPath];
    }
}
/**
 AVPlayerItem 属性变化
 */
- (void)playerItemValueChange:(NSDictionary<NSKeyValueChangeKey,id> *)change forKey:(NSString *)key {
    if ([key isEqualToString:@"status"]) {
        AVPlayerItemStatus status = [change[NSKeyValueChangeNewKey] integerValue];
        [self playerItemStatusChanged:status];
    } else if ([key isEqualToString:@"playbackLikelyToKeepUp"]) {
        BOOL playbackLikelyToKeepUp = [change[NSKeyValueChangeNewKey] boolValue];
        [self playerItemKeepUpChanged:playbackLikelyToKeepUp];
    }
}
/**
 AVPlayer 属性变化
 */
- (void)playerValueChange:(NSDictionary<NSKeyValueChangeKey,id> *)change forKey:(NSString *)key {
    if ([key isEqualToString:@"rate"]) {
        float rate = [change[NSKeyValueChangeNewKey] floatValue];
        [self playerRateChanged:rate];
    }
}

/**
 AVPlayerItem status属性变化
 */
- (void)playerItemStatusChanged:(AVPlayerItemStatus)status {
    switch (status) {
        case AVPlayerItemStatusReadyToPlay:
            [self playerDidReadyToPlay];
            break;
        case AVPlayerItemStatusFailed:
        case AVPlayerItemStatusUnknown:
            [self playerDidPlayFail];
            break;
            
        default:
            break;
    }
}

/**
 AVPlayerItem isPlaybackLikelyToKeepUp属性变化
 */
- (void)playerItemKeepUpChanged:(BOOL)isPlaybackLikelyToKeepUp {
    if (!self.playWhenReady) {
        return;
    }
    // 卡顿时暂停等待缓冲
    if (isPlaybackLikelyToKeepUp) {
        [self.player play];
    } else {
        [self.player pause];
    }
}

- (void)playerRateChanged:(float)rate {
    if (!self.playWhenReady) {
        [self changePlayStateTo:XMIAdPlayerStatePause];
        return;
    }
    
    // 缓冲中
    if (!self.currentItem.isPlaybackLikelyToKeepUp) {
        _currentTime = 0;
        [self changePlayStateTo:XMIAdPlayerStateBuffering];
        return;
    }
    
    // 播放中
    if (rate > 0.01) {
        [self changePlayStateTo:XMIAdPlayerStatePlaying];
        return;
    }
    
    [self changePlayStateTo:XMIAdPlayerStatePause];
}

/**
 播放结束通知
 */
- (void)handlePlayToEndNotification:(NSNotification *)notification {
    if (self.repeat) {
        [self playerDidPlayFinish:NO];
        [self seek:0];
    } else {
        [self playerDidPlayFinish];
    }
}
- (void)handleFailedPlayToEndNotification:(NSNotification *)notification {
    [self changePlayStateTo:XMIAdPlayerStateFailed];
}

/**
 播放中断通知
 */
- (void)handlePlayInterruptionNotification:(NSNotification *)notification {
    static bool isAudioSessionInterrupted = false;
    static bool resumeOnBecomingActive = false;
    static bool pauseOnResignActive = false;

    if ([notification.name isEqualToString:AVAudioSessionInterruptionNotification]) {
        NSInteger reason = [[[notification userInfo] objectForKey:AVAudioSessionInterruptionTypeKey] integerValue];
        if (reason == AVAudioSessionInterruptionTypeBegan) {
            isAudioSessionInterrupted = true;

            if ([UIApplication sharedApplication].applicationState != UIApplicationStateActive) {
                XMILog(@"AVAudioSessionInterruptionTypeBegan, application != UIApplicationStateActive, pause");
                [self pause];
            }
            else {
                XMILog(@"AVAudioSessionInterruptionTypeBegan, application == UIApplicationStateActive, pauseOnResignActive = true");
                pauseOnResignActive = true;
            }
        }
        else if (reason == AVAudioSessionInterruptionTypeEnded) {
            isAudioSessionInterrupted = false;

            if ([UIApplication sharedApplication].applicationState == UIApplicationStateActive) {
                XMILog(@"AVAudioSessionInterruptionTypeEnded, application == UIApplicationStateActive, resume");
                NSError *error = nil;
                [[AVAudioSession sharedInstance] setActive:YES error:&error];
                [self play];
                
                XMILog(@"AVAudioSessionInterruptionTypeEnded, was paused, try to resume it.");
            }
            else {
                XMILog(@"AVAudioSessionInterruptionTypeEnded, application != UIApplicationStateActive, resumeOnBecomingActive = true");
                resumeOnBecomingActive = true;
            }
        }
    }
    else if ([notification.name isEqualToString:UIApplicationWillResignActiveNotification]) {
        XMILog(@"UIApplicationWillResignActiveNotification");
        if (pauseOnResignActive) {
            pauseOnResignActive = false;
            XMILog(@"UIApplicationWillResignActiveNotification, pause");
            [self pause];
        }
    }
    else if ([notification.name isEqualToString:UIApplicationDidBecomeActiveNotification]) {
        XMILog(@"UIApplicationDidBecomeActiveNotification");
        if (resumeOnBecomingActive) {
            resumeOnBecomingActive = false;
            XMILog(@"UIApplicationDidBecomeActiveNotification, play");
            NSError *error = nil;
            BOOL success = [[AVAudioSession sharedInstance] setCategory:AVAudioSessionCategoryPlayback error:&error];
            if (!success) {
                XMILog(@"Fail to set audio session.");
                return;
            }
            [[AVAudioSession sharedInstance] setActive:YES error:&error];
            [self play];
        } else if (isAudioSessionInterrupted) {
            XMILog(@"Audio session is still interrupted, resume it!");
            isAudioSessionInterrupted = false;
            NSError *error = nil;
            BOOL success = [[AVAudioSession sharedInstance] setCategory:AVAudioSessionCategoryPlayback error:&error];
            if (!success) {
                XMILog(@"Fail to set audio session.");
                return;
            }
            [[AVAudioSession sharedInstance] setActive:YES error:&error];
            [self play];
        }
    }
}

- (void)dealloc {
    [self cleanIfNeed];
}

@end
