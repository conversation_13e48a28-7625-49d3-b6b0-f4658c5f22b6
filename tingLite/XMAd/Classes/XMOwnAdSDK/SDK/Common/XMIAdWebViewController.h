//
//  XMIAdWebViewController.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/25.
//

#import <UIKit/UIKit.h>
#import "XMIWebVCProtocol.h"

NS_ASSUME_NONNULL_BEGIN

@interface XMIAdWebViewController : UIViewController<XMIWebVCProtocol>

@property (nonatomic, copy) void (^viewDidLoadHandler)(void);
@property (nonatomic, copy) void (^viewWillDealloc)(void);
@property (nonatomic, copy) void (^finishLoadingHandler)(BOOL success, NSError *_Nullable error);
@property (nonatomic, copy) void (^firstPaintHandler)();

- (instancetype)initWithURL:(NSURL *)url;
- (void)goBack;
- (void)reload;

@end

NS_ASSUME_NONNULL_END
