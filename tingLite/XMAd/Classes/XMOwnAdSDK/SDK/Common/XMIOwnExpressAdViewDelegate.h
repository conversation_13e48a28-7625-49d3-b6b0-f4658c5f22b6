//
//  XMIOwnExpressAdViewDelegate.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/11/3.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "XMIAdNewVideoPlayer.h"

NS_ASSUME_NONNULL_BEGIN

@class XMIExpressAdView;

@protocol XMIOwnExpressAdViewDelegate <NSObject>

@optional
- (void)expressAdViewDidRender:(XMIExpressAdView *)adView hasNeedRefresh:(BOOL)isNeedRefresh;
- (void)expressAdView:(XMIExpressAdView *)adView didRenderFailWithError:(NSError *)error;
- (void)expressAdView:(XMIExpressAdView *)adView adViewWillShow:(UIView *)aView;
- (void)expressAdView:(XMIExpressAdView *)adView adViewDidClick:(UIView *)aView withUserInfo:(nullable NSDictionary *)userInfo;
- (void)expressAdView:(XMIExpressAdView *)adView sdkNotSupportWithAdView:(XMIExpressAdView *)adView adViewDidClick:(UIView *)aView withUserInfo:(nullable NSDictionary *)userInfo;
- (void)expressAdView:(XMIExpressAdView *)adView playerStateChanged:(XMIAdPlayerPlayState)state;
- (void)expressAdView:(XMIExpressAdView *)adView playerDidPlayFinish:(nullable NSError *)error;
- (void)expressAdView:(XMIExpressAdView *)adView playTimeDidChanged:(CGFloat)currentTime;
- (void)expressAdViewPlayerDidBecomeInvisible:(XMIExpressAdView *)adView;
- (void)expressAdView:(XMIExpressAdView *)adView adViewDidClickedClose:(UIView *)aView;

@end

NS_ASSUME_NONNULL_END
