//
//  XMIAdWebViewController.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/25.
//

#import "XMIAdWebViewController.h"
#import <WebKit/WebKit.h>
#import <Masonry/Masonry.h>

@interface XMIAdWebViewController ()<WKNavigationDelegate>

@property (nonatomic, strong) NSURL *url;
@property (nonatomic, strong) WKWebView *webView;

@property (nonatomic, assign) BOOL navigationBarHidden;

@end

@implementation XMIAdWebViewController

- (instancetype)initWithURL:(NSURL *)url {
    self = [super init];
    if (self) {
        self.url = url;
    }
    return self;
}

- (void)goBack {
    [self.webView goBack];
}

- (void)reload {
    [self.webView reload];
}

- (void)commonInit {
    if (self.navigationController) {
        // 记录导航栏状态
        self.navigationBarHidden = self.navigationController.navigationBarHidden;
        [self.navigationController setNavigationBarHidden:NO animated:YES];
        self.hidesBottomBarWhenPushed = YES;
        [self setupBackButton];
    }
    
    self.webView = [[WKWebView alloc] initWithFrame:CGRectZero];
    self.webView.navigationDelegate = self;
    [self.view addSubview:self.webView];
    
    [self.webView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self.view);
    }];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self commonInit];
    
    if (self.viewDidLoadHandler) {
        self.viewDidLoadHandler();
    }
    
    NSURLRequest *request = [[NSURLRequest alloc] initWithURL:self.url];
    [self.webView loadRequest:request];
}

- (void)dealloc {
    if (self.viewWillDealloc) {
        self.viewWillDealloc();
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    // 恢复导航栏状态
    if (self.navigationController) {
        [self.navigationController setNavigationBarHidden:self.navigationBarHidden animated:YES];
    }
}

- (void)setupBackButton {
    UIButton *backBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [backBtn setTintColor:[UIColor blackColor]];
    [backBtn setTitle:@"返回" forState:UIControlStateNormal];
    [backBtn setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    [backBtn addTarget:self action:@selector(handleBackBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    
    UIBarButtonItem *leftBar = [[UIBarButtonItem alloc] initWithCustomView:backBtn];
    self.navigationItem.leftBarButtonItem = leftBar;
}

- (void)handleBackBtnClick:(id)sender {
    if (self == [self.navigationController.viewControllers firstObject]) {
        [self.navigationController dismissViewControllerAnimated:YES completion:nil];
    } else {
        if (!self.navigationController) {
            [self dismissViewControllerAnimated:YES completion:nil];
        } else {
            [self.navigationController popViewControllerAnimated:YES];
        }
    }
}

#pragma mark - WKNavigationDelegate
- (void)webView:(WKWebView *)webView didFinishNavigation:(null_unspecified WKNavigation *)navigation {
    if (self.finishLoadingHandler) {
        self.finishLoadingHandler(YES, nil);
    }
}

- (void)webView:(WKWebView *)webView didFailNavigation:(null_unspecified WKNavigation *)navigation withError:(NSError *)error {
    if (self.finishLoadingHandler) {
        self.finishLoadingHandler(NO, error);
    }
}

@end
