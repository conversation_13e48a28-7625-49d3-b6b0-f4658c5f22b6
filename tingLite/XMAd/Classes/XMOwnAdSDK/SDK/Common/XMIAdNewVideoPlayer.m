//
//  XMIAdNewVideoPlayer.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/1/21.
//

#import "XMIAdNewVideoPlayer.h"

#import "XMIAdVideoPlayer.h"
#import "XMIAdijkVideoPlayer.h"
#import <XMAVKit/XMAVKit.h>
#import "XMIAdManager.h"
#import <XMCategories/NSObject+XMCommon.h>

@implementation XMIAdNewVideoView

+ (BOOL)isUseijkPlayer
{
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(getConfigCenterWithKey:)]) {
        id object = [manager.delegate getConfigCenterWithKey:@"isUseIJKPlayer"];
        if ([object isKindOfClass:[NSDictionary class]]) {
            NSDictionary *dict = (NSDictionary *)object;
            // 首页双排视频对应的是ijk
            return [dict[@"238"] aktBoolValue];
        }
    }
    return NO;
}

+ (UIView *)createVideoView
{
    if ([self isUseijkPlayer]) {
        XMAVPlayerView *ijkView = [[XMAVPlayerView alloc] initWithFrame:CGRectZero];
        ijkView.hidden = YES;
        return ijkView;
    }else{
        XMIAdVideoView *videoView = [[XMIAdVideoView alloc] initWithFrame:CGRectZero];
        videoView.backgroundColor = [UIColor clearColor];
        [videoView advanceCreatePlayer];
        videoView.hidden = YES;
        return videoView;
    }
}




- (void)advanceCreatePlayer
{
    
}
- (void)preparedWithPlayer:(AVPlayer *)player
{
    
}






@end



@implementation XMIAdNewVideoPlayer


+ (BOOL)isUseijkPlayer
{
    return [XMIAdNewVideoView isUseijkPlayer];
}

+ (XMIAdNewVideoPlayer *)createVideoPlayer
{
    if ([self isUseijkPlayer]) {
        return [[XMIAdijkVideoPlayer alloc] init];
    }else{
        return [[XMIAdVideoPlayer alloc] init];
    }
}





/**
 初始化
 @param URL :本地或远程链接
 */
- (instancetype)initWithURL:(NSURL *)URL
{
    if (self = [super init]) {
        
    }
    return self;
}

- (instancetype)initWithURL:(NSURL *)URL
                 playerView:(UIView *)playerView
{
    if (self = [super init]) {
        
    }
    return self;
}

- (void)setURL:(NSURL *)url
    playerView:(UIView *)playerView
{
    
}

- (void)startPlayWithURL:(NSURL *)url
              playerView:(UIView *)playerView
{
    
}

- (void)getVideoSize
{
    
}

/**
 设置播放源，会自动停止当前播放的视频
 */
- (void)setURL:(NSURL *)url
{
    
}
/**
 播放
 */
- (void)play
{
    
}
/**
 暂停
 */
- (void)pause
{
    
}
/**
 停止
 */
- (void)stop
{
    
}
/**
 time 单位秒
 */
- (void)seek:(CGFloat)time
{
    
}
/**
 重播，等价于seek:0
 */
- (void)replay
{
    
}
/**
 是否正在播放
 */
- (BOOL)isPlaying
{
    return NO;
}

/// 获取视频的size
- (CGSize)getMediaSize
{
    return CGSizeZero;
}

- (void)removeActiveControl {
    
}

@end
