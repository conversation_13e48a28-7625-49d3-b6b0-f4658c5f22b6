//
//  XMIOwnFeedAdModelFactory.m
//  XMAd
//
//  Created by cuiyuanzhe on 2022/3/4.
//

#import "XMIOwnFeedAdModelFactory.h"
#import "XMIOwnFeedAdModel.h"

@implementation XMIOwnFeedAdModelFactory

+ (XMIFeedAdModel *)adModelWithRelatedData:(XMIAdRelatedData *)relatedData
{
    XMIOwnFeedAdModel *adModel = [[XMIOwnFeedAdModel alloc] init];
    adModel.relatedData = relatedData;
    return adModel;
}


@end
