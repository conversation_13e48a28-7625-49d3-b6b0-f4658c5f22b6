//
//  XMIOwnExpressAd.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/13.
//

#import "XMIOwnExpressAd.h"
#import "XMIOwnExpressAdManager.h"

@interface XMIOwnExpressAd ()<XMIOwnExpressAdDelegate>

@property (nonatomic, strong) XMIOwnExpressAdManager *adManager;

@end

@implementation XMIOwnExpressAd
@synthesize rootViewController = _rootViewController;

- (instancetype)initWithSlot:(XMIAdSlot *)adSlot {
    self = [super initWithSlot:adSlot];
    if (self) {
        [self setupAdManager];
    }
    
    return self;
}

- (void)loadAdData {
    [super loadAdData];
    
    [self.adManager loadAdData];
}

/**
 private method
 */
- (void)setupAdManager {
    self.adManager = [[XMIOwnExpressAdManager alloc] initWithSlot:self.adSlot];
    self.adManager.delegate = self;
}

- (void)setRootViewController:(UIViewController *)rootViewController {
    _rootViewController = rootViewController;
    self.adManager.rootViewController = rootViewController;
}

#pragma mark - XMIOwnExpressAdDelegate
- (void)expressAdManager:(XMIOwnExpressAdManager *)manager didLoadData:(XMIAdRelatedData *)relatedData {
    relatedData.rootViewController = self.rootViewController;
    [self didLoadData:@[relatedData]];
}

- (void)expressAdManager:(XMIOwnExpressAdManager *)manager didLoadFailWithError:(NSError *)error {
    [self didLoadFailWithError:error];
}

@end
