//
//  XMIOwnFeedAdViewFactory.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON>z<PERSON> on 2022/3/8.
//

#import "XMIOwnFeedAdViewFactory.h"
#import "XMIOwnFindFeedNativeImageAdView.h"
#import "XMIOwnFindFeedNativeVideoAdView.h"
#import "XMIOwnFindWindowAdView.h"
#import "XMIOwnFeedCreativeAdView.h"
#import "XMIOwnPlayingDraftAdView.h"
#import "XMIOwnPlayingBubbleTextNoArrowAdView.h"
#import "XMIOwnPlayingBubbleTextArrowAdView.h"
#import "XMIOwnPlayingMBubbleTextAdView.h"
#import "XMIOwnFindFeedNativeAdABTestImageView.h"
#import "XMIOwnFindFeedNativeImageListAdView.h"
#import "XMIOwnFindFeedNativeCompositeAdView.h"
#import "XMIOwnFindFeedNativeSocialImageAdView.h"
#import "XMIOwnFindFeedNativeSocialVideoAdView.h"
#import "XMIOwnFindFeedNativeAdABTestVideoView.h"
#import "XMIOwnSimpleCardAdImageView.h"
#import "XMIOwnSimpleCardAdVideoView.h"
#import "XMIOwnFindFeedGXAdView.h"

@implementation XMIOwnFeedAdViewFactory

+ (Class)feedAdViewClasslWithShowStyle:(XMIAdShowStyle)showstyle adType:(XMIAdType)adType subShowStyle:(XMIAdShowSubStyle)subShowStyle
{
    if (adType != XMIAdTypeXM) {
        return nil;
    }
    switch (showstyle) {
        case XMIAdStyleHomeLargeImage:
            if (subShowStyle == XMIAdSubShowSubStyleFeedAB) {
                return [XMIOwnFindFeedNativeAdABTestImageView class];
            } else if (subShowStyle == XMIAdSubShowSubStyleSocial) {
                return [XMIOwnFindFeedNativeSocialImageAdView class];
            }
            return [XMIOwnFindFeedNativeImageAdView class];
            break;
        case XMIAdStyleHomeVideo:
            if (subShowStyle == XMIAdSubShowSubStyleFeedAB) {
                return [XMIOwnFindFeedNativeAdABTestVideoView class];
            } else if (subShowStyle == XMIAdSubShowSubStyleSocial) {
                return [XMIOwnFindFeedNativeSocialVideoAdView class];
            }
            return [XMIOwnFindFeedNativeVideoAdView class];
            break;
        case XMIAdStyleHomeShopWindow:
            return [XMIOwnFindWindowAdView class];
            break;
        case XMIAdStyleHomeBackgroundImage:
            return [XMIOwnFeedCreativeAdView class];
            break;
        case XMIAdStyleDraftSquareImage:
        case XMIAdStyleDraftLargeImage:
            return [XMIOwnPlayingDraftAdView class];
            break;
        case XMIAdStylePlayPageBubbleSmallImage:
        case XMIAdStylePlayPageBubbleBigImage:
            if (subShowStyle == XMIAdSubShowStyleBubbleOnDraft) {
                return [XMIOwnPlayingBubbleTextArrowAdView class];
            } else if (subShowStyle == XMIAdSubShowStyleMPlaying) {
                return [XMIOwnPlayingMBubbleTextAdView class];
            }
            return [XMIOwnPlayingBubbleTextNoArrowAdView class];
            break;
        case XMIAdStyleSimpleCardFeedImage:
            return [XMIOwnSimpleCardAdImageView class];
            break;
        case XMIAdStyleSimpleCardFeedVideo:
            return [XMIOwnSimpleCardAdVideoView class];
            break;
        case XMIAdStyleHomeListImage3_4:
        case XMIAdStyleHomeListImage9_16:
        case XMIAdStyleHomeListImage1_1:
            return [XMIOwnFindFeedNativeImageListAdView class];
            break;
        case XMIAdStyleHomeCompositeBanner9_16:
        case XMIAdStyleHomeCompositeBanner1_1:
        case XMIAdStyleHomeCompositeBanner3_4:
            return [XMIOwnFindFeedNativeCompositeAdView class];
            break;
        case XMIAdStyleGXImage:
        case XMIAdStyleGXVideo:
        case XMIAdStyleGXWebP:
            return [XMIOwnFindFeedGXAdView class];
            break;
        default:
            break;
    }
    return nil;
}


@end
