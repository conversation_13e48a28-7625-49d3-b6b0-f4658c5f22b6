//
//  XMIOwnExpressAdViewFactory.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/12/3.
//

#import "XMIOwnExpressAdViewFactory.h"
//#import "XMIOwnExpressAdDoubleAudioView.h"
#import "XMIOwnExpressAdDoubleView.h"
#import "XMIOwnMixFeedGuessYouLikeAdView.h"
#import "XMIOwnMixFeedGuessYouLikeAdViewD.h"
#import "XMIOwnMixFeedCardAdView.h"

@implementation XMIOwnExpressAdViewFactory

+ (XMIExpressAdView *)expressAdViewWithShowStyle:(XMIAdShowStyle)showstyle
                                           frame:(CGRect)frame {
    if (showstyle == XMIAdStyleHomeMixRowGuessYouLike) {
        XMIOwnMixFeedGuessYouLikeAdView *adView = [[XMIOwnMixFeedGuessYouLikeAdView alloc] initWithFrame:frame];
        return adView;
    } else if (showstyle == XMIAdStyleHomeMixRowDoubleBigImage) {
        XMIOwnMixFeedCardAdView *adView = [[XMIOwnMixFeedCardAdView alloc] initWithFrame:frame];
        return adView;
    } else if (showstyle == XMIAdStyleHomeMixRowGuessYouLikeB) {
        XMIOwnMixFeedGuessYouLikeAdViewB *adView = [[XMIOwnMixFeedGuessYouLikeAdViewB alloc] initWithFrame:frame];
        return adView;
    } else if (showstyle == XMIAdStyleHomeMixRowGuessYouLikeC) {
        XMIOwnMixFeedGuessYouLikeAdViewC *adView = [[XMIOwnMixFeedGuessYouLikeAdViewC alloc] initWithFrame:frame];
        return adView;
    }else if (showstyle == XMIAdStyleHomeMixRowGuessYouLikeD) {
        XMIOwnMixFeedGuessYouLikeAdViewD *adView = [[XMIOwnMixFeedGuessYouLikeAdViewD alloc] initWithFrame:frame];
        return adView;
    }
    
    XMIOwnExpressAdDoubleView *adDoubleView = [[XMIOwnExpressAdDoubleView alloc] initWithFrame:frame];
    return adDoubleView;
    
}

@end
