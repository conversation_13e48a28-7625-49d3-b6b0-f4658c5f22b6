//
//  XMIGDTAdSDKManager.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/6/30.
//

#import "XMIGDTAdSDKManager.h"
#import <GDTMobSDK/GDTSDKConfig.h>

@implementation XMIGDTAdSDKManager

+ (void)initWithAppID:(NSString *)appID {
    [GDTSDKConfig registerAppId:appID];
    [GDTSDKConfig enableDefaultAudioSessionSetting:NO];
}

+ (NSString *)getSDKVersion {
    return [GDTSDKConfig sdkVersion];
}

@end
