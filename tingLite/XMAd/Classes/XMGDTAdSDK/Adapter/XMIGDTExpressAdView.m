//
//  XMIGDTExpressAdView.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/13.
//

#import "XMIGDTExpressAdView.h"
#import <GDTMobSDK/GDTUnifiedNativeAdDataObject.h>
#import <GDTMobSDK/GDTUnifiedNativeAdView.h>
#import "XMICommonUtils.h"
#import "XMIAdDefines.h"
#import "XMIAdViewProtocol.h"
#import "XMIExpressAdSingleView+Internal.h"

@interface XMIGDTExpressAdView ()<XMIAdViewProtocol>

@property (nonatomic, strong) GDTUnifiedNativeAdDataObject *adData;

@end

@implementation XMIGDTExpressAdView
@synthesize isCustom = _isCustom;
@synthesize contentView = _contentView;
@synthesize logoImageView = _logoImageView;
@synthesize titleLabel = _titleLabel;
@synthesize descLabel = _descLabel;
@synthesize videoView = _videoView;
@synthesize dislikeButton = _dislikeButton;
@synthesize bottomView = _bottomView;
@synthesize actionView = _actionView;
@synthesize imgViewArray = _imgViewArray;

- (instancetype)initWithOriginView:(UIView *)originView {
    self = [super initWithOriginView:originView];
    if (self) {
        
    }
    return self;
}

- (instancetype)initWithOriginData:(id)adData {
    self = [super initWithOriginData:adData];
    if (self) {
        [self setupWithOriginData:(GDTUnifiedNativeAdDataObject *)adData];
    }
    return self;
}

- (void)setupWithOriginData:(GDTUnifiedNativeAdDataObject *)adData {
    _isCustom = YES;
    if (adData == nil) {
        return;
    }
    self.adData = adData;
}

- (void)render:(CGSize)size {
    if (_isCustom) {
        [self renderCustom];
    } else {
        [self renderTemplate];
    }
}

/**
 模版渲染
 */
- (void)renderTemplate {
}

/**
 自渲染
 */
- (void)renderCustom {
    if (self.adData == nil) {
        return;
    }
    
    self.frame = CGRectMake(0, 0, [self adWidth], [self adHeight]);
    CGFloat contentWidth = [self contentWidth];
    CGFloat contentHeight = [self contentHeight];
    GDTUnifiedNativeAdView *adRelatedView = (GDTUnifiedNativeAdView *)self.relatedView;
    // content view
    adRelatedView.frame = CGRectMake(16, 0, contentWidth, contentHeight);
    adRelatedView.layer.cornerRadius = 12;
    adRelatedView.layer.masksToBounds = YES;
    _contentView = adRelatedView;
    
    // bottom
    UIView *bottomView = [self defaultBottomView];
    [_contentView addSubview:bottomView];
    _bottomView = bottomView;
    // action
    UIView *actionView = [self defaultActionViewWithText:self.adData.callToAction];
    actionView.frame = CGRectMake(contentWidth - actionView.frame.size.width - 10, contentHeight - 26 - 10, actionView.frame.size.width, actionView.frame.size.height);
    [_contentView addSubview:actionView];
    _actionView = actionView;
    // title
    UILabel *titleLabel = [self defaultTitleViewWithTitle:self.adData.title];
    [_bottomView addSubview:titleLabel];
    _titleLabel = titleLabel;
    // desc
    UILabel *descLabel = [self defaultDescViewWithDesc:self.adData.desc];
    [_contentView addSubview:descLabel];
    _descLabel = descLabel;
    
    // ad content
    [self renderAdContent:CGSizeMake(contentWidth, contentHeight)];
    // logo
    adRelatedView.logoView.frame = CGRectMake(contentWidth - 6 - kGDTLogoImageViewDefaultWidth * 0.85, 6, kGDTLogoImageViewDefaultWidth * 0.85, kGDTLogoImageViewDefaultHeight * 0.85);
    _logoImageView = adRelatedView.logoView;
}
/**
 广告实际内容
 */
- (void)renderAdContent:(CGSize)size {
    GDTUnifiedNativeAdView *adRelatedView = (GDTUnifiedNativeAdView *)self.relatedView;
    // video
    if (self.adData.isVideoAd) {
        // register后logo、mediaView才会生成
        [adRelatedView registerDataObject:self.adData clickableViews:@[self.actionView, self.bottomView]];
        
        adRelatedView.mediaView.frame = CGRectMake(0, 0, size.width, size.height);
        [adRelatedView sendSubviewToBack:adRelatedView.mediaView];
        _videoView = adRelatedView.mediaView;
    }
    // 三小图
    else if (self.adData.isThreeImgsAd) {
        // TODO:信息流暂不支持三小图，简单填充
        CGRect frame = CGRectMake(0, 0, (size.width - 2 * 2) / 3, size.height);
        UIImageView *imageView1 = [[UIImageView alloc] initWithFrame:frame];
        [_contentView insertSubview:imageView1 atIndex:0];
        //
        frame = CGRectOffset(frame, frame.size.width + 2, 0);
        UIImageView *imageView2 = [[UIImageView alloc] initWithFrame:frame];
        [_contentView insertSubview:imageView2 atIndex:0];
        //
        frame = CGRectOffset(frame, frame.size.width + 2, 0);
        UIImageView *imageView3 = [[UIImageView alloc] initWithFrame:frame];
        [_contentView insertSubview:imageView3 atIndex:0];
        _imgViewArray = [[NSArray alloc] initWithObjects:imageView1, imageView2, imageView3, nil];
        [self.adData bindImageViews:_imgViewArray placeholder:[XMICommonUtils imageNamed:@"ad_bkg_default"]];
        
        [adRelatedView registerDataObject:self.adData clickableViews:@[self.actionView, imageView1, imageView2, imageView3, self.bottomView]];
    }
    // 图片
    else {
        UIImageView *bigImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, size.width, size.height)];
        [_contentView insertSubview:bigImageView atIndex:0];
        _imgViewArray = [[NSArray alloc] initWithObjects:bigImageView, nil];
        [self.adData bindImageViews:_imgViewArray placeholder:[XMICommonUtils imageNamed:@"ad_bkg_default"]];
        
        [adRelatedView registerDataObject:self.adData clickableViews:@[self.actionView, bigImageView, self.bottomView]];
    }
}

- (int)getAdShowType {
    XMIAdShowType showType = XMIAdShowTypeImage;
    // video
    if (self.adData.isVideoAd) {
        showType = XMIAdShowTypeVideo;
    }
    // 三小图
    else if (self.adData.isThreeImgsAd) {
        showType = XMIAdShowTypeImage;
    }
    return showType;
}

@end
