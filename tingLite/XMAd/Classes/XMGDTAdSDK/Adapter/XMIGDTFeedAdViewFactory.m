//
//  XMIGDTFeedAdViewFactory.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON>z<PERSON> on 2022/3/8.
//

#import "XMIGDTFeedAdViewFactory.h"
#import "XMIGDTFindFeedNativeAdView.h"
#import "XMIGDTFindFeedNativeAdABTestView.h"
#import "XMIGDTFindFeedNativeAdSocialView.h"

@implementation XMIGDTFeedAdViewFactory

+ (Class)feedAdViewClasslWithShowStyle:(XMIAdShowStyle)showstyle adType:(XMIAdType)adType subShowStyle:(XMIAdShowSubStyle)subShowStyle
{
    if (adType != XMIAdTypeGDT) {
        return nil;
    }
    switch (showstyle) {
        case XMIAdStyleHomeLargeImage:
            if (subShowStyle == XMIAdSubShowSubStyleFeedAB) {
                return [XMIGDTFindFeedNativeAdABTestView class];
            } else if (subShowStyle == XMIAdSubShowSubStyleSocial) {
                return [XMIGDTFindFeedNativeAdSocialView class];
            }
            return [XMIGDTFindFeedNativeAdView class];
            break;
            
        default:
            break;
    }
    return nil;
}


@end
