//
//  XMIGDTCustomExpressAd.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/13.
//

#import "XMIGDTCustomExpressAd.h"
#import <GDTMobSDK/GDTUnifiedNativeAd.h>
#import "XMIAdMacro.h"

@interface XMIGDTCustomExpressAd ()<GDTUnifiedNativeAdDelegate>

@property (nonatomic, strong) GDTUnifiedNativeAd *adManager;

@end

@implementation XMIGDTCustomExpressAd

/**
 public methods
 */
- (instancetype)initWithSlot:(XMIAdSlot *)adSlot {
    self = [super initWithSlot:adSlot];
    if (self) {
        [self setupAdManager];
    }
    return self;
}

- (void)loadAdData {
    [super loadAdData];

    [self.adManager loadAdWithAdCount:self.adSlot.adCount];
}

/**
 private methods
 */
- (void)setupAdManager {
    if (self.adSlot.slotRealBid && self.adSlot.slotAdm.length) {
        self.adManager = [[GDTUnifiedNativeAd alloc] initWithPlacementId:self.adSlot.positionID token:self.adSlot.slotAdm];
    } else {
        self.adManager = [[GDTUnifiedNativeAd alloc] initWithPlacementId:self.adSlot.positionID];
    }
    self.adManager.delegate = self;
}

#pragma mark - GDTUnifiedNativeAdDelegate
- (void)gdt_unifiedNativeAdLoaded:(NSArray<GDTUnifiedNativeAdDataObject *> * _Nullable)unifiedNativeAdDataObjects error:(NSError * _Nullable)error {
    XMILog(@"gdt_unifiedNativeAdLoaded:error:");
    if (error != nil) {
        XMILog(@"%@", error);
        [self didLoadFailWithError:error];
        return;
    }
    
    NSMutableArray *adDataArray = [[NSMutableArray alloc] init];
    for (int i = 0; i < unifiedNativeAdDataObjects.count; i++) {
        GDTUnifiedNativeAdDataObject *dataObject = unifiedNativeAdDataObjects[i];
        XMIAdRelatedData *relatedData = self.adSlot.relatedData;
        relatedData.originData = dataObject;
        [adDataArray addObject:self.adSlot.relatedData];
    }
    [self didLoadData:adDataArray];
}

@end
