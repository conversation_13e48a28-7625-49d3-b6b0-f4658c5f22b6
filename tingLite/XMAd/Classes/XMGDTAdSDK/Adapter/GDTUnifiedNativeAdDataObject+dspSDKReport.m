//
//  GDTUnifiedNativeAdDataObject+dspSDKReport.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/9/15.
//

#import "GDTUnifiedNativeAdDataObject+dspSDKReport.h"
#import "XMIGDTAdSDKManager.h"
#import <XMCategories/NSObject+XMCommon.h>

@implementation GDTUnifiedNativeAdDataObject (dspSDKReport)

- (BOOL)reportDSPSDK
{
    return YES;
}

- (NSDictionary *)dspSDKReportParams
{
    id adModel = [self valueForKey:@"adModel"];
    NSString *appid = [adModel valueForKey:@"itunesId"];
    NSString *appName = [adModel valueForKey:@"appName"];
    NSString *bundleId = [adModel valueForKey:@"packageName"];
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"downloadAppName"] = appName;
    params[@"downloadAppId"] = appid;
    params[@"downloadAppBundleId"] = bundleId;
    params[@"dsp_src"] = @"gdt";
    params[@"dsp_innerversion"] = [XMIGDTAdSDKManager getSDKVersion];
    return [params copy];
}

- (NSDictionary *)materialExtraInfo {
    if (!self.extraInfo.count) return nil;
    
    NSMutableDictionary *params = [NSMutableDictionary new];
    NSDictionary *adInfo = self.extraInfo[@"ad_info"];
    params[@"crid"] = [@([adInfo integerMaybeForKey:@"crid"]) stringValue];
    params[@"ad_examine_type"] = [adInfo stringMaybeForKey:@"ad_examine_type"];
    params[@"title"] = [adInfo stringMaybeForKey:@"title"];
    params[@"description"] = [adInfo stringMaybeForKey:@"description"];
    NSArray *img = [adInfo arrayMaybeForKey:@"img"];
    params[@"img"] = [img.firstObject description];
    params[@"video"] = [adInfo stringMaybeForKey:@"video"];
    params[@"landingpage"] = [adInfo stringMaybeForKey:@"landingpage"];
    params[@"icon"] = [adInfo stringMaybeForKey:@"icon"];
    params[@"advertiser_name"] = [adInfo stringMaybeForKey:@"advertiser_name"];
    params[@"adType"] = @"4";
    return params;
}

@end
