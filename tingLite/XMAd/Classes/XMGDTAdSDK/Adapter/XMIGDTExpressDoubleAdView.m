//
//  XMIGDTExpressDoubleAdView.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/11/29.
//

#import "XMIGDTExpressDoubleAdView.h"
#import "XMIExpressAdBusniessExtraModel.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import "XMIAdRelatedData.h"
#import "XMICommonUtils.h"
#import <Masonry/Masonry.h>
#import "XMIAdError.h"
#import "NSString+XMIUtils.h"
#import "XMIGDTConverter.h"

#import <GDTMobSDK/GDTUnifiedNativeAdDataObject.h>
#import <GDTMobSDK/GDTUnifiedNativeAdView.h>
#import "XMICommonUtils.h"
#import "XMIAdDefines.h"
#import "XMIAdViewProtocol.h"
#import "XMIAdMacro.h"
#import "XMIAnimatedImageView.h"

@interface XMIGDTExpressDoubleAdView()<GDTUnifiedNativeAdViewDelegate, GDTMediaViewDelegate, XMIAdViewProtocol>

@property (nonatomic, strong) GDTUnifiedNativeAdDataObject *adData;
@property (nonatomic, strong) GDTUnifiedNativeAdView *relatedView;
@property (nonatomic, strong) UIView *bottomView;
@property (nonatomic, strong) YYTextLayout *titleLayout;
//@property (nonatomic, strong) UIImageView *gdtCoverImageV;
@property (nonatomic, assign) BOOL isUnClicked;

@end

@implementation XMIGDTExpressDoubleAdView
@synthesize isCustom = _isCustom;

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        _isCustom = YES;
        self.clipsToBounds = YES;
        self.relatedView = [[GDTUnifiedNativeAdView alloc] initWithFrame:self.bounds];
        self.relatedView.delegate = self;
        [self p_setUI];
        [self p_setContrains];
        
    }
    return self;
}

- (UIView *)bottomView
{
    if (!_bottomView) {
        _bottomView = [[UIView alloc] initWithFrame:CGRectZero];
        _bottomView.clipsToBounds = YES;
    }
    return _bottomView;
}

//- (UIImageView *)gdtCoverImageV
//{
//    if (!_gdtCoverImageV) {
//        _gdtCoverImageV = [[UIImageView alloc] initWithFrame:self.bounds];
//        _gdtCoverImageV.hidden = YES;
//    }
//    return _gdtCoverImageV;
//}

- (void)beginUnregisterDataObject
{
    
}

- (void)p_setUI
{
    [self addSubview:self.contentView];
    [self addSubview:self.placeHolderView];
    self.placeHolderView.frame = self.bounds;
    
    [self.contentView addSubview:self.contentMaskView];
    [self.contentView addSubview:self.relatedView];
    [self.relatedView addSubview:self.coverImageView];
    
    [self addSubview:self.titleMaskView];
    [self addSubview:self.titleLabel];
    [self addSubview:self.statusButton];
    [self addSubview:self.closeButton];
}

- (void)p_setContrains
{
    
    [self.titleMaskView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.bottom.right.equalTo(self);
        make.height.mas_equalTo(105);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.mas_left).offset(kXMIExpressAdDoubleVideoViewHoriznalMargin);
        make.bottom.equalTo(self.mas_bottom).offset(-36);
        make.right.equalTo(self.mas_right).offset(-kXMIExpressAdDoubleVideoViewHoriznalMargin);
    }];
    
    [self.statusButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(kXMIExpressAdDoubleVideoViewHoriznalMargin);
        make.bottom.mas_equalTo(-10);
        make.height.mas_equalTo(18);
        make.width.mas_equalTo(0);
    }];
    
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.bottom.equalTo(self).mas_offset(-7);
        make.size.mas_equalTo(CGSizeMake(16, 16));
    }];
}

- (void)refreshWithData:(XMIAdRelatedData *)adData {
    [super refreshWithData:adData];
    if (adData.loadingStatus == XMIAdRelatedLoadingStatusNormal) {
        return;
    }
    self.adData = (GDTUnifiedNativeAdDataObject *)adData.originData;
    if (!self.adData || ![self.adData isKindOfClass:GDTUnifiedNativeAdDataObject.class]) {
        return;
    }
    self.frame = CGRectMake(0, 0, adData.adWidth, adData.adHeight);
    self.contentView.frame = self.bounds;
    self.placeHolderView.frame = self.bounds;
    self.contentMaskView.frame = self.bounds;
    self.relatedView.frame = self.bounds;
    self.coverImageView.frame = self.relatedView.bounds;
    
    
    self.relatedView.viewController = adData.rootViewController;
    
    [self.relatedView unregisterDataObject];
    
    self.relatedView.mediaView.hidden = YES;
    
    [self configTitleShow];
    
    [self startSetUpUIWithModel];
    
    [self renderAdContent];
    
    [self setStatusButtonColorAnimation];
}




- (void)startSetUpUIWithModel
{
    self.titleLabel.hidden = NO;
    self.closeButton.hidden = NO;
    self.titleMaskView.hidden = NO;
    self.coverImageView.hidden = YES;
    self.contentView.hidden = NO;
    self.contentMaskView.hidden = NO;
    if (self.relatedData.loadingStatus != XMIAdRelatedLoadingStatusHasSureSizeRadio) {
        self.placeHolderView.hidden = NO;
        self.contentView.hidden = YES;
    }else{
        self.placeHolderView.hidden = YES;
        self.contentView.hidden = NO;
    }
    
    self.titleLabel.textLayout = self.titleLayout;
    
    [self p_configStatusButton:self.adData];
    [self.contentMaskView sd_setImageWithURL:[NSURL URLWithString:self.adData.imageUrl]];
    if (!self.statusButton.hidden) {
        self.titleLabel.preferredMaxLayoutWidth = self.relatedData.adWidth - kXMIExpressAdDoubleVideoViewHoriznalMargin - kXMIExpressAdDoubleVideoViewHoriznalMargin;
        [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.mas_bottom).offset(-36);
            make.right.equalTo(self.mas_right).offset(-kXMIExpressAdDoubleVideoViewHoriznalMargin);
        }];
    }else{
        self.titleLabel.preferredMaxLayoutWidth = self.relatedData.adWidth - kXMIExpressAdDoubleVideoViewHoriznalMargin - kXMIExpressAdDoubleVideoViewBigHoriznalMargin;
        [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.mas_bottom).offset(-8);
            make.right.equalTo(self.mas_right).offset(-kXMIExpressAdDoubleVideoViewBigHoriznalMargin);
        }];
    }
    if (self.statusButton.hidden && !self.adData.title.length) {
        self.titleMaskView.hidden = YES;
    }else{
        self.titleMaskView.hidden = NO;
    }

}

- (void)configTitleShow
{
    if (self.relatedData.titleTextLayout) {
        self.titleLayout = self.relatedData.titleTextLayout;
    }else{
        YYTextLayout *textLayout = [XMIExpressAdDoubleView getTitleTextLayout:self.adData adViewWidth:self.relatedData.adWidth];
        self.relatedData.titleTextLayout = textLayout;
        self.titleLayout = textLayout;
    }
}



/**
 广告实际内容
 */
- (void)renderAdContent {
    [self setupLogoFrame];
    self.coverImageView.hidden = YES;
    // video
    if (self.adData.isVideoAd) {
        [self.relatedView registerDataObject:self.adData clickableViews:@[self.relatedView,self.titleMaskView, self.titleLabel]];
        self.relatedView.mediaView.delegate = self;
        self.relatedView.mediaView.hidden = NO;
    }
    // 三小图
    else if (self.adData.isThreeImgsAd) {
        self.coverImageView.hidden = NO;
        NSArray *imgViewArray = @[self.coverImageView];
        [self.adData bindImageViews:imgViewArray placeholder:nil];

        [self.relatedView registerDataObject:self.adData clickableViews:@[self.relatedView, self.coverImageView, self.titleLabel, self.statusButton, self.titleMaskView]];
    }
    // 图片
    else {
        self.coverImageView.hidden = NO;
        NSArray *imgViewArray = @[self.coverImageView];
        [self.adData bindImageViews:imgViewArray placeholder:nil];

        [self.relatedView registerDataObject:self.adData clickableViews:@[self.relatedView, self.coverImageView, self.titleLabel, self.statusButton, self.titleMaskView]];
    }
    
    [self didRenderResultWithDataObject:self.adData];
    
}

/// 获取是否是下载
- (BOOL)getAdIsDownloadApp
{
    if ([self.adData isKindOfClass:GDTUnifiedNativeAdDataObject.class]) {
        return self.adData.isAppAd;
    }
    return NO;
}

/// 设置logoview frame
- (void)setupLogoFrame
{
    CGFloat logoWidth = 12 * kGDTLogoImageViewDefaultWidth/kGDTLogoImageViewDefaultHeight;
    self.relatedView.logoView.frame = CGRectMake(self.relatedData.adWidth - logoWidth, 0, logoWidth, 12);
}

- (void)p_configStatusButton:(GDTUnifiedNativeAdDataObject *)model
{
    if (model.callToAction.length) {
        [self.statusButton setTitle:model.callToAction forState:UIControlStateNormal];
        self.statusButton.hidden = NO;
        if (model.callToAction.length == 2) {
            [self.statusButton mas_updateConstraints:^(MASConstraintMaker *make) {
                make.width.mas_equalTo(42);
            }];
        }else{
            [self.statusButton mas_updateConstraints:^(MASConstraintMaker *make) {
                make.width.mas_equalTo(54);
            }];
        }
        
//        if ([model isAppAd]) {
//            /// 下载
//            self.statusButton.backgroundColor = XMI_COLOR_RGB(0x1295FF);
//        }else{
//            self.statusButton.backgroundColor = XMI_COLOR_RGB(0xFF4646);
//        }
        
    }else{
        self.statusButton.hidden = YES;
    }
    
}


- (void)didRenderResultWithDataObject:(GDTUnifiedNativeAdDataObject *)dataObject
{
    self.placeHolderView.hidden = YES;
    CGFloat sourceRadio = dataObject.imageHeight/@(dataObject.imageWidth).floatValue;
    CGFloat originalSourceRadio = sourceRadio;
    BOOL isErrorVideoHorizinal = NO;
    if (sourceRadio > 0 && sourceRadio < 1.4) {
        isErrorVideoHorizinal = YES;
        sourceRadio = 1.4;
    }
    if (sourceRadio > 1.8) {
        sourceRadio = 1.8;
    }
    self.relatedData.sourceRadio = originalSourceRadio;
    self.relatedData.sizeRadio = sourceRadio;
    
    CGFloat adHeight = [XMIGDTExpressDoubleAdView getAdViewHeightWithRelatedData:self.relatedData];
    if (fabs(self.relatedData.adHeight - adHeight) > 5) {
        self.relatedData.loadingStatus = XMIAdRelatedLoadingStatusNormal;
    }
    self.relatedData.adHeight = adHeight;
    CGFloat videoTopMargin = 0;
    CGFloat videoHeight = self.relatedData.adWidth*originalSourceRadio;
    if (isErrorVideoHorizinal) {
        videoTopMargin = (adHeight - videoHeight)/2.0;
    }
    if (self.adData.isVideoAd) {
        GDTUnifiedNativeAdView *adRelatedView = (GDTUnifiedNativeAdView *)self.relatedView;
        adRelatedView.mediaView.frame = CGRectMake(0, 0, self.relatedData.adWidth, videoHeight);
        adRelatedView.frame = CGRectMake(0, videoTopMargin, self.relatedData.adWidth, videoHeight);
    }// 三小图
    else if (self.adData.isThreeImgsAd) {
        self.coverImageView.frame = CGRectMake(0, videoTopMargin, self.relatedData.adWidth, videoHeight);
    }else{
        self.coverImageView.frame = CGRectMake(0, videoTopMargin, self.relatedData.adWidth, videoHeight);
    }
    
    self.relatedData.isDidRender = YES;
    
    BOOL needRefresh = self.relatedData.loadingStatus != XMIAdRelatedLoadingStatusHasSureSizeRadio;
    self.relatedData.loadingStatus = XMIAdRelatedLoadingStatusHasSureSizeRadio;
    [self adViewDidRenderHasNeedRefresh:needRefresh];
}

- (void)closeButtonAction:(UIButton *)button
{
    [self adViewDidClickedClose:button];
}

/**
 获取显示类型 0-静态图 1-git 2-视频 3-备胎图
 */
- (int)getAdShowType {
    XMIAdShowType showType = XMIAdShowTypeImage;
    if (self.adData == nil) {
        return showType;
    }
    if (self.adData.isVideoAd) {
        showType = XMIAdShowTypeVideo;
    }
    else{
        showType = XMIAdShowTypeImage;
    }
    return showType;
}

#pragma mark - GDTUnifiedNativeAdViewDelegate
/**
 广告曝光回调

 @param unifiedNativeAdView GDTUnifiedNativeAdView 实例
 */
- (void)gdt_unifiedNativeAdViewWillExpose:(GDTUnifiedNativeAdView *)unifiedNativeAdView {
    XMILog(@"gdt_unifiedNativeAdViewWillExpose");
}

/**
 广告点击回调

 @param unifiedNativeAdView GDTUnifiedNativeAdView 实例
 */
- (void)gdt_unifiedNativeAdViewDidClick:(GDTUnifiedNativeAdView *)unifiedNativeAdView {
    XMILog(@"gdt_unifiedNativeAdViewDidClick");
    if (!self.isUnClicked) {
        self.isUnClicked = YES;
        __weak typeof(self) weakSelf = self;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            weakSelf.isUnClicked = NO;
            [weakSelf adViewDidClick:unifiedNativeAdView.superview];
        });
    }
    
}

/**
 广告详情页关闭回调

 @param unifiedNativeAdView GDTUnifiedNativeAdView 实例
 */
- (void)gdt_unifiedNativeAdDetailViewClosed:(GDTUnifiedNativeAdView *)unifiedNativeAdView {
    XMILog(@"gdt_unifiedNativeAdDetailViewClosed");
    [self adViewDetailControllerDidClosed];
    
}

/**
 当点击应用下载或者广告调用系统程序打开时调用
 
 @param unifiedNativeAdView GDTUnifiedNativeAdView 实例
 */
- (void)gdt_unifiedNativeAdViewApplicationWillEnterBackground:(GDTUnifiedNativeAdView *)unifiedNativeAdView {
    XMILog(@"gdt_unifiedNativeAdViewApplicationWillEnterBackground");
}

/**
 广告详情页面即将展示回调

 @param unifiedNativeAdView GDTUnifiedNativeAdView 实例
 */
- (void)gdt_unifiedNativeAdDetailViewWillPresentScreen:(GDTUnifiedNativeAdView *)unifiedNativeAdView {
    XMILog(@"gdt_unifiedNativeAdDetailViewWillPresentScreen");
    [self adViewWillPresentScreen];
}

/**
 视频广告播放状态更改回调

 @param unifiedNativeAdView GDTUnifiedNativeAdView 实例
 @param status 视频广告播放状态
 @param userInfo 视频广告信息
 */
- (void)gdt_unifiedNativeAdView:(GDTUnifiedNativeAdView *)unifiedNativeAdView playerStatusChanged:(GDTMediaPlayerStatus)status userInfo:(NSDictionary *)userInfo {
    XMILog(@"gdt_unifiedNativeAdView:playerStatusChanged:");
    XMIPlayerPlayState state = [XMIGDTConverter playStateFromGDTPlayState:status];
    [self adViewPlayerStateChanged:state];
}

#pragma mark - GDTMediaViewDelegate
/**
 用户点击 MediaView 回调，当 GDTVideoConfig userControlEnable 设为 YES，用户点击 mediaView 会回调。
 
 @param mediaView 播放器实例
 */
- (void)gdt_mediaViewDidTapped:(GDTMediaView *)mediaView {
    XMILog(@"gdt_mediaViewDidTapped");
}

/**
 播放完成回调

 @param mediaView 播放器实例
 */
- (void)gdt_mediaViewDidPlayFinished:(GDTMediaView *)mediaView {
    XMILog(@"gdt_mediaViewDidPlayFinished");
    [self adViewPlayerDidPlayFinish:nil];
}
- (void)dealloc
{
    _relatedView = nil;
}
@end

