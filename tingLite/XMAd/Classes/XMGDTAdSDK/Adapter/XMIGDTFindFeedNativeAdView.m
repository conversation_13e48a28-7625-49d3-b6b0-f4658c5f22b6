//
//  XMIGDTFindFeedNativeAdView.m
//  XMAd
//
//  Created by cuiyuanz<PERSON> on 2022/3/7.
//

#import "XMIGDTFindFeedNativeAdView.h"
#import <GDTMobSDK/GDTUnifiedNativeAdDataObject.h>
#import <GDTMobSDK/GDTUnifiedNativeAdView.h>
#import "XMIAdError.h"
#import "XMICommonUtils.h"
#import "XMIAdNativeLogger.h"
#import "XMIGDTConverter.h"
#import "UIView+XMIUtils.h"
#import "XMIAdMacro.h"
#import "XMIAdHelper.h"

@interface XMIGDTFindFeedNativeAdView ()<GDTUnifiedNativeAdViewDelegate>

@property (nonatomic, strong) GDTUnifiedNativeAdDataObject *adData;
@property (nonatomic, strong) GDTUnifiedNativeAdView *relatedView;
@property (nonatomic, strong) UIView *clickView;
@property (nonatomic, weak) UIViewController *rootViewController;

@end

@implementation XMIGDTFindFeedNativeAdView

- (GDTUnifiedNativeAdView *)relatedView
{
    if (!_relatedView) {
        _relatedView = [[GDTUnifiedNativeAdView alloc] initWithFrame:_relatedView.bounds];
        [self.contentView insertSubview:_relatedView belowSubview:self.coverImageView];
        _relatedView.autoresizingMask = UIViewAutoresizingNone;
        _relatedView.viewController = self.rootViewController;
        _relatedView.delegate = self;
    }
    return _relatedView;
}

- (UIView *)clickView {
    if (!_clickView) {
        _clickView = [[UIView alloc] initWithFrame:self.contentView.bounds];
        [self.contentView insertSubview:_clickView belowSubview:self.adMarkButtonView];
        _clickView.autoresizingMask = UIViewAutoresizingNone;
    }
    return _clickView;
}

#pragma mark - adview protocol

- (void)updateRootViewController:(UIViewController *)rootViewController
{
    self.rootViewController = rootViewController;
    _relatedView.viewController = rootViewController;
}

- (void)customRenderWithAdData:(id)adData
{
    if (!adData || ![adData isKindOfClass:[GDTUnifiedNativeAdDataObject class]]) {
        self.adData = nil;
        _relatedView.hidden = YES;
        [self failRenderWithError:[XMIAdError emptyDataError]];
        return;
    }
    if ([self.adData equalsAdData:adData]) {
        return;
    }
    self.adData = adData;
    [self doCustomRender];
}

- (void)doCustomRender
{
    [self.relatedView unregisterDataObject];
    self.coverImageView.hidden = NO;
    self.coverImageView.userInteractionEnabled = YES;
    [self.relatedView registerDataObject:self.adData clickableViews:@[self.clickView,self.coverImageView]];
    [self.adData bindImageViews:@[self.coverImageView] placeholder:[XMICommonUtils imageNamed:@"ad_bkg_default"]];
    [self addThirdLogo];
    [self updateThirdLogo];
    [self.adMarkButtonView updateFeedAdType:XMIAdMarkAdTypeGDT];
    if ([self isVideoAdView]) {
        [self.coverImageView.superview insertSubview:self.relatedView.mediaView  belowSubview:self.coverImageView];
        self.relatedView.mediaView.frame = self.coverImageView.frame;
    }
}

- (void)reloadUI
{
    [self doCustomRender];
}

- (void)updateImageURLs:(NSArray<NSString *> *)imageURLs
{
    
}

- (BOOL)isVideoAdView
{
    return self.adData.isVideoAd;
}

#pragma mark - gdt delegate

- (void)gdt_unifiedNativeAdViewWillExpose:(GDTUnifiedNativeAdView *)unifiedNativeAdView
{
    XMILogNativeAdInfo(@"gdt_unifiedNativeAdViewWillExpose");
    [self didExposeDspView];
}

- (void)gdt_unifiedNativeAdViewDidClick:(GDTUnifiedNativeAdView *)unifiedNativeAdView
{
    [self clickAdView:@{}];
}


- (void)gdt_unifiedNativeAdDetailViewClosed:(GDTUnifiedNativeAdView *)unifiedNativeAdView
{
    [self didCloseAdDetail];
}


- (void)gdt_unifiedNativeAdViewApplicationWillEnterBackground:(GDTUnifiedNativeAdView *)unifiedNativeAdView
{
    XMILogNativeAdInfo(@"gdt_unifiedNativeAdViewApplicationWillEnterBackground");
}

- (void)gdt_unifiedNativeAdDetailViewWillPresentScreen:(GDTUnifiedNativeAdView *)unifiedNativeAdView
{
    [self willPresentScreen];
}

- (void)gdt_unifiedNativeAdView:(GDTUnifiedNativeAdView *)unifiedNativeAdView playerStatusChanged:(GDTMediaPlayerStatus)status userInfo:(NSDictionary *)userInfo
{
    [self playerStateChanged:[XMIGDTConverter playStateFromGDTPlayState:status]];
    switch (status) {
        case GDTMediaPlayerStatusWillStart:
        case GDTMediaPlayerStatusStarted:
            self.btnLabel.hidden = NO;
            self.titleLabel.hidden = NO;
            [self.coverImageView insertSubview:self.relatedView.mediaView atIndex:0];
            self.relatedView.mediaView.frame = self.coverImageView.bounds;
            if ([XMIAdHelper isSocialHome]) {
                self.bottomMask.hidden = NO;
                self.titleLabel.hidden = NO;
                self.btnLabel.hidden = NO;
            }
            break;
        case GDTMediaPlayerStatusError:
            [self failRenderWithError:[XMIAdError otherTimeoutError]];
            break;
        case GDTMediaPlayerStatusStoped:
            if ([XMIAdHelper isSocialHome]) {
                self.bottomMask.hidden = YES;
                self.titleLabel.hidden = YES;
                self.btnLabel.hidden = YES;
            }
            break;
        default:
            break;
    }
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    _relatedView.frame = self.contentView.bounds;
    if ([self isVideoAdView]) {
        if (_relatedView.mediaView.superview == self.coverImageView) {
            _relatedView.mediaView.frame = self.coverImageView.bounds;
        } else {
            _relatedView.mediaView.frame = self.coverImageView.frame;
        }
    }
}

- (void)addThirdLogo
{
    [self.contentView addSubview:self.relatedView.logoView];

}

- (void)updateThirdLogo
{
    CGFloat sideMargin = 6;
    CGFloat width = 58*0.65f;
    CGFloat height = 22*0.65f;
//    self.relatedView.logoView.frame = CGRectMake(self.closeButton.xmi_left - 10 - width - sideMargin                        , sideMargin, width, height);
}

- (void)updateCloseAndMarkUI
{
    [super updateCloseAndMarkUI];
    [self updateThirdLogo];
    self.clickView.frame = CGRectMake(self.adMarkButtonView.xmi_left - 5, self.adMarkButtonView.xmi_top - 5, self.adMarkButtonView.xmi_width + 10, self.adMarkButtonView.xmi_height + 10);
}

- (void)updateLogoView
{
    CGFloat sideMargin = 6;
    CGFloat width = kGDTLogoImageViewDefaultWidth*0.85;
    CGFloat height = kGDTLogoImageViewDefaultHeight*0.85;
    [self.coverImageView addSubview:self.relatedView.logoView];
    self.relatedView.logoView.frame = CGRectMake(CGRectGetWidth(self.relatedView.bounds) - width - sideMargin
                                            , sideMargin, width, height);
}

+ (CGFloat)calAdHeight:(id)adData withAdWidth:(CGFloat)adWidth
{
    return adWidth * 9 / 16.0  + kContentViewEdgeTop * 2;
}
/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
