//
//  XMIGDTTemplateFeedAdModel.m
//  XMAd
//
//  Created by cuiyuanzhe on 2022/3/4.
//

#import "XMIGDTTemplateFeedAdModel.h"
#import <GDTMobSDK/GDTNativeExpressAd.h>
#import <GDTMobSDK/GDTNativeExpressAdView.h>

@interface XMIGDTTemplateFeedAdModel ()<GDTNativeExpressAdDelegete>

@property (nonatomic, strong) GDTNativeExpressAd *expressAd;

@property (nonatomic, copy) NSArray<__kindof GDTNativeExpressAdView *> *views;

@end

@implementation XMIGDTTemplateFeedAdModel

- (void)loadAdData
{
    if (self.loadingStatus != XMIFeedAdModelLoadingStatusInit) {
        return;
    }
    [super loadAdData];
    [self.expressAd loadAd:1];
}

- (GDTNativeExpressAd *)expressAd
{
    if (!_expressAd) {
        _expressAd = [[GDTNativeExpressAd alloc] initWithPlacementId:self.relatedData.dspPositionId adSize:CGSizeMake(self.adWidth, self.adHeight)];
        _expressAd.delegate = self;
    }
    return _expressAd;
}

- (void)setRootViewController:(UIViewController *)rootViewController
{
    [super setRootViewController:rootViewController];
    [self.views enumerateObjectsUsingBlock:^(__kindof GDTNativeExpressAdView * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        obj.controller = self.rootViewController;
    }];
}

#pragma mark - delegate

- (void)nativeExpressAdSuccessToLoad:(GDTNativeExpressAd *)nativeExpressAd views:(NSArray<__kindof GDTNativeExpressAdView *> *)views
{
    [views enumerateObjectsUsingBlock:^(__kindof GDTNativeExpressAdView * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        obj.controller = self.rootViewController;
    }];
    [self loadSuccess];
}

- (void)nativeExpressAdFailToLoad:(GDTNativeExpressAd *)nativeExpressAd error:(NSError *)error
{
    [self loadFailed:error];
}

#pragma mark - parse

- (id)adData
{
    return self.expressAd;
}

- (UIView *)templateAdView
{
    for (GDTNativeExpressAdView *view in self.views) {
        if ([view isAdValid]) {
            return view;
        }
    }
    return nil;
}

@end
