//
//  XMIGDTConverter.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/14.
//

#import "XMIGDTConverter.h"
#import <GDTMobSDK/GDTSDKDefines.h>

@implementation XMIGDTConverter

+ (XMIPlayerPlayState)playStateFromGDTPlayState:(NSInteger)playState {
    XMIPlayerPlayState state = XMIPlayerStateUnknown;
    switch (playState) {
        case GDTMediaPlayerStatusInitial:
            state = XMIPlayerStateInitial;
            break;
        case GDTMediaPlayerStatusLoading:
            state = XMIPlayerStateBuffering;
            break;
        case GDTMediaPlayerStatusStarted:
            state = XMIPlayerStatePlaying;
            break;
        case GDTMediaPlayerStatusPaused:
            state = XMIPlayerStatePause;
            break;
        case GDTMediaPlayerStatusStoped:
            state = XMIPlayerStateStopped;
            break;
        case GDTMediaPlayerStatusError:
            state = XMIPlayerStateFailed;
            break;
            
        default:
            break;
    }
    return state;
}

@end
