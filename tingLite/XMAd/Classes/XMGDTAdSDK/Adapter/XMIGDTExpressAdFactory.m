//
//  XMIGDTExpressAdFactory.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/9/3.
//

#import "XMIGDTExpressAdFactory.h"
#import "XMIGDTTemplateExpressAd.h"
#import "XMIGDTCustomExpressAd.h"

@implementation XMIGDTExpressAdFactory

+ (XMIExpressAd *)expressAdWithSlot:(XMIAdSlot *)adSlot {
    XMIExpressAd *expressAd = nil;
    switch (adSlot.adType) {
        case XMIAdSlotAdTypeFeed:
        case XMIAdSlotAdTypeFeed_Draw:
            expressAd = [[XMIGDTTemplateExpressAd alloc] initWithSlot:adSlot];
            break;
        case XMIAdSlotAdTypeFeed_Custom:
        case XMIAdSlotAdTypeFeed_Paster:
        case XMIAdSlotAdTypeFeed_DrawCustom:
            expressAd = [[XMIGDTCustomExpressAd alloc] initWithSlot:adSlot];
            break;
        default:
            break;
    }
    return expressAd;
}

@end
