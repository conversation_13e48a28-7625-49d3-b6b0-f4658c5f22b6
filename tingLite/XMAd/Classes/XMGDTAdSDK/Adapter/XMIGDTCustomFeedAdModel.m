//
//  XMIGDTCustomFeedAdModel.m
//  XMAd
//
//  Created by cuiyuanzhe on 2022/3/4.
//

#import "XMIGDTCustomFeedAdModel.h"
#import <GDTMobSDK/GDTUnifiedNativeAd.h>
#import <GDTMobSDK/GDTAdProtocol.h>
#import "XMIAdMacro.h"
#import "XMIGDTAdSDKManager.h"
#import "GDTUnifiedNativeAdDataObject+dspSDKReport.h"

@interface XMIGDTCustomFeedAdModel ()<GDTUnifiedNativeAdDelegate>

@property (nonatomic, strong) GDTUnifiedNativeAd *adManager;

@property (nonatomic, copy)NSArray<GDTUnifiedNativeAdDataObject *> *unifiedNativeAdDataObjects;

@end

@implementation XMIGDTCustomFeedAdModel


- (void)loadAdData {
    if (self.loadingStatus != XMIFeedAdModelLoadingStatusInit) {
        return;
    }
    self.loadingStatus = XMIFeedAdModelLoadingStatusLoading;
    [super loadAdData];
    if (!self.adManager) {
        [self setupAdManager];
    }
    [self.adManager loadAdWithAdCount:1];
}

/**
 private methods
 */
- (void)setupAdManager {
    
    NSString *slotAdm = nil;
    if (self.relatedData.slotRealBid) {
        slotAdm = self.relatedData.slotAdm;
    }
    if (slotAdm) {
        self.adManager = [[GDTUnifiedNativeAd alloc] initWithPlacementId:self.relatedData.dspPositionId token:slotAdm];
    } else {
        self.adManager = [[GDTUnifiedNativeAd alloc] initWithPlacementId:self.relatedData.dspPositionId];
    }
    self.adManager.delegate = self;
}

- (void)win:(nullable NSNumber *)auctionBidToWin {
    GDTUnifiedNativeAdDataObject *ad = [self unifiedNativeAdDataObject];
    if (ad && auctionBidToWin) {
        [ad sendWinNotificationWithInfo:@{GDT_M_W_E_COST_PRICE : auctionBidToWin}];
    }
}

- (void)loss:(nullable NSNumber *)auctionPrice {
    GDTUnifiedNativeAdDataObject *ad = [self unifiedNativeAdDataObject];
    if (ad && auctionPrice && self.loadingStatus == XMIFeedAdModelLoadingStatusLoadSuccess) {
        [ad sendLossNotificationWithInfo:@{GDT_M_L_WIN_PRICE : auctionPrice}];
    }
}

#pragma mark - GDTUnifiedNativeAdDelegate
- (void)gdt_unifiedNativeAdLoaded:(NSArray<GDTUnifiedNativeAdDataObject *> * _Nullable)unifiedNativeAdDataObjects error:(NSError * _Nullable)error {
    XMILog(@"gdt_unifiedNativeAdLoaded:error:");
    if (error != nil) {
        [self loadFailed:error];
        return;
    }
    self.unifiedNativeAdDataObjects = unifiedNativeAdDataObjects;
    self.relatedData.originData = [self unifiedNativeAdDataObject];
    if (self.relatedData.isMobileRtb) {
        // 转换价格，单位为元，原始为分
        float originPrice = [self unifiedNativeAdDataObject].eCPM;
        self.relatedData.price = originPrice * 1.0 / 100;
    }
    [self loadSuccess];
}

- ( GDTUnifiedNativeAdDataObject *)unifiedNativeAdDataObject
{
    if (self.unifiedNativeAdDataObjects.count > 0) {
        return [self.unifiedNativeAdDataObjects firstObject];
    }
    return nil;
}

- (NSString *)adTitle
{
    return [[self unifiedNativeAdDataObject] title];
}

- (NSString *)description
{
    return [[self unifiedNativeAdDataObject] description];
}

- (NSString *)adDescription
{
    return [[self unifiedNativeAdDataObject] desc];
}

- (NSString *)adButtonText
{
    return [self unifiedNativeAdDataObject].buttonText;
}

- (NSArray<NSString *> *)adImageURLs
{
//    if ([[self unifiedNativeAdDataObject] mediaUrlList].count > 0) {
//        return [[self unifiedNativeAdDataObject] mediaUrlList];
//    }
    return @[[[self unifiedNativeAdDataObject] imageUrl]];
}

- (NSString *)adVideoURL
{
    return nil;
}

- (id)adData
{
    return [self unifiedNativeAdDataObject];
}

- (NSString *)iconUrl
{
    return [self unifiedNativeAdDataObject].iconUrl;
}

@end

