//
//  XMIGDTFeedAdModelFactory.m
//  XMAd
//
//  Created by cuiyuanzhe on 2022/3/4.
//

#import "XMIGDTFeedAdModelFactory.h"
#import "XMIGDTCustomFeedAdModel.h"

@implementation XMIGDTFeedAdModelFactory

+ (XMIFeedAdModel *)adModelWithRelatedData:(XMIAdRelatedData *)relatedData
{
    XMIGDTCustomFeedAdModel *model = [[XMIGDTCustomFeedAdModel alloc] init];
    model.relatedData = relatedData;
    return model;
}

@end
