//
//  XMIGDTRewardAd.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON>z<PERSON> on 2022/6/8.
//

#import "XMIGDTRewardAd.h"
#import <GDTMobSDK/GDTRewardVideoAd.h>
#import "XMIAdHelper.h"

@interface XMIGDTRewardAd ()<GDTRewardedVideoAdDelegate>

@property (nonatomic, strong) GDTRewardVideoAd *rewardVideoAd;

@end

@implementation XMIGDTRewardAd

- (void)startLoadAd
{
    if (self.rewardVideoAd) {
        return;
    }
    self.rewardVideoAd = [[GDTRewardVideoAd alloc] initWithPlacementId:self.relatedData.dspPositionId];
    self.rewardVideoAd.delegate = self;
    self.rewardVideoAd.videoMuted = NO;
    [self.rewardVideoAd loadAd];
}

- (void)showFromRootViewController:(UIViewController *)rootViewController
{
    [self.rewardVideoAd showAdFromRootViewController:rootViewController];
}

#pragma mark - gdt delegate

- (void)gdt_rewardVideoAdDidLoad:(GDTRewardVideoAd *)rewardedVideoAd
{
    [self loadSuccess];
}

- (void)gdt_rewardVideoAdVideoDidLoad:(GDTRewardVideoAd *)rewardedVideoAd
{
    [self loadVideoSuccess];
}

- (void)gdt_rewardVideoAdWillVisible:(GDTRewardVideoAd *)rewardedVideoAd
{
    [self controllerWillShow];
}

- (void)gdt_rewardVideoAdDidExposed:(GDTRewardVideoAd *)rewardedVideoAd
{
    [self controllerDidShow];
}

- (void)gdt_rewardVideoAdDidClose:(GDTRewardVideoAd *)rewardedVideoAd
{
    [self controllerWillClose];
    [self controllerDidHide];
}


- (void)gdt_rewardVideoAdDidClicked:(GDTRewardVideoAd *)rewardedVideoAd
{
    [self didClickAd];
}

- (void)gdt_rewardVideoAd:(GDTRewardVideoAd *)rewardedVideoAd didFailWithError:(NSError *)error
{
    [self loadFail:error];
    [XMIAdHelper apmLogMessage:[NSString stringWithFormat:@"gdt reward error: position=%lld, code=%zd,message =%@", self.relatedData.positionId, error.code, error.localizedDescription]];
}


- (void)gdt_rewardVideoAdDidRewardEffective:(GDTRewardVideoAd *)rewardedVideoAd info:(NSDictionary *)info
{
    [self rewardSuccess:info];
}

- (void)gdt_rewardVideoAdDidPlayFinish:(GDTRewardVideoAd *)rewardedVideoAd
{
    [self playFinished];
}


@end
