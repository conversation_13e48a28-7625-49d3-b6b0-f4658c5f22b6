//
//  XMIGDTExpressAdViewFactory.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/12/3.
//

#import "XMIGDTExpressAdViewFactory.h"
#import "XMIGDTExpressDoubleAdView.h"
#import "XMIGDTMixFeedCardAdView.h"
@implementation XMIGDTExpressAdViewFactory

+ (XMIExpressAdView *)expressAdViewWithShowStyle:(XMIAdShowStyle)showstyle
                                           frame:(CGRect)frame {
    
    if (showstyle == XMIAdStyleHomeLargeImage || showstyle == XMIAdStyleHomeVideo) {
        XMIGDTMixFeedCardAdView *adView = [[XMIGDTMixFeedCardAdView alloc] initWithFrame:frame];
        return adView;
    }
    return [[XMIGDTExpressDoubleAdView alloc] initWithFrame:frame];
}

@end
