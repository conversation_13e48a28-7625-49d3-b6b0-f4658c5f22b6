//
//  XMIGDTFindFeedNativeAdABTestBaseView.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/3/1.
//

#import "XMIGDTFindFeedNativeAdSocialView.h"
#import <GDTMobSDK/GDTUnifiedNativeAdDataObject.h>
#import <GDTMobSDK/GDTUnifiedNativeAdView.h>
#import "XMIAdError.h"
#import "XMICommonUtils.h"
#import "XMIAdNativeLogger.h"
#import "XMIGDTConverter.h"
#import "UIView+XMIUtils.h"
#import "XMIAdMacro.h"

@interface XMIGDTFindFeedNativeAdSocialView ()<GDTUnifiedNativeAdViewDelegate>

@property (nonatomic, strong) GDTUnifiedNativeAdDataObject *adData;
@property (nonatomic, strong) GDTUnifiedNativeAdView *relatedView;
@property (nonatomic, strong) UIView *clickView;
@property (nonatomic, weak) UIViewController *rootViewController;

@end

@implementation XMIGDTFindFeedNativeAdSocialView

- (GDTUnifiedNativeAdView *)relatedView
{
    if (!_relatedView) {
        _relatedView = [[GDTUnifiedNativeAdView alloc] initWithFrame:_relatedView.bounds];
        [self.contentView insertSubview:_relatedView belowSubview:self.coverImageView];
        _relatedView.autoresizingMask = UIViewAutoresizingNone;
        _relatedView.viewController = self.rootViewController;
        _relatedView.delegate = self;
    }
    return _relatedView;
}

- (UIView *)clickView {
    if (!_clickView) {
        _clickView = [[UIView alloc] initWithFrame:self.contentView.bounds];
        [self.contentView insertSubview:_clickView belowSubview:self.coverImageView];
        _clickView.autoresizingMask = UIViewAutoresizingNone;
    }
    return _clickView;
}

#pragma mark - adview protocol

- (void)updateRootViewController:(UIViewController *)rootViewController
{
    self.rootViewController = rootViewController;
    _relatedView.viewController = rootViewController;
}

- (void)customRenderWithAdData:(id)adData
{
    if (!adData || ![adData isKindOfClass:[GDTUnifiedNativeAdDataObject class]]) {
        self.adData = nil;
        _relatedView.hidden = YES;
        [self failRenderWithError:[XMIAdError emptyDataError]];
        return;
    }
    if ([self.adData equalsAdData:adData]) {
        return;
    }
    self.adData = adData;
    [self doCustomRender];
}

- (void)doCustomRender
{
    [self.relatedView unregisterDataObject];
    self.coverImageView.hidden = NO;
    [self.relatedView registerDataObject:self.adData clickableViews:@[self.clickView,self.coverImageView]];
    [self.adData bindImageViews:@[self.coverImageView] placeholder:[XMICommonUtils imageNamed:@"ad_bkg_default"]];
    [self.adMarkButtonView updateFeedAdType:XMIAdMarkAdTypeGDT];
    if ([self isVideoAdView]) {
        [self.coverImageView.superview insertSubview:self.relatedView.mediaView  belowSubview:self.coverImageView];
        self.relatedView.mediaView.frame = self.coverImageView.frame;
        [self doVideoRender];
        self.coverImageView.layer.borderColor = nil;
        self.coverImageView.layer.borderWidth = 0;
    }
}

- (void)reloadUI
{
}

- (void)updateImageURLs:(NSArray<NSString *> *)imageURLs
{
    
}

- (BOOL)isVideoAdView
{
    return self.adData.isVideoAd;
}

#pragma mark - gdt delegate

- (void)gdt_unifiedNativeAdViewWillExpose:(GDTUnifiedNativeAdView *)unifiedNativeAdView
{
    XMILogNativeAdInfo(@"gdt_unifiedNativeAdViewWillExpose");
    [self didExposeDspView];
}

- (void)gdt_unifiedNativeAdViewDidClick:(GDTUnifiedNativeAdView *)unifiedNativeAdView
{
    [self clickAdView:@{}];
}

- (void)gdt_unifiedNativeAdDetailViewClosed:(GDTUnifiedNativeAdView *)unifiedNativeAdView
{
    [self didCloseAdDetail];
}

- (void)gdt_unifiedNativeAdViewApplicationWillEnterBackground:(GDTUnifiedNativeAdView *)unifiedNativeAdView
{
    XMILogNativeAdInfo(@"gdt_unifiedNativeAdViewApplicationWillEnterBackground");
}

- (void)gdt_unifiedNativeAdDetailViewWillPresentScreen:(GDTUnifiedNativeAdView *)unifiedNativeAdView
{
    [self willPresentScreen];
}

- (void)gdt_unifiedNativeAdView:(GDTUnifiedNativeAdView *)unifiedNativeAdView playerStatusChanged:(GDTMediaPlayerStatus)status userInfo:(NSDictionary *)userInfo
{
    [self playerStateChanged:[XMIGDTConverter playStateFromGDTPlayState:status]];
    switch (status) {
        case GDTMediaPlayerStatusWillStart:
        case GDTMediaPlayerStatusStarted:
            self.btnLabel.hidden = NO;
            [self.coverImageView insertSubview:self.relatedView.mediaView atIndex:0];
            self.relatedView.mediaView.frame = self.coverImageView.bounds;
            break;
        case GDTMediaPlayerStatusStoped:
            self.btnLabel.hidden = YES;
        case GDTMediaPlayerStatusError:
            [self failRenderWithError:[XMIAdError otherTimeoutError]];
            break;
        default:
            break;
    }
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    _relatedView.frame = self.contentView.bounds;
    if ([self isVideoAdView]) {
        if (_relatedView.mediaView.superview == self.coverImageView) {
            _relatedView.mediaView.frame = self.coverImageView.bounds;
        } else {
            _relatedView.mediaView.frame = self.coverImageView.frame;
        }
    }
}

- (void)updateCloseAndMarkUI
{
    [super updateCloseAndMarkUI];
    self.clickView.frame = self.contentView.bounds;
}

@end
