//
//  XMIBUAdSDKManager.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/6/30.
//

#import "XMIBUAdSDKManager.h"
#import <BUAdSDK/BUAdSDKManager.h>
#import <BUAdSDK/BUAdSDKConfiguration.h>

@implementation XMIBUAdSDKManager

+ (void)initWithAppID:(NSString *)appID {
    BUAdSDKConfiguration *configuration = [BUAdSDKConfiguration configuration];
        configuration.appID = appID;//除appid外，其他参数配置按照项目实际需求配置即可。
    [BUAdSDKManager startWithAsyncCompletionHandler:^(BOOL success, NSError *error) {
  
    }];
}

+ (NSString *)getSDKVersion {
    return [BUAdSDKManager SDKVersion];
}

@end
