//
//  XMIBUCustomExpressAd.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/6.
//

#import "XMIBUCustomExpressAd.h"
#import <BUAdSDK/BUNativeAdsManager.h>
#import "XMIBUConverter.h"

@interface XMIBUCustomExpressAd () <BUNativeAdsManagerDelegate>

@property (nonatomic, strong) BUNativeAdsManager *adManager;

@end

@implementation XMIBUCustomExpressAd

/**
 public methods
 */
- (instancetype)initWithSlot:(XMIAdSlot *)adSlot {
    self = [super initWithSlot:adSlot];
    if (self) {
        [self setupAdManager];
    }
    
    return self;
}

- (void)loadAdData {
    [super loadAdData];
    NSLog(@"+++++++++++开始请求");
    // 实时竞价不需调用loadAdData
    if (self.adSlot.slotRealBid && self.adSlot.slotAdm.length) {
        [self.adManager setAdMarkup:self.adSlot.slotAdm];
    } else {
        [self.adManager loadAdDataWithCount:self.adSlot.adCount];
    }
}


/**
 private methods
 */
- (void)setupAdManager {
    BUAdSlot *slot = [[BUAdSlot alloc] init];
    slot.ID = self.adSlot.positionID;
    slot.AdType = [self getBUAdType:self.adSlot.adType];
    slot.position = BUAdSlotPositionTop;
    slot.imgSize = [BUSize sizeBy:BUProposalSize_Feed228_150];
    
    self.adManager = [[BUNativeAdsManager alloc] initWithSlot:slot];
    self.adManager.adSize = self.adSlot.adSize;
    self.adManager.delegate = self;
}

- (BUAdSlotAdType)getBUAdType:(XMIAdSlotAdType)adType {
    BUAdSlotAdType buAdType = BUAdSlotAdTypeUnknown;
    switch (adType) {
        case XMIAdSlotAdTypeFeed_Custom:
            buAdType = BUAdSlotAdTypeFeed;
            break;
        case XMIAdSlotAdTypeFeed_DrawCustom:
            buAdType = BUAdSlotAdTypeDrawVideo;
            break;
            
        default:
            break;
    }
    
    return buAdType;
}

#pragma mark - BUNativeAdsManagerDelegate
- (void)nativeAdsManagerSuccessToLoad:(BUNativeAdsManager *)adsManager nativeAds:(NSArray<BUNativeAd *> *_Nullable)nativeAdDataArray {
    NSMutableArray *adDataArray = [[NSMutableArray alloc] init];
    for (int i = 0; i < nativeAdDataArray.count; i++) {
        BUNativeAd *buNativeAd = nativeAdDataArray[i];
        // 必要初始化
        buNativeAd.rootViewController = self.rootViewController;
        //
        self.adSlot.relatedData.originData = buNativeAd;
        
        [adDataArray addObject:self.adSlot.relatedData];
    }
    [self didLoadData:adDataArray];
}

- (void)nativeAdsManager:(BUNativeAdsManager *)adsManager didFailWithError:(NSError *_Nullable)error {
    [self didLoadFailWithError:error];
}

@end
