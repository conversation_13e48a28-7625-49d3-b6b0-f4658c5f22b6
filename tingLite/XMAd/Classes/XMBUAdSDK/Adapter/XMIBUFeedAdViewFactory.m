//
//  XMIBUFeedAdViewFactory.m
//  XMAd
//
//  Created by cuiyuanz<PERSON> on 2022/3/8.
//

#import "XMIBUFeedAdViewFactory.h"
#import "XMIBUFindNativeAdView.h"
#import "XMIBUFindFeedNativeAdABTestView.h"
#import "XMIBUFindFeedNativeAdSocialView.h"

@implementation XMIBUFeedAdViewFactory

+ (Class)feedAdViewClasslWithShowStyle:(XMIAdShowStyle)showstyle adType:(XMIAdType)adType subShowStyle:(XMIAdShowSubStyle)subShowStyle
{
    if (adType != XMIAdTypeBU) {
        return nil;
    }
    switch (showstyle) {
        case XMIAdStyleHomeLargeImage:
            if (subShowStyle == XMIAdSubShowSubStyleFeedAB) {
                return [XMIBUFindFeedNativeAdABTestView class];
            } else if (subShowStyle == XMIAdSubShowSubStyleSocial) {
                return [XMIBUFindFeedNativeAdSocialView class];
            }
            return [XMIBUFindNativeAdView class];
            break;
            
        default:
            break;
    }
    return nil;
}


@end
