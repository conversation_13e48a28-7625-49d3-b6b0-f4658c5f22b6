//
//  XMIBUTemplateFeedAdModel.m
//  XMAd
//
//  Created by cuiyuanzhe on 2022/3/4.
//

#import "XMIBUTemplateFeedAdModel.h"
#import <BUAdSDK/BUNativeExpressAdManager.h>
#import <XMAd/XMIAdDefines.h>

@interface XMIBUTemplateFeedAdModel () <BUNativeExpressAdViewDelegate>

@property (nonatomic, strong) BUNativeExpressAdManager *adManager;

@property (nonatomic, copy) NSArray *adViews;

@end

@implementation XMIBUTemplateFeedAdModel

- (BUNativeExpressAdManager *)adManager
{
    if (_adManager) {
        BUAdSlot *slot = [[BUAdSlot alloc] init];
        slot.ID = self.relatedData.dspPositionId;
        slot.AdType = [self getBUAdType:self.relatedData.showstyle];
        BUSize *imgSize = [BUSize sizeBy:BUProposalSize_Feed228_150];
        slot.imgSize = imgSize;
        slot.position = BUAdSlotPositionFeed;
        
        _adManager = [[BUNativeExpressAdManager alloc] initWithSlot:slot adSize:CGSizeMake(self.adWidth, self.adHeight)];
        _adManager.delegate = self;
    }
    return _adManager;
}

- (void)loadAdData
{
    if (self.loadingStatus != XMIFeedAdModelLoadingStatusInit) {
        return;
    }
    [super loadAdData];
    [self.adManager loadAdDataWithCount:1];
}


#pragma mark - inner methods

- (BUAdSlotAdType)getBUAdType:(XMIAdShowStyle)showStyle {
    BUAdSlotAdType buAdType = BUAdSlotAdTypeUnknown;
    switch (showStyle) {
        case XMIAdStyleHomeLargeImage:
        case XMIAdStyleHomeBackgroundImage:
        case XMIAdStyleHomeShopWindow:
            buAdType = BUAdSlotAdTypeFeed;
            break;
        default:
            break;
    }
    
    return buAdType;
}

#pragma mark - delegate

/**
 * Sent when views successfully load ad
 */
- (void)nativeExpressAdSuccessToLoad:(BUNativeExpressAdManager *)nativeExpressAdManager views:(NSArray<__kindof BUNativeExpressAdView *> *)views {
    self.adViews = views;
    [self loadSuccess];
}

/**
 * Sent when views fail to load ad
 */
- (void)nativeExpressAdFailToLoad:(BUNativeExpressAdManager *)nativeExpressAdManager error:(NSError *_Nullable)error {
    [self loadFailed:error];
}

#pragma mark - parse

- (UIView *)templateAdView
{
    if (self.adViews.count == 0) {
        return nil;
    }
    return [self.adViews firstObject];
}

@end
