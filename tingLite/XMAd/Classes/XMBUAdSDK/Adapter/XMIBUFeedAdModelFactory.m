//
//  XMIBUFeedAdModelFactory.m
//  XMAd
//
//  Created by cuiyuanzhe on 2022/3/4.
//

#import "XMIBUFeedAdModelFactory.h"
#import "XMIBUCustomFeedAdModel.h"
#import "XMIBUTemplateFeedAdModel.h"

@implementation XMIBUFeedAdModelFactory

+ (XMIFeedAdModel *)adModelWithRelatedData:(XMIAdRelatedData *)relatedData
{
    XMIFeedAdModel *model = nil;
    switch (relatedData.adtype) {
        case XMIAdTypeBUExpress:
            model = [[XMIBUTemplateFeedAdModel alloc] init];
            break;
        case XMIAdTypeBU:
            model = [[XMIBUCustomFeedAdModel alloc] init];
            break;
        default:
            break;
    }
    model.relatedData = relatedData;
    return model;
}

@end
