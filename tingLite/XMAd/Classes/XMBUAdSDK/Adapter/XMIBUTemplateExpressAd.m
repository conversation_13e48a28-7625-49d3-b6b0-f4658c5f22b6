//
//  XMIBUTemplateExpressAd.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/6.
//

#import "XMIBUTemplateExpressAd.h"
#import <BUAdSDK/BUNativeExpressAdManager.h>
#import "XMIAdMacro.h"
#import "XMIBUExpressAdView.h"
#import "XMIExpressAdSingleView+Internal.h"
#import "XMIBUConverter.h"

@interface XMIBUTemplateExpressAd () <BUNativeExpressAdViewDelegate>

@property (nonatomic, strong) BUNativeExpressAdManager *adManager;

@end

@implementation XMIBUTemplateExpressAd

/**
 public methods
 */
- (instancetype)initWithSlot:(XMIAdSlot *)adSlot {
    self = [super initWithSlot:adSlot];
    if (self) {
        [self setupAdManager];
    }
    
    return self;
}

- (void)loadAdData {
    [super loadAdData];
    // 实时竞价不需调用loadAdData
    if (self.adSlot.slotRealBid && self.adSlot.slotAdm.length) {
        [self.adManager setAdMarkup:self.adSlot.slotAdm];
    } else {
        [self.adManager loadAdDataWithCount:self.adSlot.adCount];
    }
}


/**
 private methods
 */
- (void)setupAdManager {
    BUAdSlot *slot = [[BUAdSlot alloc] init];
    slot.ID = self.adSlot.positionID;
    slot.AdType = [self getBUAdType:self.adSlot.adType];
    BUSize *imgSize = [BUSize sizeBy:BUProposalSize_Feed228_150];
    slot.imgSize = imgSize;
    slot.position = BUAdSlotPositionFeed;
    
    self.adManager = [[BUNativeExpressAdManager alloc] initWithSlot:slot adSize:self.adSlot.adSize];
    self.adManager.delegate = self;
}

- (BUAdSlotAdType)getBUAdType:(XMIAdSlotAdType)adType {
    BUAdSlotAdType buAdType = BUAdSlotAdTypeUnknown;
    switch (adType) {
        case XMIAdSlotAdTypeFeed:
            buAdType = BUAdSlotAdTypeFeed;
            break;
        case XMIAdSlotAdTypeFeed_Draw:
            buAdType = BUAdSlotAdTypeDrawVideo;
            break;
        case XMIAdSlotAdTypeFeed_Paster:
            buAdType = BUAdSlotAdTypePaster;
            break;
            
        default:
            break;
    }
    
    return buAdType;
}

#pragma mark - BUNativeExpressAdViewDelegate
/**
 * Sent when views successfully load ad
 */
- (void)nativeExpressAdSuccessToLoad:(BUNativeExpressAdManager *)nativeExpressAdManager views:(NSArray<__kindof BUNativeExpressAdView *> *)views {
    NSMutableArray *adViewArray = [[NSMutableArray alloc] init];
    if (views.count > 0) {
        for (int i = 0; i < views.count; i++) {
            BUNativeExpressAdView *adView = views[i];
            adView.rootViewController = self.rootViewController;
            
            XMIBUExpressAdView *xmAdView = [[XMIBUExpressAdView alloc] initWithOriginView:adView];
            [adViewArray addObject:xmAdView];
        }
    }
    [self didLoad:adViewArray];
}

/**
 * Sent when views fail to load ad
 */
- (void)nativeExpressAdFailToLoad:(BUNativeExpressAdManager *)nativeExpressAdManager error:(NSError *_Nullable)error {
    [self didLoadFailWithError:error];
}

@end
