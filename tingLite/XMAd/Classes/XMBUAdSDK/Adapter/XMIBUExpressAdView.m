//
//  XMIBUExpressAdView.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/6.
//

#import "XMIBUExpressAdView.h"
#import <BUAdSDK/BUNativeExpressAdView.h>
#import <BUAdSDK/BUNativeAd.h>
#import <BUAdSDK/BUNativeAdRelatedView.h>
#import <XMWebImage/UIImageView+WebCache.h>
#import "XMIAdMacro.h"
#import "XMIAdDefines.h"
#import "XMICommonUtils.h"
#import "XMIAdViewProtocol.h"
#import "XMIExpressAdSingleView+Internal.h"

@interface XMIBUExpressAdView ()<XMIAdViewProtocol>

@property (nonatomic, strong) BUNativeExpressAdView *adView;
@property (nonatomic, strong) BUNativeAd *adData;
@end

@implementation XMIBUExpressAdView
@synthesize isCustom = _isCustom;
@synthesize contentView = _contentView;
@synthesize logoImageView = _logoImageView;
@synthesize titleLabel = _titleLabel;
@synthesize descLabel = _descLabel;
@synthesize videoView = _videoView;
@synthesize dislikeButton = _dislikeButton;
@synthesize bottomView = _bottomView;
@synthesize actionView = _actionView;
@synthesize imgViewArray = _imgViewArray;

- (instancetype)initWithOriginView:(UIView *)originView {
    self = [super initWithOriginView:originView];
    if (self) {
        [self setupWithOriginView:(BUNativeExpressAdView *)originView];
    }
    return self;
}

- (void)setupWithOriginView:(BUNativeExpressAdView *)adView {
    _isCustom = NO;
    if (adView == nil) {
        return;
    }
    self.adView = adView;
    [self addSubview:adView];
}

- (instancetype)initWithOriginData:(id)adData {
    self = [super initWithOriginData:adData];
    if (self) {
        [self setupWithOriginData:(BUNativeAd *)adData];
    }
    return self;
}

- (void)setupWithOriginData:(BUNativeAd *)adData {
    _isCustom = YES;
    if (adData == nil) {
        return;
    }
    self.adData = adData;
}

- (void)render:(CGSize)size {
    if (_isCustom) {
        [self renderCustom];
    } else {
        [self renderTemplate];
    }
}

/**
 模版渲染
 */
- (void)renderTemplate {
    if (self.adView != nil) {
        [self.adView render];
    }
}

/**
 自渲染
 */
- (void)renderCustom {
    if (self.relatedView == nil) {
        return;
    }
    
    // 保持广告内容宽高16/9，留白左右16
    self.frame = CGRectMake(0, 0, [self adWidth], [self adHeight]);
    CGFloat contentWidth = [self contentWidth];
    CGFloat contentHeight = [self contentHeight];
    // content view
    UIView *contentView = [self defaultContentView];
    [self addSubview:contentView];
    _contentView = contentView;
    
    BUNativeAdRelatedView *adRelatedView = (BUNativeAdRelatedView *)self.relatedView;
    // ad content
    [self renderAdContent:CGSizeMake(contentWidth, contentHeight)];
    // logo 主站拷贝的宽高
    adRelatedView.logoADImageView.frame = CGRectMake(contentWidth - 6 - 58 * 0.65, 6, 58 * 0.65, 18.5 * 0.65);
    [contentView addSubview:adRelatedView.logoADImageView];
    _logoImageView = adRelatedView.logoADImageView;
    // dislike button
    adRelatedView.dislikeButton.hidden = YES;
    [contentView addSubview:adRelatedView.dislikeButton];
    _dislikeButton = adRelatedView.dislikeButton;
    
    // bottom
    UIView *bottomView = [self defaultBottomView];
    [_contentView addSubview:bottomView];
    _bottomView = bottomView;
    // action
    UIView *actionView = [self defaultActionViewWithText:self.adData.data.buttonText];
    actionView.frame = CGRectMake(contentWidth - actionView.frame.size.width - 10, contentHeight - 26 - 10, actionView.frame.size.width, actionView.frame.size.height);
    [_contentView addSubview:actionView];
    _actionView = actionView;
    // title
    UILabel *titleLabel = [self defaultTitleViewWithTitle:self.adData.data.AdTitle];
    [_bottomView addSubview:titleLabel];
    _titleLabel = titleLabel;
    // desc
    UILabel *descLabel = [self defaultDescViewWithDesc:self.adData.data.AdDescription];
    [_contentView addSubview:descLabel];
    _descLabel = descLabel;
    
    [self.adData registerContainer:self withClickableViews:@[actionView]];
}
/**
 广告实际内容
 */
- (void)renderAdContent:(CGSize)size {
    BUNativeAdRelatedView *adRelatedView = (BUNativeAdRelatedView *)self.relatedView;
    // video
    if (self.adData.data.imageMode == BUFeedVideoAdModeImage
        || self.adData.data.imageMode == BUFeedVideoAdModePortrait
        || self.adData.data.imageMode == BUFeedADModeSquareVideo) {
        if (adRelatedView.videoAdView != nil) {
            adRelatedView.videoAdView.frame = CGRectMake(0, 0, size.width, size.height);
            [self.contentView addSubview:adRelatedView.videoAdView];
            _videoView = adRelatedView.videoAdView;
        }
    }
    // 大图
    else if (self.adData.data.imageMode == BUFeedADModeLargeImage) {
        UIImageView *bigImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, size.width, size.height)];
        if (self.adData.data.imageAry != nil) {
            BUImage *buImage = self.adData.data.imageAry.firstObject;
            [self fillImageView:bigImageView withBUImage:buImage];
        } else {
            bigImageView.image = [XMICommonUtils imageNamed:@"ad_bkg_default"];
        }
        [self.contentView addSubview:bigImageView];
        _imgViewArray = [[NSArray alloc] initWithObjects:bigImageView, nil];
    }
    // 三小图
    else if (self.adData.data.imageMode == BUFeedADModeGroupImage) {
        // TODO:信息流暂不支持三小图，简单填充
        if (self.adData.data.imageAry != nil && self.adData.data.imageAry.count > 2) {
            CGRect frame = CGRectMake(0, 0, (size.width - 2 * 2) / 3, size.height);
            UIImageView *imageView1 = [[UIImageView alloc] initWithFrame:frame];
            [self fillImageView:imageView1 withBUImage:self.adData.data.imageAry[0]];
            [self.contentView addSubview:imageView1];
            //
            frame = CGRectOffset(frame, frame.size.width + 2, 0);
            UIImageView *imageView2 = [[UIImageView alloc] initWithFrame:frame];
            [self fillImageView:imageView2 withBUImage:self.adData.data.imageAry[1]];
            [self.contentView addSubview:imageView2];
            //
            frame = CGRectOffset(frame, frame.size.width + 2, 0);
            UIImageView *imageView3 = [[UIImageView alloc] initWithFrame:frame];
            [self fillImageView:imageView3 withBUImage:self.adData.data.imageAry[2]];
            [self.contentView addSubview:imageView3];
            _imgViewArray = [[NSArray alloc] initWithObjects:imageView1, imageView2, imageView3, nil];
        } else {
            // 容错
            UIImageView *bigImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, size.width, size.height)];
            bigImageView.image = [XMICommonUtils imageNamed:@"ad_bkg_default"];
            [self.contentView addSubview:bigImageView];
            _imgViewArray = [[NSArray alloc] initWithObjects:bigImageView, nil];
        }
    }
}

- (void)fillImageView:(UIImageView *)imageView withBUImage:(BUImage *)buImage {
    if (buImage.imageURL != nil) {
        [imageView sd_setImageWithURL:[NSURL URLWithString:buImage.imageURL] placeholderImage:[XMICommonUtils imageNamed:@"ad_bkg_default"]];
    } else {
        imageView.image = [XMICommonUtils imageNamed:@"ad_bkg_default"];
    }
}

- (int)getAdShowType {
    XMIAdShowType showType = XMIAdShowTypeImage;
    if (self.adData == nil) {
        return showType;
    }
    if (self.adData.data.imageMode == BUFeedVideoAdModeImage
        || self.adData.data.imageMode == BUFeedVideoAdModePortrait
        || self.adData.data.imageMode == BUFeedADModeSquareVideo) {
        showType = XMIAdShowTypeVideo;
    }
    else if (self.adData.data.imageMode == BUFeedADModeLargeImage) {
        showType = XMIAdShowTypeImage;
    } else if (self.adData.data.imageMode == BUFeedADModeGroupImage) {
        showType = XMIAdShowTypeImage;
    }
    return showType;
}

@end
