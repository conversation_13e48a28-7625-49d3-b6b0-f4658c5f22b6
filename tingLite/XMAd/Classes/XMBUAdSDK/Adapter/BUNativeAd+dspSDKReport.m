//
//  BUNativeAd+dspSDKReport.m
//  XMAd
//
//  Created by cuiyuanzhe on 2022/9/15.
//

#import "BUNativeAd+dspSDKReport.h"
#import "XMIBUAdSDKManager.h"

@implementation BUNativeAd (dspSDKReport)

- (BOOL)reportDSPSDK
{
    return YES;
}


- (NSDictionary *)dspSDKReportParams
{
    NSDictionary *dic = [[[self data] valueForKey:@"materialDictionary"] valueForKey:@"app"];
    NSString *appName = [dic objectForKey:@"app_name"];
    NSString *bundleId = [dic objectForKey:@"package_name"];
    NSString *appleid = [dic objectForKey:@"appleid"];
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"downloadAppName"] = appName;
    params[@"downloadAppId"] = appleid;
    params[@"downloadAppBundleId"] = bundleId;
    params[@"dsp_src"] = @"csj";
    params[@"dsp_innerversion"] = [XMIBUAdSDKManager getSDKVersion];
    return [params copy];
}


@end
