//
//  XMIBUExpressAdViewFactory.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/12/3.
//

#import "XMIBUExpressAdViewFactory.h"
#import "XMIBUExpressDoubleAdView.h"
#import "XMIBUMixFeedCardAdView.h"
@implementation XMIBUExpressAdViewFactory

+ (XMIExpressAdView *)expressAdViewWithShowStyle:(XMIAdShowStyle)showstyle
                                           frame:(CGRect)frame {
    if (showstyle == XMIAdStyleHomeLargeImage || showstyle == XMIAdStyleHomeVideo) {
        XMIBUMixFeedCardAdView *adView = [[XMIBUMixFeedCardAdView alloc] initWithFrame:frame];
        return adView;
    }
    return [[XMIBUExpressDoubleAdView alloc] initWithFrame:frame];
}

@end
