//
//  XMIBUCustomFeedAdModel.m
//  XMAd
//
//  Created by cuiyuanzhe on 2022/3/4.
//

#import "XMIBUCustomFeedAdModel.h"
#import <BUAdSDK/BUNativeAdsManager.h>
#import <XMCategories/NSObject+XMCommon.h>
#import "BUNativeAd+dspSDKReport.h"

@interface XMIBUCustomFeedAdModel () <BUNativeAdsManagerDelegate>

@property (nonatomic, strong) BUNativeAdsManager *adManager;

@property (nonatomic, copy) NSArray<BUNativeAd *> *nativeAdDataArray;

@end

@implementation XMIBUCustomFeedAdModel

- (BUNativeAdsManager *)adManager
{
    if (!_adManager) {
        BUAdSlot *slot = [[BUAdSlot alloc] init];
        slot.ID = self.relatedData.dspPositionId;
        slot.AdType = [self getBUAdType:self.relatedData.showstyle];
        slot.position = BUAdSlotPositionTop;
        slot.imgSize = [BUSize sizeBy:BUProposalSize_Feed228_150];
        
        _adManager = [[BUNativeAdsManager alloc] initWithSlot:slot];
        _adManager.adSize = CGSizeMake(self.adWidth, self.adHeight);
        _adManager.delegate = self;
    }
    return _adManager;
}

- (void)loadAdData {
    if (self.loadingStatus != XMIFeedAdModelLoadingStatusInit) {
        return;
    }
    self.loadingStatus = XMIFeedAdModelLoadingStatusLoading;
    [super loadAdData];
    NSString *slotAdm = nil;
    if (self.relatedData.slotRealBid) {
        slotAdm = self.relatedData.slotAdm;
    }
    if (slotAdm) {
        [self.adManager setAdMarkup:slotAdm];
    } else {
        [self.adManager loadAdDataWithCount:1];
    }
}

#pragma mark - inner methods

- (BUAdSlotAdType)getBUAdType:(XMIAdShowStyle)showStyle {
    BUAdSlotAdType buAdType = BUAdSlotAdTypeUnknown;
    switch (showStyle) {
        case XMIAdStyleHomeLargeImage:
        case XMIAdStyleHomeBackgroundImage:
        case XMIAdStyleHomeShopWindow:
            buAdType = BUAdSlotAdTypeFeed;
            break;
        default:
            break;
    }
    
    return buAdType;
}

- (void)setRootViewController:(UIViewController *)rootViewController
{
    [super setRootViewController:rootViewController];
    [self.nativeAdDataArray enumerateObjectsUsingBlock:^(BUNativeAd * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        obj.rootViewController = rootViewController;
    }];
}

- (void)win:(nullable NSNumber *)auctionBidToWin {
    BUNativeAd *ad = [self buNativeAd];
    if (ad && auctionBidToWin) {
        [ad win:auctionBidToWin];
    }
}

- (void)loss:(nullable NSNumber *)auctionPrice {
    BUNativeAd *ad = [self buNativeAd];
    if (ad && auctionPrice && self.loadingStatus == XMIFeedAdModelLoadingStatusLoadSuccess) {
        [ad loss:auctionPrice lossReason:nil winBidder:nil];
    }
}

#pragma mark - BUNativeAdsManagerDelegate
- (void)nativeAdsManagerSuccessToLoad:(BUNativeAdsManager *)adsManager nativeAds:(NSArray<BUNativeAd *> *_Nullable)nativeAdDataArray {
    [nativeAdDataArray enumerateObjectsUsingBlock:^(BUNativeAd * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        obj.rootViewController = self.rootViewController;
    }];
    self.nativeAdDataArray = nativeAdDataArray;
    self.relatedData.originData = [self buNativeAd];
    if (self.relatedData.isMobileRtb) {
        // 转换价格，单位为元，原始为分
        NSDictionary *dic = [self buNativeAd].data.mediaExt;
        NSInteger originPrice = [dic integerMaybeForKey:@"price"];
        self.relatedData.price = originPrice * 1.0 / 100;
    }
    [self loadSuccess];
}

- (void)nativeAdsManager:(BUNativeAdsManager *)adsManager didFailWithError:(NSError *_Nullable)error {
    [self loadFailed:error];
}

#pragma mark - parse

- (id)adData
{
    if (self.nativeAdDataArray.count == 0) {
        return nil;
    }
    return self.nativeAdDataArray.firstObject;
}

- (BUNativeAd *)buNativeAd
{
    return self.adData;
}

- (NSString *)adTitle
{
    return [[[self buNativeAd] data] AdTitle];
}

- (NSString *)description
{
    return [[[self buNativeAd] data] AdDescription];
}

- (NSString *)adDescription
{
    return [[[self buNativeAd] data] AdDescription];
}

- (NSString *)adButtonText
{
    return [[[self buNativeAd] data] buttonText];
}

- (NSArray<NSString *> *)adImageURLs
{
    NSMutableArray *imageURLs=  [NSMutableArray arrayWithCapacity:[[[self buNativeAd] data] imageAry].count];
    [[[[self buNativeAd] data] imageAry] enumerateObjectsUsingBlock:^(BUImage * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        [imageURLs addObject:obj.imageURL];
    }];
    return [imageURLs copy];
}

- (NSString *)adVideoURL
{
    return nil;
}

- (NSString *)iconUrl
{
    return [[[self buNativeAd] data] icon].imageURL;
}

@end
