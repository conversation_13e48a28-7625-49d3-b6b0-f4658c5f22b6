//
//  XMIBUFindFeedNativeAdABTestView.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/3/1.
//

#import "XMIBUFindFeedNativeAdABTestView.h"
#import "XMIAdMarkButtonView.h"
#import <BUAdSDK/BUNativeAd.h>
#import <BUAdSDK/BUNativeAdRelatedView.h>
#import "XMIAdError.h"
#import "XMIBUConverter.h"
#import "XMIAdNativeLogger.h"
#import "UIView+XMIUtils.h"
#import "XMIAdMacro.h"

@interface XMIBUFindFeedNativeAdABTestView ()<BUNativeAdDelegate, BUVideoAdViewDelegate>

@property (nonatomic, strong) BUNativeAdRelatedView *relatedView;
@property (nonatomic, strong) BUNativeAd *adData;

@property (nonatomic, strong) BUVideoAdView *videoView;

@property (nonatomic, weak) UIViewController *rootViewController;

@end

@implementation XMIBUFindFeedNativeAdABTestView

- (BUNativeAdRelatedView *)relatedView
{
    if (!_relatedView) {
        _relatedView = [[BUNativeAdRelatedView alloc] init];
    }
    return _relatedView;
}

#pragma mark - adview delegate

- (void)updateRootViewController:(UIViewController *)rootViewController
{
    self.rootViewController = rootViewController;
    self.adData.rootViewController = self.rootViewController;
}

- (void)customRenderWithAdData:(id)adData
{
    if (!adData || ![adData isKindOfClass:[BUNativeAd class]]) {
        [self failRenderWithError:[XMIAdError emptyDataError]];
        return;
    }
    if (self.adData == adData) {
        return;
    }
    [self.adData unregisterView];
    self.adData = adData;
    self.adData.delegate = self;
    self.adData.rootViewController = self.rootViewController;
    [self doCustomRender];
}


- (void)doCustomRender
{
    if (self.videoView) {
        [self.videoView removeFromSuperview];
    }
    [self.relatedView refreshData:self.adData];
    [self.adData registerContainer:self withClickableViews:@[self.contentView]];
    [self.adMarkButtonView updateFeedAdType:XMIAdMarkAdTypeBU];
    switch (self.adData.data.imageMode) {
        case BUFeedVideoAdModeImage:
        case BUFeedADModeSquareVideo:
        case BUFeedADModeUnionSplashVideo:
        case BUFeedVideoAdModePortrait:
            [self loadVideo];
            break;
            
        default:
            self.videoView.delegate = nil;
            [self.videoView removeFromSuperview];
            self.videoView = nil;
            break;
    }
}

- (void)loadVideo
{
    self.videoView.delegate = nil;
    [self.videoView removeFromSuperview];
    self.videoView = self.relatedView.videoAdView;
    self.videoView.delegate = self;
    self.videoView.autoresizingMask = UIViewAutoresizingNone;
    [self.coverImageView.superview insertSubview:self.videoView belowSubview:self.coverImageView];
    self.videoView.frame = self.coverImageView.frame;
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    if (self.videoView.superview == self.coverImageView) {
        self.videoView.frame = self.coverImageView.bounds;
    } else {
        self.videoView.frame = self.coverImageView.frame;
    }
}

#pragma mark - video delegate

- (void)videoAdView:(BUVideoAdView *)videoAdView didLoadFailWithError:(NSError *_Nullable)error
{
    XMILogNativeAdInfo(@"BU VODEP FAILED");
    [self failRenderWithError:error];
}

- (void)playerReadyToPlay:(BUVideoAdView *)videoAdView
{
    XMILogNativeAdInfo(@"BU VIDEO READY TO PLAY");
    [self.coverImageView insertSubview:self.videoView atIndex:0];
    self.videoView.frame = self.coverImageView.bounds;
}

- (void)videoAdView:(BUVideoAdView *)videoAdView stateDidChanged:(BUPlayerPlayState)playerState
{
    [self playerStateChanged:[XMIBUConverter playStateFromBUPlayState:playerState]];
    if (self.videoView.superview != self.coverImageView && playerState == BUPlayerStatePlaying) {
        [self.coverImageView insertSubview:self.videoView atIndex:0];
        self.videoView.frame = self.coverImageView.bounds;
    }
}

- (void)nativeAdDidClick:(BUNativeAd *)nativeAd withView:(UIView *)view
{
    [self clickAdView:@{}];
}

- (void)playerDidPlayFinish:(BUVideoAdView *)videoAdView
{
    XMILogNativeAdInfo(@"BU VIDEO PLAY FINISHED");
}

- (void)videoAdViewDidClick:(BUVideoAdView *)videoAdView
{
    XMILogNativeAdInfo(@"BU VIDEO CLICK");
    [self clickAdView:@{}];
}

- (void)videoAdViewFinishViewDidClick:(BUVideoAdView *)videoAdView
{
    XMILogNativeAdInfo(@"BU VIDEO FINISH CLICK");
    [self clickAdView:@{}];
}

- (void)videoAdViewDidCloseOtherController:(BUVideoAdView *)videoAdView interactionType:(BUInteractionType)interactionType;
{
    XMILogNativeAdInfo(@"BU VIDEO CLOSE DETAIL INTERACTION TYPE %zd", interactionType);
    [self didCloseAdDetail];
}


+ (CGFloat)calAdHeight:(id)adData withAdWidth:(CGFloat)adWidth
{
    return adWidth * 9 / 16.0 + XMIAdHSpace(24);
}

- (void)dealloc
{
    [self.videoView removeFromSuperview];
    self.videoView.delegate = nil;
    self.adData.delegate = nil;
}

- (void)reloadUI
{
    [self.adData unregisterView];
    [self doCustomRender];
}

@end
