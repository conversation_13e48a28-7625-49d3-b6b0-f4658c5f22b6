//
//  XMIBUConverter.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/14.
//

#import "XMIBUConverter.h"
#import <BUAdSDK/BUPlayerPublicDefine.h>

@implementation XMIBUConverter

+ (XMIPlayerPlayState)playStateFromBUPlayState:(NSInteger)buPlayState {
    XMIPlayerPlayState state = XMIPlayerStateUnknown;
    switch (buPlayState) {
        case BUPlayerStateDefalt:
            state = XMIPlayerStateInitial;
            break;
        case BUPlayerStateBuffering:
            state = XMIPlayerStateBuffering;
            break;
        case BUPlayerStatePlaying:
            state = XMIPlayerStatePlaying;
            break;
        case BUPlayerStatePause:
            state = XMIPlayerStatePause;
            break;
        case BUPlayerStateStopped:
            state = XMIPlayerStateStopped;
            break;
        case BUPlayerStateFailed:
            state = XMIPlayerStateFailed;
            break;
            
        default:
            break;
    }
    return state;
}

@end
