//
//  XMIBUExpressAdFactory.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/9/3.
//

#import "XMIBUExpressAdFactory.h"
#import "XMIBUTemplateExpressAd.h"
#import "XMIBUCustomExpressAd.h"

@implementation XMIBUExpressAdFactory

+ (XMIExpressAd *)expressAdWithSlot:(XMIAdSlot *)adSlot {
    XMIExpressAd *expressAd = nil;
    switch (adSlot.adType) {
        case XMIAdSlotAdTypeFeed:
        case XMIAdSlotAdTypeFeed_Draw:
            expressAd = [[XMIBUTemplateExpressAd alloc] initWithSlot:adSlot];
            break;
        case XMIAdSlotAdTypeFeed_Custom:
        case XMIAdSlotAdTypeFeed_Paster:
        case XMIAdSlotAdTypeFeed_DrawCustom:
            expressAd = [[XMIBUCustomExpressAd alloc] initWithSlot:adSlot];
            break;
        default:
            break;
    }
    return expressAd;
}

@end
