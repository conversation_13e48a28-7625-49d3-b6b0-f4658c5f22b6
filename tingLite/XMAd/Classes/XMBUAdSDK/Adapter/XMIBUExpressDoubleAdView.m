//
//  XMIBUExpressDoubleAdView.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/11/30.
//

#import "XMIBUExpressDoubleAdView.h"
#import <BUAdSDK/BUNativeExpressAdView.h>
#import <BUAdSDK/BUNativeAd.h>
#import <BUAdSDK/BUNativeAdRelatedView.h>
#import <XMWebImage/UIImageView+WebCache.h>
#import "XMIAdMacro.h"
#import "XMIAdDefines.h"
#import "XMIAdRelatedData.h"
#import "XMICommonUtils.h"
#import "XMIAdViewProtocol.h"
#import "XMIExpressAdSingleView+Internal.h"
#import "XMIBUConverter.h"
#import <Masonry/Masonry.h>
#import "XMAdDownloadMediaManager.h"
#import "NSURL+XMICommon.h"
#import "XMIAnimatedImageView.h"

@interface XMIBUExpressDoubleAdView ()<XMIAdViewProtocol, BUNativeAdDelegate, BUVideoAdViewDelegate>

@property (nonatomic, strong) BUNativeExpressAdView *adView;
@property (nonatomic, strong) BUNativeAd *adData;
@property (nonatomic, strong) BUNativeAdRelatedView *relatedObject;
//@property (nonatomic, strong) UIImageView *buCoverImageView;
@property (nonatomic, strong) XMAdDownloadMediaManager *downloadManager;

@end

@implementation XMIBUExpressDoubleAdView
@synthesize isCustom = _isCustom;

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        _isCustom = YES;
        self.clipsToBounds = YES;
        self.relatedObject = [[BUNativeAdRelatedView alloc] init];
        self.downloadManager = [[XMAdDownloadMediaManager alloc] init];

        [self p_setUI];
        [self p_setContrains];
        
    }
    return self;
}
//- (UIImageView *)buCoverImageView
//{
//    if (!_buCoverImageView) {
//        _buCoverImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, [self adWidth], [self adHeight])];
//    }
//    return _buCoverImageView;
//}

- (void)refreshWithData:(XMIAdRelatedData *)adData {
    [super refreshWithData:adData];
    if (adData.loadingStatus == XMIAdRelatedLoadingStatusNormal) {
        return;
    }
    self.adData = (BUNativeAd *)adData.originData;
    if (!self.adData || ![self.adData isKindOfClass:BUNativeAd.class]) {
        return;
    }
    self.adData.delegate = self;
    
    BUNativeAdRelatedView *relatedView = (BUNativeAdRelatedView *)self.relatedObject;
    relatedView.videoAdView.hidden = YES;
    [relatedView refreshData:self.adData];
    if (relatedView.videoAdView != nil) {
        relatedView.videoAdView.delegate = self;
    }
    
    self.frame = CGRectMake(0, 0, adData.adWidth, adData.adHeight);

    
    [self renderAdContent];
    
    [self.adData registerContainer:self.contentView withClickableViews:@[self.contentView]];
    
    [self startSetUpUIWithModel:self.adData];
    
    UIImage *adMarkImage = relatedView.logoADImageView.image;
    if (!adMarkImage) {
        adMarkImage = [XMICommonUtils imageNamed:@"pic_ad_mark_3"];
    }
    [self p_setAdMark:adMarkImage];
    [self setStatusButtonColorAnimation];
}


/**
 模版渲染
 */
- (void)renderTemplate {
    if (self.adView != nil) {
        [self.adView render];
    }
}

/**
 自渲染
 */
- (void)renderCustom {
    

}


- (void)p_setUI
{
    BUNativeAdRelatedView *relatedObject = (BUNativeAdRelatedView *)self.relatedObject;
    [self addSubview:self.placeHolderView];
    self.placeHolderView.frame = self.bounds;
    [self addSubview:self.contentView];
    [self.contentView addSubview:self.contentMaskView];
    [self.contentMaskView addSubview:self.coverImageView];
    [self.contentMaskView addSubview:relatedObject.videoAdView];
    [self.contentView addSubview:self.adMark];
    [self.contentView addSubview:self.titleMaskView];
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.statusButton];
    [self.contentView addSubview:self.closeButton];
}

- (void)p_setContrains
{

    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    [self.placeHolderView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
//    CGFloat logoWidth = 12*58/18.5;
//    [relatedObject.logoADImageView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.right.top.equalTo(self.contentView);
//        make.size.mas_equalTo(CGSizeMake(logoWidth, 12));
//    }];
    
    [self.contentMaskView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
    
    [self.titleMaskView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.bottom.right.equalTo(self.contentView);
        make.height.mas_equalTo(105);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView.mas_left).offset(kXMIExpressAdDoubleVideoViewHoriznalMargin);
        make.bottom.equalTo(self.contentView.mas_bottom).offset(-36);
        make.right.equalTo(self.contentView.mas_right).offset(-kXMIExpressAdDoubleVideoViewHoriznalMargin);
    }];
    
    [self.statusButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(kXMIExpressAdDoubleVideoViewHoriznalMargin);
        make.bottom.mas_equalTo(-10);
        make.height.mas_equalTo(18);
        make.width.mas_equalTo(0);
    }];
    
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.bottom.equalTo(self).mas_offset(-7);
        make.size.mas_equalTo(CGSizeMake(16, 16));
    }];
    
}

/**
 广告实际内容
 */
- (void)renderAdContent {
    self.coverImageView.hidden = YES;
    BUNativeAdRelatedView *relatedObject = (BUNativeAdRelatedView *)self.relatedObject;
    // video
    if (self.adData.data.imageMode == BUFeedVideoAdModeImage
        || self.adData.data.imageMode == BUFeedVideoAdModePortrait
        || self.adData.data.imageMode == BUFeedADModeSquareVideo) {
        relatedObject.videoAdView.hidden = NO;
        BUImage *buImage = self.adData.data.imageAry.firstObject;
        [self.contentMaskView setImageWithURL:[NSURL URLWithString:buImage.imageURL] placeholderImage:nil];
        [self didRenderResultWithDataObject:self.adData mediaSize:CGSizeMake(self.adData.data.videoResolutionWidth, self.adData.data.videoResolutionHeight) withShowType:XMIAdStyleHomeDoubleRowVerticalVideo];
    }
    // 大图
    else if (self.adData.data.imageMode == BUFeedADModeLargeImage ||
             self.adData.data.imageMode == BUFeedADModeImagePortrait) {
        if (self.adData.data.imageAry && self.adData.data.imageAry.count) {
            BUImage *buImage = self.adData.data.imageAry.firstObject;
            [self fillImageView:self.coverImageView withBUImage:buImage];
        } else {
            self.coverImageView.image = [XMICommonUtils imageNamed:@"ad_bkg_default"];
        }
    }
    // 三小图
    else if (self.adData.data.imageMode == BUFeedADModeGroupImage) {
        // TODO:信息流暂不支持三小图，简单填充
        if (self.adData.data.imageAry && self.adData.data.imageAry.count) {
            BUImage *buImage = self.adData.data.imageAry.firstObject;
            [self fillImageView:self.coverImageView withBUImage:buImage];
        } else {
            self.coverImageView.image = [XMICommonUtils imageNamed:@"ad_bkg_default"];
        }
    }
}

- (void)fillImageView:(XMIAnimatedImageView *)imageView withBUImage:(BUImage *)buImage {
    self.coverImageView.hidden = NO;
    self.contentMaskView.hidden = NO;
    if (buImage.imageURL != nil) {
        __weak typeof(self) weakSelf = self;
        NSURL *URL = [NSURL URLWithString:buImage.imageURL];
        [self.contentMaskView setImageWithURL:[NSURL URLWithString:buImage.imageURL] placeholderImage:nil];
        self.downloadManager.identifier = [self.relatedData getIdentifier];
        self.downloadManager.url = URL;
        UIImage *placeImage = [XMICommonUtils imageNamed:@"ad_bkg_default"];
        [self.downloadManager setImageWithImageView:imageView placeholderImage:placeImage complete:^(XMAdDownloadMediaManager * _Nonnull manager) {
            NSString * identifier = [weakSelf.relatedData getIdentifier];
            if ([identifier isEqualToString:manager.identifier]) {
                if (!manager.isSuccess) {
                    weakSelf.coverImageView.image = nil;
                    weakSelf.coverImageView.backgroundColor = XMI_COLOR_RGB(0xCCCCCC);
                }
                if (manager.image) {
                    [weakSelf didRenderResultWithDataObject:weakSelf.adData mediaSize:manager.image.size withShowType:XMIAdStyleHomeDoubleRowVerticalImage];
                }
            }
        }];
    } else {
        imageView.image = [XMICommonUtils imageNamed:@"ad_bkg_default"];
    }
}

- (int)getAdShowType {
    XMIAdShowType showType = XMIAdShowTypeImage;
    if (self.adData == nil) {
        return showType;
    }
    if (self.adData.data.imageMode == BUFeedVideoAdModeImage
        || self.adData.data.imageMode == BUFeedVideoAdModePortrait
        || self.adData.data.imageMode == BUFeedADModeSquareVideo) {
        showType = XMIAdShowTypeVideo;
    }
    else if (self.adData.data.imageMode == BUFeedADModeLargeImage) {
        showType = XMIAdShowTypeImage;
    } else if (self.adData.data.imageMode == BUFeedADModeGroupImage) {
        showType = XMIAdShowTypeImage;
    }
    return showType;
}


- (void)startSetUpUIWithModel:(id)model
{
    BUNativeAd *nativeAd = nil;
    if ([model isKindOfClass:BUNativeAd.class]) {
        nativeAd = (BUNativeAd *)model;
    }
    if (!nativeAd) {
        return;
    }
    self.placeHolderView.hidden = NO;
    self.closeButton.hidden = NO;
    self.contentView.hidden = NO;
    self.contentMaskView.hidden = NO;
    self.titleLabel.hidden = NO;
    self.titleMaskView.hidden = NO;
    self.coverImageView.backgroundColor = XMI_COLOR_RGB(0xEDEDED);
    if (self.relatedData.loadingStatus != XMIAdRelatedLoadingStatusHasSureSizeRadio) {
        self.placeHolderView.hidden = NO;
        self.contentView.hidden = YES;
    }else{
        self.placeHolderView.hidden = YES;
        self.contentView.hidden = NO;
    }
    if (self.relatedData.titleTextLayout) {
        self.titleLabel.textLayout = self.relatedData.titleTextLayout;
    }else{
        YYTextLayout *textLayout = [XMIExpressAdDoubleView getTitleTextLayout:nativeAd adViewWidth:self.relatedData.adWidth];
        self.relatedData.titleTextLayout = textLayout;
        self.titleLabel.textLayout = textLayout;
    }
    
    [self p_configStatusButton:nativeAd];
    
    if (!self.statusButton.hidden) {
        self.titleLabel.preferredMaxLayoutWidth = self.relatedData.adWidth - kXMIExpressAdDoubleVideoViewHoriznalMargin - kXMIExpressAdDoubleVideoViewHoriznalMargin;
        [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.contentView.mas_bottom).offset(-36);
            make.right.equalTo(self.contentView.mas_right).offset(-kXMIExpressAdDoubleVideoViewHoriznalMargin);
        }];
    }else{
        self.titleLabel.preferredMaxLayoutWidth = self.relatedData.adWidth - kXMIExpressAdDoubleVideoViewHoriznalMargin - kXMIExpressAdDoubleVideoViewBigHoriznalMargin;
        [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.contentView.mas_bottom).offset(-8);
            make.right.equalTo(self.contentView.mas_right).offset(-kXMIExpressAdDoubleVideoViewBigHoriznalMargin);
        }];
    }
    
    if (self.statusButton.hidden && !nativeAd.data.AdTitle.length) {
        self.titleMaskView.hidden = YES;
    }else{
        self.titleMaskView.hidden = NO;
    }
    
}

- (void)p_setAdMark:(UIImage *)image
{
    if (!image) {
        return;
    }
    self.adMark.image = image;
    CGFloat width = 13 * image.size.width/image.size.height;
    self.adMark.frame = CGRectMake(self.bounds.size.width - width, 0, width, 13);
}

- (void)p_configStatusButton:(BUNativeAd *)model
{
    if (model.data.buttonText.length) {
        [self.statusButton setTitle:model.data.buttonText forState:UIControlStateNormal];
        self.statusButton.hidden = NO;
        if (model.data.buttonText.length == 2) {
            [self.statusButton mas_updateConstraints:^(MASConstraintMaker *make) {
                make.width.mas_equalTo(42);
            }];
        }else{
            [self.statusButton mas_updateConstraints:^(MASConstraintMaker *make) {
                make.width.mas_equalTo(54);
            }];
        }
        
//        if ([model.data interactionType] == BUInteractionTypeDownload) {
//            /// 下载
//            self.statusButton.backgroundColor = XMI_COLOR_RGB(0x1295FF);
//        }else{
//            self.statusButton.backgroundColor = XMI_COLOR_RGB(0xFF4646);
//        }
        
    }else{
        self.statusButton.hidden = YES;
    }
    
}

/// 获取是否是下载
- (BOOL)getAdIsDownloadApp
{
    if ([self.adData isKindOfClass:BUNativeAd.class]) {
        return [self.adData.data interactionType] == BUInteractionTypeDownload;
    }
    return NO;
}

- (void)didRenderResultWithDataObject:(BUNativeAd *)dataObject
                            mediaSize:(CGSize)mediaSize
                         withShowType:(XMIAdShowStyle)showstyle
{
    CGFloat sourceRadio = mediaSize.height/mediaSize.width;
    CGFloat originalSourceRadio = sourceRadio;
    BOOL isErrorVideoHorizinal = NO;
    if (sourceRadio > 0 && sourceRadio < 1.4) {
        isErrorVideoHorizinal = YES;
        sourceRadio = 1.4;
    }
    
    if (sourceRadio > 1.8) {
        sourceRadio = 1.8;
    }
    self.relatedData.sourceRadio = originalSourceRadio;
    self.relatedData.sizeRadio = sourceRadio;
    
    CGFloat adHeight = [XMIBUExpressDoubleAdView getAdViewHeightWithRelatedData:self.relatedData];
    if (fabs(self.relatedData.adHeight - adHeight) > 5) {
        self.relatedData.loadingStatus = XMIAdRelatedLoadingStatusNormal;
    }
    self.relatedData.adHeight = adHeight;
    CGFloat videoTopMargin = 0;
    CGFloat videoHeight = self.relatedData.adWidth*originalSourceRadio;
    if (isErrorVideoHorizinal) {
        videoTopMargin = (adHeight - videoHeight)/2.0;
    }
    
    if (self.adData.data.imageMode == BUFeedVideoAdModeImage
        || self.adData.data.imageMode == BUFeedVideoAdModePortrait
        || self.adData.data.imageMode == BUFeedADModeSquareVideo) {
        BUNativeAdRelatedView *relatedObject = (BUNativeAdRelatedView *)self.relatedObject;
        relatedObject.videoAdView.frame = CGRectMake(0, videoTopMargin, self.relatedData.adWidth, videoHeight);
    }
    // 大图
    else if (self.adData.data.imageMode == BUFeedADModeLargeImage ||
             self.adData.data.imageMode == BUFeedADModeImagePortrait) {
        self.coverImageView.frame = CGRectMake(0, videoTopMargin, self.relatedData.adWidth, videoHeight);
    }
    // 三小图
    else if (self.adData.data.imageMode == BUFeedADModeGroupImage) {
        self.coverImageView.frame = CGRectMake(0, videoTopMargin, self.relatedData.adWidth, videoHeight);
    }
    
    
    self.placeHolderView.hidden = YES;
    self.relatedData.isDidRender = YES;
    
    BOOL needRefresh = self.relatedData.loadingStatus != XMIAdRelatedLoadingStatusHasSureSizeRadio;
    self.relatedData.loadingStatus = XMIAdRelatedLoadingStatusHasSureSizeRadio;
    [self adViewDidRenderHasNeedRefresh:needRefresh];
}

- (void)closeButtonAction:(UIButton *)button
{
    [self adViewDidClickedClose:button];
}

#pragma mark - BUNativeAdDelegate
- (void)nativeAdDidLoad:(BUNativeAd *)nativeAd view:(UIView *_Nullable)view {
    XMILog(@"nativeAdDidLoad:view:");
}

- (void)nativeAd:(BUNativeAd *)nativeAd didFailWithError:(NSError *_Nullable)error {
    XMILog(@"nativeAd:didFailWithError:");
}

- (void)nativeAdDidBecomeVisible:(BUNativeAd *)nativeAd {
    XMILog(@"nativeAdDidBecomeVisible");
    [self adViewWillShow];
}

- (void)nativeAdDidCloseOtherController:(BUNativeAd *)nativeAd interactionType:(BUInteractionType)interactionType {
    XMILog(@"nativeAdDidCloseOtherController");
    [self adViewDetailControllerDidClosed];
}

- (void)nativeAdDidClick:(BUNativeAd *)nativeAd withView:(UIView *_Nullable)view {
    XMILog(@"nativeAdDidClick:withView:");
    [self adViewDidClick:view];
}

- (void)nativeAd:(BUNativeAd *_Nullable)nativeAd dislikeWithReason:(NSArray<BUDislikeWords *> *_Nullable)filterWords {
    XMILog(@"nativeAd:dislikeWithReason:");
    NSMutableArray *reasonArray = [[NSMutableArray alloc] init];
    for (int i = 0; i < filterWords.count; i++) {
        BUDislikeWords *dWord = filterWords[i];
        if (dWord.name != nil) {
            [reasonArray addObject:dWord.name];
        }
    }
    [self adViewDislikeWithReason:reasonArray];
}

- (void)nativeAd:(BUNativeAd *_Nullable)nativeAd adContainerViewDidRemoved:(UIView *)adContainerView {
    [self adViewDidRemoved];
}

#pragma mark - BUVideoAdViewDelegate
- (void)videoAdView:(BUVideoAdView *)videoAdView didLoadFailWithError:(NSError *_Nullable)error {
    XMILog(@"BUVideoAdView:didLoadFailWithError:");
}

- (void)playerReadyToPlay:(BUVideoAdView *)videoAdView {
    XMILog(@"playerReadyToPlay");
}

- (void)videoAdView:(BUVideoAdView *)videoAdView stateDidChanged:(BUPlayerPlayState)playerState {
    XMILog(@"videoAdView:stateDidChanged:");
    XMIPlayerPlayState state = [XMIBUConverter playStateFromBUPlayState:playerState];
    [self adViewPlayerStateChanged:state];
}

- (void)playerDidPlayFinish:(BUVideoAdView *)videoAdView {
    XMILog(@"playerDidPlayFinish");
    [self adViewPlayerDidPlayFinish:nil];
}

- (void)videoAdViewDidClick:(BUVideoAdView *)videoAdView {
    XMILog(@"videoAdViewDidClick");
    [self adViewDidClick:videoAdView.superview];
}

- (void)videoAdViewFinishViewDidClick:(BUVideoAdView *)videoAdView {
    XMILog(@"videoAdViewFinishViewDidClick");
}

- (void)videoAdViewDidCloseOtherController:(BUVideoAdView *)videoAdView interactionType:(BUInteractionType)interactionType {
    XMILog(@"videoAdViewDidCloseOtherController:interactionType:");
    [self adViewDetailControllerDidClosed];
}

- (void)dealloc
{
    _relatedObject = nil;
}

@end

