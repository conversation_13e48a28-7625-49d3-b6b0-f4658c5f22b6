//
//  XMIExpressAdBusniessExtraModel.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/11/7.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XMIExpressAdBusniessExtraModel : NSObject
@property(nonatomic, strong) NSString *albumCornerMark;
@property(nonatomic, strong) NSString *cover;
@property(nonatomic, strong) NSString *title;
@property(nonatomic, strong) NSString *broadcasterAvata;
@property(nonatomic, strong) NSString *broadcasterName;
@property(nonatomic, strong) NSString *recommendTags;
@property(nonatomic, strong) NSString *subscribleCount;
@property(nonatomic, strong) NSString *evaluateScore;
/// ALBUM TRACK LIVE
@property(nonatomic, strong) NSString *promoteType;
@property(nonatomic, strong) NSString *promoteId;

/// 直播场次ID
@property(nonatomic, strong) NSString *liveId;
//直播房间ID
@property(nonatomic, strong) NSString *roomId;
//直播状态
@property(nonatomic, strong) NSString *LiveBroadcastState;
//直播类型
@property(nonatomic, strong) NSString *liveRoomType;
//主播ID
@property(nonatomic, strong) NSString *anchorId;
@property(nonatomic, strong) NSString *broadcasterId;


@end


//@interface XMBusinessExtraInfo : NSObject
//@property(nonatomic, strong)XMIExpressAdBusniessExtraModel *adInfo;
//@end





NS_ASSUME_NONNULL_END
