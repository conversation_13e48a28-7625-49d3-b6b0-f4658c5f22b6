//
//  XMIExpressAdView.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/6.
//

#import <UIKit/UIKit.h>
#import "XMIAdDefines.h"
NS_ASSUME_NONNULL_BEGIN

@protocol XMIExpressAdViewDelegate;
@class XMIAdRelatedData;

@interface XMIExpressAdView : UIView

/**
 是否自渲染
 */
@property (nonatomic, assign, readonly) BOOL isCustom;

/**
 背景板样式滚动区域
 相对于window的坐标，仅背景板样式广告需要设置
 */
@property (nonatomic, assign) CGRect scrollFrame;
@property (nonatomic, weak) id<XMIExpressAdViewDelegate> delegate;

@property (nonatomic, weak) UIViewController *rootViewController;

@property (nonatomic, strong) XMIAdRelatedData *relatedData;

- (void)refreshWithData:(XMIAdRelatedData *)adData;

- (void)render:(CGSize)size;

- (void)beginUnregisterDataObject;

- (UIView *)dislikePointView;

/**
 广告视图高度
 */
- (CGFloat)adHeight;
/**
 广告视图宽度
 */
- (CGFloat)adWidth;

/**
 通知view滚动了
 */
- (void)scrollViewDidScroll;

/**
 通知滑动停止了
 */
- (void)scrollViewDidEndScroll;

/// 广告按钮关闭
- (void)adViewDidCloseClickedResult;


/// 控制器已经出现
- (void)controllerViewDidAppear;

/// 控制器已经消失
- (void)controllerViewDidDisappear;


- (void)detectExpose:(BOOL)needDelay;

/**
 internal
 */

- (void)adViewDidRenderHasNeedRefresh:(BOOL)isNeedRefresh;
- (void)adViewDidRenderFailWithError:(NSError *)error;
- (void)adViewWillShow;
- (void)adViewDidClick:(UIView *)aView;
- (void)adViewDidClick:(UIView *)aView withUserInfo:(nullable NSDictionary *)userInfo;
- (void)adViewDidRemoved;
- (void)adViewWillPresentScreen;
- (void)adViewDidPresentScreen;
- (void)adViewDetailControllerDidClosed;
- (void)adViewPlayerStateChanged:(XMIPlayerPlayState)state;
- (void)adViewPlayerDidPlayFinish:(NSError *_Nullable)error;
- (void)adViewPlayerDidBecomeInvisible;
- (void)adViewDislikeWithReason:(NSArray<__kindof NSString *> *)reasons;
- (void)adViewDidClickedClose:(UIView *)aView;
- (void)adViewDidExpose;

@end

@protocol XMIExpressAdViewDelegate <NSObject>

@optional
- (void)expressAdViewDidRender:(XMIExpressAdView *)adView hasNeedRefresh:(BOOL)isNeedRefresh;
- (void)expressAdView:(XMIExpressAdView *)adView didRenderFailWithError:(NSError *)error;
- (void)expressAdView:(XMIExpressAdView *)adView adViewWillShow:(UIView *)aView;
- (void)expressAdView:(XMIExpressAdView *)adView adViewDidClick:(UIView *)aView withUserInfo:(nullable NSDictionary *)userInfo;
- (void)expressAdView:(XMIExpressAdView *)adView playerStateChanged:(XMIPlayerPlayState)state;
- (void)expressAdView:(XMIExpressAdView *)adView playerDidPlayFinish:(nullable NSError *)error;
- (void)expressAdView:(XMIExpressAdView *)adView playTimeDidChanged:(CGFloat)currentTime;
- (void)expressAdView:(XMIExpressAdView *)adView relatedData:(XMIAdRelatedData *)relatedData andPlayStatusChange:(NSInteger)status;
- (void)expressAdViewPlayerDidBecomeInvisible:(XMIExpressAdView *)adView;
- (void)expressAdViewWillPresentScreen:(XMIExpressAdView *)adView;
- (void)expressAdViewDidPresentScreen:(XMIExpressAdView *)adView;
- (void)expressAdViewDetailControllerDidClosed:(XMIExpressAdView *)adView;
- (void)expressAdView:(XMIExpressAdView *)adView adViewDidClickedClose:(UIView *)aView;
- (void)expressAdViewDidExpose:(XMIExpressAdView *)adView;
- (void)expressAdViewWillShow:(XMIExpressAdView *)adView;
- (void)expressAdViewDidRemoved:(XMIExpressAdView *)adView;
- (void)expressAdView:(XMIExpressAdView *)adView dislikeWithReason:(NSArray<__kindof NSString *> *)reasons;
- (void)expressAdView:(XMIExpressAdView *)adView
 didClickedAnchorItem:(XMIAdRelatedData *)relatedData
          clickedView:(UIView *)aView;

@end

extern NSString * const kUserInfoAbsX;
extern NSString * const kUserInfoAbsY;
extern NSString * const kUserInfoX;
extern NSString * const kUserInfoY;
extern NSString * const kUserInfoJumpNotSupport;

NS_ASSUME_NONNULL_END
