//
//  XMIExpressAdloadManager.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/12/14.
//

#import "XMIExpressAdloadManager.h"
#import "XMIAdRelatedData.h"
#import "XMICommonUtils.h"
#import "XMIAdReporter.h"
#import "XMIAdReporter+AD.h"
#import "XMIExpressAd.h"
#import "XMIAdConverter.h"
#import "XMIAdMacro.h"
#import "XMIExpressAdFactory.h"
#import "XMITimeoutHelper.h"
#import "XMIExpressAdDoubleView.h"
#import "XMIAdError.h"
#import "XMIAdDataCenter.h"
#import "XMIAdModel.h"

NSString * XMIExpressAdloadManagerLoadDataSuccessNotification = @"XMIExpressAdloadManagerLoadDataSuccessNotification";
NSString * XMIExpressAdloadManagerLoadDataFailNotification = @"XMIExpressAdloadManagerLoadDataFailNotification";

@interface XMIExpressAdloadManager()<XMIExpressAdDelegate>
@property(nonatomic, strong)NSMutableDictionary *adLoaders;
@end

@implementation XMIExpressAdloadManager
/**
 广告加载
 */
- (void)loadAdData:(XMIAdRelatedData *)relatedData {
    if (relatedData.isLoaded) {
        return;
    }
    relatedData.isLoaded = YES;
    
    XMIAdStatusReporter *reporter = [[XMIAdStatusReporter alloc] init];
    reporter.paddingMs = relatedData.paddingTimeMs;
    reporter.startMS = [XMICommonUtils currentMS];
    [[XMIAdReporter sharedInstance] addStatusReport:reporter];
    
    [self doLoadAdData:relatedData withReporter:reporter.reporterId];
}

/**
 adLoaders广告加载器
 1.加载过程中持有加载器，防止container重用导致被释放无回调
 2.加载完成后解除持有，释放不可见ad持有的加载器
 */
- (NSMutableDictionary *)adLoaders {
    if (_adLoaders == nil) {
        _adLoaders = [[NSMutableDictionary alloc] init];
    }
    return _adLoaders;
}

- (NSString *)getLoaderKey:(XMIAdRelatedData *)relatedData {
    return [NSString stringWithFormat:@"%p", relatedData];
}

- (XMIExpressAd *)getLoaderWithAdData:(XMIAdRelatedData *)relatedData {
    return [self.adLoaders objectForKey:[self getLoaderKey:relatedData]];
}

- (void)saveLoader:(XMIExpressAd *)expressAd withAdData:(XMIAdRelatedData *)relatedData {
    [self.adLoaders setObject:expressAd forKey:[self getLoaderKey:relatedData]];
}

- (void)removeLoaderWithAdData:(XMIAdRelatedData *)relatedData {
    [self.adLoaders removeObjectForKey:[self getLoaderKey:relatedData]];
}


/**
 调用相应提供方加载广告
 */
- (void)doLoadAdData:(XMIAdRelatedData *)relatedData withReporter:(long long)reporterId {
    XMIExpressAd *expressAd = [self getLoaderWithAdData:relatedData];
    if (expressAd == nil) {
        NSString *provider = [XMIAdConverter providerFromAdType:relatedData.adtype];
        NSString *clsName = [NSString stringWithFormat:@"XMI%@ExpressAdFactory", provider];
        __typeof([XMIExpressAdFactory class]) cls = NSClassFromString(clsName);
        if (cls == nil) {
            return;
        }
        
        XMIAdSlot *adSlot = [[XMIAdSlot alloc] init];
        adSlot.adSize = CGSizeMake(XMI_SCREEN_WIDTH, 0);
        adSlot.adCount = 1;
        adSlot.positionID = relatedData.dspPositionId;
        adSlot.adType = [XMIAdConverter slotAdTypeFromAdType:relatedData.adtype];
        adSlot.slotRealBid = relatedData.slotRealBid;
        adSlot.slotAdm = relatedData.slotAdm;
        adSlot.relatedData = relatedData;
        expressAd = [cls expressAdWithSlot:adSlot];
        if (expressAd == nil) {
            XMILog(@"get XMIExpressAd instance fail !!!");
            return;
        }
        
        [self saveLoader:expressAd withAdData:relatedData];
    }
    XMIHandlerID timeoutID = [self doLoadAdData:relatedData withTimeout:reporterId];
    expressAd.delegate = self;
    expressAd.rootViewController = self.rootViewController;
    expressAd.oid = timeoutID;
    expressAd.reporterId = reporterId;
    
    [expressAd loadAdData];
}





#pragma mark - XMIExpressAdDelegate
- (void)expressAd:(XMIExpressAd *)expressAd didLoadData:(NSArray<__kindof XMIAdRelatedData *> *)adDataArray {
    if (![expressAd isValid]) {
        return;
    }
    [[XMITimeoutHelper sharedInstance] cancelHandler:expressAd.oid];
    
    XMIAdRelatedData *relatedData = expressAd.adSlot.relatedData;
    // 上报
    [XMIAdReporter reportRequestStatus:adDataArray.count < 1 withReporter:expressAd.reporterId andAd:relatedData];
    [XMIAdReporter dspSDKReportLoad:relatedData];
    // 渲染
    if (adDataArray != nil && adDataArray.count > 0) {
        XMIAdRelatedData *relatedData = adDataArray.firstObject;
        relatedData.loadingStatus = XMIAdRelatedLoadingStatusLoadCompleted;
        [[NSNotificationCenter defaultCenter] postNotificationName:XMIExpressAdloadManagerLoadDataSuccessNotification object:relatedData];
        [self removeLoaderWithAdData:relatedData];
    }
}

- (void)expressAd:(XMIExpressAd *)expressAd didLoadFailWithError:(NSError *)error {
    if (![expressAd isValid]) {
        return;
    }
    [[XMITimeoutHelper sharedInstance] cancelHandler:expressAd.oid];
    
    XMIAdRelatedData *relatedData = expressAd.adSlot.relatedData;
    XMIAdStatusReporter *reporter = [XMIAdReporter reporterWithError:error andReporter:expressAd.reporterId andAd:relatedData];
    if (reporter != nil) {
        [[XMIAdReporter sharedInstance] addStatusReport:reporter];
    }
    // 第三方广告加载失败，填充喜马广告
    if (relatedData && relatedData.adtype != XMIAdSourceXM) {
        NSLog(@"+++++++++++替换");
        [self doLoadXMAdData:relatedData withRefReporter:expressAd.reporterId];
//        if (ret) {
//            XMILog(@"备胎广告已替换");
//        } else {
//            XMILog(@"备胎广告替换失败");
//        }
    }
    
    [XMIAdReporter reportRequestStatusWithReporter:reporter];
    
//    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:didLoadFailWithError:)]) {
//        [self.delegate expressAd:self didLoadFailWithError:error];
//    }
    NSMutableDictionary *infoDict = [NSMutableDictionary dictionaryWithCapacity:0];
    if (relatedData) {
        infoDict[@"data"] = relatedData;
    }
    if (error) {
        infoDict[@"error"] = error;
    }
    [[NSNotificationCenter defaultCenter] postNotificationName:XMIExpressAdloadManagerLoadDataFailNotification object:infoDict];
    
    [self removeLoaderWithAdData:relatedData];
}

- (void)doLoadXMAdData:(XMIAdRelatedData *)relatedData withRefReporter:(long long)refReporterId {
//    XMIAdRelatedData *rData = relatedData.spareTireData;
//    if (rData == nil) {
        if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdLoad:requireNextReplaceableXMAd:withRefReporter:)]) {
            [self.delegate expressAdLoad:self requireNextReplaceableXMAd:relatedData withRefReporter:refReporterId];
//            rData = [self.delegate expressAdLoad:self requireNextReplaceableXMAd:adModel];
        }
//        if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:requireNextReplaceableXMAd:)]) {
//            rData = [self.delegate expressAd:self requireNextReplaceableXMAd:relatedData];
//        }
//        if (rData) {
//            [XMIExpressAdDoubleView startRefreshSizeWithRelatedData:rData adViewWidth:relatedData.adWidth];
//            relatedData.spareTireData = rData;
//            rData.indexPath = relatedData.indexPath;
//        }
//    }

//    if (rData == nil) {
        // 无备胎广告，上报
//        XMIAdStatusReporter *refReporter = [[XMIAdReporter sharedInstance] statusReportForId:refReporterId];
//        if (refReporter != nil) {
//            refReporter.ready = YES;
//            [[XMIAdReporter sharedInstance] addStatusReport:refReporter];
//        }
//        relatedData.adHeight = 0.01;
//        BOOL isNeedRefresh = relatedData.loadingStatus != XMIAdRelatedLoadingStatusHasSureSizeRadio;
//        relatedData.loadingStatus = XMIAdRelatedLoadingStatusHasSureSizeRadio;
//
//        if (isNeedRefresh && self.delegate && [self.delegate respondsToSelector:@selector(expressAdLoad:findNoSpareTireData:)]) {
//            [self.delegate expressAdLoad:self findNoSpareTireData:relatedData];
//        }
//        return NO;
//    }
    
//    XMIAdStatusReporter *reporter = [[XMIAdStatusReporter alloc] init];
//    reporter.startMS = [XMICommonUtils currentMS];
//    reporter.refReporterId = refReporterId;
//    [[XMIAdReporter sharedInstance] addStatusReport:reporter];
//    //
//    [self doLoadAdData:rData withReporter:reporter.reporterId];
    
//    return NO;
}

// 第三方超时
- (XMIHandlerID)doLoadAdData:(XMIAdRelatedData *)relatedData withTimeout:(long long)reporterId {
    NSInteger timeout = [XMIAdDataCenter getHomeFeedThirdTimeoutMS];
    @weakify(self)
    __block XMIHandlerID handlerID =  [[XMITimeoutHelper sharedInstance] delay:timeout runHandler:^{
        XMILog(@"*** SDK加载超时了");
        @strongify(self)
        if (!self) {
            return;
        }
        
        [self doLoadAdData:relatedData failWithError:[XMIAdError otherTimeoutError] andReporter:reporterId];
    }];
    
    return handlerID;
}

- (void)doLoadAdData:(XMIAdRelatedData *)relatedData failWithError:(NSError *)error andReporter:(long long)reporterId {
    XMIExpressAd *expressAd = [self getLoaderWithAdData:relatedData];
    NSLog(@"+++++++++++超时");
    [self expressAd:expressAd didLoadFailWithError:error];
    [expressAd invalidate];
}

@end
