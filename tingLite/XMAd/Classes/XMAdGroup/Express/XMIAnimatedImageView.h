//
//  XMAnimatedImageView.h
//  Pods
//
//  Created by baight chen on 2019/11/27.
//

#import <XMWebImage/FLAnimatedImage.h>

/*
XMAnimatedImageView 对gif的展示，进行了特别的优化，展示时不会内存暴增加
*/

@interface XMIAnimatedImageView : FLAnimatedImageView

- (void)didInitialize NS_REQUIRES_SUPER;

// 仅支持 UIControlEventTouchUpInside
- (void)addTarget:(id)target action:(SEL)action forControlEvents:(UIControlEvents)controlEvents;

- (void)setImageWithURL:(NSURL *)url;

- (void)setImageWithURL:(NSURL *)url placeholderImage:(UIImage *)placeholder;

- (void)setImageWithURL:(NSURL *)url placeholderImage:(UIImage *)placeholder loadSuccess:(void (^)(UIImage *))success loadFailure:(void (^)(NSError *))failure;

- (void)cancelCurrentImageLoadAndSetImage:(UIImage*)image;

@end

