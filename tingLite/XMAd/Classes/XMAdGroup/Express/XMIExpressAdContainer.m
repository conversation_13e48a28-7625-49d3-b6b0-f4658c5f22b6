//
//  XMIExpressAdContainer.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/9/16.
//

#import "XMIExpressAdContainer.h"
#import "XMIAdMacro.h"
#import "XMICommonUtils.h"
#import "XMITimeoutHelper.h"
#import "XMIAdConverter.h"
#import "XMIAdDataCenter.h"
#import "XMIAdError.h"
#import "XMIExpressAdView.h"
#import "XMIExpressAd.h"
#import "XMIExpressAdFactory.h"
#import "XMIExpressAdViewFactory.h"
#import "XMIAdReporter+AD.h"
#import "XMIAdSlot.h"
#import <Masonry/Masonry.h>
#import "XMIExpressAdDoubleView.h"
#import "XMIExpressAdloadManager.h"
#import "XMIAdModel.h"
#import "XMIAdManager.h"

NSString * XMIExpressAdContainerScrollViewDidEndScrollNotification = @"XMIExpressAdContainerScrollViewDidEndScrollNotification";
NSString * XMIExpressAdContainerScrollViewDidScrollNotification = @"XMIExpressAdContainerScrollViewDidScrollNotification";
NSString * XMIExpressAdContainerScrollViewPreloadDataNotification = @"XMIExpressAdContainerScrollViewPreloadDataNotification";
NSString * XMIExpressAdContainerControllerDidAppearNotification = @"XMIExpressAdContainerControllerDidAppearNotification";
NSString * XMIExpressAdContainerControllerDidDisappearNotification = @"XMIExpressAdContainerControllerDidDisappearNotification";
NSString * XMIExpressAdContainerControllerPlayerStatusDidChange = @"XMIExpressAdContainerControllerPlayerStatusDidChange";

@interface XMIExpressAdContainer ()<XMIExpressAdViewDelegate, XMIExpressAdloadManagerDelegate>

@property (nonatomic, weak) id<XMIExpressAdContainerDelegate> delegate;
@property (nonatomic, weak) UIViewController *rootViewController;

@property (nonatomic, strong) XMIExpressAdloadManager *loadManager;
/**
 adx请求时间，用于统计上报
 */
@property (nonatomic, assign) long long paddingTimeMs;

@property (nonatomic, strong) XMIExpressAd *expressAd;
/**
 广告加载器，需要记录每个加载器，container复用后可以回调
 */
@property (nonatomic, strong) NSMutableDictionary *adLoaders;
/**
 上一次的滚动偏移
 */
@property (nonatomic, assign) CGFloat lastOffsetY;

/// 三方广告加载失败时的兜底广告
@property (nonatomic, strong) XMIExpressAdView *xmAdView;
@property (nonatomic, strong) UIView *placeHolderView;

@property (nonatomic, assign) XMIAdType adType;


@end

@implementation XMIExpressAdContainer


- (void)beginUnregisterDataObject
{
    [self.adView beginUnregisterDataObject];
}


- (UIView *)placeHolderView
{
    if (!_placeHolderView) {
        _placeHolderView = [[UIView alloc] initWithFrame:self.bounds];
    }
    return _placeHolderView;
}


- (instancetype)initWithFrame:(CGRect)frame reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithFrame:frame];
    if (self) {
        self.scrollFrame = [UIScreen mainScreen].bounds;
        self.loadManager = [XMIExpressAdloadManager new];
        self.loadManager.delegate = self;
        
        self.lastOffsetY = 0;
        self.reuseIdentifier = reuseIdentifier;
        [self addSubview:self.placeHolderView];
        [self.placeHolderView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.right.equalTo(self);
            make.bottom.mas_equalTo(-1);
        }];
        
        [self setupAdViewWithReuseIdentifier:reuseIdentifier frame:frame];
        
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(scrollViewDidEndScrollNotification:) name:XMIExpressAdContainerScrollViewDidEndScrollNotification object:nil];
        
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(scrollViewDidScrollNotification:) name:XMIExpressAdContainerScrollViewDidScrollNotification object:nil];
        
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(adLoadManagerLoadDataSuccess:) name:XMIExpressAdloadManagerLoadDataSuccessNotification object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(adLoadManagerLoadDataFailure:) name:XMIExpressAdloadManagerLoadDataFailNotification object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(controllerDidAppearNotification:) name:XMIExpressAdContainerControllerDidAppearNotification object:nil];
        
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(controllerDidDisappearNotification:) name:XMIExpressAdContainerControllerDidDisappearNotification object:nil];
        
    }
    return self;
}

- (void)controllerDidAppearNotification:(NSNotification *)notification
{
    if (!self.adView.isHidden) {
        [self.adView controllerViewDidAppear];
    }
    if (self.xmAdView != nil && !self.xmAdView.isHidden) {
        [self.xmAdView controllerViewDidAppear];
    }
}

- (void)controllerDidDisappearNotification:(NSNotification *)notification
{
    if (!self.adView.isHidden) {
        [self.adView controllerViewDidDisappear];
    }
    if (self.xmAdView != nil && !self.xmAdView.isHidden) {
        [self.xmAdView controllerViewDidDisappear];
    }
}

- (void)setupAdViewWithReuseIdentifier:(NSString *)reuseIdentifier
                                 frame:(CGRect)frame{
    NSArray *array =  [reuseIdentifier componentsSeparatedByString:@"-"];
    if (array.count < 2) {
        return;
    }
    NSString *provider = [XMIAdConverter providerFromAdType:[array[0] integerValue]];
    NSString *clsName = [NSString stringWithFormat:@"XMI%@ExpressAdViewFactory", provider];
    __typeof([XMIExpressAdViewFactory class]) cls = NSClassFromString(clsName);
    if (cls == nil) {
        return;
    }
    XMIExpressAdView *adView = [cls expressAdViewWithShowStyle:[array[1] integerValue] frame:frame];
    adView.delegate = self;
    [self addSubview:adView];
    self.adView = adView;
    self.adType = [array[0] integerValue];
    
    // 做兜底逻辑
    if (![provider isEqualToString:XMIAdProviderXM]) {
        __typeof([XMIExpressAdViewFactory class]) cls1 = NSClassFromString(@"XMIOwnExpressAdViewFactory");
        if (cls == nil) {
            return;
        }
        XMIExpressAdView *xmAdView = [cls1 expressAdViewWithShowStyle:XMIAdStyleHomeMixRowDoubleBigImage frame:frame];
        xmAdView.delegate = self;
        xmAdView.hidden = YES;
        [self addSubview:xmAdView];
        self.xmAdView = xmAdView;
    }
}

/// 判断广告素材有无变化 高度如果已经计算过了就不在计算
/// 此处指对竖屏广告做判断包括穿山甲以及广点通
/// 多次计算会让界面比较卡顿
//- (BOOL)compareWithAdContainer:(XMIExpressAdContainer *)container
//{
//    XMIAdRelatedData *adData = self.relatedData;
//    XMIAdRelatedData *oldAdData = container.relatedData;
//    if (adData.adid == oldAdData.adid &&
//        adData.adtype == oldAdData.adtype &&
//        adData.showstyle == oldAdData.showstyle &&
//        (adData.showstyle == XMIAdStyleHomeDoubleRowVerticalImage || adData.showstyle == XMIAdStyleHomeDoubleRowVerticalVideo)) {
//        return YES;
//    }
//    return NO;
//}

//- (BOOL)compareAdData:(XMIAdRelatedData *)adData
//            oldAdData:(XMIAdRelatedData *)oldAdData
//{
//    if (adData.showstyle == XMIAdStyleHomeDoubleRowVerticalImage) {
//        /// 竖版图片
//        if (adData.adtype == XMIAdTypeXM) {
//            if ([adData.cover?:@"" isEqualToString:oldAdData.cover?:@""] &&
//                [adData.name?:@"" isEqualToString:oldAdData.name?:@""] &&
//                [adData.clickTitle?:@"" isEqualToString:oldAdData.clickTitle?:@""]) {
//                return YES;
//            }
//            return NO;
//        }
//
//    }else if (adData.showstyle == XMIAdStyleHomeDoubleRowVerticalVideo){
//        if (adData.adtype == XMIAdTypeXM) {
//            if ([adData.videoCover?:@"" isEqualToString:oldAdData.videoCover?:@""] &&
//                [adData.cover?:@"" isEqualToString:oldAdData.cover?:@""] &&
//                [adData.name?:@"" isEqualToString:oldAdData.name?:@""] &&
//                [adData.clickTitle?:@"" isEqualToString:oldAdData.clickTitle?:@""]) {
//                return YES;
//            }
//            return NO;
//        }
//    }
//    return YES;
//}

- (void)refreshData:(XMIAdModel *)adModel {
    
    self.adModel = adModel;
    
    XMIAdRelatedData *relatedData = adModel.relatedData;
    
    self.paddingTimeMs = relatedData.paddingTimeMs;
    self.delegate = relatedData.delegate;
    self.rootViewController = relatedData.rootViewController;
//    self.relatedData = relatedData;
    self.indexPath = relatedData.indexPath;
    self.adView.rootViewController = self.rootViewController;
    self.xmAdView.rootViewController = self.rootViewController;
    self.loadManager.rootViewController = self.rootViewController;
    if (relatedData.isLoaded) {
        if (relatedData.loadingStatus == XMIAdRelatedLoadingStatusHasNoSpareTireData) {
            return;
        }
//        if (relatedData.spareTireData != nil) {
//            self.relatedData = relatedData.spareTireData;
//        }
        if (relatedData.loadingStatus == XMIAdRelatedLoadingStatusNormal) {
            self.adView.hidden = YES;
            self.xmAdView.hidden = YES;
            [self refreshAdView:relatedData];
            return;
        }
        [self refreshAdView:relatedData];
    } else {
        [self loadAdData];
    }
}

- (void)refreshAdView:(XMIAdRelatedData *)relatedData
{
    // 1.xmAdView不是空，adtype是喜马，表明是备胎
    // 2.其它情况刷新adview
//    self.relatedData = relatedData;
    // 渲染
    if (self.xmAdView != nil && self.adModel.relatedData.adtype == XMIAdTypeXM) {
        self.adView.hidden = YES;
        self.xmAdView.hidden = NO;
        [self.xmAdView refreshWithData:relatedData];
    }else{
        self.adView.hidden = NO;
        self.xmAdView.hidden = YES;
        [self.adView refreshWithData:relatedData];
        
    }
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    if (self.xmAdView != nil && self.adModel.relatedData.adtype == XMIAdTypeXM) {
        self.xmAdView.frame = self.bounds;
    }else{
        self.adView.frame = self.bounds;
    }
}

- (void)scrollViewDidEndScrollNotification:(NSNotification *)notification
{
    if (!self.adView.isHidden) {
        [self.adView scrollViewDidEndScroll];
    }
    if (self.xmAdView != nil && !self.xmAdView.isHidden) {
        [self.xmAdView scrollViewDidEndScroll];
    }
}

- (void)scrollViewDidScrollNotification:(NSNotification *)notification
{
    NSObject *object = notification.object;
    if ([object isKindOfClass:UIScrollView.class]) {
        UIScrollView *scrollView = (UIScrollView *)object;
        [self scrollViewDidScroll:scrollView];
    }
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    
    if ([scrollView isKindOfClass:[UITableView class]]) {
        [self preloadAdDataInTable:(UITableView *)scrollView withOffsetY:scrollView.contentOffset.y];
    }
    
    if (!self.adView.isHidden) {
        [self.adView scrollViewDidScroll];
    }
    if (self.xmAdView != nil && !self.xmAdView.isHidden) {
        [self.xmAdView scrollViewDidScroll];
    }
}
/**
 广告预加载逻辑
 1. 只针对已经加到view上的广告
 2. 已可见的广告要加载
 3. 向上滚动，下一屏的要加载
 4. 向下滚动，上一屏的要加载
 */
- (void)preloadAdDataInTable:(UITableView *)tableView withOffsetY:(CGFloat)offsetY {
    if (self.adModel.relatedData.isLoaded) {
        return;
    }
    if (self.indexPath == nil) {
        return;
    }
    if (tableView == nil) {
        return;
    }

    CGRect detectRect;
    CGRect rect = [tableView rectForRowAtIndexPath:self.indexPath];
    if (offsetY > self.lastOffsetY) {
        detectRect = CGRectMake(tableView.frame.origin.x, offsetY, tableView.frame.size.width, tableView.frame.size.height * 2);
    } else {
        detectRect = CGRectMake(tableView.frame.origin.x, offsetY - tableView.frame.size.height, tableView.frame.size.width, tableView.frame.size.height * 2);
    }
    if (CGRectIntersectsRect(rect, detectRect)) {
        [self loadAdData];
    }

    self.lastOffsetY = offsetY;
}



/**
 广告加载
 */
- (void)loadAdData {
//    if (self.relatedData.isLoaded) {
//        return;
//    }
//    self.relatedData.isLoaded = YES;
//    XMILog(@"d*** load ad");
//
//    XMIAdStatusReporter *reporter = [[XMIAdStatusReporter alloc] init];
//    reporter.paddingMs = self.paddingTimeMs;
//    reporter.startMS = [XMICommonUtils currentMS];
//    [[XMIAdReporter sharedInstance] addStatusReport:reporter];
//
//    [self doLoadAdData:self.relatedData withReporter:reporter.reporterId];
    
    [self.loadManager loadAdData:self.adModel.relatedData];
}


- (void)expressAdLoad:(XMIExpressAdloadManager *)manager requireNextReplaceableXMAd:(XMIAdRelatedData *)oldAdData withRefReporter:(long long)refReporterId
{
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:requireNextReplaceableXMAd:withRefReporter:)]) {
        [self.delegate expressAd:self requireNextReplaceableXMAd:oldAdData withRefReporter:refReporterId];
    }
}

//- (void)expressAdLoad:(XMIExpressAdloadManager *)manager findNoSpareTireData:(XMIAdRelatedData *)relatedData
//{
//    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdViewDidRender:hasNeedRefresh:)]) {
//        [self.delegate expressAdViewDidRender:self hasNeedRefresh:YES];
//    }
//}

- (void)adLoadManagerLoadDataSuccess:(NSNotification *)notification
{
    id object = notification.object;
    if ([object isKindOfClass:XMIAdRelatedData.class]) {
        XMIAdRelatedData *relatedData = (XMIAdRelatedData *)object;
        if (self.adType != relatedData.adtype) {
            return;
        }
//        if (relatedData.adid == self.adModel.relatedData.adid) {
//            [self refreshAdView:relatedData];
//        }
        NSString *oneIdentifier = [relatedData getIdentifier];
        NSString *twoIdentifier = [self.adModel.relatedData getIdentifier];
        if ([oneIdentifier isEqualToString:twoIdentifier]) {
            [self refreshAdView:relatedData];
        }
//        if ([relatedData getIdentifier] == [self.adModel.relatedData getIdentifier]) {
//            [self refreshAdView:relatedData];
//        }
    }
}

- (void)adLoadManagerLoadDataFailure:(NSNotification *)notification
{
    id object = notification.object;
    if ([object isKindOfClass:NSDictionary.class]) {
        NSDictionary *infoDict = (NSDictionary *)object;
        NSError *error = infoDict[@"error"];
        XMIAdRelatedData *relatedData = infoDict[@"data"];
        NSString *oneIdentifier = [relatedData getIdentifier];
        NSString *twoIdentifier = [self.adModel.relatedData getIdentifier];
        if (![oneIdentifier isEqualToString:twoIdentifier]) {
            return;
        }
        if ([error isKindOfClass:NSError.class] && [relatedData isKindOfClass:XMIAdRelatedData.class]) {
            if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:didLoadFailWithError:)]) {
                [self.delegate expressAd:self didLoadFailWithError:error];
            }
        }
    }
}

- (void)adViewDidCloseClickedResult
{
    [self.adView adViewDidCloseClickedResult];
}

#pragma mark - XMIExpressAdViewDelegate
- (void)expressAdViewDidRender:(XMIExpressAdView *)adView hasNeedRefresh:(BOOL)isNeedRefresh {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdViewDidRender:hasNeedRefresh:)]) {
        [self.delegate expressAdViewDidRender:self hasNeedRefresh:isNeedRefresh];
    }
}

- (void)expressAdView:(XMIExpressAdView *)adView didRenderFailWithError:(NSError *)error {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:didRenderFailWithError:)]) {
        [self.delegate expressAd:self didRenderFailWithError:error];
    }
}

- (void)expressAdView:(XMIExpressAdView *)adView adViewWillShow:(UIView *)aView {
    
}

- (void)expressAdView:(XMIExpressAdView *)adView adViewDidClick:(UIView *)aView withUserInfo:(nullable NSDictionary *)userInfo {
    // 点击上报
    [XMIAdReporter clickReportWithAd:adView.relatedData andView:aView andUserInfo:userInfo];
    [XMIAdReporter dspSDKReportClick:adView.relatedData];
    if (userInfo != nil && userInfo[kUserInfoJumpNotSupport] != nil && [userInfo[kUserInfoJumpNotSupport] boolValue]) {
        if ([[XMIAdManager sharedInstance].delegate respondsToSelector:@selector(managerHandleSDKNotSupportedAdJump:)]) {
            //处理不了交给主站跳转
            [[XMIAdManager sharedInstance].delegate managerHandleSDKNotSupportedAdJump:adView.relatedData];
        }
    }
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:adViewDidClick:withUserInfo:)]) {
        [self.delegate expressAd:self adViewDidClick:aView withUserInfo:userInfo];
    }
}

- (void)expressAdView:(XMIExpressAdView *)adView playerStateChanged:(XMIPlayerPlayState)state {
    [XMIAdReporter videoPlayReportWithAd:adView.relatedData andAdView:(UIView<XMIAdViewProtocol> *)adView andPlayerState:state];
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:playerStateChanged:)]) {
        [self.delegate expressAd:self playerStateChanged:state];
    }
}

- (void)expressAdView:(XMIExpressAdView *)adView playerDidPlayFinish:(nullable NSError *)error {
    if (error) {
        [XMIAdReporter videoPlayReportWithAd:adView.relatedData andAdView:(UIView<XMIAdViewProtocol> *)adView andPlayerState:XMIPlayerStateFailed];
    }else{
        [XMIAdReporter videoPlayFinishReportWithAd:adView.relatedData andAdView:(UIView<XMIAdViewProtocol> *)adView];
    }
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:playerDidPlayFinish:)]) {
        [self.delegate expressAd:self playerDidPlayFinish:error];
    }
}
- (void)expressAdView:(XMIExpressAdView *)adView relatedData:(XMIAdRelatedData *)relatedData andPlayStatusChange:(XMIAdPlayStatus)status
{
    [XMIAdReporter videoPlayReportWithAd:adView.relatedData andAdView:(UIView<XMIAdViewProtocol> *)adView andPlayStatus:status];
}

- (void)expressAdView:(XMIExpressAdView *)adView playTimeDidChanged:(CGFloat)currentTime {
    
}

- (void)expressAdViewPlayerDidBecomeInvisible:(XMIExpressAdView *)adView {
    // 上报
    [XMIAdReporter videoPlayInvisibleReportWithAd:adView.relatedData andAdView:(UIView<XMIAdViewProtocol> *)adView];
}

- (void)expressAdViewWillPresentScreen:(XMIExpressAdView *)adView {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdViewWillPresentScreen:)]) {
        [self.delegate expressAdViewWillPresentScreen:self];
    }
}

- (void)expressAdViewDidPresentScreen:(XMIExpressAdView *)adView {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdViewDidPresentScreen:)]) {
        [self.delegate expressAdViewDidPresentScreen:self];
    }
}

- (void)expressAdView:(XMIExpressAdView *)adView dislikeWithReason:(NSArray<__kindof NSString *> *)reasons {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:dislikeWithReason:)]) {
        [self.delegate expressAd:self dislikeWithReason:reasons];
    }
}

- (void)expressAdView:(XMIExpressAdView *)adView adViewDidClickedClose:(UIView *)aView {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:adViewDidClickedClose:)]) {
        [self.delegate expressAd:self adViewDidClickedClose:adView];
    }
}

- (void)expressAdViewDetailControllerDidClosed:(XMIExpressAdView *)adView {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdViewDetailControllerDidClosed:)]) {
        [self.delegate expressAdViewDetailControllerDidClosed:self];
    }
}

- (void)expressAdViewDidRemoved:(XMIExpressAdView *)adView {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdViewDidRemoved:)]) {
        [self.delegate expressAdViewDidRemoved:self];
    }
}

- (void)expressAdViewDidExpose:(XMIExpressAdView *)adView {
    XMILog(@"expressAdViewDidExpose");
    // 曝光上报
    [XMIAdReporter exposeReportWithAd:adView.relatedData andView:(UIView *)adView];
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdViewDidExpose:)]) {
        [self.delegate expressAdViewDidExpose:self];
    }
}

- (void)expressAdView:(XMIExpressAdView *)adView didClickedAnchorItem:(XMIAdRelatedData *)relatedData clickedView:(UIView *)aView
{
    // 点击上报
//    [XMIAdReporter clickReportWithAd:adView.relatedData andView:aView andUserInfo:nil];

    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:didClickedAnchorItem:clickedView:)]) {
        [self.delegate expressAd:self didClickedAnchorItem:relatedData clickedView:aView];
    }
}

@end
