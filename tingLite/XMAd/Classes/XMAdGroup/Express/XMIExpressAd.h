//
//  XMIExpressAd.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/6/30.
//

#import "XMIBaseAd.h"
#import "XMIAdDefines.h"

NS_ASSUME_NONNULL_BEGIN

@protocol XMIExpressAdDelegate;
@class XMIExpressAdView;
@class XMIAdData, XMIAdModel;

@interface XMIExpressAd : XMIBaseAd

@property (nonatomic, weak) id<XMIExpressAdDelegate> delegate;

/**
 internal
 */
- (void)didLoadData:(NSArray *)adDataArray;
- (void)didLoadFailWithError:(NSError *)error;
- (void)didLoad:(NSArray *)views;

@end

@protocol XMIExpressAdDelegate <NSObject>

@optional

- (void)expressAd:(XMIExpressAd *)expressAd didLoadData:(NSArray<__kindof XMIAdRelatedData *> *)adDataArray;
- (void)expressAd:(XMIExpressAd *)expressAd didLoadFailWithError:(NSError *)error;
- (void)expressAd:(XMIExpressAd *)expressAd didLoad:(NSArray<__kindof XMIExpressAdView *> *)views;

@end

NS_ASSUME_NONNULL_END
