//
//  XMAnimatedImageView.m
//  Pods
//
//  Created by baight chen on 2019/11/27.
//

#import "XMIAnimatedImageView.h"
#import <XMWebImage/UIView+WebCache.h>
#import <XMWebImage/UIImageView+WebCache.h>

@interface XMIAnimatedImageView (){
    __weak id _target;
    SEL _eventSel;
    UITapGestureRecognizer* _tapGestureRecognizer;
}
@end

@implementation XMIAnimatedImageView
- (id)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self didInitialize];
    }
    return self;
}
- (id)initWithImage:(UIImage *)image {
    self = [super initWithImage:image];
    if (self) {
        [self didInitialize];
    }
    return self;
}
- (void)didInitialize{}

- (void)dealloc{
    _eventSel = nil;
}

- (void)cancelCurrentImageLoadAndSetImage:(UIImage*)image{
    [self sd_internalSetImageWithURL:nil placeholderImage:image options:0 operationKey:nil setImageBlock:nil progress:nil completed:nil];
}

#pragma mark - Target
- (void)addTarget:(id)target action:(SEL)action forControlEvents:(UIControlEvents)controlEvents{
    switch (controlEvents) {
        case UIControlEventTouchUpInside:{
            _target = target;
            _eventSel = action;
            if (_tapGestureRecognizer == nil) {
                self.userInteractionEnabled = YES;
                _tapGestureRecognizer = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(tapAction:)];
                [self addGestureRecognizer:_tapGestureRecognizer];
            }
        }
            break;
        default:
            NSLog(@"不支持的 UIControlEvents");
            break;
    }
}

- (void)tapAction:(UIGestureRecognizer*)gestureRecognizer{
    if(_target && [_target respondsToSelector:_eventSel]){
        #pragma clang diagnostic push
        #pragma clang diagnostic ignored "-Warc-performSelector-leaks"
        [_target performSelector:_eventSel withObject:self];
        #pragma clang diagnostic pop
    }
}

#pragma mark - Set Image
- (void)setImageWithURL:(NSURL *)url {
    [self setImageWithURL:url placeholderImage:nil];
}
- (void)setImageWithURL:(NSURL *)url placeholderImage:(UIImage *)placeholder {
    [self sd_setImageWithURL:url placeholderImage:placeholder];
}

- (void)setImageWithURL:(NSURL *)url placeholderImage:(UIImage *)placeholder loadSuccess:(void (^)(UIImage *))success loadFailure:(void (^)(NSError *))failure {
    [self sd_setImageWithURL:url placeholderImage:placeholder
                   completed:^(UIImage * _Nullable image,
                               NSError * _Nullable error,
                               SDImageCacheType cacheType,
                               NSURL * _Nullable imageURL) {
        if (error != nil) {
            if (failure) {
                failure(error);
            }
        } else {
            if (success) {
                success(image);
            }
        }
    }];
}

@end
