//
//  XMIExpressAdDoubleView.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/11/2.
//

#import "XMIExpressAdView.h"
#import <YYText/YYText.h>
#import "XMIAdDefines.h"

@class XMIAdRelatedData, YYTextLayout, XMIAnimatedImageView, XMIAdModel;
/// 标题font
#define kAdAudioTitleFont XMI_AD_PingFangMediumFont(14)
extern const CGFloat XMIExpressAdDoubleViewCoverMaskHeight;
extern const CGFloat kXMIExpressAdDoubleVideoViewBigHoriznalMargin;
extern const CGFloat kXMIExpressAdDoubleVideoViewHoriznalMargin;
extern const CGFloat kXMIExpressAdDoubleVideoViewCoverTopMargin;
extern const CGFloat kXMIExpressAdDoubleVideoViewTagImageHeight;
extern const CGFloat kXMIExpressAdDoubleVideoViewAnchorHeight;
extern const CGFloat kXMIExpressAdDoubleVideoViewTitleRecSpace;
extern const CGFloat kXMIExpressAdDoubleVideoViewCoverTitleSpace;

NS_ASSUME_NONNULL_BEGIN
@class XMIAdRelatedData;
/// 双列
@interface XMIExpressAdDoubleView : XMIExpressAdView
//@property (nonatomic, assign) CGFloat initWidth;
//@property (nonatomic, assign) CGFloat initHeight;
@property(nonatomic, strong, readonly) UIView *contentView;
@property(nonatomic, strong, readonly) UIButton *closeButton;
@property(nonatomic, strong, readonly) UIImageView *adMark;
@property(nonatomic, strong, readonly) YYLabel *titleLabel;
@property(nonatomic, strong, readonly) XMIAnimatedImageView *coverImageView;
@property(nonatomic, strong, readonly) UIView *placeHolderView;
@property(nonatomic, strong, readonly) UIButton *statusButton;
@property(nonatomic, strong, readonly) XMIAnimatedImageView *contentMaskView;
@property(nonatomic, strong, readonly) CAGradientLayer *contentBlackLayer;
@property(nonatomic, strong, readonly) UIView *titleMaskView;
@property(nonatomic, strong, readonly) UIButton *volumeButton;
@property(nonatomic, strong, readonly) UILabel *recLabel;
@property(nonatomic, strong, readonly) XMIAnimatedImageView *anchorImageView;
@property(nonatomic, strong, readonly) UILabel *anchorNameLabel;
@property(nonatomic, strong, readonly) UIImageView *playTagIcon;
@property(nonatomic, strong, readonly) UILabel *playCountLabel;
@property(nonatomic, strong, readonly) UIImageView *scoreImgV;
@property(nonatomic, strong, readonly) UILabel *scoreLabel;
@property(nonatomic, strong, readonly) UIView *coverMaskView;
@property(nonatomic, strong, readonly) UIImageView *tagImageView;
@property(nonatomic, strong, readonly) UIView *anchorTapView;



/// 刷新adModel的size等
+ (void)startRefreshSizeWithAdModel:(XMIAdModel *)adModel;

/// 开始刷新size等
+ (void)startRefreshSizeWithRelatedData:(XMIAdRelatedData *)relatedData
                            adViewWidth:(CGFloat)adViewWidth;

+ (CGFloat)getAdViewHeight:(XMIAdRelatedData *)adData;

/// 设置标题
+ (void)startSetTitleTextLayout:(XMIAdRelatedData *)adData
                    adViewWidth:(CGFloat)adViewWidth;

+ (CGFloat)getAdViewHeightWithRelatedData:(XMIAdRelatedData *)relatedData;

+ (YYTextLayout *)getTitleTextLayout:(id)adData
                         adViewWidth:(CGFloat)adViewWidth;

//+ (YYTextLayout *)getAudioTitleTextLayout:(NSString *)title
//                              adViewWidth:(CGFloat)adViewWidth;

/// 数据进行赋值
//- (void)startSetUpUIWithModel:(id)model;

/**
 增加及处理点击事件
 */
- (void)registerOwnClickableViews:(NSArray *)views;

- (void)adViewDidTap:(UITapGestureRecognizer *)tap;

- (void)adViewDidClicked;

- (void)setStatusButtonColorAnimation;

- (void)closeButtonAction:(UIButton *)button;

/// 获取是否是下载
- (BOOL)getAdIsDownloadApp;


@end

NS_ASSUME_NONNULL_END
