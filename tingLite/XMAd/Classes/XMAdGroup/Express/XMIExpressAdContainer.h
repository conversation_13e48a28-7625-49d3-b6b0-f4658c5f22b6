//
//  XMIExpressAdContainer.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/9/16.
//

#import <UIKit/UIKit.h>
#import "XMIAdDefines.h"

NS_ASSUME_NONNULL_BEGIN

@class XMIExpressAdView;
@class XMIAdRelatedData, XMIAdModel;
@protocol XMIExpressAdContainerDelegate;

extern NSString * XMIExpressAdContainerScrollViewDidEndScrollNotification;
extern NSString * XMIExpressAdContainerScrollViewDidScrollNotification;
extern NSString * XMIExpressAdContainerScrollViewPreloadDataNotification;
extern NSString * XMIExpressAdContainerControllerDidAppearNotification;
extern NSString * XMIExpressAdContainerControllerDidDisappearNotification;
extern NSString * XMIExpressAdContainerControllerPlayerStatusDidChange;

@interface XMIExpressAdContainer : UIView

/**
 广告内容view
 */
@property (nonatomic, strong) XMIExpressAdView *adView;

/**
 tableview, collectionview复用标识，不同广告样式不同
 用于复用场景刷新数据，不用反复add/remove广告视图
 */
@property (nonatomic, copy) NSString *reuseIdentifier;
/**
 背景板样式滚动区域
 相对于window的坐标，仅背景板样式广告需要设置
 */
@property (nonatomic, assign) CGRect scrollFrame;

// ---------- 以下参数设置后会开启预加载功能，不设置没有该特性 --------
/**
 广告在table中的indexPath
 */
@property (nonatomic, strong) NSIndexPath *indexPath;
// -----------------------------------------------------------

/// 用户自定义数据使用
@property (nonatomic, strong) id userInfo;

//@property (nonatomic, strong) XMIAdRelatedData *relatedData;

@property (nonatomic, strong) XMIAdModel *adModel;


@property (nonatomic, assign) CGFloat initWidth;
@property (nonatomic, assign) CGFloat initHeight;
@property (nonatomic, assign) BOOL isRendered;

- (instancetype)initWithFrame:(CGRect)frame reuseIdentifier:(NSString *)reuseIdentifier;

/**
 通知view滚动了
 */
- (void)scrollViewDidScroll:(UIScrollView *)scrollView;

/// 判断广告素材有无变化 高度如果已经计算过了就不在计算
/// 此处指对竖屏广告做判断包括穿山甲以及广点通
/// 多次计算会让界面比较卡顿
//- (BOOL)compareWithAdContainer:(XMIExpressAdContainer *)container;

- (void)refreshData:(XMIAdModel *)adModel;

- (void)adViewDidCloseClickedResult;

- (void)beginUnregisterDataObject;

@end

@protocol XMIExpressAdContainerDelegate <NSObject>

@optional
- (void)expressAd:(XMIExpressAdContainer *)adContainer didLoadFailWithError:(NSError *)error;
- (void)expressAdDidLoadFailureReplaceAdContainer:(XMIExpressAdContainer *)adContainer withError:(NSError *)error;
- (CGSize)expressAdViewWillRender:(XMIExpressAdContainer *)adContainer;
- (void)expressAdViewDidRender:(XMIExpressAdContainer *)adContainer hasNeedRefresh:(BOOL)isNeedRefresh;
- (void)expressAd:(XMIExpressAdContainer *)adContainer didRenderFailWithError:(NSError *)error;
- (void)expressAdViewDidExpose:(XMIExpressAdContainer *)adContainer;
- (void)expressAd:(XMIExpressAdContainer *)adContainer adViewDidClick:(UIView *)aView withUserInfo:(nullable NSDictionary *)userInfo;
- (void)expressAd:(XMIExpressAdContainer *)adContainer adViewDidClickSDKNotSupport:(UIView *)aView withUserInfo:(nullable NSDictionary *)userInfo;
- (void)expressAdViewDidRemoved:(XMIExpressAdContainer *)adContainer;
- (void)expressAdViewWillPresentScreen:(XMIExpressAdContainer *)adContainer;
- (void)expressAdViewDidPresentScreen:(XMIExpressAdContainer *)adContainer;
- (void)expressAdViewDetailControllerDidClosed:(XMIExpressAdContainer *)adContainer;
- (void)expressAd:(XMIExpressAdContainer *)adContainer playerStateChanged:(XMIPlayerPlayState)state;
- (void)expressAd:(XMIExpressAdContainer *)adContainer playerDidPlayFinish:(NSError *_Nullable)error;
- (void)expressAd:(XMIExpressAdContainer *)adContainer dislikeWithReason:(NSArray<__kindof NSString *> *)reasons;
- (void)expressAd:(XMIExpressAdContainer *)adContainer adViewDidClickedClose:(UIView *)aView;

// 当前广告不可用，需要备胎广告
- (void)expressAd:(XMIExpressAdContainer *)adContainer requireNextReplaceableXMAd:(XMIAdRelatedData *)oldAdData withRefReporter:(long long)refReporterId;
/// 主播点击
- (void)expressAd:(XMIExpressAdContainer *)adContainer didClickedAnchorItem:(XMIAdRelatedData *)relatedData clickedView:(UIView *)aView;

@end

NS_ASSUME_NONNULL_END
