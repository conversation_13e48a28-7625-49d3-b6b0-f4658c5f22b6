//
//  XMIExpressAdloadManager.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/12/14.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

extern NSString * XMIExpressAdloadManagerLoadDataSuccessNotification;
extern NSString * XMIExpressAdloadManagerLoadDataFailNotification;
extern NSString * XMIExpressAdloadManagerLoadThirdViewsSuccessNotification;
extern NSString * XMIExpressAdloadManagerThirdViewsKey;

@class XMIExpressAdloadManager, XMIAdRelatedData, XMIAdModel;

@protocol XMIExpressAdloadManagerDelegate <NSObject>

- (void)expressAdLoad:(XMIExpressAdloadManager *)manager requireNextReplaceableXMAd:(XMIAdRelatedData *)relatedData withRefReporter:(long long)refReporterId;

//- (void)expressAdLoad:(XMIExpressAdloadManager *)manager findNoSpareTireData:(XMIAdRelatedData *)relatedData;

@end
@interface XMIExpressAdloadManager : NSObject
@property(nonatomic, weak) id<XMIExpressAdloadManagerDelegate>delegate;
@property(nonatomic, weak) UIViewController *rootViewController;

/**
 广告加载
 */
- (void)loadAdData:(XMIAdRelatedData *)relatedData;

@end

NS_ASSUME_NONNULL_END
