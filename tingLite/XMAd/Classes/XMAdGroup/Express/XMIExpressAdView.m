//
//  XMIExpressAdView.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/6.
//

#import "XMIExpressAdView.h"
#import "XMIAdMacro.h"
#import "UIView+XMIUtils.h"
#import "XMIExpressAdSingleView+Internal.h"
#import "XMIAdRelatedData.h"
#import "XMIAdReporter+AD.h"
#import <XMConfigCenter/XMConfigCenter.h>


@interface XMIExpressAdView ()

@end

@implementation XMIExpressAdView

- (void)render:(CGSize)size {
    CGRect frame = self.frame;
    frame.size = size;
    self.frame = frame;
}

- (CGFloat)adHeight {
    CGFloat height = ([self adWidth] - 16*2) * 9.0 / 16;
    return height;
}

- (CGFloat)adWidth {
    return XMI_SCREEN_WIDTH;
}

- (CGSize)adSize
{
    return self.frame.size;
}

- (void)refreshWithData:(XMIAdRelatedData *)adData {
    self.relatedData = adData;
    [self detectExpose:NO];
}

- (void)scrollViewDidScroll {
    [self detectExpose:NO];
}

/**
 通知滑动停止了
 */
- (void)scrollViewDidEndScroll
{
    
}

/// 控制器已经出现
- (void)controllerViewDidAppear
{
    
}

/// 控制器已经消失
- (void)controllerViewDidDisappear
{
    
}



- (void)didMoveToWindow {
    [super didMoveToWindow];
    if (!self.window) {
        return;
    }
    
    XMILog(@"*** feedAd didMoveToWindow");
//    if (self.relatedData) {
//        [self detectExpose:YES];
//    }
}

/**
 检测曝光
 view刚加到cell上时，cell高度还不真实，这时需要延迟检测
 */
- (void)detectExpose:(BOOL)needDelay {
    if (!self.relatedData) {
        return;
    }
    // 已曝光，不再检测
    if (self.relatedData.isExposed &&
        self.relatedData.isNewExposed &&
        self.relatedData.isNewExposed50) {
        return;
    }
    
    if (!needDelay) {
        [self doDetectExpose];
    } else {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, 0.05 * NSEC_PER_SEC), dispatch_get_main_queue(), ^{
            [self doDetectExpose];
        });
    }
}
- (void)doDetectExpose {
    [self doNewDetectExpose];
    // 防止重复检测
    if (self.relatedData.isExposed) {
        return;
    }
    
    BOOL isExposed = [self xmi_isExposed:self.rootViewController.view radio:0.01];
//    XMILog(@"e*** exposed %d", isExposed);
    if (!isExposed) {
        return;
    }
    self.relatedData.isExposed = isExposed;
    
    [self adViewDidExpose];
    [self newExposeWithRadio:1];
}

- (void)doNewDetectExpose {
    // 防止重复检测
    if (!self.relatedData.isNewExposed) {
        CGFloat radio = (CGFloat)[XMConfigCenter.sharedConfigCenter getIntValueWithGroup:@"ad" andItem:@"adNewExposeRadio" defaultValue:30];
        BOOL exposed = [self xmi_isExposed:self.rootViewController.view radio:radio/100];
        if (exposed) {
            self.relatedData.isNewExposed = exposed;
            [self newExposeWithRadio:radio];
        }
    }
    if (!self.relatedData.isNewExposed50) {
        BOOL exposed = [self xmi_isExposed:self.rootViewController.view radio:0.5];
        if (exposed) {
            self.relatedData.isNewExposed50 = exposed;
            [self newExposeWithRadio:50];
        }
    }
}

- (void)newExposeWithRadio:(NSInteger)radio {
    if (self.relatedData) {
        [XMIAdReporter exposeNewReportValidAds:@[self.relatedData] radio:radio];
    }
}

// delegate事件统一处理
- (void)adViewDidRenderHasNeedRefresh:(BOOL)isNeedRefresh {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdViewDidRender:hasNeedRefresh:)]) {
        [self.delegate expressAdViewDidRender:self hasNeedRefresh:isNeedRefresh];
    }
}

- (void)adViewDidRenderFailWithError:(NSError *)error {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdView:didRenderFailWithError:)]) {
        [self.delegate expressAdView:self didRenderFailWithError:error];
    }
}

- (void)adViewWillShow {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdViewWillShow:)]) {
        [self.delegate expressAdViewWillShow:self];
    }
}

- (void)adViewDidClick:(UIView *)aView {
    [self adViewDidClick:aView withUserInfo:nil];
}

- (void)adViewDidClick:(UIView *)aView withUserInfo:(nullable NSDictionary *)userInfo {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdView:adViewDidClick:withUserInfo:)]) {
        [self.delegate expressAdView:self adViewDidClick:self withUserInfo:userInfo];
    }
}

- (void)adViewDidRemoved {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdViewDidRemoved:)]) {
        [self.delegate expressAdViewDidRemoved:self];
    }
}

- (void)adViewWillPresentScreen {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdViewWillPresentScreen:)]) {
        [self.delegate expressAdViewWillPresentScreen:self];
    }
}

- (void)adViewDidPresentScreen {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdViewDidPresentScreen:)]) {
        [self.delegate expressAdViewDidPresentScreen:self];
    }
}

- (void)adViewDetailControllerDidClosed {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdViewDetailControllerDidClosed:)]) {
        [self.delegate expressAdViewDetailControllerDidClosed:self];
    }
}

- (void)adViewPlayerStateChanged:(XMIPlayerPlayState)state {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdView:playerStateChanged:)]) {
        [self.delegate expressAdView:self playerStateChanged:state];
    }
}

- (void)adViewPlayerDidPlayFinish:(NSError *_Nullable)error {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdView:playerDidPlayFinish:)]) {
        [self.delegate expressAdView:self playerDidPlayFinish:error];
    }
}

- (void)adViewPlayerDidBecomeInvisible {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdViewPlayerDidBecomeInvisible:)]) {
        [self.delegate expressAdViewPlayerDidBecomeInvisible:self];
    }
}

- (void)adViewDislikeWithReason:(NSArray<__kindof NSString *> *)reasons {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdView:dislikeWithReason:)]) {
        [self.delegate expressAdView:self dislikeWithReason:reasons];
    }
}

- (void)adViewDidClickedClose:(UIView *)aView
{
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdView:adViewDidClickedClose:)]) {
        [self.delegate expressAdView:self adViewDidClickedClose:aView];
    }
}

- (void)adViewDidExpose
{
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdViewDidExpose:)]) {
        [self.delegate expressAdViewDidExpose:self];
    }
}

/// 广告按钮关闭
- (void)adViewDidCloseClickedResult
{
    
}

- (void)beginUnregisterDataObject
{
    
}

- (UIView *)dislikePointView {
    return self;
}

@end

NSString * const kUserInfoAbsX = @"absX";
NSString * const kUserInfoAbsY = @"absY";
NSString * const kUserInfoX = @"x";
NSString * const kUserInfoY = @"y";
NSString * const kUserInfoJumpNotSupport = @"jumpNotSupport";
