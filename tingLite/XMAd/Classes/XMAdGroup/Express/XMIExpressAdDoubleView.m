//
//  XMIExpressAdDoubleView.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/11/2.
//

#import "XMIExpressAdDoubleView.h"
#import "XMIAdMacro.h"
#import "XMICommonUtils.h"
#import "XMIAdRelatedData.h"
#import "XMIExpressAdBusniessExtraModel.h"
#import "NSObject+XMIModel.h"
#import <Masonry/Masonry.h>
#import "XMIAdRelatedData.h"
#import "XMIAdConverter.h"
#import <GDTMobSDK/GDTUnifiedNativeAdDataObject.h>
#import <BUAdSDK/BUNativeAd.h>
#import "XMIExpressAdBusniessExtraModel.h"
#import "UIView+XMIUtils.h"
#import "XMIAnimatedImageView.h"
#import <XMCategories/UIImage+XMDynamic.h>
#import "XMIAdModel.h"
#import "XMIAdReporter.h"
#import "XMIAdButton.h"

#define kAdVideoRecFont XMI_AD_PingFangFont(12)

const CGFloat kXMIExpressAdDoubleVideoViewHoriznalMargin = 8.0f;
const CGFloat kXMIExpressAdDoubleVideoViewBigHoriznalMargin = 30.0f;
static CGFloat kXMIExpressAdDoubleVideoViewCoverBottomMargin = 84.0f;
const CGFloat kXMIExpressAdDoubleVideoViewCoverTopMargin = 36.0f;

const CGFloat kXMIExpressAdDoubleVideoViewCoverTitleSpace = 8.0f;
/// tag height
const CGFloat kXMIExpressAdDoubleVideoViewTagImageHeight = 16.0f;
/// 主播头像高度
const CGFloat kXMIExpressAdDoubleVideoViewAnchorHeight = 16.0f;
/// title rec 间距
const CGFloat kXMIExpressAdDoubleVideoViewTitleRecSpace = 8.0f;

/// 推荐 距离底部 距离
static CGFloat kXMIExpressAdDoubleVideoViewRecommendBottomMargin = 36.0f;

const CGFloat XMIExpressAdDoubleViewCoverMaskHeight = 36;
/// 标题行数
const NSInteger kXMIExpressAdDoubleViewTitleLines = 2;

@interface XMIExpressAdDoubleView()
@property(nonatomic, strong, readwrite) UIView *contentView;
@property(nonatomic, strong, readwrite) UIButton *closeButton;
@property(nonatomic, strong, readwrite) UIImageView *adMark;
@property(nonatomic, strong, readwrite) YYLabel *titleLabel;
@property(nonatomic, strong, readwrite) XMIAnimatedImageView *coverImageView;
@property(nonatomic, strong, readwrite) UIView *placeHolderView;
@property(nonatomic, strong, readwrite) XMIAnimatedImageView *contentMaskView;
@property(nonatomic, strong, readwrite) CAGradientLayer *contentBlackLayer;
@property(nonatomic, strong, readwrite) UIButton *statusButton;
@property(nonatomic, strong, readwrite) UIButton *playButton;
@property(nonatomic, strong, readwrite) UIButton *volumeButton;
@property(nonatomic, strong, readwrite) UIView *titleMaskView;
/// 声播
@property(nonatomic, strong, readwrite) UIImageView *tagImageView;
@property(nonatomic, strong, readwrite) UILabel *recLabel;
@property(nonatomic, strong, readwrite) XMIAnimatedImageView *anchorImageView;
@property(nonatomic, strong, readwrite) UILabel *anchorNameLabel;
@property(nonatomic, strong, readwrite) UIImageView *playTagIcon;
@property(nonatomic, strong, readwrite) UILabel *playCountLabel;
@property(nonatomic, strong, readwrite) UIImageView *scoreImgV;
@property(nonatomic, strong, readwrite) UILabel *scoreLabel;
@property(nonatomic, strong, readwrite) UIView *coverMaskView;
@property(nonatomic, strong, readwrite) UIView *anchorTapView;
@end

@implementation XMIExpressAdDoubleView

/// 刷新adModel的size等
+ (void)startRefreshSizeWithAdModel:(XMIAdModel *)adModel
{
    XMIAdRelatedData *relateData = adModel.relatedData;
    [self startRefreshSizeWithRelatedData:relateData adViewWidth:adModel.adWidth];
}


/// 开始刷新size等
+ (void)startRefreshSizeWithRelatedData:(XMIAdRelatedData *)relatedData
                            adViewWidth:(CGFloat)adViewWidth
{
    [XMIExpressAdDoubleView startSetTitleTextLayout:relatedData adViewWidth:adViewWidth];
//    relatedData.used = YES;
    relatedData.adWidth = adViewWidth;
    relatedData.adHeight = [XMIExpressAdDoubleView getAdViewHeight:relatedData];
}

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
//        [self render:frame.size];
    }
    return self;
}


- (UIView *)placeHolderView
{
    if (!_placeHolderView) {
        _placeHolderView = [[UIView alloc] initWithFrame:self.bounds];
        _placeHolderView.backgroundColor = XMI_COLOR_RGB(0xEDEDED);
        UIImageView *bigImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, [self adWidth], [self adHeight])];
        bigImageView.contentMode = UIViewContentModeScaleAspectFit;
        UIImage *placeImage = [XMICommonUtils imageNamed:@"ad_bkg_default"];
        bigImageView.image = placeImage;
        [_placeHolderView addSubview:bigImageView];
        [bigImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(_placeHolderView);
        }];
    }
    return _placeHolderView;
}

- (XMIAnimatedImageView *)contentMaskView
{
    if (!_contentMaskView) {
        _contentMaskView = [[XMIAnimatedImageView alloc] initWithFrame:CGRectMake(0, 0, [self adWidth], [self adHeight])];
        _contentMaskView.contentMode = UIViewContentModeScaleAspectFill;
        _contentMaskView.clipsToBounds = YES;
        
        UIBlurEffect *effect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleLight];
        UIVisualEffectView *effectView = [[UIVisualEffectView alloc] initWithEffect:effect];
        [_contentMaskView addSubview:effectView];
        [effectView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(_contentMaskView);
        }];
        
        CAGradientLayer *contentBlackLayer = [CAGradientLayer layer];
        contentBlackLayer.colors = @[(__bridge id)XMI_COLOR_RGBA(0x000000, 0.0).CGColor, (__bridge id)XMI_COLOR_RGBA(0x000000, 0.0).CGColor, (__bridge id)XMI_COLOR_RGBA(0x000000, 0.34).CGColor];
        contentBlackLayer.locations = @[@0, @0.5, @1];
        contentBlackLayer.startPoint = CGPointMake(0, 0);
        contentBlackLayer.endPoint = CGPointMake(0, 1);
        contentBlackLayer.frame = _contentMaskView.bounds;
        _contentBlackLayer = contentBlackLayer;
        [effectView.contentView.layer addSublayer:contentBlackLayer];
    }
    return _contentMaskView;
}

/// 查看或是下载
- (UIButton *)statusButton
{
    if (!_statusButton) {
        _statusButton = [[UIButton alloc] initWithFrame:CGRectZero];
        _statusButton.hidden = YES;
        _statusButton.titleLabel.font = XMI_AD_PingFangMediumFont(10);
        [_statusButton setTitleColor:XMI_COLOR_RGB(0xffffff) forState:UIControlStateNormal];
        _statusButton.layer.cornerRadius = 9;
        _statusButton.layer.masksToBounds = YES;
        _statusButton.userInteractionEnabled = NO;
        _statusButton.titleLabel.lineBreakMode = NSLineBreakByTruncatingTail;
        _statusButton.backgroundColor = XMI_COLOR_RGBA(0x000000, 0.28);
    }
    return _statusButton;
}
/// 音量
- (UIButton *)volumeButton
{
    if (!_volumeButton) {
        _volumeButton = [[UIButton alloc] initWithFrame:CGRectZero];
        _volumeButton.hidden = YES;
        _volumeButton.userInteractionEnabled = NO;
    }
    return _volumeButton;
}

/// 播放
- (UIButton *)playButton
{
    if (!_playButton) {
        _playButton = [[UIButton alloc] initWithFrame:CGRectZero];
        _playButton.hidden = YES;
        _playButton.userInteractionEnabled = NO;
        [_playButton setImage:[XMICommonUtils imageNamed:@"ad_double_video_play"] forState:UIControlStateNormal];
    }
    return _playButton;
}

/// 标题下的maskView
- (UIView *)titleMaskView
{
    if (!_titleMaskView) {
        _titleMaskView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.bounds.size.width, 105)];
        _titleMaskView.hidden = YES;
        // gradient
        CAGradientLayer *gl = [CAGradientLayer layer];
        gl.frame = CGRectMake(0, 0, self.bounds.size.width, 105);
        gl.startPoint = CGPointMake(0.5, 0.15);
        gl.endPoint = CGPointMake(0.5, 0.98);
        gl.colors = @[(__bridge id)XMI_COLOR_DynamicFromRGBA(0x262626,0.0,0x262626,0.0).CGColor,(__bridge id)XMI_COLOR_DynamicFromRGBA(0x262626,0.7,0x262626,0.7).CGColor];
        gl.locations = @[@(0), @(1.0f)];
        [_titleMaskView.layer addSublayer:gl];
        
    }
    return _titleMaskView;
}

/// 封面阴影
- (UIView *)coverMaskView
{
    if (!_coverMaskView) {
        _coverMaskView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.bounds.size.width, XMIExpressAdDoubleViewCoverMaskHeight)];
        // gradient
        CAGradientLayer *gl = [CAGradientLayer layer];
        gl.frame = CGRectMake(0, 0, self.bounds.size.width, XMIExpressAdDoubleViewCoverMaskHeight);
        gl.startPoint = CGPointMake(0.5, 0.15);
        gl.endPoint = CGPointMake(0.5, 0.98);
        gl.colors = @[(__bridge id)XMI_COLOR_DynamicFromRGBA(0x000000,0.0,0x000000,0.0).CGColor,(__bridge id)XMI_COLOR_DynamicFromRGBA(0x000000,0.4,0x000000,0.4).CGColor];
        gl.locations = @[@(0), @(1.0f)];
        [_coverMaskView.layer addSublayer:gl];
            
        
    }
    return _coverMaskView;
}

- (UIView *)anchorTapView
{
    if (!_anchorTapView) {
        _anchorTapView = [[UIView alloc] initWithFrame:CGRectZero];
        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(anchorItemClicked:)];
        [_anchorTapView addGestureRecognizer:tapGesture];
    }
    return _anchorTapView;
}

- (UIImageView *)tagImageView
{
    if (!_tagImageView) {
        _tagImageView = [[UIImageView alloc] initWithFrame:CGRectZero];
        _tagImageView.contentMode = UIViewContentModeScaleAspectFit;
        _tagImageView.hidden = YES;
    }
    return _tagImageView;
}

/// 主播头像
- (XMIAnimatedImageView *)anchorImageView
{
    if (!_anchorImageView) {
        _anchorImageView = [[XMIAnimatedImageView alloc] initWithFrame:CGRectMake(0, 0, kXMIExpressAdDoubleVideoViewAnchorHeight, kXMIExpressAdDoubleVideoViewAnchorHeight)];
        _anchorImageView.layer.cornerRadius = kXMIExpressAdDoubleVideoViewAnchorHeight/2.0;
        _anchorImageView.layer.borderColor = XMI_COLOR_RGB(0xffffff).CGColor;
        _anchorImageView.layer.borderWidth = 1.0f;
        _anchorImageView.layer.masksToBounds = YES;
        _anchorImageView.userInteractionEnabled = NO;
        [_anchorImageView setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
        [_anchorImageView setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    }
    return _anchorImageView;
}

- (UILabel *)anchorNameLabel
{
    if (!_anchorNameLabel) {
        _anchorNameLabel = [[UILabel alloc] initWithFrame:CGRectZero];
        _anchorNameLabel.textColor = XMI_COLOR_RGB(0xffffff);
        _anchorNameLabel.font = XMI_AD_PingFangFont(11);
        _anchorNameLabel.userInteractionEnabled = NO;
    }
    return _anchorNameLabel;
}
/// 播放标识
- (UIImageView *)playTagIcon
{
    if (!_playTagIcon) {
        _playTagIcon = [[UIImageView alloc] initWithFrame:CGRectZero];
        _playTagIcon.image = [XMICommonUtils imageNamed:@"home_feed_ad_subscribe_count"];
        [_playTagIcon setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
        [_playTagIcon setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    }
    return _playTagIcon;
}
/// 播放数量
- (UILabel *)playCountLabel
{
    if (!_playCountLabel) {
        _playCountLabel = [[UILabel alloc] initWithFrame:CGRectZero];
        _playCountLabel.font = XMI_AD_PingFangFont(11);
        _playCountLabel.textColor = XMI_COLOR_RGB(0xffffff);
        [_playCountLabel setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
        [_playCountLabel setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    }
    return _playCountLabel;
}

- (UIImageView *)scoreImgV
{
    if (!_scoreImgV) {
        _scoreImgV = [[UIImageView alloc] initWithFrame:CGRectZero];
        _scoreImgV.image = [XMICommonUtils imageNamed:@"home_feed_ad_grade"];
        _scoreImgV.hidden = YES;
    }
    return _scoreImgV;
}

- (UILabel *)scoreLabel
{
    if (!_scoreLabel) {
        _scoreLabel = [[UILabel alloc] initWithFrame:CGRectZero];
        _scoreLabel.textColor = XMI_COLOR_DynamicFromRGB(0xFF8B20, 0xFF8B20);
        _scoreLabel.font = XMI_AD_PingFangFont(12);
        _scoreLabel.hidden = NO;
    }
    return _scoreLabel;
}

/// 推荐语
- (UILabel *)recLabel
{
    if (!_recLabel) {
        _recLabel = [[UILabel alloc] initWithFrame:CGRectZero];
        _recLabel.font = kAdVideoRecFont;
        _recLabel.textColor = XMI_COLOR_DynamicFromRGB(0x666666, 0x888888);
        _recLabel.hidden = YES;
    }
    return _recLabel;
}





- (UIView *)contentView
{
    if (!_contentView) {
        _contentView = [[UIView alloc] initWithFrame:self.bounds];
        _contentView.backgroundColor = XMI_COLOR_DynamicFromRGB(0xffffff, 0x1E1E1E);
//        _contentView.layer.shadowColor = XMI_COLOR_RGBA(0x535459, 0.14).CGColor;
//        _contentView.layer.shadowOffset = CGSizeMake(0,10);
//        _contentView.layer.shadowOpacity = 1;
//        _contentView.layer.shadowRadius = 52;
        _contentView.layer.masksToBounds = YES;
        _contentView.hidden = YES;
    }
    return _contentView;
}


/// 关闭按钮
- (UIButton *)closeButton
{
    if (!_closeButton) {
        _closeButton = [[XMIAdButton alloc] initWithFrame:CGRectZero];
        UIImage *normalImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        UIImage *darkImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close_dark"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        [_closeButton setImage:[UIImage xm_imageWithLight:normalImage dark:darkImage] forState:UIControlStateNormal];
        _closeButton.imageView.tintColor = XMI_COLOR_DynamicFromRGBA(0x666666, 0.7, 0xD8D8D8, 1);
        [_closeButton addTarget:self action:@selector(closeButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _closeButton;
}

- (UIImageView *)adMark
{
    if (!_adMark) {
        _adMark = [[UIImageView alloc] initWithFrame:CGRectZero];
        _adMark.image = [XMICommonUtils imageNamed:@"pic_ad_mark_3"];
        _adMark.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _adMark;
}

/// 标题
- (YYLabel *)titleLabel
{
    if (!_titleLabel) {
        _titleLabel = [[YYLabel alloc] initWithFrame:CGRectZero];
        _titleLabel.font = kAdAudioTitleFont;
        _titleLabel.hidden = YES;
        _titleLabel.numberOfLines = kXMIExpressAdDoubleViewTitleLines;
        _titleLabel.textColor = XMI_COLOR_DynamicFromRGB(0x333333, 0xcfcfcf);
    }
    return _titleLabel;
}

/// 封面
- (XMIAnimatedImageView *)coverImageView
{
    if (!_coverImageView) {
        _coverImageView = [[XMIAnimatedImageView alloc] initWithFrame:CGRectMake(0, 0, self.bounds.size.width, self.bounds.size.width)];
        _coverImageView.contentMode = UIViewContentModeScaleAspectFit;
        _coverImageView.clipsToBounds = YES;
//        _coverImageView.backgroundColor = XMI_COLOR_RGB(0xCCCCCC);
        _coverImageView.backgroundColor = XMI_COLOR_RGB(0xEDEDED);
        UIImage *placeImage = [XMICommonUtils imageNamed:@"ad_bkg_default"];
        _coverImageView.image = placeImage;
    }
    return _coverImageView;
}


+ (CGFloat)getAdViewHeight:(XMIAdRelatedData *)adData
{
    if ([XMIAdConverter isVirtualAD:adData.adid]) {
        return 1.0f;
    }
    if (adData.showstyle == XMIAdStyleHomeDoubleRowAudio ||
        adData.showstyle == XMIAdStyleHomeDoubleRowSquareImage ||
        adData.showstyle == XMIAdStyleHomeDoubleRowVideo ||
        adData.showstyle == XMIAdStyleHomeDoubleRowVerticalVideo ||
        adData.showstyle == XMIAdStyleHomeDoubleRowVerticalImage) {
        return [XMIExpressAdDoubleView getAdViewHeightWithRelatedData:adData];
    }
    return 0;
}



+ (void)startSetTitleTextLayout:(XMIAdRelatedData *)adData
                    adViewWidth:(CGFloat)adViewWidth
{
    if (adData.showstyle == XMIAdStyleHomeDoubleRowAudio) {
        if (adData.businessExtraInfo) {
            XMIExpressAdBusniessExtraModel *extralModel = [XMIExpressAdBusniessExtraModel xmi_modelWithJSON:adData.businessExtraInfo];
            adData.businessExtraModel = extralModel;
            adData.titleTextLayout = [XMIExpressAdDoubleView getAudioTitleTextLayout:extralModel.title adViewWidth:adViewWidth];
        }
    }else if (adData.showstyle == XMIAdStyleHomeDoubleRowSquareImage){
        adData.titleTextLayout = [XMIExpressAdDoubleView getAudioTitleTextLayout:adData.name adViewWidth:adViewWidth];
    }
    else if (adData.showstyle == XMIAdStyleHomeDoubleRowVideo ||
              adData.showstyle == XMIAdStyleHomeDoubleRowVerticalVideo ||
              adData.showstyle == XMIAdStyleHomeDoubleRowVerticalImage){
        if (adData.adtype == XMIAdTypeXM) {
            adData.titleTextLayout = [XMIExpressAdDoubleView getTitleTextLayout:adData adViewWidth:adViewWidth];
        }
    }
}

+ (YYTextLayout *)getTitleTextLayout:(id)adData
                         adViewWidth:(CGFloat)adViewWidth
{
    NSString *title = @"";
    CGFloat totalWidth = adViewWidth;
    CGFloat titleWidth = totalWidth - kXMIExpressAdDoubleVideoViewHoriznalMargin*2;
    if ([adData isKindOfClass:XMIAdRelatedData.class]) {
        XMIAdRelatedData *relatedData = (XMIAdRelatedData *)adData;
        title = relatedData.name ?: @"";
        BOOL showStyle = relatedData.showstyle == XMIAdStyleHomeDoubleRowVerticalVideo || relatedData.showstyle == XMIAdStyleHomeDoubleRowVerticalImage;
        if (showStyle && !relatedData.clickTitle.length) {
            titleWidth = totalWidth - kXMIExpressAdDoubleVideoViewHoriznalMargin - kXMIExpressAdDoubleVideoViewBigHoriznalMargin;
        }
    }else if ([adData isKindOfClass:GDTUnifiedNativeAdDataObject.class]){
        GDTUnifiedNativeAdDataObject *dataObject = (GDTUnifiedNativeAdDataObject *)adData;
        title = dataObject.title ?: @"";
        if (!dataObject.callToAction.length) {
            titleWidth = totalWidth - kXMIExpressAdDoubleVideoViewHoriznalMargin - kXMIExpressAdDoubleVideoViewBigHoriznalMargin;
        }
    }else if ([adData isKindOfClass:BUNativeAd.class]){
        BUNativeAd *dataObject = (BUNativeAd *)adData;
        title = dataObject.data.AdTitle ?: @"";
        if (!dataObject.data.buttonText.length) {
            titleWidth = totalWidth - kXMIExpressAdDoubleVideoViewHoriznalMargin - kXMIExpressAdDoubleVideoViewBigHoriznalMargin;
        }
    }
    
    NSMutableAttributedString *str = [[NSMutableAttributedString alloc] initWithString:title];
    str.yy_font = XMI_AD_PingFangMediumFont(14);
    str.yy_color = XMI_COLOR_RGB(0xffffff);
    YYTextContainer *titleContainer      = [YYTextContainer containerWithSize:(CGSizeMake(titleWidth, 999))];
    titleContainer.maximumNumberOfRows   = 2;
    [titleContainer setTruncationType:(YYTextTruncationTypeEnd)];
    YYTextLayout *layout = [YYTextLayout layoutWithContainer:titleContainer text:str];
    return layout;
}

+ (YYTextLayout *)getAudioTitleTextLayout:(NSString *)title
                              adViewWidth:(CGFloat)adViewWidth
{
    CGFloat totalWidth = adViewWidth;
    CGFloat titleWidth = totalWidth - kXMIExpressAdDoubleVideoViewHoriznalMargin*2;
    NSMutableAttributedString *str = [[NSMutableAttributedString alloc] initWithString:title?:@""];
    str.yy_font = kAdAudioTitleFont;
    str.yy_color = XMI_COLOR_DynamicFromRGB(0x333333, 0xcfcfcf);
    YYTextContainer *titleContainer      = [YYTextContainer containerWithSize:(CGSizeMake(titleWidth, 999))];
    titleContainer.maximumNumberOfRows   = 2;
    [titleContainer setTruncationType:(YYTextTruncationTypeEnd)];
    YYTextLayout *layout = [YYTextLayout layoutWithContainer:titleContainer text:str];
    return layout;
}

+ (CGFloat)getAdViewHeightWithRelatedData:(XMIAdRelatedData *)relatedData
{
    NSInteger showStyle = relatedData.showstyle;
    CGFloat adViewWidth = relatedData.adWidth;
    if (showStyle == XMIAdStyleHomeDoubleRowAudio) {
        return [self getAudioViewHeightWithRelatedData:relatedData];
    }
    else if (showStyle == XMIAdStyleHomeDoubleRowVideo) {
        /// 16:9
        CGFloat totalHeight = 0;
        CGFloat coverTopMargin = kXMIExpressAdDoubleVideoViewCoverTopMargin;
        totalHeight += coverTopMargin;
        /// 封面高度
        CGFloat coverHeight = adViewWidth * 9/16.0;
        totalHeight += coverHeight;
        /// 封面底部
        CGFloat coverBottomMargin = kXMIExpressAdDoubleVideoViewCoverBottomMargin;
        totalHeight += coverBottomMargin;
        
        return totalHeight;
    }else if(showStyle == XMIAdStyleHomeDoubleRowVerticalImage ||
             showStyle == XMIAdStyleHomeDoubleRowVerticalVideo){
        CGFloat sizeRadio = relatedData.sizeRadio;
        if (sizeRadio == 0) {
//            if (relatedData.adtype == XMIAdTypeXM) {
//                sizeRadio = 16/9.0;
//            }else{
//                return 0.0f;
//            }
            sizeRadio = 16/9.0;
        }
        CGFloat coverHeight = adViewWidth*sizeRadio;
        return coverHeight;
    }
    return 0;
}

+ (CGFloat)getAudioViewHeightWithRelatedData:(XMIAdRelatedData *)adData
{
    CGFloat adViewWidth = adData.adWidth;
    CGFloat totalWidth = adViewWidth;
    CGFloat height = 0;
    /// 封面
    CGFloat coverHeight = totalWidth;
    height += coverHeight;
        
    /// 标题
    CGFloat titleHeight = adData.titleTextLayout.textBoundingSize.height;
    height += kXMIExpressAdDoubleVideoViewCoverTitleSpace;
    height += titleHeight;
    if ([adData.businessExtraModel isKindOfClass:XMIExpressAdBusniessExtraModel.class]) {
        XMIExpressAdBusniessExtraModel *extraModel = (XMIExpressAdBusniessExtraModel *)adData.businessExtraModel;
        if (extraModel.recommendTags.length) {
            /// 推荐
            height += kXMIExpressAdDoubleVideoViewTitleRecSpace;
            height += (kAdVideoRecFont.lineHeight);
        }
    }
    
    height += kXMIExpressAdDoubleVideoViewRecommendBottomMargin;
    return height;
}

- (void)refreshWithData:(XMIAdRelatedData *)adData {
    [self p_setCloseButtonBackImage:adData];
    if (adData.loadingStatus == XMIAdRelatedLoadingStatusNormal) {
        self.coverImageView.image = nil;
        return;
    }
    self.relatedData = adData;
    [self startSetUpUIWithModel:adData];
}



- (void)p_setCloseButtonBackImage:(XMIAdRelatedData *)adData
{
    UIImage *normalImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
    UIImage *darkImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close_dark"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
    if (adData.showstyle == XMIAdStyleHomeDoubleRowAudio) {
        /// 声波
        darkImage = [XMICommonUtils imageNamed:@"home_feed_close_audio_dark"];
    }else if (adData.showstyle == XMIAdStyleHomeDoubleRowVideo ||
              adData.showstyle == XMIAdStyleHomeDoubleRowVerticalImage ||
              adData.showstyle == XMIAdStyleHomeDoubleRowVerticalVideo){
        darkImage = [XMICommonUtils imageNamed:@"home_feed_close_vertical_dark"];
    }
    [self.closeButton setImage:[UIImage xm_imageWithLight:normalImage dark:darkImage] forState:UIControlStateNormal];
    self.closeButton.imageView.tintColor = XMI_COLOR_DynamicFromRGBA(0x666666, 0.7, 0xD8D8D8, 1);
    ((XMIAdButton *)self.closeButton).hitTestEdgeOutsets = [adData closeAreaPaddingWithDefaultPadding:UIEdgeInsetsMake(7, 7, 7, 7)];
}


- (void)layoutSubviews
{
    [super layoutSubviews];
}

- (void)closeButtonAction:(UIButton *)button
{
    
}

- (void)startSetUpUIWithModel:(id)model
{
    self.coverImageView.hidden = NO;
    self.adMark.hidden = NO;
    self.titleLabel.hidden = NO;
    self.closeButton.hidden = NO;
    self.placeHolderView.hidden = YES;
}




/**
 增加及处理点击事件
 */
- (void)registerOwnClickableViews:(NSArray *)views {
    for (UIView *aView in views) {
        aView.userInteractionEnabled = YES;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleViewClick:)];
        [aView addGestureRecognizer:tap];
    }
}

- (void)handleViewClick:(UITapGestureRecognizer *)tap {
    [self adViewDidTap:tap];
}




- (void)adViewDidTap:(UITapGestureRecognizer *)tap
{
    
}


- (void)adViewDidClicked
{
    
}


/// 主播头像点击
- (void)anchorItemClicked:(UITapGestureRecognizer *)tap
{
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdView:didClickedAnchorItem:clickedView:)]) {
        [self.delegate expressAdView:self didClickedAnchorItem:self.relatedData clickedView:tap.view];
    }
    
    if ([self.relatedData.businessExtraModel isKindOfClass:XMIExpressAdBusniessExtraModel.class]) {
        XMIExpressAdBusniessExtraModel *extraModel = (XMIExpressAdBusniessExtraModel *)self.relatedData.businessExtraModel;
        XMIAdAnchorReporter *reporter = [[XMIAdAnchorReporter alloc] init];
        reporter.broadcasterId = extraModel.broadcasterId;
        [reporter fillWithAd:self.relatedData];
        [[XMIAdReporter sharedInstance] addAnchorClickReport:reporter];
    }
}


- (void)traitCollectionDidChange:(UITraitCollection *)previousTraitCollection
{
    [super traitCollectionDidChange:previousTraitCollection];
    self.titleLabel.textLayout = self.titleLabel.textLayout;
    
    [self p_setCloseButtonBackImage:self.relatedData];
    
}




- (void)scrollViewDidEndScroll
{
    [super scrollViewDidEndScroll];
    [self setStatusButtonColorAnimation];
}


- (void)controllerViewDidDisappear
{
    [super controllerViewDidDisappear];
    BOOL isExposed = [self xmi_isExposed:self.rootViewController.view radio:0.01];
    if (isExposed) {
        self.statusButton.backgroundColor = XMI_COLOR_RGBA(0x000000, 0.28);
        self.relatedData.isNeedAnimated = YES;
    }
}

- (void)controllerViewDidAppear
{
    [super controllerViewDidAppear];
    [self setStatusButtonColorAnimation];
}


- (void)setStatusButtonColorAnimation
{
    if (!self.relatedData.isNeedAnimated && self.relatedData.isAnimatedCompleted) {
        [self p_setStatusColor];
        return;
    }
    [self p_startStatusButtonColorAnimation];
}


- (void)p_startStatusButtonColorAnimation
{
    if (self.statusButton.hidden || !self.statusButton.superview) {
        return;
    }
    
    BOOL isExposed = [self xmi_isExposed:self.rootViewController.view radio:0.01];
    if (isExposed) {
        [self performSelector:@selector(p_setStatusAnimation) withObject:nil afterDelay:1.0];
    }else{
        if (!self.relatedData.isAnimatedCompleted) {
            self.statusButton.backgroundColor = XMI_COLOR_RGBA(0x000000, 0.28);
            self.relatedData.isNeedAnimated = YES;
            [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(p_setStatusAnimation) object:nil];
        }
    }
    self.relatedData.isNeedAnimated = NO;
}



/// 获取是否是下载
- (BOOL)getAdIsDownloadApp
{
    return NO;
}


- (void)p_setStatusAnimation
{
    __weak typeof(self) weakSelf = self;
    [UIView animateWithDuration:0.5 delay:0.0 options:UIViewAnimationOptionCurveEaseInOut animations:^{
        [weakSelf p_setStatusColor];
        weakSelf.relatedData.isAnimatedCompleted = YES;
    } completion:nil];
}

- (void)p_setStatusColor
{
    BOOL isDownLoad = [self getAdIsDownloadApp];
    if (isDownLoad) {
        /// 下载
        self.statusButton.backgroundColor = XMI_COLOR_RGB(0x1295FF);
    }else{
        self.statusButton.backgroundColor = XMI_COLOR_RGB(0xFF4646);
    }
}


@end
