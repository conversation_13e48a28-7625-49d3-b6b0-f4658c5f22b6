//
//  XMIExpressAdView+Internal.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/26.
//

#import "XMIExpressAdSingleView+Internal.h"
#import "XMIAdMacro.h"
#import "XMICommonUtils.h"

@implementation XMIExpressAdSingleView (Internal)

- (instancetype)initWithOriginView:(UIView *)originView {
    self = [super init];
    if (self) {
        
    }
    
    return self;
}

- (instancetype)initWithOriginData:(id)adData {
    self = [super init];
    if (self) {
        
    }
    
    return self;
}

- (CGFloat)contentWidth {
    return [self adWidth] - 2 * 16;
}

- (CGFloat)contentHeight {
    return [self adHeight];
}

- (UIView *)defaultContentView {
    CGFloat contentWidth = [self contentWidth];
    CGFloat contentHeight = [self contentHeight];
    UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(16, 0, contentWidth, contentHeight)];
    contentView.layer.cornerRadius = 12;
    contentView.layer.masksToBounds = YES;
    
    return contentView;
}

- (UIView *)defaultActionViewWithText:(NSString *)text {
    // 限制最多6个字
    NSString *aText = text != nil &&text.length > 0 ? text : @"立即查看";
    if (text != nil && text.length > 6) {
        aText = [NSString stringWithFormat:@"%@...", [text substringWithRange:NSMakeRange(0, 6)]];
    }
    NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:aText];
    NSTextAttachment *spaceAttach = [[NSTextAttachment alloc] init];
    spaceAttach.bounds = CGRectMake(0, 0, 2, 0);
    NSAttributedString *spaceStr = [NSAttributedString attributedStringWithAttachment:spaceAttach];
    [attrStr appendAttributedString:spaceStr];
    
    [attrStr addAttributes:@{NSFontAttributeName : XMI_AD_PingFangFont(12), NSForegroundColorAttributeName : [UIColor whiteColor]} range:NSMakeRange(0, attrStr.length)];
    
    NSTextAttachment *arrowAttach = [[NSTextAttachment alloc] init];
    UIImage *arrowImage = [XMICommonUtils imageNamed:@"ad_native_arrow_white"];
    arrowAttach.image = arrowImage;
    arrowAttach.bounds = CGRectMake(0, -1, arrowImage.size.width, arrowImage.size.height);
    NSAttributedString *arrowStr = [NSAttributedString attributedStringWithAttachment:arrowAttach];
    [attrStr appendAttributedString:arrowStr];
    
    UILabel *aLabel = [[UILabel alloc] init];
    aLabel.attributedText = attrStr;
    [aLabel sizeToFit];
    aLabel.frame = CGRectMake(0, 0, aLabel.frame.size.width + 20, 26);
    aLabel.backgroundColor = XMI_COLOR_RGB(0xFF4C2E);
    aLabel.layer.cornerRadius = aLabel.frame.size.height / 2;
    aLabel.layer.masksToBounds = YES;
    aLabel.textAlignment = NSTextAlignmentCenter;
    
    return aLabel;
}

- (UIView *)defaultBottomView {
    CGFloat contentWidth = [self contentWidth];
    CGFloat contentHeight = [self contentHeight];
    // bottom view
    UIView *bottomView = [[UIView alloc] initWithFrame:CGRectMake(10, contentHeight - 26 - 10, contentWidth - 10*2, 26)];
    bottomView.clipsToBounds = YES;
    bottomView.layer.cornerRadius = bottomView.frame.size.height / 2;
    bottomView.backgroundColor = XMI_COLOR_RGBA(0, 0.5);
    
    return bottomView;
}

- (UILabel *)defaultTitleViewWithTitle:(NSString *)title {
    // title
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(10, 0, self.bottomView.frame.size.width - 10 - self.actionView.frame.size.width, 26)];
    titleLabel.text = title;
    titleLabel.textColor = [UIColor whiteColor];
    titleLabel.backgroundColor = [UIColor clearColor];
    titleLabel.font = XMI_AD_PingFangFont(13);
    titleLabel.lineBreakMode = NSLineBreakByTruncatingTail;
    
    return titleLabel;
}

- (UILabel *)defaultDescViewWithDesc:(NSString *)desc {
    // desc
    UILabel *descLabel = [[UILabel alloc] init];
    descLabel.text = desc;
    descLabel.font = XMI_AD_PingFangFont(12);
    
    return descLabel;
}

@end
