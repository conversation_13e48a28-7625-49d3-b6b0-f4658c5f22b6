//
//  XMIExpressAdView+Internal.h
//  XMAd
//  信息流视图内部方法
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/26.
//

#import "XMIExpressAdSingleView.h"

NS_ASSUME_NONNULL_BEGIN

@interface XMIExpressAdSingleView (Internal)

- (instancetype)initWithOriginView:(UIView *)originView;
- (instancetype)initWithOriginData:(id)adData;

/**
 内容区宽度
 */
- (CGFloat)contentWidth;
/**
 内容区高度
 */
- (CGFloat)contentHeight;

/**
 一系列默认的UI
 所有内容容器视图
 */
- (UIView *)defaultContentView;
/**
 "点击查看" 视图
 */
- (UIView *)defaultActionViewWithText:(NSString *)text;
/**
 底部背景
 */
- (UIView *)defaultBottomView;
/**
 广告标题
 */
- (UILabel *)defaultTitleViewWithTitle:(NSString *)title;
/**
 广告描述
 */
- (UILabel *)defaultDescViewWithDesc:(NSString *)desc;

@end

NS_ASSUME_NONNULL_END
