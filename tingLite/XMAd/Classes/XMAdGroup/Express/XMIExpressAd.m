//
//  XMIExpressAd.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/6/30.
//

#import "XMIExpressAd.h"
#import "XMIAdViewProtocol.h"

@implementation XMIExpressAd

- (void)didLoadData:(NSArray *)adDataArray {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:didLoadData:)]) {
        [self.delegate expressAd:self didLoadData:adDataArray];
    }
}

- (void)didLoadFailWithError:(NSError *)error {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:didLoadFailWithError:)]) {
        [self.delegate expressAd:self didLoadFailWithError:error];
    }
}

- (void)didLoad:(NSArray *)views {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:didLoad:)]) {
        [self.delegate expressAd:self didLoad:views];
    }
}

@end
