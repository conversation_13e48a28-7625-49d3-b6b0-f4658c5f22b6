//
//  XMIBaseAd.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/6/30.
//

#import <Foundation/Foundation.h>
#import "XMIAdSlot.h"

NS_ASSUME_NONNULL_BEGIN

@interface XMIBaseAd : NSObject

/**
 广告对象
 */
@property (nonatomic, strong) XMIAdSlot *adSlot;

/**
 必传，用作广告点击弹出落地页等
 */
@property (nonatomic, weak) UIViewController *rootViewController;

- (instancetype)initWithSlot:(XMIAdSlot *)adSlot;

- (void)loadAdData;

/**
 internal
 */
/**
 对象ID，用来比较，判断有效性
 */
@property (nonatomic, assign) long long oid;
/**
 上报对象id
 */
@property (nonatomic, assign) long long reporterId;

- (BOOL)isValid;
- (void)invalidate;

@end

NS_ASSUME_NONNULL_END
