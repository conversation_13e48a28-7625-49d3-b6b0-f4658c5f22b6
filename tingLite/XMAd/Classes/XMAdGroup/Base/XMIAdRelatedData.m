//
//  XMIAdRelatedData.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/27.
//

#import "XMIAdRelatedData.h"
#import "XMIAdDataCenter.h"
#import "XMIExpressAdTrackInfoMapModel.h"
#import <objc/runtime.h>
#import "NSObject+XMIModel.h"
#import <XMCategories/NSString+XMCommon.h>
#import <XMCategories/NSData+XMCommon.h>
#import <YYModel/NSObject+YYModel.h>
#import <CommonCrypto/CommonCryptor.h>

@implementation XMIAdRelatedData
@synthesize adno = _adno;

- (id)copyWithZone:(NSZone *)zone {
    return [self yy_modelCopy];
}

/// 获取唯一表示
- (NSString *)getIdentifier
{
    return [NSString stringWithFormat:@"%p-%lld", self, self.adid];
}

- (NSString *)mixGuessYouLikePromoteType {
    if ([self.trackInfoMapModel isKindOfClass:[XMIExpressAdTrackInfoMapModel class]]) {
        XMIExpressAdTrackInfoMapModel *infoModel = (XMIExpressAdTrackInfoMapModel *)self.trackInfoMapModel;
        return infoModel.promoteType;
    }
    return @"";
}

- (BOOL)isExpired
{
    if (self.adCacheExpireTime == 0) {
        return NO;
    }
    long long interval = [[NSDate date] timeIntervalSince1970] * 1000;
    return interval > self.adCacheExpireTime;
}

- (BOOL)videoUrlVaild {
    return self.videoUrl && self.videoUrl.length > 0 && ![self.videoUrl hasSuffix:@".webp"];
}

- (NSInteger)adItemSDKType {
    NSInteger type = 3;
    if (self.adtype == XMIAdTypeGDT || self.adtype == XMIAdTypeGDTSplash) {
        type = 1;
    }
    else if (self.adtype == XMIAdTypeBU || self.adtype == XMIAdTypeBUExpress) {
        type = 2;
    }
    return type;
}

- (BOOL)isGuessYouLikeAd {
    if (self.positionId == 44 || [self.positionName isEqualToString:@"home_guess_you_like"]) {
        return YES;
    }
    return NO;
}

- (void)setAdno:(NSUInteger)adno {
    _adno = adno;
    [self.compositeAds enumerateObjectsUsingBlock:^(XMIAdRelatedData * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        obj.adno = adno;
    }];
}

- (float)price {
    // 如果该物料是客户端竞价
    if (self.isMobileRtb) {
        // 物料类型为穿山甲或京东或广点通 那么直接返回价格，价格是从dsp的回调获取的
        if (self.adtype == XMIAdTypeJAD || 
            self.adtype == XMIAdTypeBU ||
            self.adtype == XMIAdTypeGDT ||
            self.adtype == XMIAdTypeBAIDU ||
            self.adtype == XMIAdTypeBD) {
            if (_price > 0) {
                return _price;
            }
            return 0;
        }
    }
    // priceEncrypt不为空，解析后获得价格
    if (_priceEncrypt.length) {
        return [self decryptionPriceWithToken:_priceEncrypt].floatValue;
    }
    return _price;
}

- (float)bidMinPrice {
    // bidMinPriceEncrypt不为空，解析后获得价格
    if (self.isMobileRtb && _bidMinPriceEncrypt.length) {
        return [self decryptionPriceWithToken:_bidMinPriceEncrypt].floatValue;
    }
    return 0;
}

- (NSString *)priceEncrypt {
    // 如果该物料是客户端竞价 && 物料类型为穿山甲或京东或广点通 ，那么需要把SDK返回的价格进行加密
    if (self.isMobileRtb && (self.adtype == XMIAdTypeJAD || self.adtype == XMIAdTypeBU || self.adtype == XMIAdTypeGDT || self.adtype == XMIAdTypeBD || self.adtype == XMIAdTypeBAIDU)) {
        if (_price <= 0) {
            return @"";
        }
        NSString *value = [self encryptPriceWithAES:[NSString stringWithFormat:@"%.4f", _price]];
        return value;
    }
    // 如果该物料不是客户端竞价（这种也可能是穿山甲或京东或广点通），只需要把adx传给客户端的priceEncrypt回传
    return _priceEncrypt;
}

- (NSString *)decryptionPriceWithToken:(NSString *)priceString {
    if(!priceString) return nil;
    static NSString *privateKey = @"chbWyWugtSYjewMAZrVA/w==";
    NSData *keyData = [privateKey base64DecodedData];
    NSData *textData = [priceString base64DecodedData];
    NSData *decodedData = [NSData AES256ParmDecryptWithKeyData:keyData DecryptData:textData];
    NSString *decodeString = [[NSString alloc] initWithData:decodedData encoding :NSUTF8StringEncoding];
    return decodeString;
}

- (NSString *)encryptPriceWithAES:(NSString *)content {
    NSData *contentData = [content dataUsingEncoding:NSUTF8StringEncoding];
    NSUInteger dataLength = contentData.length;
    
    static NSString *privateKey = @"chbWyWugtSYjewMAZrVA/w==";
    NSData *initVector = [privateKey base64DecodedData];
    // 密文长度
    size_t encryptSize = dataLength + kCCBlockSizeAES128;
    void *encryptedBytes = malloc(encryptSize);
    size_t actualOutSize = 0;
    
    size_t const kKeySize = kCCKeySizeAES128;

    CCCryptorStatus cryptStatus = CCCrypt(kCCEncrypt,
                                          kCCAlgorithmAES,
                                          kCCOptionPKCS7Padding | kCCOptionECBMode,
                                          initVector.bytes,
                                          kKeySize,
                                          initVector.bytes,
                                          contentData.bytes,
                                          dataLength,
                                          encryptedBytes,
                                          encryptSize,
                                          &actualOutSize);

    if (cryptStatus == kCCSuccess) {
        // 对加密后的数据进行 base64 编码
        return [[NSData dataWithBytesNoCopy:encryptedBytes length:actualOutSize] base64EncodedStringWithOptions:NSDataBase64EncodingEndLineWithLineFeed];
    }
    free(encryptedBytes);
    return nil;
}

@end

static char const kXMIAdRelatedDataClosePaddingKey;

@implementation XMIAdRelatedData (XMICloseAdPadding)

- (UIEdgeInsets)closeAreaPaddingWithDefaultPadding:(UIEdgeInsets)defaultPadding
{
    NSValue *paddingValue = [self closeAreaPadding];
    if (paddingValue) {
        UIEdgeInsets padding = paddingValue.UIEdgeInsetsValue;
        return UIEdgeInsetsMake(padding.top + defaultPadding.top, padding.left + defaultPadding.left, padding.bottom + defaultPadding.bottom, padding.right + defaultPadding.right);
    }
    return defaultPadding;
}

- (NSValue *)closeAreaPadding
{
    if (!self.positionId) {
        return nil;
    }
    NSValue *value = objc_getAssociatedObject(self, &kXMIAdRelatedDataClosePaddingKey);
    if (value) {
        if (![value isKindOfClass:[NSValue class]]) {
            return nil;
        }
        return value;
    }
    value = [XMIAdDataCenter closeAreaPadding:[NSString stringWithFormat:@"%lld", self.positionId]];
    if (value == nil) {
        objc_setAssociatedObject(self, &kXMIAdRelatedDataClosePaddingKey, [NSNull null], OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        return nil;
    }
    objc_setAssociatedObject(self, &kXMIAdRelatedDataClosePaddingKey, value, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    return value;
}

- (UIEdgeInsets)closeAreaPaddingEdge
{
    return [self.closeAreaPadding UIEdgeInsetsValue];
}

- (NSString *)description
{
    return [NSString stringWithFormat:@"<%@ %p> \n\
            responseId: %lld \n\
            slotId: %lld \n\
            adId: %lld \n\
            positionId: %lld \n\
            positionName: %@ \n\
            adType : %zd \n\
            dspPostionId : %@ \n\
            name : %@ \n\
            videoURL : %@ \n\
            imageURL : %@",
            [self class],
            self,
            self.responseId,
            self.slotId,
            self.adid,
            self.positionId,
            self.positionName,
            self.adtype,
            self.dspPositionId,
            self.name,
            self.videoUrl,
            self.cover];
}

@end
