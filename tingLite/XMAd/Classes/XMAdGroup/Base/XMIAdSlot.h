//
//  XMIAdSlot.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/5.
//

#import <Foundation/Foundation.h>
#import "XMIAdModel.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, XMIAdSlotAdType) {
    XMIAdSlotAdTypeUnknown      = 0,
    XMIAdSlotAdTypeSplash        = 1,       // 开屏广告
    XMIAdSlotAdTypeSplash_Custom = 2,       // cache splash ads
    XMIAdSlotAdTypeFeed          = 3,       // 信息流模版渲染
    XMIAdSlotAdTypeFeed_Custom   = 4,       // 信息流自渲染
    XMIAdSlotAdTypeFeed_Paster   = 5,       // 贴片自渲染
    XMIAdSlotAdTypeFeed_Draw     = 6,       // Draw模版渲染
    XMIAdSlotAdTypeFeed_DrawCustom = 7,     // Draw自渲染
    XMIAdSlotAdTypeReward        = 8,       // 激励视频
    XMIAdSlotAdTypeReward_Custom = 9,       // 激励视频自渲染
};

@interface XMIAdSlot : NSObject

/**
 广告尺寸，建议传 (屏幕宽，0)
 */
@property (nonatomic, assign) CGSize adSize;
/**
 广告数、建议最多3个
 */
@property (nonatomic, assign) NSInteger adCount;

// ------------ 以下字段为内部使用，不要修改 -----------------------------
/**
 广告位ID
 */
@property (nonatomic, copy) NSString *positionID;
/**
 广告类型
 */
@property (nonatomic, assign) XMIAdSlotAdType adType;
/**
 该广告是否为实时竞价
 */
@property (nonatomic, assign) BOOL slotRealBid;
/**
 该广告实时竞价相关数据
 */
@property (nonatomic, copy) NSString *slotAdm;

/**
 slot关联数据
 */
@property (nullable, nonatomic, strong) XMIAdRelatedData *relatedData;

@end

NS_ASSUME_NONNULL_END
