//
//  XMIBaseAd.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/6/30.
//

#import "XMIBaseAd.h"
#import "XMICommonUtils.h"
#import "XMIAdHelper.h"
@interface XMIBaseAd ()

@end

@implementation XMIBaseAd

- (instancetype)initWithSlot:(XMIAdSlot *)adSlot {
    self = [super init];
    if (self) {
        [XMIAdHelper initSdkWithAdType:adSlot.relatedData.adtype];
        [self baseInit:adSlot];
    }
    return self;
}

- (void)baseInit:(XMIAdSlot *)adSlot {
    self.adSlot = adSlot;
    self.oid = [XMICommonUtils currentTimestamp];
}

- (void)loadAdData {
    
}

- (void)invalidate {
    self.oid = 0;
}

- (BOOL)isValid {
    return self.oid != 0;
}

@end
