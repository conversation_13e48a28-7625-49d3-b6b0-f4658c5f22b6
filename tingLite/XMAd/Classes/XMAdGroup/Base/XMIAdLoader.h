//
//  XMIAdLoader.h
//  XMAd
//
//  Created by xiaodong2.zhang on 2024/8/6.
//

#import <Foundation/Foundation.h>
#import "XMIAdRelatedData.h"
#import "XMIFeedAdModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface XMIAdLoader : NSObject

+ (void)fillAdItems:(NSArray<XMIAdRelatedData *> *)adItems 
            timeout:(CGFloat)timeout
 rootViewController:(UIViewController *)rootViewController
         completion:(void(^)(NSArray<XMIFeedAdModel *> *results))completion;

@end

NS_ASSUME_NONNULL_END
