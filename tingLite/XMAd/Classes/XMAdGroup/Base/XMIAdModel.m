//
//  XMIAdModel.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/12/23.
//

#import "XMIAdModel.h"

@implementation XMIAdModel
{
    CGFloat _adHeight;
}
@dynamic adHeight;
- (CGFloat)adHeight
{
    if (!self.relatedData) {
        return 0.01;
    }
//    if (self.relatedData.spareTireData) {
//        return self.relatedData.spareTireData.adHeight;
//    }
    return self.relatedData.adHeight;
}

- (void)setAdHeight:(CGFloat)adHeight
{
    _adHeight = adHeight;
    _relatedData.adHeight = adHeight;
}

- (NSInteger)adtype
{
    return self.relatedData.adtype;
}


@end
