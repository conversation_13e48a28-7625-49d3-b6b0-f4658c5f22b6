//
//  XMIAdModel.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/12/23.
//

#import <Foundation/Foundation.h>
#import "XMIAdRelatedData.h"
NS_ASSUME_NONNULL_BEGIN

@interface XMIAdModel : NSObject

/// 高度
@property (nonatomic, assign) CGFloat adHeight;

/// 宽度
@property (nonatomic, assign) CGFloat adWidth;
/**
 广告数据源，和dspId一致。例如 0：喜马拉雅后台物料
 */
@property (nonatomic, assign) NSInteger adtype;

@property(nonatomic, strong, nullable)XMIAdRelatedData *relatedData;

/// 如果是三方是否找到备胎广告
@property (nonatomic, assign) BOOL isFindSpareAd;


@property (nonatomic, strong) NSIndexPath *indexPath;

@end

NS_ASSUME_NONNULL_END
