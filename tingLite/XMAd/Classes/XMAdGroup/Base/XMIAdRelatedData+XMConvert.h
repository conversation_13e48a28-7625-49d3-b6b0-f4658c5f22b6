//
//  XMIAdRelatedData+XMConvert.h
//  XMAd
//
//  Created by cuiyuanzhe on 2022/4/26.
//

#import <XMAd/XMIAdRelatedData.h>

NS_ASSUME_NONNULL_BEGIN

@interface XMIAdRelatedData (XMConvert)

//大促一拖2在用，从mix接口拿回的json解析成广告
//oldType:数据格式不是adx/ad的是以前的广告的
+ (NSArray *)relatedDatasWithOldTypeRawData:(NSDictionary *)oldTypeRawData positionId:(long long)positionId positionName:(NSString *)positionName isCache:(BOOL)isCache;

- (NSInteger)finalShowStyle;

@end

NS_ASSUME_NONNULL_END
