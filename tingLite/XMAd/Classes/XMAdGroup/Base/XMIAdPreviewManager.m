//
//  XMIAdPreviewManager.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/12/27.
//

#import "XMIAdPreviewManager.h"
#import "XMIAdRequest.h"
#import "XMIAdHeader.h"
#import <XMCategories/NSObject+XMCommon.h>
#import "XMIAdRespData.h"
#import "XMICommonUtils.h"
#import "XMIAdManager.h"
static XMIAdPreviewItem *previewItem = nil;
static uint32_t gl_ievent = 0;

@implementation XMIAdPreviewItem

@end

@implementation XMIAdPreviewManager

+ (void)requestPreviewDataFromUrlString:(NSString *)urlString andComplete:(void(^ __nullable)(XMIAdPreviewItem *previewItem, NSDictionary *extraInfo))complete {
    
    NSString *version       = [XMICommonUtils appVersion];
    NSString *scale         = [NSString stringWithFormat:@"%d",(int)[UIScreen mainScreen].scale];
    NSString *netType       = [[XMIAdHeader sharedInstance] netType];
    NSInteger find_native_version = [[XMIAdHeader sharedInstance] findNativeVersion];
    NSString *requestString = [NSString stringWithFormat:@"%@&device=iphone&network=%@&scale=%@&version=%@&materialPreview=1&find_native_version=%ld&forSdk=1", urlString, netType, scale, version, find_native_version];
    uint32_t event = aktEventCode();
    gl_ievent = event;
    [XMIAdRequest getWithUrl:requestString andParam:nil completionHandler:^(XMIAdRespAdData * _Nullable respAdData, NSError * _Nullable error) {
        if (gl_ievent != event) {
            return;
        }
        if (error) {
            previewItem = nil;
            if (complete) {
                complete(nil, nil);
            }
            return;
        }
        XMIAdSlotAdBean *adBean = respAdData.slotAds.firstObject;
        if (adBean) {
            [adBean.ads enumerateObjectsUsingBlock:^(XMIAdBean * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                obj.isAdPreview = YES;
            }];
            long long positionId = adBean.positionId;
            previewItem = [[XMIAdPreviewItem alloc]init];
            previewItem.positionId = positionId;
            previewItem.respAdData = respAdData;
            [previewItem aktTagObject:@(event) forKey:@"event"];
            NSDictionary *extra = [self createExtraWithAdBaen:adBean];
            if (complete) {
                complete(previewItem, extra);
            }
            [self showToastInfoWhenPreviewAD];
        }
        else {
            previewItem = nil;
            if (complete) {
                complete(nil, nil);
            }
        }
    }];
}

+ (NSDictionary *)createExtraWithAdBaen:(XMIAdSlotAdBean *)adBean {
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    if (adBean.positionId == 296) {
        XMIAdBean *bean = adBean.ads.firstObject;
        [dic setValue:bean.trackPreviewUrl forKey:@"trackPreviewUrl"];
    } else if (adBean.positionId == 299) {
        XMIAdBean *bean = adBean.ads.firstObject;
        [dic setValue:bean.trackPreviewUrl forKey:@"trackPreviewUrl"];
    }
    return [dic copy];
}

+ (void)showToastInfoWhenPreviewAD {
    long long positionId = previewItem.positionId;
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(showToastWithInfo:)]) {
        if (positionId == 14) {
            [manager.delegate showToastWithInfo:@"打开播放页预览此广告，该广告会在贴片隐藏时展示（如果该声音有商业化小黄条，将不展示下挂广告）"];
        } else if (positionId == 296) {
            [manager.delegate showToastWithInfo:@"即将跳转广告预览页-shownotes"];
        } else if (positionId == 299) {
            [manager.delegate showToastWithInfo:@"即将跳转广告预览页-shownotes"];
        }
    }
}

+ (BOOL)isAdPreviewUrlStr:(NSString *)urlStr {
    NSURL *url = urlStr.aktUrlValue;
    if (url
        && [url.path containsString:@"ting/material/preview"]) {
        return YES;
    }
    
    return NO;
}

+ (XMIAdPreviewItem *)previewItemWithPosition:(NSInteger)positionId {
    if (previewItem
        && previewItem.positionId == positionId) {
        return previewItem;
    }
    
    return nil;
}

+ (XMIAdRespAdData *)getPreviewResponseWith:(NSInteger)positionId {
    XMIAdPreviewItem *item = nil;
    XMIAdRespAdData *respAdData = nil;
    item = [XMIAdPreviewManager previewItemWithPosition:positionId];
    respAdData = item.respAdData;
    if (item) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            uint32_t event = [[item aktTagObjectWithKey:@"event"] unsignedIntValue];
            if (event == gl_ievent) {
                [XMIAdPreviewManager cleanPreviewData];
            }
        });
    }
    return respAdData;
}

+ (void)cleanPreviewData {
    previewItem = nil;
}

@end
