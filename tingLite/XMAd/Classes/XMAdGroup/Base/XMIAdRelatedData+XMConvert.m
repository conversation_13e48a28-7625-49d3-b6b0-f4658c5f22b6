//
//  XMIAdRelatedData+XMConvert.m
//  XMAd
//
//  Created by cuiyuanzhe on 2022/4/26.
//

#import "XMIAdRelatedData+XMConvert.h"
#import "XMIAdDataCenter.h"
#import "XMIAdRespData.h"
#import "NSObject+XMIModel.h"
#import "XMIAdReporter+AD.h"
#import "XMIAdConverter.h"

@implementation XMIAdRelatedData (XMConvert)

+ (NSArray *)relatedDatasWithOldTypeRawData:(NSDictionary *)oldTypeRawData positionId:(long long)positionId positionName:(NSString *)positionName isCache:(BOOL)isCache
{
    NSMutableArray *relatedDatas = [NSMutableArray array];
    long long responseId = [[oldTypeRawData objectForKey:@"responseId"] longLongValue];
    NSArray *adDatas = [oldTypeRawData objectForKey:@"data"];
    if ([adDatas isKindOfClass:[NSArray class]]) {
        for (NSDictionary *adDic in adDatas) {
            if (![adDic isKindOfClass:[NSDictionary class]]) {
                continue;
            }
            XMIAdBean *bean = [XMIAdBean xmi_modelWithJSON:adDic];
            XMIAdRelatedData *relatedData = [XMIAdDataCenter relatedDataFromAdBean:bean];
            if (relatedData) {
                relatedData.responseId = responseId;
                relatedData.positionId = positionId;
                relatedData.positionName = positionName;
                relatedData.slotId = [XMIAdDataCenter getSlotIdByPositionName:positionName];
                [relatedDatas addObject:relatedData];
            }
        }
    }
    if (!isCache) {
        [XMIAdReporter exposeReportValidAds:relatedDatas];
    }
    return [relatedDatas copy];
}

- (NSInteger)finalShowStyle
{
    return [XMIAdConverter showTypeFromRelatedData:self];
}

@end
