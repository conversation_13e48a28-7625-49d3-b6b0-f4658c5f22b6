//
//  XMIAdRelatedData.h
//  XMAd
//  slot关联数据，传递上报需要的相关字段
//  喜马广告时，可当作广告数据用
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/27.
//

#import <Foundation/Foundation.h>
#import "XMIAdDefines.h"
#import <XMAd/XMIAdDSPSDKReportProtocol.h>
NS_ASSUME_NONNULL_BEGIN

//主播直播状态
typedef NS_ENUM(NSUInteger, XMIAdRelatedLoadingStatus) {
    XMIAdRelatedLoadingStatusNormal = 0,   //!< 正常加载
    XMIAdRelatedLoadingStatusUseSpareTireData = 1,   //!< 三方使用备胎图
    XMIAdRelatedLoadingStatusHasNoSpareTireData = 2,     //!< 三方找不到备胎图了
    XMIAdRelatedLoadingStatusHasSureSizeRadio = 3,      //!< 已经确定好最终的尺寸
    XMIAdRelatedLoadingStatusLoadCompleted = 4,      //!< 正在加载完成
};

@class YYTextLayout,XMIAdBeanComposite;
@interface XMIAdRelatedData : NSObject

/**
 广告标题
 */
@property (nullable, nonatomic, copy) NSString *name;

/**
 广告富文本标题
 */
@property (nullable, nonatomic, strong) YYTextLayout *titleTextLayout;

/**
 广告副标题(接口返回description)
 */
@property (nullable, nonatomic, copy) NSString *adDescription;
/**
 广告logo
*/
@property (nullable, nonatomic, copy) NSString *adMark;
@property (nullable, nonatomic, copy) NSString *darkAdMark;

/**
 广告logo式样
*/
@property (nonatomic, assign) NSInteger adMarkStyle;
/**
 按钮文案
 */
@property (nullable, nonatomic, copy) NSString *buttonText;
/**
 [重要]图片类素材的图片地址，或是视频类素材的备选图地址（在视频下载失败时展示该图片）
 */
@property (nullable, nonatomic, copy) NSString *cover;
/**
 [重要]视频类素材的视频地址
 */
@property (nullable, nonatomic, copy) NSString *videoUrl;
/**
 [重要]视频类素材的视频地址
 */
@property (nullable, nonatomic, copy) NSString *videoCover;
/**
 广告位样式ID 46:图文样式 47:纯音贴
 */
//XMIAdShowStyle
@property (nonatomic, assign) NSInteger showstyle;
/**
 点击动作 1:可以点击 2:不可点击
 */
@property (nonatomic, assign) NSInteger clickType;
/**
 落地页地址
 */
@property (nullable, nonatomic, copy) NSString *realLink;
/**
 跳转类型：1、跳转落地页；2、下载APP
 */
@property (nonatomic, assign) NSInteger linkType;
/**
 广告跳转打开类型 0:应用内打开 1:第三方浏览器打开 2:喜马拉雅活动 3:拨打电话 4;游戏中心
 */
@property (nonatomic, assign) NSInteger openlinkType;
/**
 deeplink或UniversalLink
 */
@property (nullable, nonatomic, copy) NSString *dpRealLink;
/**
 计划ID
 */
@property (nonatomic, assign)           long long planId;
/**
 是否开启视频半拼接webview
 */
@property (nonatomic, assign) BOOL enableContinuePlay;
/**
 是否开启视频半拼接appstore
 */
@property (nonatomic, assign) BOOL enableVideoJoinAppStore;
/**
 待下载app名
 */
@property (nonatomic, copy) NSString *downloadAppName;
/**
 序号
 */
@property (nonatomic, assign) NSUInteger adno;
/**
 子广告序号
 */
@property (nonatomic, assign) NSUInteger frames_num;

/*
 广告标签
 */
@property (nullable, nonatomic, copy) NSArray *tags;
/**
 客户端竞价： 加密底价过滤
 */
@property (nullable, nonatomic, copy)   NSString *bidMinPriceEncrypt;
@property (nonatomic, assign)           float bidMinPrice;

/**
 客户端竞价：加密价格
 */
@property (nullable, nonatomic, copy)   NSString *priceEncrypt;
/**
 客户端竞价：客户端sdk竞价物料
 */
@property (nonatomic, assign)           BOOL isMobileRtb;
/**
 客户端竞价：价格
 */
@property (nonatomic, assign)           float price;
/**
 客户端竞价：优先级
 */
@property (nonatomic, assign)           NSInteger rankLevel;
/**
 竞价是否成功
 */
@property (nonatomic, assign)           BOOL rtbSuccess;
/*
 是否需要自渲染
 https://thoughts.ximalaya.com/workspaces/5ceff641be825bee8c1e80c0/docs/5f8d5be9f4c0000001fde4be
 */
@property (nonatomic, assign) BOOL needRender;

@property (nonatomic, assign) CGFloat renderWidth;

@property (nonatomic, assign) CGFloat renderHeight;

/**
 自动关闭时间 x秒后自动关闭 单位ms
 */
@property (nonatomic, assign) NSInteger adShowTime;

/**
 自动重新请求间隔  单位ms
 */
@property (nonatomic, assign) NSInteger adIntervalTime;

/**
 点击区域 1:支持点击任意区域响应；2:仅点击按钮区域响应
 */
@property (nonatomic, assign) NSInteger clickableAreaType;

/**
 是否显示主播标签
 */
@property (nonatomic, assign) BOOL enableAnchorRec;

//---------------------- 以下字段用于上报 --------------------------

/**
 服务端唯一id
 */
@property (nonatomic, assign) long long responseId;
/**
 代码位id
 */
@property (nonatomic, assign) long long slotId;
/**
 广告位名称
 */
@property (nonatomic, copy) NSString *positionName;
/**
 物料ID
 */
@property (nonatomic, assign) long long adid;
/**
 广告唯一ID
 */
@property (nonatomic, assign) long long adUniqId;
/**
 广告数据源，和dspId一致。例如 0：喜马拉雅后台物料
 */
@property (nonatomic, assign) NSInteger adtype;
/**
 是否是DSP
 */
@property (nonatomic, assign) BOOL dsp;
/**
 SDK接入DSP的广告位ID
 */
@property (nullable, nonatomic, copy) NSString *dspPositionId;
/**
 该广告是否为实时竞价
 */
@property (nonatomic, assign) BOOL slotRealBid;
/**
 该广告实时竞价相关数据
 */
@property (nonatomic, copy) NSString *slotAdm;
/**
 广告位置的ID
 */
@property (nonatomic, assign) long long positionId;
/**
 广告点击时需要数据上报
 */
@property (nullable, nonatomic, strong) NSArray<NSString *> *thirdClickStatUrls;
/**
 ab透传字段
 */
@property (nullable, nonatomic, copy) NSString *abPassThroughParams;
/**
 多图样式
 */
@property (nullable, nonatomic, strong) NSArray<XMIAdBeanComposite *> *composite;

/**
 混投
 */
@property (nullable, nonatomic, strong) NSArray<XMIAdRelatedData *> *compositeAds;

@property (nullable, nonatomic, strong) NSMutableArray<XMIAdRelatedData *> *currentCompositeAds;

/**
 广告展示时需要数据上报(与thirdStatUrl)全部触发
 */
@property (nullable, nonatomic, strong) NSArray<NSString *> *thirdShowStatUrls;
/**
 广告展示时需要数据上报(与thirdShowStatUrls)全部触发
 */
@property (nullable, nonatomic, copy) NSString *thirdStatUrl;
/**
 是否需要上报真实曝光
 */
@property (nonatomic, assign) BOOL isTrueExposure;
/**
 广告展示时是否需要携带Token
 */
@property (nonatomic, assign) BOOL showTokenEnable;
/**
 广告展示时携带Token
 */
@property (nullable, nonatomic, strong) NSArray<NSString *> *showTokens;
/**
 广告点击时是否需要携带Token
 */
@property (nonatomic, assign) BOOL clickTokenEnable;
/**
 广告点击时携带Token
 */
@property (nullable, nonatomic, strong) NSArray<NSString *> *clickTokens;
/**
 点击统计地址
 */
@property (nullable, nonatomic, copy) NSString *link;
/**
 广告上报透传信息
 */
@property (nullable, nonatomic, copy) NSString *commonReportMap;
/**
 分桶信息
 */
@property (nullable, nonatomic, copy)   NSString *bucketIds;
/**
 CTR算法追踪字段
 */
@property (nullable, nonatomic, copy)   NSString *recSrc;
/**
 CTR算法追踪字段
 */
@property (nullable, nonatomic, copy)   NSString *recTrack;
/**
 cpm竞价时的出价”经过加密处理后的密文
 */
@property (nullable, nonatomic, copy)   NSString *adpr;

@property (nonatomic, strong) NSString *wxMiniProgramId;

/// 由各业务线补充
@property (nullable, nonatomic, copy) NSDictionary *businessExtraInfo;

// 首页混排-猜你喜欢
@property (nullable, nonatomic, copy) NSDictionary *trackInfoMap;

/// 行动按钮文案
@property (nullable, nonatomic, copy) NSString *clickTitle;

//广告主类型
@property (copy, nonatomic) NSString *adUserType;

// 内容推广一期 跳转声音字段
@property (nonatomic, assign) long long promoteTrackId;
@property (nonatomic, assign) NSInteger clickJumpType;

// 内容推广二期，广告跳转声音Id
@property (nonatomic, assign) long long jumpTrackId;
// 多长时间自动弹起广告弹窗
@property (nonatomic, assign) float autoJumpTime;
// 内容推广，播放页底部视图标题
@property (nonatomic, copy) NSString *contentAdTitle;

// 播音类广告弹窗字段
//弹窗提示样式,0-不出弹窗提示，1-弹窗提示样式1，2-弹窗提示样式2
@property (nonatomic, copy) NSString *popReminderStyle;
@property (nonatomic, copy) NSString *popReminderText;
// 声音流彩蛋弹窗样式
@property (nonatomic, copy) NSString *soundAggType;


//////////////自定义属性////////////
/// 映射成对应的model
@property (nullable, nonatomic, strong) id businessExtraModel;

// 首页混排-猜你喜欢
@property (nullable, nonatomic, strong) id trackInfoMapModel;

/// 资源对应的高宽比 客户端本地定义使用
@property (nonatomic, assign) CGFloat sourceRadio;
/// 高度对应的宽高比 此处只是对应的是尺寸的宽高比
@property (nonatomic, assign) CGFloat sizeRadio;
/// 竖版视频的比例是高宽比是大于1.4 如果发现高宽比的比例小于了1.4 则任务是横版视频
@property (nonatomic, assign) BOOL isErrorVideoHorizinal;

@property (nonatomic, assign) BOOL isDidRender;
/// 是否需要刷新
@property (nonatomic, assign) BOOL isNeedRefresh;
/// 是否需要高度以及标题等数据
@property (nonatomic, assign) BOOL isNeedReloadData;

/**
 事件处理
 */
@property (nonatomic, weak) UIViewController *rootViewController;
@property (nonatomic, weak) id delegate;
/**
 adx请求时间，用于统计上报
 */
@property (nonatomic, assign) long long paddingTimeMs;
@property (nonatomic, assign) CGFloat adWidth;
@property (nonatomic, assign) CGFloat adHeight;
@property (nonatomic, strong) NSIndexPath *indexPath;
@property (nonatomic, copy) NSString *reuseIdentifier;
@property (nonatomic, strong) id<XMIAdDSPSDKReportProtocol> originData;
/// 预览广告点击是否上报
@property (nonatomic, assign) BOOL clickReportFlag;

/// 备胎资源
//@property (nonatomic, strong) XMIAdRelatedData *spareTireData;

/// 是否曝光
@property (nonatomic, assign) BOOL isExposed;

/// 是否曝光(新曝光 自定义曝光比例)
@property (nonatomic, assign) BOOL isNewExposed;
@property (nonatomic, assign) BOOL isNewExposed50;

/// 当前的界面加载状态
@property (nonatomic, assign) XMIAdRelatedLoadingStatus loadingStatus;

/**
 是否已加载
 */
@property (nonatomic, assign) BOOL isLoaded;

/**
 是否已被使用
 */
@property (nonatomic, assign) BOOL used;

/// 是否需要执行动画
@property (nonatomic, assign) BOOL isNeedAnimated;
/// 是否需要执行动画
@property (nonatomic, assign) BOOL isAnimatedCompleted;
///是否是预览广告
@property (nonatomic, assign) BOOL isAdPreview;

/**
 是否展示来源（仅dsp可能为非0，1：文字，2：logo）
 */
@property (nonatomic, assign) XMIInScreenSource inScreenSource;
/**
 水印文字内容或logo地址
 */
@property (nullable, nonatomic, copy)   NSString *materialProvideSource;


/// 目前用于混排-广告样式区分字段
@property (nonatomic, assign) NSInteger bizType;
// 混排猜你喜欢内容类型
@property (nonatomic, copy) NSString *mixGuessYouLikePromoteType;
/**
 猜你喜欢隐藏底部分割线
 */
@property (nonatomic, assign) BOOL hideDivideLine;

/**
 Social首页header：中插大图
 */
@property (nonatomic, assign) BOOL isHeaderModule;

/**
 是否是猜你喜欢广告
 */
@property (nonatomic, assign) BOOL isGuessYouLikeAd;

//视频可关闭时间
@property (assign, nonatomic) NSInteger videoDuration;

// 视频总时长
@property (assign, nonatomic) NSInteger unlockTime;

//过期时间 毫秒
@property (nonatomic, assign) long long adCacheExpireTime;

@property (nullable, nonatomic, copy) NSString *iconUrl;

@property (nullable, nonatomic, strong) NSNumber *playPageRevision;

@property (nonatomic, copy) NSDictionary *extraReportParams;
//广告主

@property (nullable, nonatomic, copy) NSString *providerAvatar;

@property (nullable, nonatomic, copy) NSString *providerName;

// 下挂浮层广告gif
@property (nullable, nonatomic, copy) NSString *dynamicImage;

@property (nullable, nonatomic, copy) NSString *dynamicCover;

@property (nullable, nonatomic, copy) NSString *hightingDynamicPicUrl;

@property (nullable, nonatomic, copy) NSString *backupCover;

@property (nonatomic, assign) double videoDurationTime;

@property (nonatomic, assign) BOOL videoAutoJump;//激励视频完播自动跳转落地页

/**
 最终已播放时长
 */
@property (nonatomic, assign) CGFloat finishSeconds;

//是否支持dsp摇一摇
@property (nonatomic, assign) BOOL enableShake;

@property (nonatomic, copy) NSString *adTextMark;

@property (nonatomic, copy) NSString *ubtReportMap;

@property (nonatomic, copy) NSString *clickRequestMethod;

@property (nonatomic, copy) NSString *clickPostMap;

// 是否需要一键留资
@property (nonatomic, assign) BOOL oneClickLeaveCash;

// webview缓存
@property (nonatomic, assign) NSInteger preLoadH5LandingPage;

// 落地页视频地址新增物料字段
@property (nonatomic, copy) NSString *landVideoUrl;
// 落地页视频logo
@property (nonatomic, copy) NSString *downloadAppLogo;

// 落地页自动拉起时间新增字段 ms, 默认5000
@property (nonatomic, assign) double autoShowWebTime;

@property (nonatomic, assign) double volume;

@property (nonatomic, assign) NSInteger internalHomeGuessIndex;

@property (nonatomic, assign) BOOL internalCirculationFlag;
//真实停留时间
@property (nonatomic, assign) NSInteger actualStayTime;
//文本提示停留时间
@property (nonatomic, assign) NSInteger tipStayTime;

@property (nonatomic, assign) float dpRetrySecond;

@property (nonatomic, assign) BOOL isCenterBigFeedAd;

// 用于shownotes预览
@property (nonatomic, copy) NSString *trackPreviewUrl;


// 9.2.87新增激励相关上报
@property (nonatomic, copy) NSString *sourceName;
@property (nonatomic, copy) NSString *startSource;

@property (nonatomic, assign) XMIUnlockAdItemAgainPopupType againPopupType;

@property (nonatomic, assign) NSInteger againDuration;

//9.2.93唤端套娃

@property (nonatomic, assign) BOOL homePicTitleDown;

//点击取重标记
@property (nonatomic, assign) BOOL needDedupClick;
@property (nonatomic, assign) BOOL inClickFrequencyControl;

// https://alidocs.dingtalk.com/i/nodes/YMyQA2dXW793xlpjh9GQozPRJzlwrZgb?corpId=ding51f195092fd77474
@property (nonatomic, assign) NSInteger businessReportClickTime;
@property (nonatomic, assign) BOOL businessReportedClickTime;// 已上报

/// 获取唯一表示
- (NSString *)getIdentifier;

- (BOOL)isExpired;

- (BOOL)videoUrlVaild;

- (NSInteger)adItemSDKType;

@end

@interface XMIAdRelatedData (XMICloseAdPadding)

- (UIEdgeInsets)closeAreaPaddingWithDefaultPadding:(UIEdgeInsets)defautPadding;

- (UIEdgeInsets)closeAreaPaddingEdge;

@end

typedef NS_ENUM(NSInteger, XMIAdClickType) {
    XMIAdClickTypeNone = 2,     // 不跳转
    XMIAdClickTypeWeb = 1,      // webview
    XMIAdClickTypeBrowser = 3,  // 浏览器
    XMIAdClickTypeCall = 16,    // 打电话
    XMIAdClickTypeWeixin = 17,  // 微信小程序
    XMIAdClickTypeStore = 18    // 应用内appstore
};

typedef NS_ENUM(NSInteger, XMIAdOpenlinkType) {
    XMIAdOpenlinkTypeWeb = 0,       // webview
    XMIAdOpenlinkTypeBrowser = 1,   // 浏览器
    XMIAdOpenlinkTypeCall = 3,      // 打电话
    XMIAdOpenlinkTypeGame = 4,       // 游戏中心
};

NS_ASSUME_NONNULL_END
