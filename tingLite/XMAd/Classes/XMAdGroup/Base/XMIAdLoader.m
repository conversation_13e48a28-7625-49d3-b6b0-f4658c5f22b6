//
//  XMIAdLoader.m
//  XMAd
//
//  Created by xia<PERSON><PERSON>2.zhang on 2024/8/6.
//

#import "XMIAdLoader.h"
#import "XMIAdNativeLogger.h"
#import "XMIAdReporter.h"
#import "XMIAdManager.h"
#import <XMConfigCenter/XMConfigCenter.h>
#import <XMCategories/NSArray+XMCommon.h>

#define XMIAdLoaderLogInfo(fmt, ... ) XMILogNativeInfo(@"AD_LOG_NATIVE_RTB", fmt, ##__VA_ARGS__)

@interface XMIAdLoader ()<XMIFeedAdModelDelegate>

@property (strong, nonatomic) NSMutableArray<XMIFeedAdModel *> *adModelQueue;

@property (strong, nonatomic) NSMutableArray<XMIFeedAdModel *> *adModelResultList;

@property (strong, nonatomic) NSMutableArray<XMIAdRelatedData *> *adItems;

@property (nonatomic, weak) UIViewController *rootViewController;

@property (copy, nonatomic) void(^completion)(NSArray<XMIFeedAdModel *> *results);

@property (assign, nonatomic) CGFloat timeout;

@property (assign, nonatomic) BOOL finish;

@property (assign, nonatomic) NSInteger rtbAdWaitCount;

@property (assign, nonatomic) NSInteger adTimeoutCount;

@end

@implementation XMIAdLoader

- (void)dealloc {
    XMIAdLoaderLogInfo(@"");
}

+ (void)fillAdItems:(NSArray<XMIAdRelatedData *> *)adItems
            timeout:(CGFloat)timeout
 rootViewController:(UIViewController *)rootViewController
         completion:(void(^)(NSArray<XMIFeedAdModel *> *results))completion {
    if (!adItems || !completion) {
        if (completion) {
            completion(nil);
        }
        return;
    }
    XMIAdLoader *loader = [XMIAdLoader new];
    loader.completion = completion;
    loader.adItems = [adItems mutableCopy];
    loader.timeout = timeout;
    loader.rootViewController = rootViewController;
    [loader startFill];
}

- (instancetype)init {
    self = [super init];
    if (self != nil) {
        self.adModelQueue = [NSMutableArray array];
        self.adModelResultList = [NSMutableArray array];
    }
    return self;
}

- (void)startFill {
    XMIAdLoaderLogInfo(@"当前dsp列表中- 包含  客户端竞价广告");
    if ([self.adItems.firstObject isExpired]) {
        if (self.completion) {
            self.completion(nil);
        }
        return;
    }
    [self.adItems enumerateObjectsUsingBlock:^(XMIAdRelatedData *relatedData, NSUInteger idx, BOOL * _Nonnull stop) {
        XMIFeedAdModel *model = [XMIFeedAdModel feedAdModelWithRelatedData:relatedData];
        relatedData.loadingStatus = XMIAdRelatedLoadingStatusLoadCompleted;
        model.rootViewController = self.rootViewController;
        model.delegate = self;
        [self.adModelQueue addObject:model];
        if (model.loadingStatus == XMIFeedAdModelLoadingStatusLoadSuccess) {
            [self.adModelResultList addObject:model];
        }
        if (relatedData.isMobileRtb) {
            self.rtbAdWaitCount ++;
        }
        [model loadAdData];
    }];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(self.timeout * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self finishWithTimeout:YES];
    });
}

- (void)addAdModelToResultArray:(XMIFeedAdModel *)adModel {
    if (adModel.loadingStatus == XMIFeedAdModelLoadingStatusLoadSuccess) {
        if (self.finish) {
            XMIAdLoaderLogInfo(@"dsp广告请求超时 【%lld(%ld, %@)】", adModel.relatedData.adid, adModel.relatedData.adtype, adModel.relatedData.dspPositionId);
            adModel.loadingStatus = XMIFeedAdModelLoadingStatusTimeout;
            self.adTimeoutCount ++;
            [self.adModelResultList addObject:adModel];
            return;
        }
        XMIAdLoaderLogInfo(@"dsp广告请求完成，加到比价列表 【%lld(%ld, %@)】", adModel.relatedData.adid, adModel.relatedData.adtype, adModel.relatedData.dspPositionId);
    } else {
        XMIAdLoaderLogInfo(@"dsp广告请求失败，丢弃 【%lld(%ld, %@)】, status = %@", adModel.relatedData.adid, adModel.relatedData.adtype, adModel.relatedData.dspPositionId, adModel.loadingStatusString);
    }
    if (adModel.relatedData.isMobileRtb) {
        self.rtbAdWaitCount --;
    }
    
    [self.adModelResultList addObject:adModel];
    // 所有的SDK 客户端rtb都已经返回才能进入最终比价环节
    if (self.rtbAdWaitCount != 0) {
        XMIAdLoaderLogInfo(@"还剩余 【%ld】客户端rtb广告，继续等待",self.rtbAdWaitCount);
        return;
    }
    
    for (XMIFeedAdModel *currentItem in self.adModelQueue) {
        if (currentItem.relatedData.isMobileRtb) {
            continue;
        }
        if (currentItem.loadingStatus == XMIFeedAdModelLoadingStatusLoadSuccess) {
            ////adx第一位固价已返回,可以进入最终比价环节
            break;
        } else if (currentItem.loadingStatus == XMIFeedAdModelLoadingStatusLoadFailed)  {
            ////adx第一位固价已确认失败，查看下一位状态
            continue;
        } else if (currentItem.loadingStatus == XMIFeedAdModelLoadingStatusLoading)  {
            XMIAdLoaderLogInfo(@"还剩余【%lld(%ld,%@)】dsp固价广告加载中，继续等待",currentItem.relatedData.adid, currentItem.relatedData.adtype, currentItem.relatedData.dspPositionId);
            return;
        }
    }

    [self finishWithTimeout:NO];
}

- (void)finishWithTimeout:(BOOL)timeout {
    if (self.finish) {
        return;
    }
    self.finish = YES;
    [self.adModelResultList sortUsingComparator:^NSComparisonResult(XMIFeedAdModel *adModel1, XMIFeedAdModel* adModel2) {
        if (adModel1.relatedData.rankLevel < adModel2.relatedData.rankLevel) {
            return NSOrderedDescending;
        } else if (adModel1.relatedData.rankLevel == adModel2.relatedData.rankLevel) {
            if (adModel1.relatedData.price < adModel2.relatedData.price) {
                return NSOrderedDescending;
            }
        }
        return NSOrderedSame;
    }];
    
    XMIAdLoaderLogInfo(@"开始遍历物料");
    for (XMIFeedAdModel *printObject in self.adModelResultList) {
        XMIAdLoaderLogInfo(@"adItem【%lld(%ld, %@)】, price = %.4f, rankLevel = %ld, status = %@ ", printObject.relatedData.adid, printObject.relatedData.adtype, printObject.relatedData.dspPositionId, printObject.relatedData.price, printObject.relatedData.rankLevel, printObject.loadingStatusString);
    }
    XMIAdLoaderLogInfo(@"结束遍历物料");

    XMIFeedAdModel *willShowItem = nil;
    for (XMIFeedAdModel *tempAdModel in self.adModelResultList) {
        if (willShowItem == nil && tempAdModel.loadingStatus == XMIFeedAdModelLoadingStatusLoadSuccess) {
            if (tempAdModel.relatedData.bidMinPrice > 0 && tempAdModel.relatedData.price < tempAdModel.relatedData.bidMinPrice) {
                //底价过滤
                XMIAdLoaderLogInfo(@"dsp广告底价未满足，丢弃 【%lld(%ld, %@)】", tempAdModel.relatedData.adid, tempAdModel.relatedData.adtype, tempAdModel.relatedData.dspPositionId);
            } else {
                //命中
                willShowItem = tempAdModel;
                break;
            }
        }
    }
    
    XMIAdLoaderLogInfo(@"-------  dsp广告全部请求完成 比价------- 是否超时:%@ 竞胜物料:【%lld(%ld, %@) price = %.4f】",  @(timeout), willShowItem.relatedData.adid, willShowItem.relatedData.adtype, willShowItem.relatedData.dspPositionId,willShowItem.relatedData.price);
    if (willShowItem) {
        [self reportDSPAdClientBiddingInfoWithItem:willShowItem allItems:self.adModelResultList];
    }
    willShowItem.relatedData.rtbSuccess = YES;
    [self adXlogReportWithMobileRtbAds:self.adModelResultList timeout:timeout logtype:@"mobileRtbAds"];
    if (self.completion) {
        self.completion(willShowItem ? @[willShowItem] : @[]);
        self.completion = nil;
    }
    
    if (timeout) {
        //客户端实时竞价（包含超时）上报
        NSInteger reportTime = [XMConfigCenter.sharedConfigCenter getIntValueWithGroup:@"ad" andItem:@"mobileRtbFullAdsTime" defaultValue:10];
        if (reportTime > self.timeout && self.adModelResultList.count != self.adModelQueue.count) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)((reportTime - self.timeout) * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self reportFullAds];
            });
        }
    }
}

- (void)reportDSPAdClientBiddingInfoWithItem:(XMIFeedAdModel *)willShowItem allItems:(NSArray *)allItems {
    NSMutableArray *lossItems = [NSMutableArray array];
    for (XMIFeedAdModel *adItem in allItems) {
        if (adItem != willShowItem) {
            [lossItems addObject:adItem];
        }
    }
    
    float ratio = 100;
    float winPrice = willShowItem.relatedData.price * ratio;
    NSNumber *winNum = [NSNumber numberWithFloat:winPrice];
    // 竞胜物料上报
    [willShowItem win:winNum];
    
    // 竞败物料上报
    for (XMIFeedAdModel *lossItem in lossItems) {
        [lossItem loss:winNum];
    }
}

- (void)reportFullAds {
    if (self.adTimeoutCount > 0) {
        [self adXlogReportWithMobileRtbAds:self.adModelResultList timeout:YES logtype:@"mobileRtbFullAds"];
    }
}

- (void)adXlogReportWithMobileRtbAds:(NSArray<XMIFeedAdModel *> *)adModelList timeout:(BOOL)timeout logtype:(NSString *)logtype {
    XMIFeedAdModel *firstItem = adModelList.firstObject;
    if (firstItem.relatedData.responseId >= 0) {
        NSMutableDictionary *dic = [NSMutableDictionary dictionary];
        dic[@"responseId"] = @(firstItem.relatedData.responseId);
        dic[@"positionId"] = @(firstItem.relatedData.positionId);
        dic[@"sdkVersion"] = [XMIAdManager sdkVersion];
        NSString *bundleReleaseVersion = [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleShortVersionString"];
        dic[@"version"] = bundleReleaseVersion ?: @"";
        
        NSMutableArray *mobileRtbList = [NSMutableArray array];
        
        for (XMIFeedAdModel *model in adModelList) {
            NSMutableDictionary *mobileRtbDic = [NSMutableDictionary dictionary];
            mobileRtbDic[@"adid"] = [NSString stringWithFormat:@"%lld", model.relatedData.adid];
            mobileRtbDic[@"price"] = model.relatedData.priceEncrypt;
            mobileRtbDic[@"adUniqId"] = [NSString stringWithFormat:@"%lld", model.relatedData.adUniqId];
            
            int dspResult = -1;
            if (model.relatedData.dsp) {
                if (model.loadingStatus == XMIFeedAdModelLoadingStatusLoadSuccess) {
                    dspResult = 1;
                } else if (model.loadingStatus == XMIFeedAdModelLoadingStatusLoadFailed) {
                    dspResult = 0;
                } else {
                    dspResult = 3;
                }
            }
            mobileRtbDic[@"dspResult"] = @(dspResult);
            
            mobileRtbDic[@"rtbResult"] = model.relatedData.rtbSuccess ? @1 : @0;
            mobileRtbDic[@"rtbPrice"] = model.relatedData.priceEncrypt;
            mobileRtbDic[@"adSource"] = [NSString stringWithFormat:@"%ld", model.relatedData.adtype];
            mobileRtbDic[@"slotId"] = model.relatedData.dspPositionId ?  : @"";
                        
            NSInteger sdkCallbackStatus = -10000;
            NSString *sdkCallbackMsg = model.relatedData.dsp ? @"" : @"xmly-该物料为adx物料";
            
            if (model.relatedData.dsp) {
                if (model.loadingStatus == XMIFeedAdModelLoadingStatusLoadSuccess) {
                    sdkCallbackStatus = 10000;
                    sdkCallbackMsg = [NSString stringWithFormat:@"xmly-SDK请求成功-%@", model.relatedData.rtbSuccess ? @"竞胜": @"竞败"];
                } else if (model.loadingStatus == XMIFeedAdModelLoadingStatusLoadFailed) {
                    sdkCallbackStatus = -1;
                    sdkCallbackMsg = @"请求失败";
                } else {
                    sdkCallbackStatus = -99999;
                    sdkCallbackMsg = @"xmly-sdk在规定时间内未返回";
                }
            }
            mobileRtbDic[@"timeCost"] = @(model.requestTimeCost);
            mobileRtbDic[@"sdkCallbackStatus"] = @(sdkCallbackStatus);
            mobileRtbDic[@"sdkCallbackMsg"] = sdkCallbackMsg;
            [mobileRtbList addObject:mobileRtbDic];
        }
        
        NSString *mobileRtbListStr = [mobileRtbList jsonString];
        dic[@"mobileRtbReportList"] = mobileRtbListStr ?: @"";
        [[XMIAdReporter sharedInstance] reportParams:dic withSubtype:logtype];
    }
}

#pragma mark - XMIFeedAdModelDelegate

- (void)feedAdModelDidLoadDataSuccess:(XMIFeedAdModel *)adModel timeout:(BOOL)isTimeout {
    if (![self.adModelQueue containsObject:adModel]) {
        return;
    }
    [self addAdModelToResultArray:adModel];
}

- (void)feedAdModel:(XMIFeedAdModel *)adModel didLoadDataFailWithError:(NSError *)error {
    if (![self.adModelQueue containsObject:adModel]) {
        return;
    }
    [self addAdModelToResultArray:adModel];
}

@end
