//
//  XMIAdDefines.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/13.
//

#ifndef XMIAdDefines_h
#define XMIAdDefines_h

extern NSString * const XMIAdProviderXM;    // 喜马
extern NSString * const XMIAdProviderBU;    // 穿山甲
extern NSString * const XMIAdProviderGDT;   // 广点通
extern NSString * const XMIAdProviderJAD;   // 京东
extern NSString * const XMIAdProviderBAIDU; // 百度

extern NSString * const XMIAdLocalSDKVersion; //sdk本地版本，若有修复会自增

typedef NS_ENUM(NSInteger, XMIAdEnvironment) {
    XMIAdEnvironmentDefault = 0, // 默认生产环境
    XMIAdEnvironmentTest = 1,    // 测试环境
    XMIAdEnvironmentUAT = 2      // UAT环境
};

typedef NS_ENUM(NSInteger, XMIAdSource) {
    XMIAdSourceGDT = 1,
    XMIAdSourceBU = 2,
    XMIAdSourceXM = 3,
    XMIAdSourceJAD = 4,
    XMIAdSourceBAIDU = 5
};

/**
 后台返回广告类型
 */
typedef NS_ENUM(NSInteger, XMIAdType) {
    XMIAdTypeXM = 0,            // 喜马
    XMIAdTypeBU = 10014,        // 穿山甲
    XMIAdTypeBUExpress = 10026, // 穿山甲模版
    XMIAdTypeGDT = 4,           // 广点通
    XMIAdTypeGDTSplash = 8,     // 广点通开屏
    XMIAdTypeBD = 10034,        // 百度青藤
    XMIAdTypeBDExpress = 10033, // 百度青藤模版
    XMIAdTypeKS = 10037,        // 快手
    XMIAdTypeBAIDU = 10151,     // 百度
    XMIAdTypeJAD = 10074        // 京东
};

/**
 广告显示样式
 */
typedef NS_ENUM(NSInteger, XMIAdShowStyle) {
    XMIAdStyleUnknown = 0,              // 未知样式
    XMIAdStyleHomeLargeImage = 7,       // 首页大图
    XMIAdStyleHomeBackgroundImage = 19, // 首页背景板
    XMIAdStyleHomeShopWindow = 20,      // 首页橱窗
    XMIAdStyleSimpleCardFeedImage = 43,      // 热点页信息流图片
    XMIAdStyleSimpleCardFeedVideo = 44,      // 热点页信息流视频
    XMIAdStyleMyPageFeedBannerPic = 1501,   //我的页信息流-banner图片
    XMIAdStyleMyPageFeedBannerVideo = 1502, //我的页信息流-banner视频
    XMIAdStyleMyPageFeedSecondPic = 1503,   //我的页信息流-二楼图片
    XMIAdStyleMyPageFeedSecondVideo = 1504,   //我的页信息流-二楼视频
    XMIAdStyleDownloadAlbumDetailsPageInformationFlowPic = 1601,   //下载专辑详情页信息流图片
    XMIAdStyleDownloadAlbumDetailsPageInformationFlowVideo = 1602,   //下载专辑详情页信息流视频
    XMIAdStyleHomeVideo = 2821,          // 首页视频
    XMIAdStyleDraftLargeImage = 24202,    //播放页大图
    XMIAdStyleDraftSquareImage = 24203,   //播放页字幕小图
    XMIAdStylePlayPageBubbleSmallImage = 24301,     //播放页主播气泡小图
    XMIAdStylePlayPageBubbleBigImage = 24302,     //播放页主播气泡大图
    XMIAdStyleHomeDoubleRowVideo = 23802, // 双列信息流 横版大图
    XMIAdStyleHomeDoubleRowAudio = 23805, // 双列信息流专辑声音等
    XMIAdStyleHomeDoubleRowVerticalVideo = 23804, // 双列信息流 竖版视频
    XMIAdStyleHomeDoubleRowVerticalImage = 23803, // 双列信息流 竖版大图
    XMIAdStyleSearchEntryPageInformationFlowImage = 27101, //搜索页入口信息流视频
    XMIAdStyleSearchEntryPageInformationFlowVideo = 27102, //搜索页入口信息流大图
    XMIAdStyleHomeListImage9_16 = 29302,       // 首页多图整投9:16
    XMIAdStyleHomeListImage1_1 = 29303,       // 首页多图整投1:1
    XMIAdStyleHomeListImage3_4 = 29304,       // 首页多图整投4:3
    XMIAdStyleHomeCompositeBanner9_16 = 29305,       // 首页多图混投9:16
    XMIAdStyleHomeCompositeBanner1_1 = 29306,       // 首页多图混投1:1
    XMIAdStyleHomeCompositeBanner3_4 = 29307,       // 首页多图混投4:3
    XMIAdStyleHomeDoubleRowSquareImage = 1000003, // 双列信息流 1:1大图
    
    XMIAdStyleGXImage = 1000001,
    XMIAdStyleGXVideo = 1000002,
    XMIAdStyleGXWebP = 1000003,

    XMIAdStylePlayBanner = 1401, // 单独下挂
    XMIAdStylePlayBannerBottomGif = 1402, // 下挂浮层


    // 以下两个枚举 为客户端自定义用于适配混排信息流的，与服务端无关
    XMIAdStyleHomeMixRowDoubleBigImage = 99999995, // 混排双列 大图广告
    XMIAdStyleHomeMixRowGuessYouLike = 99999996, // 混排 猜你喜欢
    XMIAdStyleHomeMixRowGuessYouLikeB = 99999997, // 混排 猜你喜欢(AB实验)
    XMIAdStyleHomeMixRowGuessYouLikeC = 99999998, // 混排 猜你喜欢(新首页) before 9.2.48 已废弃
    XMIAdStyleHomeMixRowGuessYouLikeD = 99999999 // 混排 猜你喜欢(新首页) after 9.2.48
};

/**
 播放器状态
 */
typedef NS_ENUM(NSInteger, XMIPlayerPlayState) {
    XMIPlayerStateInitial   = 0,
    XMIPlayerStateBuffering = 1,
    XMIPlayerStatePlaying   = 2,
    XMIPlayerStateStopped   = 3,
    XMIPlayerStatePause     = 4,
    XMIPlayerStateFailed    = 5,
    XMIPlayerStateUnknown   = 99
};

/**
 展示类型
 */
typedef NS_ENUM(int, XMIAdShowType) {
    XMIAdShowTypeImage = 0, // 图片
    XMIAdShowTypeGif = 1,   // git
    XMIAdShowTypeVideo = 2, // 视频
    XMIAdShowTypeBackup = 3 // 备胎图
};

typedef NS_ENUM(NSInteger, XMIInScreenSource) {
    XMIInScreenSourceNone = 0,
    XMIInScreenSourceText,
    XMIInScreenSourceLogo,
};

typedef NS_ENUM(NSInteger, XMIAdNativeLogLevel) {
    XMIAdNativeLogLevelNone,
    XMIAdNativeLogLevelError,
    XMIAdNativeLogLevelWarning,
    XMIAdNativeLogLevelInfo,
    XMIAdNativeLogLevelDebug,
    XMIAdNativeLogLevelVerbose,
};

typedef NS_ENUM(NSInteger, XMIAdClickableAreaType) {
    XMIAdClickableAreaTypeAll = 1,
    XMIAdClickableAreaTypeBtn = 2,
};

typedef NS_ENUM(NSInteger, XMIAdShowSubStyle) {
    XMIAdSubShowSubStyleNone = 0,
    XMIAdSubShowStyleBubbleOnDraft = 1,
    XMIAdSubShowStyleMPlaying = 3, //新播放页
    XMIAdSubShowSubStyleFeedAB = 4,
    XMIAdSubShowSubStyleSocial = 5,
};

typedef enum : NSUInteger {
    XMIAdFindNativeShowStyleNone = 0,
    XMIAdFindNativeShowStyleAvatar = 1,
    XMIAdFindNativeShowStyleBigCard = 2,
} XMIAdFindNativeShowStyle;


typedef enum : NSUInteger {
    XMIUnlockAdItemAgainPopupTypeNone = 0,
    XMIUnlockAdItemAgainPopupTypeRewardVideo = 13,
    XMIUnlockAdItemAgainPopupTypeWakeEnd = 14,
} XMIUnlockAdItemAgainPopupType;


#define XMI_VIRTUAL_ADID 90
#define XMI_VIRTUAL_ADID_MIN 0
#define XMI_VIRTUAL_ADID_MAX 100
#define XMI_REPORT_DEFAULT_TOKEN @"-1"

#endif /* XMIAdDefines_h */
