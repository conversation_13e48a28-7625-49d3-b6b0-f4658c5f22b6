//
//  XMIAdMacro.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/20.
//

#ifndef XMIAdMacro_h
#define XMIAdMacro_h
#import "UIColor+XMICommon.h"
#import <XMCategories/XMCategory.h>
#import <XMUIConsistent/XMUIConsistent.h>
#import <XMCategories/XMUIScaleMng.h>
#define XMI_SCREEN_WIDTH [UIScreen mainScreen].bounds.size.width
#define XMI_SCREEN_HEIGHT [UIScreen mainScreen].bounds.size.height

#define XMI_COLOR_RGBA(rgbValue, a) [UIColor colorWithRed:((float)((rgbValue & 0xFF0000) >> 16))/255.0 green:((float)((rgbValue & 0xFF00) >> 8))/255.0 blue:((float)(rgbValue & 0xFF))/255.0 alpha:a]
#define XMI_COLOR_RGB(rgbValue) XMI_COLOR_RGBA(rgbValue, 1.0)
#define XMI_COLOR_DynamicFromRGB(rgbValueLight,rbgValueDark) [UIColor xmi_normalColor:rgbValueLight darkColor:rbgValueDark]

#define XMI_COLOR_DynamicFromRGBA(rgbValueLight,a,rbgValueDark,b) [UIColor xmi_normalColor:rgbValueLight normalAlpha:a darkColor:rbgValueDark darkAlpha:b]


#define XMI_AD_PingFangFont(v) ([UIFont fontWithName:@"PingFangSC-Regular" size:v])
#define XMI_AD_PingFangLightFont(v) ([UIFont fontWithName:@"PingFangSC-Light" size:v])
#define XMI_AD_PingFangMediumFont(v) ([UIFont fontWithName:@"PingFangSC-Medium" size:v])
#define XMI_AD_PingFangSemiboldFont(v) ([UIFont fontWithName:@"PingFangSC-Semibold" size:v])

#define XMILock() dispatch_semaphore_wait(_semaphore, DISPATCH_TIME_FOREVER)
#define XMIUnlock() dispatch_semaphore_signal(_semaphore)

#define kADOnePixelsLineHeight        1/[UIScreen mainScreen].scale

#define XMIAdPic(length) xmUIPic(length)
#define XMIAdFont(length) xmUIFont(length)
#define XMIAdHSpace(length) xmUIHSpace(length)
#define XMIAdVSpace(length) xmUIVSpace(length)
#define XMIAdEle(length) xmUIEle(length)
#define XMIAdUIColor(color) xmUIColor(color)
#define XMIAdImage(name) xmUIImage(name)
#define XMIAdImageWithColor(name, themeColor) xmUIImageWithColor(name, themeColor)
/**
 Log
 */
#ifdef DEBUG
#define XMILog(fmt, ...) NSLog((@"%s " fmt), __FUNCTION__, ##__VA_ARGS__)
#else
#define XMILog(fmt, ...)
#endif

/**
 Synthsize a weak or strong reference.
 
 Example:
    @weakify(self)
    [self doSomething^{
        @strongify(self)
        if (!self) return;
        ...
    }];
 */
#ifndef weakify
    #if DEBUG
        #if __has_feature(objc_arc)
        #define weakify(object) autoreleasepool{} __weak __typeof__(object) weak##_##object = object;
        #else
        #define weakify(object) autoreleasepool{} __block __typeof__(object) block##_##object = object;
        #endif
    #else
        #if __has_feature(objc_arc)
        #define weakify(object) try{} @finally{} {} __weak __typeof__(object) weak##_##object = object;
        #else
        #define weakify(object) try{} @finally{} {} __block __typeof__(object) block##_##object = object;
        #endif
    #endif
#endif

#ifndef strongify
    #if DEBUG
        #if __has_feature(objc_arc)
        #define strongify(object) autoreleasepool{} __typeof__(object) object = weak##_##object;
        #else
        #define strongify(object) autoreleasepool{} __typeof__(object) object = block##_##object;
        #endif
    #else
        #if __has_feature(objc_arc)
        #define strongify(object) try{} @finally{} __typeof__(object) object = weak##_##object;
        #else
        #define strongify(object) try{} @finally{} __typeof__(object) object = block##_##object;
        #endif
    #endif
#endif

#endif /* XMIAdMacro_h */
