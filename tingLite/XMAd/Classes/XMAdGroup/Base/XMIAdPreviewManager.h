//
//  XMIAdPreviewManager.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/12/27.
//

#import <Foundation/Foundation.h>

@class XMIAdRespAdData;
@interface XMIAdPreviewItem : NSObject
@property (nonatomic, assign) long long positionId;
@property (strong, nonatomic) XMIAdRespAdData *respAdData;
@end


NS_ASSUME_NONNULL_BEGIN
@interface XMIAdPreviewManager : NSObject

+ (void)requestPreviewDataFromUrlString:(NSString *)urlString andComplete:(void(^ __nullable)(XMIAdPreviewItem *previewItem, NSDictionary *extraInfo))complete;
+ (XMIAdRespAdData *)getPreviewResponseWith:(NSInteger)positionId;

+ (XMIAdPreviewItem *)previewItemWithPosition:(NSInteger)positionId;

@end

NS_ASSUME_NONNULL_END
