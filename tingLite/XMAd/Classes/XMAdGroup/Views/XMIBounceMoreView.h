//
//  XMIBounceMoreView.h
//  XMAd
//
//  Created by xiaodong2.zhang on 2024/7/15.
//

#import <Foundation/Foundation.h>
#import "XMIAdMacro.h"

NS_ASSUME_NONNULL_BEGIN

#define kAdMoreWidth XMIAdEle(32)
#define kAdMoreHeight XMIAdEle(105)
#define kAdMore2Last 20

#define kAdMoreMaxWidth xmUIEle(42)

@interface XMIBounceMoreView : UIView

@property (nonatomic, assign) BOOL isLookMore;

@end

NS_ASSUME_NONNULL_END
