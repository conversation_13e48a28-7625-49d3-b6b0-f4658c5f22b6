//
//  XMIAdShakeTipView.m
//  XMAd
//
//  Created by cuiyuanz<PERSON> on 2023/10/8.
//

#import "XMIAdShakeTipView.h"
#import <XMCategories/XMUIScaleMng.h>
#import <XMCategories/XMCategory.h>
#import <Lottie/Lottie.h>
#import "XMICommonUtils.h"

@interface XMIAdShakeTipView ()

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) LOTAnimationView *animationView;

@end

@implementation XMIAdShakeTipView

+ (instancetype)shakeTipViewWithTitle:(NSString *)title
{
    XMIAdShakeTipView *view = [[XMIAdShakeTipView alloc] init];
    view.title = title;
    view.userInteractionEnabled = NO;
    [view sizeToFit];
    return view;
}

- (void)setTitle:(NSString *)title
{
    _title = [title copy];
    _titleLabel.text = title;
}

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self){
        self.backgroundColor = colorFromRGBA(0x000000, 0.4f);
        LOTAnimationView *anim = [LOTAnimationView animationNamed:@"soundpatch_shake" inBundle:[XMICommonUtils adBundle]];
        anim.size = CGSizeMake(xmUIPic(28), xmUIPic(29));
        [self addSubview:anim];
        anim.loopAnimation = YES;
        [anim play];
        self.animationView = anim;
        self.titleLabel = [[UILabel alloc] init];
        [self addSubview:self.titleLabel];
        self.titleLabel.font = XM_PingFangMediumFont(xmUIFont(15));
        self.titleLabel.textColor = [UIColor whiteColor];
        self.titleLabel.textAlignment = NSTextAlignmentCenter;
        self.titleLabel.minimumScaleFactor = 0.8f;
        self.titleLabel.adjustsFontSizeToFitWidth = YES;
    }
    return self;
}

- (void)sizeToFit
{
    CGFloat textWidth = [self.titleLabel sizeThatFits:CGSizeMake(MAXFLOAT, MAXFLOAT)].width;
    CGFloat viewHeight = xmUIPic(36);
    self.size = CGSizeMake(xmUIHSpace(51) + textWidth, viewHeight);
    [self setNeedsLayout];
    [self layoutIfNeeded];
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    self.layer.cornerRadius = self.height * 0.5f;
    self.layer.masksToBounds = YES;
    [self.titleLabel sizeToFit];
    self.titleLabel.left = xmUIHSpace(36);
    self.titleLabel.width = self.width - self.titleLabel.left - xmUIHSpace(13);
    self.titleLabel.centerY = self.height * 0.5f;
    self.animationView.right = self.titleLabel.left - 1;
    self.animationView.centerY = self.height * 0.5f - xmUIPic(2.5f);
}


@end
