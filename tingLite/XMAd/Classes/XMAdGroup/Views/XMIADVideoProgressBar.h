//
//  XMIADVideoProgressBar.h
//  XMAdApi
//
//  Created by wolf on 2021/8/6.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
@class XMISlider;
@protocol XMIADVideoProgressBarDelegate <NSObject>

- (void)didBeginTouchSlider:(XMISlider *)slider;

- (void)didEndTouchSlider:(XMISlider *)slider;

- (void)didMoveSlide:(XMISlider *)slider;
@end

@interface XMIADVideoProgressBar : UIView
@property (nonatomic, strong, readonly) XMISlider          *progressSr;//进度条
@property (nonatomic, assign)           NSTimeInterval    currentTime;//播放时间
@property (nonatomic, assign)           NSTimeInterval    totalTime;;//总时间
@property (nonatomic,   weak)           id<XMIADVideoProgressBarDelegate> delegate;
@end

NS_ASSUME_NONNULL_END
