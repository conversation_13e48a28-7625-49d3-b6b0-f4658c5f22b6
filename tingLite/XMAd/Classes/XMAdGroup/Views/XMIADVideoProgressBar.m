//
//  XMIADVideoProgressBar.m
//  XMAdApi
//
//  Created by wolf on 2021/8/6.
//

#import "XMIADVideoProgressBar.h"
#import <XMCategories/XMMacro.h>
#import <XMCategories/XMCategory.h>
#import "XMICommonUtils.h"
#import "XMISlider.h"
#import <Masonry/Masonry.h>
@interface XMIADVideoProgressBar ()
{
    BOOL _isSliding;
}
@property (nonatomic, strong, readwrite) XMISlider          *progressSr;//进度条
@property (nonatomic, strong)            UILabel           *currentTimeLabel;//播放时间
@property (nonatomic, strong)            UILabel           *totalTimeLabel;//总时间
@end

@implementation XMIADVideoProgressBar

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.currentTimeLabel = [[UILabel alloc] init];
        self.currentTimeLabel.backgroundColor = [UIColor clearColor];
        self.currentTimeLabel.font = XM_PingFangLightFont(10);
        self.currentTimeLabel.text = @"00:00";
        self.currentTimeLabel.textColor = UIColor.whiteColor;
        self.currentTimeLabel.textAlignment = NSTextAlignmentCenter;
        [self addSubview:self.currentTimeLabel];
        [self.currentTimeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.centerY.equalTo(self);
        }];
        
        self.totalTimeLabel = [[UILabel alloc] init];
        self.totalTimeLabel.backgroundColor = [UIColor clearColor];
        self.totalTimeLabel.font = XM_PingFangLightFont(10);
        self.totalTimeLabel.text = @"00:00";
        self.totalTimeLabel.textColor = self.currentTimeLabel.textColor;
        self.totalTimeLabel.textAlignment = NSTextAlignmentCenter;
        [self.totalTimeLabel sizeToFit];
        [self addSubview:self.totalTimeLabel];
        
        
        [self.totalTimeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.right.equalTo(self);
        }];
        
        self.progressSr = [XMISlider new];
        self.progressSr.maximumTrackTintColor = colorFromRGB(0xFFFFFF);
        self.progressSr.minimumTrackTintColor = colorFromRGB(0xFC5832);
        [self.progressSr setThumbImage:[XMICommonUtils imageNamed:@"icon_ad_video_point"] forState:0];
        
        [self.progressSr addTarget:self action:@selector(handleTouchDown:) forControlEvents:UIControlEventTouchDown];
        [self.progressSr addTarget:self action:@selector(handleSlide:) forControlEvents:UIControlEventValueChanged];
        [self.progressSr addTarget:self action:@selector(handleTouchUp:) forControlEvents:UIControlEventTouchUpInside];
        [self.progressSr addTarget:self action:@selector(handleTouchUp:) forControlEvents:UIControlEventTouchUpOutside];
       
        [self addSubview:self.progressSr];
        [self.progressSr mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.currentTimeLabel.mas_right).mas_offset(4);
            make.centerY.equalTo(self);
            make.right.equalTo(self.totalTimeLabel.mas_left).mas_offset(-8);
            make.height.mas_equalTo(7);
        }];
        
    }
    return self;
}

- (void)setTotalTime:(NSTimeInterval)totalTime
{
    _totalTime = totalTime;
    self.totalTimeLabel.text = [[self class]stringFromTime:totalTime];
}

- (void)setCurrentTime:(NSTimeInterval)currentTime
{
    _currentTime = currentTime;
    self.currentTimeLabel.text = [[self class]stringFromTime:currentTime];
}

+ (NSString *)stringFromTime:(NSTimeInterval)time {
    NSInteger hour = (NSInteger)(time / 3600);
    NSInteger minute = (NSInteger)(time / 60);
    NSInteger second = (NSInteger)time % 60;
    NSMutableString *timeString = @"".mutableCopy;
    if (hour != 0) {
        if (hour < 10) {
            [timeString appendFormat:@"0%@:", @(hour).stringValue];
        } else {
            [timeString appendFormat:@"%@:", @(hour).stringValue];
        }
    }
    
    if (minute < 10) {
        [timeString appendFormat:@"0%@:", @(minute).stringValue];
    } else {
        [timeString appendFormat:@"%@:", @(minute).stringValue];
    }
    
    if (second < 10) {
        [timeString appendFormat:@"0%@", @(second).stringValue];
    } else {
        [timeString appendFormat:@"%@", @(second).stringValue];
    }
    return timeString;
}

#pragma mark - uiSlider
- (void)handleTouchDown:(XMISlider *)slider{
    NSLog(@"TouchDown");
    _isSliding = YES;
    if ([self.delegate respondsToSelector:@selector(didBeginTouchSlider:)]) {
        [self.delegate didBeginTouchSlider:slider];
    }
}

- (void)handleTouchUp:(XMISlider *)slider{
    NSLog(@"TouchUp");
    _isSliding = NO;
    if ([self.delegate respondsToSelector:@selector(didEndTouchSlider:)]) {
        [self.delegate didEndTouchSlider:slider];
    }
}

- (void)handleSlide:(XMISlider *)slider{
    if ([self.delegate respondsToSelector:@selector(didMoveSlide:)]) {
        [self.delegate didMoveSlide:slider];
    }
}

@end
