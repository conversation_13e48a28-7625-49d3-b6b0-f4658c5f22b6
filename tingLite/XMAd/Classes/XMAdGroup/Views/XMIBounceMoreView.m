//
//  XMIBounceMoreView.m
//  XMAd
//
//  Created by xiaodong2.zhang on 2024/7/15.
//

#import "XMIBounceMoreView.h"
#import "XMICommonUtils.h"

@interface XMIBounceMoreView ()

@property (nonatomic , strong) UIImageView *bounceView;
@property (nonatomic , strong) UIImageView *statusView;

@end

@implementation XMIBounceMoreView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        self.backgroundColor = UIColor.clearColor;
        [self addSubview:self.bounceView];
        [self addSubview:self.statusView];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.bounceView.frame = self.bounds;
    
    CGFloat offsetX = 1;
    CGFloat deltaWidth = (self.width - kAdMoreWidth);
    if (deltaWidth > 0) {
        CGFloat maxDelta = kAdMoreMaxWidth - kAdMoreWidth;
        deltaWidth = MIN(deltaWidth, maxDelta);
        offsetX = 1 + deltaWidth / maxDelta * 5;
    }
    
    CGFloat statusW = xmUIEle(self.statusView.image.size.width);
    CGFloat statusH = xmUIEle(self.statusView.image.size.height);
    CGFloat statusX = self.width - statusW - offsetX;
    CGFloat statusY = (self.height - statusH) / 2;
    self.statusView.frame = CGRectMake(statusX, statusY, statusW, statusH);
}

- (void)setIsLookMore:(BOOL)isLookMore {
    _isLookMore = isLookMore;
    UIImage *image = isLookMore ? [XMICommonUtils imageNamed:@"common_bounce_more_end"] : [XMICommonUtils imageNamed:@"common_bounce_more_start"];
    self.statusView.image = image;
}

// MARK: - Getter

- (UIImageView *)statusView {
    if (!_statusView) {
        UIImage *image = [XMICommonUtils imageNamed:@"common_bounce_more_start"];
        UIImageView *imageView = [[UIImageView alloc] initWithImage:image];
        imageView.contentMode = UIViewContentModeScaleAspectFit;
        _statusView = imageView;
    }
    return _statusView;
}

- (UIImageView *)bounceView {
    if (!_bounceView) {
        UIImage *image = [XMICommonUtils imageNamed:@"common_bounce_more"];
        UIImageView *imageView = [[UIImageView alloc] initWithImage:image];
        imageView.contentMode = UIViewContentModeScaleToFill;
        _bounceView = imageView;
    }
    return _bounceView;
}

@end

