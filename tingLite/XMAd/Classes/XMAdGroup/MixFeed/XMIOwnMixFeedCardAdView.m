//
//  XMIOwnMixFeedCardAdView.m
//  XMAd
//
//  Created by xmly on 2022/5/17.
//

#import "XMIOwnMixFeedCardAdView.h"
#import "XMIAnimatedImageView.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import "XMIAdButton.h"
#import "XMIAdMacro.h"
#import "XMICommonUtils.h"
#import "XMIAdRelatedData.h"
#import <YYText/YYText.h>
#import <XMCategories/UIImage+XMDynamic.h>
#import "UIView+XMIUtils.h"
#import "XMIOwnJumpManager.h"
#import "XMIAdHelper.h"
#import "XMIAdNewVideoPlayer.h"
#import "XMIOwnMixFeedCardAdView+Video.h"

#define kCardTitleFont XMI_AD_PingFangMediumFont(15)
#define kCardTitleColor XMI_COLOR_DynamicFromRGB(0xFFFFFF, 0xFFFFFF)

#define kCardBtnLabelFont XMI_AD_PingFangFont(13)

#define kLargeCardAdTopMargin 8
#define kLargeCardAdLeftMargin 16

#define kGreyViewHeight 26

#define kSingleCloseColor XMI_COLOR_DynamicFromRGB(0xB3B3B3, 0x666666)

@interface XMIOwnMixFeedCardAdView()<XMIOwnJumpManagerDelegate>

@property (nonatomic, strong) CAGradientLayer *contentBlackLayer;

@property (nonatomic, strong) UILabel *tagLabel;

@property (nonatomic, strong) XMIOwnJumpManager *jumpManager;

@end

@implementation XMIOwnMixFeedCardAdView

//MARK: - Init
- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setup];
    }
    return self;
}

- (void)setup {
    self.videoPlayer = [self buildVideoPlayer];
    self.backgroundColor = XMI_COLOR_DynamicFromRGB(0xffffff, 0x131313);
    
    [self addSubview:self.contentView];
    [self.contentView addSubview:self.coverImageView];
    [self.coverImageView addSubview:self.videoView];
    
    [self.contentView addSubview:self.bottomMask];
    [self.bottomMask addSubview:self.infoLabel];
    [self.bottomMask addSubview:self.tagLabel];
    
    [self.contentView addSubview:self.greyView];
    [self.greyView addSubview:self.videoInfoLabel];
    
    [self.contentView addSubview:self.btnLabel];
    [self.contentView addSubview:self.adMark];
    [self.contentView addSubview:self.closeButton];
    
    [self registerOwnClickableViews:@[self]];
}

/**
 增加及处理点击事件
 */
- (void)registerOwnClickableViews:(NSArray *)views {
    for (UIView *aView in views) {
        aView.userInteractionEnabled = YES;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleViewClick:)];
        [aView addGestureRecognizer:tap];
    }
}

//MARK: - Reload UI
- (void)refreshWithData:(XMIAdRelatedData *)adData {
    if (adData.loadingStatus == XMIAdRelatedLoadingStatusNormal) {
        self.coverImageView.image = nil;
        return;
    }
    self.relatedData = adData;
    [self startSetUpUIWithModel:adData];
    [self detectExpose:YES];
}

- (void)startSetUpUIWithModel:(id)model {
    XMIAdRelatedData *relatedData = self.relatedData;
    // cover
    [self p_setCoverImageWithData:relatedData];
    // closetBtn
    ((XMIAdButton *)self.closeButton).hitTestEdgeOutsets = [relatedData closeAreaPaddingWithDefaultPadding:UIEdgeInsetsMake(7, 7, 7, 7)];
    // actionBtn
    self.btnLabel.attributedText = [self p_getBtnTextWithImage:[XMICommonUtils imageNamed:@"ad_native_arrow_white"] whiteStyle:YES];
    self.infoLabel.text = relatedData.name;
    self.videoInfoLabel.text = relatedData.name;
    // admark
    UIImage *adMarkImage = [XMICommonUtils imageNamed:@"pic_ad_mark_2"];
    NSURL *markUrl = [NSURL URLWithString:relatedData.adMark];
    [self.adMark sd_setImageWithURL:markUrl placeholderImage:adMarkImage];
    [self setToAnimationOrigin];
    // tag
    [self p_setTagsLabel];
    // layout
    [self p_refreshLayout];
    // 如果是视频广告，那么播放视频
    if (self.relatedData.showstyle == XMIAdStyleHomeVideo) {
        [self startVideoToPlay:self.relatedData];
    } else {
        self.videoView.hidden = YES;
        [self.videoPlayer pause];
    }
}


- (void)setToAnimationOrigin {
    if (!self.relatedData.isExposed) {
        self.bottomMask.alpha = self.greyView.alpha = self.btnLabel.alpha = 0;
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(hideBottomMark) object:nil];
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(hideGreyView) object:nil];
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(changeDetailLabel) object:nil];
    }
}

- (void)p_setTagsLabel {
    NSArray *tags = self.relatedData.tags;
    NSMutableString *tagStr = [[NSMutableString alloc] init];
    [tags enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj isKindOfClass:NSString.class]) {
            NSString *tag = (NSString *)obj;
            if (tag.length > 0) {
                if (tagStr.length > 0) {
                    [tagStr appendString:@" · "];
                }
                [tagStr appendString:tag];
            }
        }
    }];
    self.tagLabel.text = tagStr;
}

- (void)p_setCoverImageWithData:(XMIAdRelatedData *)relatedData {
    UIImage *normalImage = [XMICommonUtils imageNamed:@"bkg_mix_feed"];
    UIImage *darkImage = [XMICommonUtils imageNamed:@"bkg_mix_feed_dark"];
    UIImage *placeholderImage = [UIImage xm_imageWithLight:normalImage dark:darkImage];
    [self.coverImageView sd_setImageWithURL:[NSURL URLWithString:relatedData.cover] placeholderImage:placeholderImage completed:nil];
}

- (NSAttributedString *)p_getBtnTextWithImage:(UIImage *)image whiteStyle:(BOOL)isWhiteStyle {
    XMIAdRelatedData *relatedData = self.relatedData;
    NSString *btnText = relatedData.buttonText;
    if (btnText.length > 4) {
        btnText = [NSString stringWithFormat:@"%@", [btnText substringWithRange:NSMakeRange(0, 4)]];
    }
    NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:btnText.length > 0 ? btnText : @"了解详情"];
    
    NSTextAttachment *spaceAttach = [[NSTextAttachment alloc] init];
    spaceAttach.bounds = CGRectMake(0, 0, 2, 0);
    NSAttributedString *spaceAttachAttr = [NSAttributedString attributedStringWithAttachment:spaceAttach];
    [attributedStr appendAttributedString:spaceAttachAttr];
    [attributedStr addAttributes:@{NSForegroundColorAttributeName:isWhiteStyle ? XMI_COLOR_RGB(0xFFFFFF) : XMI_COLOR_RGB(0xFC5832)} range:NSMakeRange(0, attributedStr.length)];
    
    NSTextAttachment *attach = [NSTextAttachment new];
    attach.image = image;
    attach.bounds = CGRectMake(0, -1, attach.image.size.width, attach.image.size.height);
    NSAttributedString *str = [NSAttributedString attributedStringWithAttachment:attach];
    [attributedStr appendAttributedString:str];
    [attributedStr addAttributes:@{NSFontAttributeName:kCardBtnLabelFont} range:NSMakeRange(0, attributedStr.length)];
    return attributedStr;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    if (self.xmi_height > 0) {
        [self p_refreshLayout];
    }
}

- (void)p_refreshLayout {

    self.contentView.backgroundColor = XMI_COLOR_DynamicFromRGB(0xFAFAFA, 0x252525);
    CGFloat contentLeft = kLargeCardAdLeftMargin;
    CGFloat contentTop = kLargeCardAdTopMargin;
    CGFloat contentHeight = self.xmi_height - contentTop * 2;
    CGFloat contentWidth = self.xmi_width - contentLeft * 2;
    self.contentView.frame = CGRectMake(contentLeft, contentTop, contentWidth, contentHeight);
    
    self.coverImageView.frame = self.contentView.bounds;
    self.videoView.frame = self.coverImageView.bounds;
    
    CGFloat btnWidth = 76;
    CGFloat btnHeight = 26;
    CGFloat btnRightMargin = 10;
    CGFloat btnBottomMargin = self.relatedData.showstyle == XMIAdStyleHomeVideo ? 7 : 12;
        
    self.btnLabel.xmi_size = CGSizeMake(btnWidth, btnHeight);
    self.btnLabel.layer.cornerRadius = btnHeight * 0.5;
    self.btnLabel.xmi_right = self.contentView.xmi_width - btnRightMargin;
    self.btnLabel.xmi_bottom = self.contentView.xmi_height - btnBottomMargin;
    
    CGFloat bottomMaskHeight = 68;
    CGFloat infoLabelLeftMargin = 10;
    CGFloat infoLabelRightMargin = 4;
    [self.infoLabel sizeToFit];
    self.infoLabel.xmi_left = infoLabelLeftMargin;
    self.infoLabel.xmi_width = self.btnLabel.xmi_left - infoLabelLeftMargin - infoLabelRightMargin;
    self.infoLabel.xmi_centerY = bottomMaskHeight - (self.contentView.xmi_height - self.btnLabel.xmi_centerY);
    if (self.tagLabel.text.length) {
        self.infoLabel.xmi_centerY -= 6;
    }
    CGFloat tagLabelHeight = 15;
    self.tagLabel.frame = CGRectMake(infoLabelLeftMargin, self.infoLabel.xmi_bottom, self.infoLabel.xmi_width, tagLabelHeight);

    self.videoInfoLabel.xmi_left = infoLabelLeftMargin;
    self.videoInfoLabel.xmi_top = 0;
    self.videoInfoLabel.xmi_width = self.infoLabel.xmi_width - infoLabelLeftMargin;
    self.videoInfoLabel.xmi_height = kGreyViewHeight;
    
    if ([XMIAdHelper negativeFeedbackValueForAd]) {
        CGFloat markAndCloseButtonSpace = 8;
        self.closeButton.xmi_size = CGSizeMake(18, 18);
        self.closeButton.xmi_top = markAndCloseButtonSpace;
        self.closeButton.xmi_right = self.contentView.xmi_width - markAndCloseButtonSpace;

        self.adMark.frame = CGRectMake(0, 0, 24, 14);
        self.adMark.xmi_left = markAndCloseButtonSpace;
        self.adMark.xmi_top = markAndCloseButtonSpace;
    } else {
        self.closeButton.xmi_size = CGSizeMake(12, 22);
        self.closeButton.xmi_top = 2;
        self.closeButton.xmi_right = self.contentView.xmi_width - 12;
    
        self.adMark.frame = CGRectMake(0, 0, 24, 14);
        self.adMark.xmi_right = self.closeButton.xmi_left - 10;
        self.adMark.xmi_centerY = self.closeButton.centerY;
    }
}

//MARK: - Getter
- (UIView *)contentView {
    if (!_contentView) {
        _contentView = [[UIView alloc] init];
        _contentView.layer.cornerRadius = 4.0;
        _contentView.layer.masksToBounds = YES;
    }
    return _contentView;
}

- (XMIAnimatedImageView *)coverImageView {
    if (!_coverImageView) {
        XMIAnimatedImageView *cView = [[XMIAnimatedImageView alloc] init];
        cView.contentMode = UIViewContentModeScaleToFill;
        cView.backgroundColor = XMI_COLOR_DynamicFromRGB(0xF0F0F0, 0x333333);
        _coverImageView = cView;
    }
    return _coverImageView;
}

- (UILabel *)infoLabel {
    if (!_infoLabel) {
        _infoLabel = [[UILabel alloc] init];
        _infoLabel.font = kCardTitleFont;
        _infoLabel.textColor = kCardTitleColor;
    }
    return _infoLabel;
}

- (UILabel *)videoInfoLabel {
    if (!_videoInfoLabel) {
        _videoInfoLabel = [[UILabel alloc] init];
        _videoInfoLabel.font = XMI_AD_PingFangFont(13);
        _videoInfoLabel.textColor = kCardTitleColor;
    }
    return _videoInfoLabel;
}

- (UIView *)greyView {
    if (!_greyView) {
        _greyView = [[UIView alloc] init];
        _greyView.backgroundColor = [XMI_COLOR_RGB(0x000000) colorWithAlphaComponent:0.5];
        _greyView.layer.cornerRadius = 13;
        _greyView.layer.masksToBounds = YES;
        _greyView.alpha = 0;
    }
    return _greyView;
}

- (UIImageView *)adMark
{
    if (!_adMark) {
        _adMark = [[UIImageView alloc] initWithFrame:CGRectZero];
        _adMark.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _adMark;
}

- (UIButton *)closeButton
{
    if (!_closeButton) {
        _closeButton = [[XMIAdButton alloc] initWithFrame:CGRectZero];
        UIImage *closeImg = [XMICommonUtils imageNamed:@"home_mix_close_find"];
        if ([XMIAdHelper negativeFeedbackValueForAd]) {
            closeImg = [XMICommonUtils imageNamed:@"home_mix_close_find_new"];
        }
        [_closeButton setImage:closeImg forState:UIControlStateNormal];
        [_closeButton addTarget:self action:@selector(closeButtonAction:) forControlEvents:UIControlEventTouchUpInside];
        _closeButton.accessibilityLabel = @"关闭";
    }
    return _closeButton;
}

- (UIImageView *)bottomMask {
    if (!_bottomMask) {
        _bottomMask = [[UIImageView alloc] init];
        CAGradientLayer *contentBlackLayer = [CAGradientLayer layer];
        contentBlackLayer.colors = @[(__bridge id)XMI_COLOR_RGBA(0x000000, 0.0).CGColor, (__bridge id)XMI_COLOR_RGBA(0x000000, 0.5).CGColor];
        contentBlackLayer.locations = @[@0, @1];
        contentBlackLayer.startPoint = CGPointMake(0, 0);
        contentBlackLayer.endPoint = CGPointMake(0, 1);
        _contentBlackLayer = contentBlackLayer;
        [_bottomMask.layer addSublayer:contentBlackLayer];
    }
    return _bottomMask;
}

- (UILabel *)tagLabel {
    if (!_tagLabel) {
        _tagLabel = [[UILabel alloc] init];
        _tagLabel.font = XMI_AD_PingFangFont(10);
        _tagLabel.textColor = XMI_COLOR_RGB(0xFFFFFF);
    }
    return _tagLabel;
}

- (UILabel *)btnLabel
{
    if (!_btnLabel) {
        _btnLabel = [[UILabel alloc] init];
        _btnLabel.font = XMI_AD_PingFangFont(13);
        _btnLabel.textAlignment = NSTextAlignmentCenter;
        _btnLabel.backgroundColor = XMI_COLOR_RGB(0xFF4444);
        _btnLabel.layer.masksToBounds = YES;
    }
    return _btnLabel;
}

- (UIView *)videoView {
    if (!_videoView) {
        _videoView = [XMIAdNewVideoView createVideoView];
    }
    return _videoView;
}

//MARK: - Action
- (void)handleViewClick:(UITapGestureRecognizer *)tap {
    [self adViewDidTap:tap];
}

- (void)adViewDidTap:(UITapGestureRecognizer *)tap {
    NSMutableDictionary *dic = [[NSMutableDictionary alloc] init];
    // 防止除0问题
    if (self.frame.size.width > 0 && self.frame.size.height > 0) {
        CGPoint point = [tap locationInView:self];
        NSString *absX = [NSString stringWithFormat:@"%d", (int)point.x];
        NSString *absY = [NSString stringWithFormat:@"%d", (int)point.y];
        NSString *x = [NSString stringWithFormat:@"%.2f", point.x / self.frame.size.width];
        NSString *y = [NSString stringWithFormat:@"%.2f", point.y / self.frame.size.height];
        dic[@"absX"] = absX;
        dic[@"absY"] = absY;
        dic[@"x"] = x;
        dic[@"y"] = y;
    }
    BOOL jumpSupport = [self.jumpManager doJumpWithAd:self.relatedData];
    dic[kUserInfoJumpNotSupport] = @(!jumpSupport);
    [self adViewDidClick:tap.view withUserInfo:dic];
}

/**
 跳转处理
 */
- (XMIOwnJumpManager *)jumpManager {
    if (_jumpManager == nil) {
        _jumpManager = [[XMIOwnJumpManager alloc] init];
        _jumpManager.delegate = self;
    }
    _jumpManager.rootViewController = self.rootViewController;
    return _jumpManager;
}

- (void)closeButtonAction:(UIButton *)button {
    [self adViewDidClickedClose:button];
}

- (void)traitCollectionDidChange:(UITraitCollection *)previousTraitCollection {
    [super traitCollectionDidChange:previousTraitCollection];
    [self p_setCoverImageWithData:self.relatedData];
}

/// 开始刷新size等
+ (void)startRefreshSizeWithRelatedData:(XMIAdRelatedData *)relatedData
                            adViewWidth:(CGFloat)adViewWidth
{
    relatedData.adWidth = adViewWidth;
    relatedData.adHeight = (XMSCREEN_WIDTH - 16 * 2) * 9.0/16 + 8 * 2;
}

//MARK: - Animation
- (void)showDetailLabel {
    [UIView animateWithDuration:0.5 animations:^{
        self.btnLabel.alpha = 1;
    } completion:^(BOOL finished) {
        
    }];
}

- (void)hideBottomMark {
    [UIView animateWithDuration:0.5 animations:^{
        self.bottomMask.alpha = 0;
    } completion:^(BOOL finished) {
    }];
}

- (void)changeDetailLabel {
    [UIView animateWithDuration:0.2 animations:^{
        self.btnLabel.alpha = 0;
    } completion:^(BOOL finished) {
        self.btnLabel.backgroundColor = XMI_COLOR_RGB(0xFF4444);
        self.btnLabel.textColor = XMI_COLOR_RGB(0xFFFFFF);
        self.btnLabel.attributedText = [self p_getBtnTextWithImage:[XMICommonUtils imageNamed:@"ad_native_arrow_white"] whiteStyle:YES];
        
        [UIView animateWithDuration:0.3 animations:^{
            self.btnLabel.alpha = 1;
        } completion:^(BOOL finished) {

        }];
    }];
}

- (void)hideGreyView {
    [UIView animateWithDuration:0.3 animations:^{
        self.greyView.xmi_width = self.btnLabel.xmi_width-10;
        self.greyView.xmi_right = self.btnLabel.xmi_right-10;
    } completion:^(BOOL finished) {
        self.greyView.alpha = 0;
    }];
}

//MARK: - Override
- (void)adViewDidExpose {
    [super adViewDidExpose];
    
    static unsigned long currentEvent = 0;
    unsigned long eventCode = arc4random();
    currentEvent = eventCode;
    
    CGFloat greyViewLeft = 10;
    CGFloat greyViewWidth = self.contentView.xmi_width - 20;
    
    CGFloat bottomMaskHeight = 68;
    self.bottomMask.frame = CGRectMake(0, self.contentView.xmi_height, self.contentView.xmi_width, bottomMaskHeight);
    self.greyView.frame = CGRectMake(greyViewLeft, self.contentView.xmi_height, greyViewWidth, kGreyViewHeight);
    self.contentBlackLayer.frame = self.bottomMask.bounds;
        
    if (self.relatedData.showstyle == XMIAdStyleHomeVideo) {
        self.btnLabel.backgroundColor = [UIColor clearColor];
        self.btnLabel.textColor = XMI_COLOR_RGB(0xFC5832);
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            if (eventCode != currentEvent) return;
            [UIView animateWithDuration:0.4 animations:^{
                self.greyView.alpha = 1;
                self.greyView.xmi_centerY = self.btnLabel.xmi_centerY;
                self.btnLabel.alpha = 1;
            } completion:^(BOOL finished) {
                if (eventCode != currentEvent) return;
                [self performSelector:@selector(changeDetailLabel) withObject:nil afterDelay:0.3];
                [self performSelector:@selector(hideGreyView) withObject:nil afterDelay:5.0];
            }];
        });
        return;
    }
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if (eventCode != currentEvent) return;
        [UIView animateWithDuration:0.25 animations:^{
            self.bottomMask.alpha = 1;
            self.bottomMask.xmi_bottom = self.contentView.xmi_height;
        } completion:^(BOOL finished) {
            if (eventCode != currentEvent) return;
            [self showDetailLabel];
            [self performSelector:@selector(hideBottomMark) withObject:nil afterDelay:8.0];
        }];
    });
}
@end
