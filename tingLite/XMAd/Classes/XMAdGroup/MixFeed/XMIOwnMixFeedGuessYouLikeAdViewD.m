//
//  XMIOwnMixFeedGuessYouLikeAdViewD.m
//  XMAd
//
//  Created by xiaod<PERSON><PERSON>.zhang on 2024/5/7.
//

#import "XMIOwnMixFeedGuessYouLikeAdViewD.h"
#import "XMIAnimatedImageView.h"
#import "UIView+XMIUtils.h"
#import <YYText/YYText.h>
#import "XMIAdMacro.h"
#import "XMIAdModel.h"
#import "XMIAdReporter.h"
#import "XMIAdButton.h"
#import "XMIAdMarkView.h"
#import "XMICommonUtils.h"
#import <XMCommonUtil/XMUtility.h>
#import <XMCategories/UIImage+XMDynamic.h>
#import <XMWebImage/UIImageView+WebCache.h>
#import "XMIOwnMixFeedCardAdView.h"
#import "XMIExpressAdTrackInfoMapModel.h"
#import "NSObject+XMIModel.h"
#import "XMIOwnJumpManager.h"
#import "XMIAdReporter+AD.h"
#import <XMCategories/XMCategory.h>
#import "XMIExpressAdContainer.h"
#import "XMIAdHelper.h"
#import <XMAd/XMIAdMarkButtonView.h>
#import "XMIAdManager.h"

#define kCoverLeft 16
#define kCoverTop kHomeRatioSize(16)
#define kCoverSize kHomeRatioSize(70)
#define kCoverCornerRadius kHomeRatioSize([XMIAdManager findNativeSocialShowStyle] ? 2 : 12)
#define kCoverAlbumCornerRadius kHomeRatioSize([XMIAdManager findNativeSocialShowStyle] ? 2 : 4)

#define kHomeSingleTitleX (kCoverLeft + kCoverSize + 21)
#define kSingleTitleFont XMI_AD_PingFangMediumFont(kHomeRatioSize(14))
#define kSingleTitleColor XMI_COLOR_DynamicFromRGB(0x393942, 0xDCDCDC)
#define kTitleRightMargin 16

#define kReasonTop kHomeRatioSize(2)
#define kReasonHeight kHomeRatioSize(17)

#define kHomeInfoTop kHomeRatioSize(6)
#define kHomeInfoHeight kHomeRatioSize(17)
#define kHomeInfoFont XMI_AD_PingFangFont(kHomeRatioSize(12))
#define kHomeInfoColor XMI_COLOR_RGB(0xF97373)
#define kHomeInfoIconSize kHomeRatioSize(12)

#define kPlayCountImageSize kHomeRatioSize(10)
#define kPlayCountImageRight kHomeRatioSize(-1)

#define kHomeAvatarSize kHomeRatioSize(15)
#define kHomeAvatarTopSpace (kHomeRatioSize(8) + kHomeRatioSize(1))
#define kHomeAvatarBottomSpace (kHomeRatioSize(16) + 1)

#define kSingleIntroFont XMI_AD_PingFangFont(kHomeRatioSize(12))
#define kSingleIntroColor colorFromRGBA(0x8D8D91, 1.0f)
#define kSingleIntro2Color XMI_COLOR_DynamicFromRGBA(0x8D8D91, 0.8, 0x8D8D91, 0.8)
#define kSingleSubTitleColor XMI_COLOR_DynamicFromRGBA(0x8D8D91, 1, 0x8D8D91, 0.8)

#define kHomeInfoIcon2Text kHomeRatioSize(3)
#define kPlayIconSize ceil(kHomeRatioSize(24))
#define kPlayImageIconSize kHomeRatioSize(14)
#define kPlayIconBottom kHomeRatioSize(4)

#define kAdMarkWidth kHomeRatioSize(28)
#define kAdMarkHeight kHomeRatioSize(16)
#define kAdMarkTextWidth kHomeRatioSize(19)
#define kAdMarkTextHeight kHomeRatioSize(9.5)

#define kHomeAvatarNameGapX kHomeRatioSize(4) //主播头像和描述文字之间间隙
#define kMoreSize kHomeRatioSize(20)

#define kPlayButtonSize kHomeRatioSize(32)
#define kPlayButtonX (XMI_SCREEN_WIDTH - kPlayButtonSize - 16)

@interface XMIOwnMixFeedGuessYouLikeAdViewD ()
{
    UIView *_playIcon;
}

@property (nonatomic, strong) UIImageView *adMarkView;
@property (nonatomic, strong) UIView *adMarkViewBg;
@property (nonatomic, strong) UIButton *moreButton;

//右侧椭圆图片
@property (nonatomic, strong) UIImageView *ellipseImageView;
@property (nonatomic, strong) UIImageView *coverImageMask;
@property (nonatomic, strong) UIImageView *playCountImageView;

@end

@implementation XMIOwnMixFeedGuessYouLikeAdViewD

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        UILongPressGestureRecognizer *longPress = [[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(longPressAction:)];
        [self addGestureRecognizer:longPress];
        
    }
    return self;
}

/// 关闭按钮
- (UIButton *)moreButton {
    if (!_moreButton) {
        _moreButton = [[XMIAdButton alloc] initWithFrame:CGRectZero];
        UIImage *image = [XMICommonUtils imageNamed:@"home_social_feedback_more"];
        if (![[XMIAdManager sharedInstance] isRNHome]) {
            image = [image imageBlendWithColor:colorDynamicFromRGBA(0x2c2c3c, 0.4f, 0x8d8d91, 0.6f)];
        }
        [_moreButton setBackgroundImage:image forState:UIControlStateNormal];
        [_moreButton addTarget:self action:@selector(moreButtonAction) forControlEvents:UIControlEventTouchUpInside];
        _moreButton.accessibilityLabel = @"关闭";
        [self addSubview:_moreButton];
    }
    return _moreButton;
}

- (UIImageView *)adMarkView {
    if (!_adMarkView) {
        _adMarkView = [[UIImageView alloc] init];
        _adMarkView.tintColor = colorFromRGBA(0xffffff, 0.8);
        [self.adMarkViewBg addSubview:_adMarkView];
    }
    return _adMarkView;
}

- (UIView *)adMarkViewBg {
    if (!_adMarkViewBg) {
        _adMarkViewBg = [[UIView alloc] init];
        _adMarkViewBg.backgroundColor = colorFromRGBA(0x000000, 0.35);
        _adMarkViewBg.layer.cornerRadius = kHomeRatioSize(6);
        _adMarkViewBg.layer.masksToBounds = YES;
        _adMarkViewBg.layer.maskedCorners = kCALayerMaxXMinYCorner;
        _adMarkViewBg.hidden = YES;
        [self.coverView addSubview:_adMarkViewBg];
    }
    return _adMarkViewBg;
}

- (UIImageView *)ellipseImageView {
    if (!_ellipseImageView) {
        _ellipseImageView = [[UIImageView alloc] init];
        _ellipseImageView.image = [UIImage xm_dynamicImageWithNormalImage:[XMICommonUtils imageNamed:([[XMIAdManager sharedInstance] isRNHome] ? @"home_album_cover_ellipse_new_2" : @"home_album_cover_ellipse_new")] darkImage:[XMICommonUtils imageNamed:@"home_album_cover_ellipse_dark_new"]];
        [self.coverView.superview insertSubview:_ellipseImageView belowSubview:self.coverView];
    }
    return _ellipseImageView;
}

- (UIImageView *)coverImageMask
{
    if (!_coverImageMask) {
        _coverImageMask = [[UIImageView alloc] init];
        _coverImageMask.image = [XMICommonUtils imageNamed:@"home_album_cover_mask"];
    }
    return _coverImageMask;
}

- (UIView *)dislikePointView {
    return self.coverView;
}

- (UIImageView *)playCountImageView {
    if (!_playCountImageView) {
        UIImage *iconImage = [XMICommonUtils imageNamed:@"home_tag_b_playcount_icon"];
        _playCountImageView.contentMode = UIViewContentModeScaleAspectFit;
        _playCountImageView = [[UIImageView alloc] initWithImage:iconImage];
        _playCountImageView.alpha = 0.8f;
        [self addSubview:_playCountImageView];
    }
    return _playCountImageView;
}

- (UIView *)playIcon {
    if (!_playIcon) {
        XMIAdButton *playIcon = [XMIAdButton buttonWithType:UIButtonTypeCustom];
        playIcon.userInteractionEnabled = NO;
        UIImage *normalImage = [XMIAdManager findNativeSocialShowStyle] ? [XMICommonUtils imageNamed:@"comm_social_pithy_track_play_new"] : [XMICommonUtils imageNamed:@"comm_flow_track_play_new"];
        UIImage *darkImage = [XMIAdManager findNativeSocialShowStyle] ? [XMICommonUtils imageNamed:@"comm_social_pithy_track_play_new_dark"] : [XMICommonUtils imageNamed:@"comm_flow_track_play_new_dark"];
        UIImage *img = [UIImage xm_imageWithLight:normalImage dark:darkImage];
        [playIcon setBackgroundImage:img forState:UIControlStateNormal];
        [self addSubview:playIcon];
        _playIcon = playIcon;
    }
    return _playIcon;
}

- (void)longPressAction:(UILongPressGestureRecognizer *)gesture {
    if (gesture.state == UIGestureRecognizerStateBegan) {
        BOOL isExposed = [self xmi_isExposed:self.rootViewController.view radio:0.95];
        if (isExposed) {
            [XMUtility shakeFeedback];
            [self adViewDidClickedClose:self];
        }
    }
}

- (void)moreButtonAction {
    [self adViewDidClickedClose:self];
}

- (void)startSetUpUIWithModel:(id)model
{
    XMIAdRelatedData *relatedData = self.relatedData;
    id trackInfo = relatedData.trackInfoMapModel;
    [self setupCoverImageWithData:relatedData];
    if ([trackInfo isKindOfClass:[XMIExpressAdTrackInfoMapModel class]]) {
        XMIExpressAdTrackInfoMapModel *trackInfoModel = (XMIExpressAdTrackInfoMapModel *)trackInfo;
        self.titleLabel.textLayout = relatedData.titleTextLayout;
        self.subTitleLabel.text = trackInfoModel.subtitle;
        self.subTitleLabel.hidden = !trackInfoModel.subtitle.length;
        
        NSString *imgUrlString = trackInfoModel.broadcasterPic;
        if (self.traitCollection.userInterfaceStyle == UIUserInterfaceStyleDark) {
            imgUrlString = trackInfoModel.broadcasterPicDark.length ? trackInfoModel.broadcasterPicDark :trackInfoModel.broadcasterPic;
        }
        NSURL *imgUrl = [[NSURL URLWithString:imgUrlString] xm_webpURLWithSize:CGSizeMake(kHomeAvatarSize * UIScreen.mainScreen.scale, kHomeAvatarSize * UIScreen.mainScreen.scale)];
        [self.avatarView sd_setImageWithURL:imgUrl placeholderImage:[XMICommonUtils imageNamed:@"avatar_default_rectangle"] completed:nil];
        self.nameLabel.text = trackInfoModel.broadcasterName;
        
        self.playIcon.hidden = YES;
        self.scoreImageView.hidden = YES;
        self.scoreLabel.hidden = YES;
        self.scoreDivideLine.hidden = YES;
        self.subTitleLabel2.hidden = YES;
        self.playCountImageView.hidden = YES;
        if ([trackInfoModel.promoteType isEqualToString:@"TRACK"]) {
            long long promoteId = [trackInfoModel.promoteId longLongValue];
            XMIAdButton *playButton = XMTypedValue(XMIAdButton, self.playIcon);
            if (promoteId == self.playerPromoteId && self.isplaying) {
                UIImage *normalImage = [XMIAdManager findNativeSocialShowStyle] ? [XMICommonUtils imageNamed:@"comm_social_pithy_track_pause_new"] : [XMICommonUtils imageNamed:@"comm_flow_track_pause_new"];
                UIImage *darkImage = [XMIAdManager findNativeSocialShowStyle] ? [XMICommonUtils imageNamed:@"comm_social_pithy_track_pause_new_dark"] : [XMICommonUtils imageNamed:@"comm_flow_track_pause_new_dark"];
                if (![[XMIAdManager sharedInstance] isRNHome]) {
                    normalImage = [normalImage imageBlendWithColor:colorFromRGBA(0x2C2C3C, 0.6f)];
                    darkImage = [darkImage imageBlendWithColor:colorFromRGB(0x8D8D91)];
                }
                UIImage *img = [UIImage xm_imageWithLight:normalImage dark:darkImage];
                [playButton setBackgroundImage:img forState:UIControlStateNormal];
            } else {
                UIImage *normalImage = [XMIAdManager findNativeSocialShowStyle] ? [XMICommonUtils imageNamed:@"comm_social_pithy_track_play_new"] : [XMICommonUtils imageNamed:@"comm_flow_track_play_new"];
                UIImage *darkImage = [XMIAdManager findNativeSocialShowStyle] ? [XMICommonUtils imageNamed:@"comm_social_pithy_track_play_new_dark"] : [XMICommonUtils imageNamed:@"comm_flow_track_play_new_dark"];
                UIImage *img = [UIImage xm_imageWithLight:normalImage dark:darkImage];
                [playButton setBackgroundImage:img forState:UIControlStateNormal];
            }
            NSMutableAttributedString *subtitle2 = [[NSMutableAttributedString alloc] init];
            if (trackInfoModel.playCount.length) {
                [subtitle2 appendAttributedString:[[NSAttributedString alloc] initWithString:[trackInfoModel.playCount formatBigNumber] attributes:@{NSForegroundColorAttributeName : self.subTitleLabel2.textColor,  NSFontAttributeName : self.subTitleLabel.font}]];
            }
            if (subtitle2.length) {
                self.subTitleLabel2.text = nil;
                self.subTitleLabel2.attributedText = subtitle2;
                self.subTitleLabel2.hidden = NO;
                self.playCountImageView.hidden = NO;
            }
            self.playIcon.hidden = NO;
        } else if ([trackInfoModel.promoteType isEqualToString:@"ALBUM"]) {
            NSMutableAttributedString *subtitle2 = [[NSMutableAttributedString alloc] init];
            if (trackInfoModel.playCount.length) {
                [subtitle2 appendAttributedString:[[NSAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@", [trackInfoModel.playCount formatBigNumber]] attributes:@{NSForegroundColorAttributeName : self.subTitleLabel2.textColor,  NSFontAttributeName : self.subTitleLabel.font}]];
            }
            if (subtitle2.length) {
                self.subTitleLabel2.text = nil;
                self.subTitleLabel2.attributedText = subtitle2;
                self.subTitleLabel2.hidden = NO;
                self.playCountImageView.hidden = NO;
            }
        } else if ([trackInfoModel.promoteType isEqualToString:@"LIVE"]) {
            if (trackInfoModel.hotness.length) {
                self.subTitleLabel2.hidden = NO;
                self.subTitleLabel2.attributedText = nil;
                self.subTitleLabel2.text = [NSString stringWithFormat:@"热度%@", [trackInfoModel.hotness formatBigNumber]];
            }
        }
        if ([trackInfoModel.promoteType isEqualToString:@"ALBUM"]) {
            self.coverView.layer.cornerRadius = kCoverAlbumCornerRadius;
            self.coverView.maskView = self.coverImageMask;
            self.ellipseImageView.hidden = NO;
        } else {
            self.coverView.layer.cornerRadius = kCoverCornerRadius;
            self.coverView.maskView = nil;
            self.ellipseImageView.hidden = YES;
        }
    }
    
    self.userButton.accessibilityLabel = [NSString stringWithFormat:@"主播%@",self.nameLabel.text];
    
    self.divideLine.hidden = relatedData.hideDivideLine;
    [self p_refreshLayout];
}

- (void)p_refreshLayout {
    XMIAdRelatedData *relatedData = self.relatedData;
    
    CGFloat coverViewY = kCoverTop;
    CGFloat coverViewW = kCoverSize;
    CGFloat coverViewH = kCoverSize;
    CGFloat coverViewX = kCoverLeft;
    self.coverView.frame = CGRectMake(coverViewX, coverViewY, coverViewW, coverViewH);

    CGFloat playW = kPlayButtonSize;
    CGFloat playH = playW;
    CGFloat playX = kPlayButtonX;
    CGFloat playY = coverViewY + (coverViewH - playH) / 2;
    self.playIcon.frame = CGRectMake(playX, playY, playW, playH);
    
    
    CGFloat ellipseWidth = kHomeRatioSize(56);
    CGFloat ellipseHeight = kCoverSize;
    self.ellipseImageView.frame = CGRectMake(self.coverView.right - ellipseWidth + ceil(9), self.coverView.top, ellipseWidth, ellipseHeight);
    self.coverImageMask.frame = self.coverView.bounds;
    
    CGFloat titleLabelY = coverViewY;
    CGFloat titleLabelX = kHomeSingleTitleX;
    CGFloat titleLabelW = MAX(
                              ceilf(relatedData.titleTextLayout.textBoundingSize.width),
                              (self.playIcon.hidden ? XMI_SCREEN_WIDTH : kPlayButtonX) - kHomeSingleTitleX - kTitleRightMargin
                              );
    CGFloat titleLabelH = ceilf(relatedData.titleTextLayout.textBoundingSize.height);
    self.titleLabel.frame = CGRectMake(titleLabelX, titleLabelY, titleLabelW, titleLabelH);
 
    CGFloat scoreY = titleLabelY + titleLabelH + kHomeInfoTop;
    CGFloat avatarViewY = titleLabelY + titleLabelH + kHomeAvatarTopSpace;
    CGFloat bottom = self.titleLabel.xmi_bottom;
    if (!self.subTitleLabel.hidden) {
        CGFloat subTitleLabelX = titleLabelX;
        CGFloat subTitleLabelY = titleLabelY + titleLabelH + kReasonTop;
        CGFloat subTitleLabelW = titleLabelW;
        CGFloat subTitleLabelH = kReasonHeight;
        self.subTitleLabel.frame = CGRectMake(subTitleLabelX, subTitleLabelY, subTitleLabelW, subTitleLabelH);
        
        scoreY = subTitleLabelY + subTitleLabelH + kHomeInfoTop;
        avatarViewY = subTitleLabelY + subTitleLabelH + kHomeAvatarTopSpace;
        bottom = self.subTitleLabel.xmi_bottom;
    }
    if (!self.subTitleLabel2.hidden) {
        CGFloat playCountImageX = titleLabelX;
        CGFloat playCountImageY = (self.subTitleLabel.hidden ? (self.titleLabel.bottom + kReasonTop) : self.subTitleLabel.bottom + kHomeInfoTop) + (kHomeInfoHeight - kPlayCountImageSize) / 2;
        CGFloat playCountImageW = kPlayCountImageSize;
        CGFloat playCountImageH = kPlayCountImageSize;
        self.playCountImageView.frame = CGRectMake(playCountImageX, playCountImageY, playCountImageW, playCountImageH);
        
        CGFloat subTitleLabelX = playCountImageX + playCountImageW + kPlayCountImageRight;
        CGFloat subTitleLabelY = self.subTitleLabel.hidden ? (titleLabelY + titleLabelH + kReasonTop) : self.subTitleLabel.bottom + kHomeInfoTop;
        CGFloat subTitleLabelW = titleLabelW - subTitleLabelX - kTitleRightMargin;
        CGFloat subTitleLabelH = kHomeInfoHeight;
        self.subTitleLabel2.frame = CGRectMake(subTitleLabelX, subTitleLabelY, subTitleLabelW, subTitleLabelH);
        avatarViewY = subTitleLabelY + subTitleLabelH + kHomeAvatarTopSpace;
        self.subTitleLabel2.alpha = 0.8f;
    }
    self.playIcon.centerY = (self.titleLabel.top + bottom) * 0.5f;
    
    CGFloat avatarViewX = titleLabelX;
    CGFloat avatarViewW = kHomeAvatarSize;
    CGFloat avatarViewH = kHomeAvatarSize;
    self.avatarView.layer.cornerRadius = kHomeAvatarSize / 2;
    avatarViewY = MAX(avatarViewY, self.xmi_height - kHomeAvatarBottomSpace - avatarViewH);
    self.avatarView.frame = CGRectMake(avatarViewX, avatarViewY, avatarViewW, avatarViewH);
    
    self.adMarkViewBg.frame = CGRectMake(0, coverViewH - kAdMarkHeight, kAdMarkWidth, kAdMarkHeight);
    self.adMarkView.frame = CGRectMake((kAdMarkWidth - kAdMarkTextWidth)/2, (kAdMarkHeight - kAdMarkTextHeight)/2, kAdMarkTextWidth, kAdMarkTextHeight);

    [self.nameLabel sizeToFit];
    CGFloat nameLabelX = avatarViewX + avatarViewW + kHomeAvatarNameGapX;
    CGFloat nameLabelY = avatarViewY;
    CGFloat nameLabelW = MIN(self.nameLabel.xmi_width + 20, XMI_SCREEN_WIDTH - nameLabelX - kTitleRightMargin);
    CGFloat nameLabelH = avatarViewH;
    self.nameLabel.frame = CGRectMake(nameLabelX, nameLabelY, nameLabelW, nameLabelH);
    
    CGFloat userButtonX = avatarViewX;
    CGFloat userButtonY = avatarViewY - 2;
    CGFloat userButtonW = nameLabelX + nameLabelW - userButtonX;
    CGFloat userButtonH = avatarViewH + 4;
    self.userButton.frame = CGRectMake(userButtonX, userButtonY, userButtonW, userButtonH);
        
    CGFloat lineX = titleLabelX;
    CGFloat lineH = 1.f;
    CGFloat lineY = self.xmi_height - lineH;
    CGFloat lineW = XMI_SCREEN_WIDTH - lineX;
    self.divideLine.frame = CGRectMake(lineX, lineY, lineW, lineH);
    
    CGFloat moreButtonW = kMoreSize;
    CGFloat moreButtonH = kMoreSize;
    CGFloat moreButtonX = 0;
    if (self.playIcon.hidden) {
        moreButtonX = self.xmi_width - xmUIPic(22) - moreButtonW;
    } else {
        moreButtonX = self.playIcon.xmi_centerX - moreButtonW * 0.5f;
    }
    CGFloat moreButtonY = avatarViewY + avatarViewH * 0.5f - moreButtonH * 0.5f;
    self.moreButton.frame = CGRectMake(moreButtonX, moreButtonY, moreButtonW, moreButtonH);
}

- (void)refreshWithData:(XMIAdRelatedData *)adData
{
    [self updateFontAndColor];
    [super refreshWithData:adData];
}

- (void)updateFontAndColor
{
    self.subTitleLabel.font = kSingleIntroFont;
    self.subTitleLabel2.font = kSingleIntroFont;
    self.nameLabel.font = kSingleIntroFont;
    if (![[XMIAdManager sharedInstance] isRNHome]) {
        UIColor *color = colorDynamicFromRGBA(0x2c2c3c, 0.4f, 0x8d8d91, 1.0f);
        self.subTitleLabel.textColor = colorDynamicFromRGBA(0x2c2c3c, 0.4f, 0x8d8d91, 1.0f);
        self.subTitleLabel2.textColor = colorDynamicFromRGBA(0x2c2c3c, 0.6f, 0x8d8d91, 1.0f);
        self.nameLabel.textColor =  colorDynamicFromRGBA(0x2c2c3c, 0.4f, 0x8d8d91, 0.8f);
    } else {
        self.subTitleLabel.textColor = kSingleIntroColor;
        self.subTitleLabel2.textColor = kSingleSubTitleColor;
        self.nameLabel.textColor = kSingleIntro2Color;
    }
   
    
    
    self.divideLine.backgroundColor = XMI_COLOR_DynamicFromRGBA(0xF0F0F0, 1, 0x000000, 1);
}

+ (YYTextLayout *)getTitleTextLayout:(NSString *)title
                         adViewWidth:(CGFloat)adViewWidth
                             isTrack:(BOOL)isTrack
{
    NSMutableAttributedString *str = [[NSMutableAttributedString alloc] initWithString:title?:@""];
    str.yy_font = kSingleTitleFont;
    str.yy_color = kSingleTitleColor;
    
    CGFloat maxWidth = (isTrack ? kPlayButtonX : XMSCREEN_WIDTH) - kHomeSingleTitleX - kTitleRightMargin;
    YYTextContainer *titleContainer      = [YYTextContainer containerWithSize:(CGSizeMake(maxWidth, 999))];
    titleContainer.maximumNumberOfRows   = 1;
    [titleContainer setTruncationType:(YYTextTruncationTypeEnd)];
    YYTextLayout *layout = [YYTextLayout layoutWithContainer:titleContainer text:str];
    return layout;
}

+ (CGFloat)getAdViewHeight:(XMIAdRelatedData *)adData
{
    id trackInfo = adData.trackInfoMapModel;
    XMIExpressAdTrackInfoMapModel *trackInfoModel;
    if ([trackInfo isKindOfClass:[XMIExpressAdTrackInfoMapModel class]]) {
        trackInfoModel = (XMIExpressAdTrackInfoMapModel *)trackInfo;
    }
    
    CGFloat titleHeight = ceilf(adData.titleTextLayout.textBoundingSize.height);
    
    CGFloat calHeight = kCoverTop + titleHeight;
    if (trackInfoModel.subtitle.length > 0) {
        calHeight = calHeight + kReasonTop + kReasonHeight;
    }
    
    BOOL displaySub2 = NO;
    if ([trackInfoModel.promoteType isEqualToString:@"TRACK"] || [trackInfoModel.promoteType isEqualToString:@"ALBUM"]) {
        if (trackInfoModel.playCount.length) {
            displaySub2 = YES;
        }
    } else if ([trackInfoModel.promoteType isEqualToString:@"LIVE"]) {
        if (trackInfoModel.hotness.length) {
            displaySub2 = YES;
        }
    }
    
    if (displaySub2) {
        calHeight = calHeight + kHomeInfoTop + kHomeInfoHeight;
    }
    calHeight = calHeight + kHomeAvatarTopSpace + kHomeAvatarSize + kHomeAvatarBottomSpace;
    CGFloat height = MAX(kCoverTop * 2 + kCoverSize + 1, calHeight);
    return height;
}

- (void)p_setCloseButtonBackImage:(XMIAdRelatedData *)adData
{
    if ([XMIAdHelper adDataIsOperation:self.relatedData] || self.relatedData.adMarkStyle != 0) {
        self.adMarkViewBg.hidden = YES;
    } else {
        if (adData.adMark.length) {
            NSURL *url = [NSURL URLWithString:adData.adMark];
            if (url) {
                UIImage *placeholder = [XMICommonUtils imageNamed:@"pic_ad_mark_2"];
                [self.adMarkView sd_setImageWithURL:url placeholderImage:placeholder completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
                    self.adMarkView.image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
                }];
                self.adMarkViewBg.hidden = NO;
                return;
            }
        }
        self.adMarkViewBg.hidden = YES;
    }
    ((XMIAdButton *)self.moreButton).hitTestEdgeOutsets = [adData closeAreaPaddingWithDefaultPadding:UIEdgeInsetsMake(7, 7, 7, 7)];
}

- (void)playerStatusDidChange:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    id trackInfo = self.relatedData.trackInfoMapModel;
    if ([trackInfo isKindOfClass:[XMIExpressAdTrackInfoMapModel class]]) {
        long long trackId = [[userInfo valueForKey:@"trackId"] longLongValue];
        BOOL playing = [[userInfo valueForKey:@"playing"] boolValue];
        
        XMIExpressAdTrackInfoMapModel *trackInfoModel = (XMIExpressAdTrackInfoMapModel *)trackInfo;
        long long promoteId = [trackInfoModel.promoteId longLongValue];
        self.playerPromoteId = trackId;
        self.isplaying = playing;
        if (trackId != promoteId) {
            return;
        }
        XMIAdButton *playButton = XMTypedValue(XMIAdButton, self.playIcon);
        if (playing) {
            UIImage *normalImage = [XMIAdManager findNativeSocialShowStyle] ? [XMICommonUtils imageNamed:@"comm_social_pithy_track_pause_new"] : [XMICommonUtils imageNamed:@"comm_flow_track_pause_new"];
            UIImage *darkImage = [XMIAdManager findNativeSocialShowStyle] ? [XMICommonUtils imageNamed:@"comm_social_pithy_track_pause_new_dark"] : [XMICommonUtils imageNamed:@"comm_flow_track_pause_new_dark"];
            UIImage *img = [UIImage xm_imageWithLight:normalImage dark:darkImage];
            [playButton setBackgroundImage:img forState:UIControlStateNormal];
        } else {
            UIImage *normalImage = [XMIAdManager findNativeSocialShowStyle] ? [XMICommonUtils imageNamed:@"comm_social_pithy_track_play_new"] : [XMICommonUtils imageNamed:@"comm_flow_track_play_new"];
            UIImage *darkImage = [XMIAdManager findNativeSocialShowStyle] ? [XMICommonUtils imageNamed:@"comm_social_pithy_track_play_new_dark"] : [XMICommonUtils imageNamed:@"comm_flow_track_play_new_dark"];
            UIImage *img = [UIImage xm_imageWithLight:normalImage dark:darkImage];
            [playButton setBackgroundImage:img forState:UIControlStateNormal];
        }
    }
}

- (void)traitCollectionDidChange:(UITraitCollection *)previousTraitCollection
{
    if (@available(iOS 13.0, *)) {
        if ([self.traitCollection hasDifferentColorAppearanceComparedToTraitCollection:previousTraitCollection]) {
            
            [self updateMoreImage];
        }
    } else if (self.traitCollection.userInterfaceStyle != previousTraitCollection.userInterfaceStyle) {
        [self updateMoreImage];
    }
}

- (void)updateMoreImage
{
    UIImage *image = [XMICommonUtils imageNamed:@"home_social_feedback_more"];
    if (![[XMIAdManager sharedInstance] isRNHome]) {
        image = [image imageBlendWithColor:colorDynamicFromRGBA(0x2c2c3c, 0.4f, 0x8d8d91, 0.6f)];
    }
    [_moreButton setBackgroundImage:image forState:UIControlStateNormal];

}


@end
