//
//  XMIExpressAdMixGuessYouLikeView.m
//  XMAd
//
//  Created by xmly on 2022/5/13.
//

#import "XMIOwnMixFeedGuessYouLikeAdView.h"
#import "XMIAnimatedImageView.h"
#import "UIView+XMIUtils.h"
#import <YYText/YYText.h>
#import "XMIAdMacro.h"
#import "XMIAdModel.h"
#import "XMIAdReporter.h"
#import "XMIAdButton.h"
#import "XMICommonUtils.h"
#import <XMCommonUtil/XMUtility.h>
#import <XMCategories/UIImage+XMDynamic.h>
#import <XMWebImage/UIImageView+WebCache.h>
#import "XMIOwnMixFeedCardAdView.h"
#import "XMIExpressAdTrackInfoMapModel.h"
#import "NSObject+XMIModel.h"
#import "XMIOwnJumpManager.h"
#import "XMIAdReporter+AD.h"
#import <XMCategories/XMCategory.h>
#import "XMIExpressAdContainer.h"
#import "XMIAdHelper.h"
#import <XMAd/XMIAdMarkButtonView.h>

#define kCoverSize kSingleCoverSize

#define kSingleTitleFont XMI_AD_PingFangMediumFont(kHomeRatioSize(15))
#define kSingleTitleColor XMI_COLOR_DynamicFromRGB(0x333333, 0xDCDCDC)

#define kCoverTop kHomeRatioSize(12)
#define kHomeSingleStyleMargins 16

#define kHomeTitle2Cover kHomeRatioSize(12)
#define kTitleRightMargin kHomeRatioSize(28)
#define kHomeSingleTitleX (kHomeSingleStyleMargins + kCoverSize + kHomeTitle2Cover)

#define kHomeInfoTop kHomeRatioSize(4)
#define kHomeInfoHeight kHomeRatioSize(17)
#define kHomeInfoFont XMI_AD_PingFangFont(kHomeRatioSize(11))
#define kHomeInfoColor XMI_COLOR_DynamicFromRGB(0x999999, 0x66666D)

#define kReasonTop kHomeRatioSize(4)
#define kReasonHeight kHomeRatioSize(12)

#define kHomeAvatarSize kHomeRatioSize(16)
#define kHomeAvatarTopSpace kHomeRatioSize(8)
#define kHomeAvatarBottomSpace kHomeRatioSize(12)

#define kHomeAvatarStyle2Width kHomeRatioSize(44)
#define kHomeAvatarStyle2Height kHomeRatioSize(16)


#define kOnePixelsLineHeight        1/[UIScreen mainScreen].scale

#define kPlayIconSize kHomeRatioSize(26)

#define kHomeInfoIconSize kHomeRatioSize(12)

#define kSingleIntroFont XMI_AD_PingFangFont(kHomeRatioSize(12))
#define kSingleIntroColor XMI_COLOR_DynamicFromRGB(0x8F8F8F, 0x66666d)

#define kHomeInfoIcon2Text kHomeRatioSize(2)

#define kDislikeSize kHomeRatioSize(20)
#define kDislikeContainerW (kDislikeSize + kHomeSingleStyleMargins * 2)
#define kDislikeContainerH (kDislikeSize + 12)
#define kSingleCloseColor XMI_COLOR_DynamicFromRGB(0xB3B3B3, 0x444444)

#define kDislikeImageWStyle0 kHomeRatioSize(8)
#define kDislikeImageHStyle0 kHomeRatioSize(8)
#define kDislikeImageWStyle1 kHomeRatioSize(20)
#define kDislikeImageHStyle2 kHomeRatioSize(20)
#define kHomeAvatarNameGapX  kHomeRatioSize(6) //主播头像和描述文字之间间隙

@interface XMIOwnMixFeedGuessYouLikeAdView()<XMIOwnJumpManagerDelegate>

@property (nonatomic, strong) XMIOwnJumpManager *jumpManager;

@end

@implementation XMIOwnMixFeedGuessYouLikeAdView
//MARK: - Init Layout
- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setup];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(playerStatusDidChange:) name:XMIExpressAdContainerControllerPlayerStatusDidChange object:nil];
    }
    return self;
}

- (void)setup {
    
    [self addSubview:self.coverView];
    [self addSubview:self.avatarView];
    [self addSubview:self.nameLabel];
    [self addSubview:self.titleLabel];
    [self addSubview:self.subTitleLabel];
    [self addSubview:self.subTitleLabel2];

    [self addSubview:self.scoreImageView];
    [self addSubview:self.scoreLabel];
    [self addSubview:self.scoreDivideLine];
    
    [self addSubview:self.userButton];
    [self addSubview:self.divideLine];
    
    [self addSubview:self.closeButton];

    [self registerOwnClickableViews:@[self]];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    if (self.xmi_height > 0) {
        [self p_refreshLayout];
    }
}

- (void)p_refreshLayout {
    XMIAdRelatedData *relatedData = self.relatedData;
    
    CGFloat coverViewY = kCoverTop;
    CGFloat coverViewW = kCoverSize;
    CGFloat coverViewH = kCoverSize;
    CGFloat coverViewX = kHomeSingleStyleMargins;
    self.coverView.frame = CGRectMake(coverViewX, coverViewY, coverViewW, coverViewH);
    
    CGFloat titleLabelY = coverViewY;
    CGFloat titleLabelX = kHomeSingleTitleX;
    
    CGFloat titleLabelW = MAX(
                              ceilf(relatedData.titleTextLayout.textBoundingSize.width),
                              XMSCREEN_WIDTH - kHomeSingleTitleX - kTitleRightMargin
                              );;
    CGFloat titleLabelH = relatedData.titleTextLayout.textBoundingSize.height;
    self.titleLabel.frame = CGRectMake(titleLabelX, titleLabelY, titleLabelW, titleLabelH);
    
 
    CGFloat scoreY = titleLabelY + titleLabelH + kHomeInfoTop;
    CGFloat avatarViewY = titleLabelY + titleLabelH + kHomeAvatarTopSpace;

    if (!self.subTitleLabel.hidden) {
        CGFloat subTitleLabelX = titleLabelX;
        CGFloat subTitleLabelY = titleLabelY + titleLabelH + kReasonTop;
        CGFloat subTitleLabelW = XMI_SCREEN_WIDTH - subTitleLabelX - kHomeTitle2Cover;
        CGFloat subTitleLabelH = kReasonHeight;
        self.subTitleLabel.frame = CGRectMake(subTitleLabelX, subTitleLabelY, subTitleLabelW, subTitleLabelH);
        
        scoreY = subTitleLabelY + subTitleLabelH + kHomeInfoTop;
        avatarViewY = subTitleLabelY + subTitleLabelH + kHomeAvatarTopSpace;
    }
    
    if (!self.scoreImageView.hidden) {
        CGFloat scoreImageViewX = titleLabelX-2;
        CGFloat scoreImageViewW = kHomeInfoIconSize;
        CGFloat scoreImageViewH = kHomeInfoHeight;
        self.scoreImageView.frame = CGRectMake(scoreImageViewX, scoreY, scoreImageViewW, scoreImageViewH);
        
        CGFloat scoreLabelX = self.scoreImageView.xmi_right + kHomeInfoIcon2Text;
        CGFloat scoreLabelW = titleLabelW;
        CGFloat scoreLabelH = kHomeInfoHeight;
        self.scoreLabel.frame = CGRectMake(scoreLabelX, scoreY, scoreLabelW, scoreLabelH);
    }
    
    CGFloat avatarViewX = titleLabelX;
    CGFloat avatarViewW = kHomeAvatarSize;
    CGFloat avatarViewH = kHomeAvatarSize;
    avatarViewY = MAX(avatarViewY, self.xmi_height - kHomeAvatarBottomSpace - avatarViewH);
    self.avatarView.frame = CGRectMake(avatarViewX, avatarViewY, avatarViewW, avatarViewH);
    
    CGFloat closeButtonW = kDislikeContainerW;
    CGFloat closeButtonH = kDislikeContainerH;
    CGFloat closeButtonX = self.xmi_width - closeButtonW;
    CGFloat closeButtonY = avatarViewY + (avatarViewH - closeButtonH) / 2;
    self.closeButton.frame = CGRectMake(closeButtonX, closeButtonY, closeButtonW, closeButtonH);
   
    [self.nameLabel sizeToFit];
    CGFloat nameLabelX = avatarViewX + avatarViewW + 4;
    CGFloat nameLabelY = avatarViewY;
    CGFloat nameLabelW = MIN(self.nameLabel.xmi_width + 20, closeButtonX - nameLabelX - 10);
    CGFloat nameLabelH = avatarViewH;
    self.nameLabel.frame = CGRectMake(nameLabelX, nameLabelY, nameLabelW, nameLabelH);
    
    CGFloat userButtonX = avatarViewX;
    CGFloat userButtonY = avatarViewY - 2;
    CGFloat userButtonW = nameLabelX + nameLabelW - userButtonX;
    CGFloat userButtonH = avatarViewH + 4;
    self.userButton.frame = CGRectMake(userButtonX, userButtonY, userButtonW, userButtonH);
    
    CGFloat lineX = titleLabelX;
    CGFloat lineH = kOnePixelsLineHeight;
    CGFloat lineY = self.xmi_height - lineH;
    CGFloat lineW = XMI_SCREEN_WIDTH - lineX;
    self.divideLine.frame = CGRectMake(lineX, lineY, lineW, lineH);
}

//MARK: - Getter
- (YYLabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [YYLabel new];
        _titleLabel.isAccessibilityElement = NO;
        _titleLabel.numberOfLines = 2;
    }
    return _titleLabel;
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        UILabel *label = [UILabel new];
        label.textColor = XMI_COLOR_DynamicFromRGB(0x666666, 0x8D8D91);
        label.font = XMI_AD_PingFangFont(11);
        label.isAccessibilityElement = NO;
        _nameLabel = label;
    }
    return _nameLabel;
}
 
- (XMIAnimatedImageView *)avatarView {
    if (!_avatarView) {
        XMIAnimatedImageView *view = [XMIAnimatedImageView new];
        view.contentMode = UIViewContentModeScaleAspectFit;
        view.layer.masksToBounds = YES;
        _avatarView = view;
    }
    return _avatarView;
}

- (XMIAnimatedImageView *)coverView {
    if (!_coverView) {
        XMIAnimatedImageView *cView = [[XMIAnimatedImageView alloc] init];
        cView.contentMode = UIViewContentModeScaleAspectFill;
        cView.layer.cornerRadius = 4;
        cView.layer.masksToBounds = YES;
        cView.backgroundColor = XMI_COLOR_DynamicFromRGB(0xF0F0F0, 0x333333);
        [cView addSubview:self.tagImageView];
        _coverView = cView;
        
        
//        [cView addSubview:self.playIcon];
//        CGFloat playIconW = kPlayIconSize;
//        CGFloat playIconH = kPlayIconSize;
//        CGFloat playIconX = kCoverSize - playIconW - kHomeRatioSize(6);
//        CGFloat playIconY = kCoverSize - playIconH - kHomeRatioSize(6);
//        self.playIcon.frame = CGRectMake(playIconX, playIconY, playIconW, playIconH);
    }
    return _coverView;
}

- (UIImageView *)tagImageView {
    if (!_tagImageView) {
        _tagImageView = [UIImageView new];
    }
    return _tagImageView;
}

- (UIButton *)userButton {
    if (!_userButton) {
        _userButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_userButton addTarget:self action:@selector(anchorItemClicked:) forControlEvents:UIControlEventTouchUpInside];
        _userButton.backgroundColor = UIColor.clearColor;
        _userButton.adjustsImageWhenHighlighted = NO;
    }
    return _userButton;
}

- (UILabel *)subTitleLabel {
    if (!_subTitleLabel) {
        UILabel *label = [UILabel new];
        label.font = kSingleIntroFont;
        label.textColor = kSingleIntroColor;
        label.isAccessibilityElement = NO;
        _subTitleLabel = label;
    }
    return _subTitleLabel;
}

- (UILabel *)subTitleLabel2 {
    if (!_subTitleLabel2) {
        UILabel *label = [UILabel new];
        label.font = kSingleIntroFont;
        label.textColor = kSingleIntroColor;
        label.isAccessibilityElement = NO;
        _subTitleLabel2 = label;
    }
    return _subTitleLabel2;
}

- (UIView *)divideLine {
    if (!_divideLine) {
        _divideLine = [UIView new];
        _divideLine.backgroundColor = XMI_COLOR_DynamicFromRGBA(0xEEEEEE, 1, 0x000000, 1);
    }
    return _divideLine;
}

- (UIView *)scoreDivideLine
{
    if (!_scoreDivideLine) {
        _scoreDivideLine = [UIView new];
        _scoreDivideLine.backgroundColor = kSingleIntroColor;
        _scoreDivideLine.alpha = 0.6f;
    }
    return _scoreDivideLine;
}

/// 关闭按钮
- (UIButton *)closeButton
{
    if (!_closeButton) {
        _closeButton = [[XMIAdButton alloc] initWithFrame:CGRectZero];
        UIImage *normalImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        UIImage *darkImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close_dark"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        [_closeButton setImage:[UIImage xm_imageWithLight:normalImage dark:darkImage] forState:UIControlStateNormal];
        _closeButton.imageView.tintColor = kSingleCloseColor;
        [_closeButton addTarget:self action:@selector(closeButtonAction:) forControlEvents:UIControlEventTouchUpInside];
        _closeButton.accessibilityLabel = @"关闭";
    }
    return _closeButton;
}

- (UIImageView *)scoreImageView {
    if (!_scoreImageView) {
        _scoreImageView = [[UIImageView alloc] initWithFrame:CGRectZero];
        _scoreImageView.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _scoreImageView;
}

- (UILabel *)scoreLabel {
    if (!_scoreLabel) {
        _scoreLabel = [[UILabel alloc] init];
        _scoreLabel.font = kHomeInfoFont;
        _scoreLabel.textColor = kHomeInfoColor;
    }
    return _scoreLabel;
}

- (UIView *)playIcon {
    if (!_playIcon) {
        _playIcon = [UIView new];
        _playIcon.backgroundColor = UIColor.clearColor;
        _playIcon.frame = CGRectMake(0, 0, kPlayIconSize, kPlayIconSize);
        
        UIBlurEffect *effect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleLight];
        UIVisualEffectView *effectView = [[UIVisualEffectView alloc] initWithEffect:effect];
        effectView.layer.cornerRadius = kPlayIconSize / 2;
        effectView.layer.masksToBounds = YES;
        [_playIcon addSubview:effectView];
        effectView.frame = CGRectMake(0, 0, kPlayIconSize, kPlayIconSize);
        effectView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        effectView.tag = 10000;
        
        UIImage *image = [XMICommonUtils imageNamed:@"icon_mix_feed_track_play"];
        UIImageView *play = [[UIImageView alloc] initWithImage:image];
        play.contentMode = UIViewContentModeScaleAspectFit;
        _playIconImageView = play;
        [_playIcon addSubview:play];

        CGFloat playW = image.size.width;
        CGFloat playH = image.size.height;
        CGFloat playX = (kPlayIconSize - playW) / 2;
        CGFloat playY = (kPlayIconSize - playH) / 2;
        play.frame = CGRectMake(playX, playY, playW, playH);
        play.autoresizingMask = UIViewAutoresizingFlexibleTopMargin | UIViewAutoresizingFlexibleBottomMargin | UIViewAutoresizingFlexibleLeftMargin | UIViewAutoresizingFlexibleRightMargin;
    }
    return _playIcon;
}

- (XMIOwnJumpManager *)jumpManager {
    if (_jumpManager == nil) {
        _jumpManager = [[XMIOwnJumpManager alloc] init];
        _jumpManager.delegate = self;
    }
    _jumpManager.rootViewController = self.rootViewController;
    return _jumpManager;
}


- (void)traitCollectionDidChange:(UITraitCollection *)previousTraitCollection
{
    [super traitCollectionDidChange:previousTraitCollection];
    self.titleLabel.textLayout = nil;
    XMIExpressAdTrackInfoMapModel *trackInfoMapModel = self.relatedData.trackInfoMapModel;
    if ([trackInfoMapModel isKindOfClass:[XMIExpressAdTrackInfoMapModel class]]) {
        BOOL isTrack = [trackInfoMapModel.promoteType isEqualToString:@"TRACK"];
        self.relatedData.titleTextLayout =  [[self class] getTitleTextLayout:trackInfoMapModel.title adViewWidth:self.relatedData.adWidth isTrack:isTrack];
        self.titleLabel.textLayout = self.relatedData.titleTextLayout;
    }
    
    [self p_setCloseButtonBackImage:self.relatedData];
    [self setupCoverImageWithData:self.relatedData];
    if (self.traitCollection.userInterfaceStyle != previousTraitCollection.userInterfaceStyle) {
        XMIAdRelatedData *relatedData = self.relatedData;
        id trackInfo = relatedData.trackInfoMapModel;
        if ([trackInfo isKindOfClass:[XMIExpressAdTrackInfoMapModel class]]) {
            XMIExpressAdTrackInfoMapModel *trackInfoModel = (XMIExpressAdTrackInfoMapModel *)trackInfo;
            NSString *imgUrlString = trackInfoModel.broadcasterPic;
            if (self.traitCollection.userInterfaceStyle == UIUserInterfaceStyleDark) {
                imgUrlString = trackInfoModel.broadcasterPicDark;
            }
            NSURL *imgUrl = [[NSURL URLWithString:imgUrlString] xm_webpURLWithSize:CGSizeMake(kHomeAvatarSize * UIScreen.mainScreen.scale, kHomeAvatarSize * UIScreen.mainScreen.scale)];
            [self.avatarView sd_setImageWithURL:imgUrl placeholderImage:[XMICommonUtils imageNamed:@"avatar_default_rectangle"] completed:nil];
        }
    }
}

//MARK: - Refresh With AdModel
/// 刷新adModel的size等
+ (void)startRefreshSizeWithAdModel:(XMIAdModel *)adModel
{
    XMIAdRelatedData *relateData = adModel.relatedData;
    [self startRefreshSizeWithRelatedData:relateData adViewWidth:adModel.adWidth];
}

/// 开始刷新size等
+ (void)startRefreshSizeWithRelatedData:(XMIAdRelatedData *)relatedData
                            adViewWidth:(CGFloat)adViewWidth
{
    [self startSetTitleTextLayout:relatedData adViewWidth:adViewWidth];
    relatedData.adWidth = adViewWidth;
    relatedData.adHeight = [self getAdViewHeight:relatedData];
    
}

+ (void)startSetTitleTextLayout:(XMIAdRelatedData *)adData
                    adViewWidth:(CGFloat)adViewWidth
{
    if (adData.bizType == XMIOwnMixFeedCardAdViewTypeGuessYouLike) {
        if (adData.trackInfoMap) {
            XMIExpressAdTrackInfoMapModel *trackInfoModel = [XMIExpressAdTrackInfoMapModel xmi_modelWithJSON:adData.trackInfoMap];
            adData.trackInfoMapModel = trackInfoModel;
            BOOL isTrack = [trackInfoModel.promoteType isEqualToString:@"TRACK"];
            adData.titleTextLayout = [self getTitleTextLayout:trackInfoModel.title adViewWidth:adViewWidth isTrack:isTrack];
            adData.contentAdTitle = trackInfoModel.title;
            adData.cover = trackInfoModel.iconUrl;
        }
    }
}

+ (CGFloat)getAdViewHeight:(XMIAdRelatedData *)adData
{
    id trackInfo = adData.trackInfoMapModel;
    XMIExpressAdTrackInfoMapModel *trackInfoModel;
    if ([trackInfo isKindOfClass:[XMIExpressAdTrackInfoMapModel class]]) {
        trackInfoModel = (XMIExpressAdTrackInfoMapModel *)trackInfo;
    }
    
    CGFloat titleHeight = ceilf(adData.titleTextLayout.textBoundingSize.height);
    
    CGFloat calHeight = kCoverTop + titleHeight;
    if (trackInfoModel.subtitle.length > 0) {
        calHeight = calHeight + kReasonTop + kReasonHeight;
    }
    
    BOOL displayScore = NO;
    if ([trackInfoModel.promoteType isEqualToString:@"TRACK"]) {
        if (trackInfoModel.playCount.length) {
            displayScore = YES;
        }
    } else if ([trackInfoModel.promoteType isEqualToString:@"ALBUM"]) {
        if (trackInfoModel.subscribeCount.length) {
            displayScore = YES;
        }
    } else if ([trackInfoModel.promoteType isEqualToString:@"LIVE"]) {
        if (trackInfoModel.hotness.length) {
            displayScore = YES;
        }
    }
    
    if (displayScore) {
        calHeight = calHeight + kHomeInfoTop + kHomeInfoHeight;
    }
    calHeight = calHeight + kHomeAvatarTopSpace + kHomeAvatarSize + kHomeAvatarBottomSpace;
    CGFloat height = MAX(kCoverTop * 2 + kCoverSize, calHeight);
    return height;
}

+ (YYTextLayout *)getTitleTextLayout:(NSString *)title
                         adViewWidth:(CGFloat)adViewWidth
                             isTrack:(BOOL)isTrack
{
    NSMutableAttributedString *str = [[NSMutableAttributedString alloc] initWithString:title?:@""];
    str.yy_font = kSingleTitleFont;
    str.yy_color = kSingleTitleColor;
    
    CGFloat maxWidth = XMI_SCREEN_WIDTH - kHomeSingleTitleX - kTitleRightMargin;
    YYTextContainer *titleContainer      = [YYTextContainer containerWithSize:(CGSizeMake(maxWidth, 999))];
    titleContainer.maximumNumberOfRows   = 2;
    [titleContainer setTruncationType:(YYTextTruncationTypeEnd)];
    YYTextLayout *layout = [YYTextLayout layoutWithContainer:titleContainer text:str];
    return layout;
}

- (void)refreshWithData:(XMIAdRelatedData *)adData {
    self.relatedData = adData;
    [self p_setCloseButtonBackImage:adData];
    [self startSetUpUIWithModel:adData];
    [self detectExpose:YES];
}

- (void)startSetUpUIWithModel:(id)model
{
    XMIAdRelatedData *relatedData = self.relatedData;
    id trackInfo = relatedData.trackInfoMapModel;
    [self setupCoverImageWithData:relatedData];
    if ([trackInfo isKindOfClass:[XMIExpressAdTrackInfoMapModel class]]) {
        XMIExpressAdTrackInfoMapModel *trackInfoModel = (XMIExpressAdTrackInfoMapModel *)trackInfo;
        self.titleLabel.textLayout = relatedData.titleTextLayout;
        self.subTitleLabel.text = trackInfoModel.subtitle;
        self.subTitleLabel.hidden = !trackInfoModel.subtitle.length;
        
        NSString *imgUrlString = trackInfoModel.broadcasterPic;
        if (self.traitCollection.userInterfaceStyle == UIUserInterfaceStyleDark) {
            imgUrlString = trackInfoModel.broadcasterPicDark;
        }
        NSURL *imgUrl = [[NSURL URLWithString:imgUrlString] xm_webpURLWithSize:CGSizeMake(kHomeAvatarSize * UIScreen.mainScreen.scale, kHomeAvatarSize * UIScreen.mainScreen.scale)];
        [self.avatarView sd_setImageWithURL:imgUrl placeholderImage:[XMICommonUtils imageNamed:@"avatar_default_rectangle"] completed:nil];
        self.nameLabel.text = trackInfoModel.broadcasterName;
        
        self.playIcon.hidden = YES;
        self.scoreImageView.hidden = self.scoreLabel.hidden = YES;
        self.scoreDivideLine.hidden = YES;
        self.subTitleLabel2.hidden = YES;
        if ([trackInfoModel.promoteType isEqualToString:@"TRACK"]) {
            long long promoteId = [trackInfoModel.promoteId longLongValue];
            if (promoteId == self.playerPromoteId && self.isplaying) {
                self.playIconImageView.image = [XMICommonUtils imageNamed:@"icon_mix_feed_track_pause"];
            } else {
                self.playIconImageView.image = [XMICommonUtils imageNamed:@"icon_mix_feed_track_play"];
            }
            NSMutableAttributedString *subtitle2 = [[NSMutableAttributedString alloc] init];
            if (trackInfoModel.playCount.length) {
                [subtitle2 appendAttributedString:[[NSAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@次播放", [trackInfoModel.playCount formatBigNumber]] attributes:@{NSForegroundColorAttributeName : self.subTitleLabel2.textColor,  NSFontAttributeName : self.subTitleLabel.font}]];

            }
            if (trackInfoModel.duration.length > 0) {
                if (subtitle2.length) {
                    if (![self isKindOfClass:[XMIOwnMixFeedGuessYouLikeAdViewC class]]) {
                        [subtitle2 appendAttributedString:[[NSAttributedString alloc] initWithString:@" · " attributes:@{NSForegroundColorAttributeName : colorDynamicFromRGBA(0xD8D8D8, 1.0f, 0x8D8D91, 1.0f),  NSFontAttributeName : self.subTitleLabel.font}]];
                        
                    } else {
                        [subtitle2 appendAttributedString:[[NSAttributedString alloc] initWithString:@" " attributes:@{NSForegroundColorAttributeName : colorDynamicFromRGBA(0xD8D8D8, 1.0f, 0x8D8D91, 1.0f),  NSFontAttributeName : XMI_AD_PingFangFont(kHomeRatioSize(14))}]];
                        NSTextAttachment *attachment = [[NSTextAttachment alloc] init];
                        attachment.image = [XMICommonUtils imageNamed:@"guess_sep_line"];
                        attachment.bounds = CGRectMake(0, 0, 4, 7);
                        [subtitle2 appendAttributedString:[NSAttributedString attributedStringWithAttachment:attachment]];
                        [subtitle2 appendAttributedString:[[NSAttributedString alloc] initWithString:@" " attributes:@{NSForegroundColorAttributeName : colorDynamicFromRGBA(0xD8D8D8, 1.0f, 0x8D8D91, 1.0f),  NSFontAttributeName : XMI_AD_PingFangFont(kHomeRatioSize(14))}]];
//                        [subtitle2 appendAttributedString:[[NSAttributedString alloc] initWithString:@" / " attributes:@{NSForegroundColorAttributeName : colorDynamicFromRGBA(0x8D8D91, 0.3f, 0x8D8D91, 1.0f),  NSFontAttributeName : XM_PingFangFont(kHomeRatioSize(8)), NSBaselineOffsetAttributeName : @(0)}]];
                    }
                  
                }
                [subtitle2 appendAttributedString:[[NSAttributedString alloc] initWithString:[NSString stringWithFormat:@"总时长%@", [XMIAdHelper stringForSeconds2:[trackInfoModel.duration longLongValue]]] attributes:@{NSForegroundColorAttributeName : self.subTitleLabel.textColor, NSFontAttributeName : self.subTitleLabel.font}]];
            }
            if (subtitle2.length) {
              
                self.subTitleLabel2.text = nil;
                self.subTitleLabel2.attributedText = subtitle2;
                self.subTitleLabel2.hidden = NO;
            }
            self.playIcon.hidden = NO;
        } else if ([trackInfoModel.promoteType isEqualToString:@"ALBUM"]) {
            if (trackInfoModel.albumScore.length > 0) {
                self.scoreImageView.hidden = self.scoreLabel.hidden = YES;
                self.scoreDivideLine.hidden = YES;
            }
        } else if ([trackInfoModel.promoteType isEqualToString:@"LIVE"]) {
            if (trackInfoModel.hotness.length) {
                self.subTitleLabel2.hidden = NO;
                self.subTitleLabel2.attributedText = nil;
                self.subTitleLabel2.text = [NSString stringWithFormat:@"热度%@", [trackInfoModel.hotness formatBigNumber]];
            }
        }
    }
    
    self.userButton.accessibilityLabel = [NSString stringWithFormat:@"主播%@",self.nameLabel.text];
    
    self.divideLine.hidden = relatedData.hideDivideLine;
    
    [self p_refreshLayout];
}

- (void)p_setCloseButtonBackImage:(XMIAdRelatedData *)adData
{
    UIImage *normalImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
    UIImage *darkImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close_dark"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
    [self.closeButton setImage:[UIImage xm_imageWithLight:normalImage dark:darkImage] forState:UIControlStateNormal];
    self.closeButton.imageView.tintColor = kSingleCloseColor;
    ((XMIAdButton *)self.closeButton).hitTestEdgeOutsets = [adData closeAreaPaddingWithDefaultPadding:UIEdgeInsetsMake(7, 7, 7, 7)];
}

//MARK: - Actions
- (void)closeButtonAction:(UIButton *)button
{
    [self adViewDidClickedClose:button];
}

/**
 增加及处理点击事件
 */
- (void)registerOwnClickableViews:(NSArray *)views {
    for (UIView *aView in views) {
        aView.userInteractionEnabled = YES;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleViewClick:)];
        [aView addGestureRecognizer:tap];
    }
}

- (void)handleViewClick:(UITapGestureRecognizer *)tap {
    NSMutableDictionary *dic = [[NSMutableDictionary alloc] init];
    // 防止除0问题
    if (self.frame.size.width > 0 && self.frame.size.height > 0) {
        CGPoint point = [tap locationInView:self];
        NSString *absX = [NSString stringWithFormat:@"%d", (int)point.x];
        NSString *absY = [NSString stringWithFormat:@"%d", (int)point.y];
        NSString *x = [NSString stringWithFormat:@"%.2f", point.x / self.frame.size.width];
        NSString *y = [NSString stringWithFormat:@"%.2f", point.y / self.frame.size.height];
        dic[@"absX"] = absX;
        dic[@"absY"] = absY;
        dic[@"x"] = x;
        dic[@"y"] = y;
    }
    BOOL jumpSupport = [self.jumpManager doJumpWithAd:self.relatedData];
    dic[kUserInfoJumpNotSupport] = @(!jumpSupport);
    [self adViewDidClick:tap.view withUserInfo:dic];
}


/// 主播头像点击
- (void)anchorItemClicked:(UIButton *)button
{
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdView:didClickedAnchorItem:clickedView:)]) {
        [self.delegate expressAdView:self didClickedAnchorItem:self.relatedData clickedView:button];
    }
}

- (void)setupCoverImageWithData:(XMIAdRelatedData *)relatedData {
    id trackInfo = relatedData.trackInfoMapModel;
    if ([trackInfo isKindOfClass:[XMIExpressAdTrackInfoMapModel class]]) {
        XMIExpressAdTrackInfoMapModel *trackInfoModel = (XMIExpressAdTrackInfoMapModel *)trackInfo;
        UIImage *normalImage = [XMICommonUtils imageNamed:@"bkg_mix_feed"];
        UIImage *darkImage = [XMICommonUtils imageNamed:@"bkg_mix_feed_dark"];
        UIImage *placeholderImage = [UIImage xm_imageWithLight:normalImage dark:darkImage];
        
        NSURL *imgUrl = [[NSURL URLWithString:trackInfoModel.iconUrl] xm_webpURLWithSize:CGSizeMake(kCoverSize * UIScreen.mainScreen.scale, kCoverSize * UIScreen.mainScreen.scale)];
        [self.coverView sd_setImageWithURL:imgUrl placeholderImage:placeholderImage completed:nil];
    }
}

- (void)playerStatusDidChange:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    id trackInfo = self.relatedData.trackInfoMapModel;
    if ([trackInfo isKindOfClass:[XMIExpressAdTrackInfoMapModel class]]) {
        long long trackId = [[userInfo valueForKey:@"trackId"] longLongValue];
        BOOL playing = [[userInfo valueForKey:@"playing"] boolValue];
        
        XMIExpressAdTrackInfoMapModel *trackInfoModel = (XMIExpressAdTrackInfoMapModel *)trackInfo;
        long long promoteId = [trackInfoModel.promoteId longLongValue];
        self.playerPromoteId = trackId;
        self.isplaying = playing;
        if (trackId != promoteId) {
            return;
        }
        if (playing) {
            self.playIconImageView.image = [XMICommonUtils imageNamed:@"icon_mix_feed_track_pause"];
        } else {
            self.playIconImageView.image = [XMICommonUtils imageNamed:@"icon_mix_feed_track_play"];
        }
    }
}

//MARK: - Override
- (void)adViewDidExpose
{
    // 混排猜你喜欢，需要在展示时上报tingshow，请求时不上报tingshow
    [XMIAdReporter exposeReportValidAds:@[self.relatedData]];
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdViewDidExpose:)]) {
        [self.delegate expressAdViewDidExpose:self];
    }
}
@end

#define kSingleTitleFont_B XMI_AD_PingFangFont(kHomeRatioSize(16))
#define kSingleTitleColor_B XMI_COLOR_DynamicFromRGB(0x131313, 0xDCDCDC)

#define kCoverTop_B kHomeRatioSize(16)
#define kHomeSingleStyleMargins_B 16

#define kHomeTitle2Cover_B kHomeRatioSize(12)
#define kTitleRightMargin_B kHomeRatioSize(21)
#define kHomeSingleTitleX_B (kHomeSingleStyleMargins_B + kCoverSize + kHomeTitle2Cover_B)

#define kHomeInfoTop_B kHomeRatioSize(4)
#define kHomeInfoHeight_B kHomeRatioSize(18)
#define kHomeInfoFont_B XMI_AD_PingFangFont(kHomeRatioSize(12))
#define kHomeInfoColor_B XMI_COLOR_RGB(0xF97373)

#define kReasonTop_B kHomeRatioSize(4)
#define kReasonHeight_B kHomeRatioSize(18)

#define kHomeAvatarSize kHomeRatioSize(16)
#define kHomeAvatarTopSpace_B kHomeRatioSize(7)
#define kHomeAvatarBottomSpace_B kHomeRatioSize(16)


#define kPlayIconSize_B kHomeRatioSize(26)
#define kPlayIconInterval_B kHomeRatioSize(4)

#define kHomeInfoIconSize_B kHomeRatioSize(12)

#define kSingleIntroFont_B XMI_AD_PingFangFont(kHomeRatioSize(12))
#define kSingleIntroColor_B XMI_COLOR_DynamicFromRGB(0x8F8F8F, 0x66666B)

#define kHomeInfoIcon2Text_B kHomeRatioSize(3)


@interface XMIOwnMixFeedGuessYouLikeAdViewB ()

@property (nonatomic, strong) UIImageView *closeImageView;
@property (nonatomic, strong) XMIAdMarkButtonView *adMarkButtonView;
@end

@implementation XMIOwnMixFeedGuessYouLikeAdViewB

- (UIImageView *)closeImageView
{
    if (!_closeImageView) {
        UIImage *normalImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        UIImage *darkImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close_dark"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        _closeImageView = [[UIImageView alloc] initWithImage:[UIImage xm_imageWithLight:normalImage dark:darkImage]];
        _closeImageView.tintColor = kSingleCloseColor;;
        [self.closeButton addSubview:_closeImageView];
        [self.closeButton setImage:nil forState:UIControlStateNormal];
    }
    return _closeImageView;
}

- (XMIAdMarkButtonView *)adMarkButtonView {
    if (!_adMarkButtonView) {
        _adMarkButtonView = [[XMIAdMarkButtonView alloc] initWithType:XMIAdMarkTypeArrow];
        _adMarkButtonView.adMarkUIMode = XMIAdMarkUIModeLight;
        [self addSubview:_adMarkButtonView];
        @weakify(self)
        _adMarkButtonView.clickAction = ^{
            @strongify(self)
            [self adViewDidClickedClose:self];
        };
    }
    return _adMarkButtonView;
}

- (void)p_refreshLayout {
    XMIAdRelatedData *relatedData = self.relatedData;
    
    CGFloat coverViewY = kCoverTop_B;
    CGFloat coverViewW = kCoverSize;
    CGFloat coverViewH = kCoverSize;
    CGFloat coverViewX = kHomeSingleStyleMargins_B;
    self.coverView.frame = CGRectMake(coverViewX, coverViewY, coverViewW, coverViewH);
    
    CGFloat titleLabelY = coverViewY;
    CGFloat titleLabelX = kHomeSingleTitleX_B;
    
    CGFloat titleLabelW = MAX(
                              ceilf(relatedData.titleTextLayout.textBoundingSize.width),
                              XMSCREEN_WIDTH - kHomeSingleTitleX_B - kTitleRightMargin_B
                              );;
    CGFloat titleLabelH = relatedData.titleTextLayout.textBoundingSize.height;
    self.titleLabel.frame = CGRectMake(titleLabelX, titleLabelY - kHomeRatioSize(3), titleLabelW, titleLabelH);
    
 
    CGFloat scoreY = titleLabelY + titleLabelH + kHomeInfoTop_B;
    CGFloat avatarViewY = titleLabelY + titleLabelH + kHomeAvatarTopSpace_B;

    if (!self.subTitleLabel.hidden) {
        CGFloat subTitleLabelX = titleLabelX;
        CGFloat subTitleLabelY = titleLabelY + titleLabelH + kReasonTop_B;
        CGFloat subTitleLabelW = XMI_SCREEN_WIDTH - subTitleLabelX - kHomeTitle2Cover_B;
        CGFloat subTitleLabelH = kReasonHeight_B;
        self.subTitleLabel.frame = CGRectMake(subTitleLabelX, subTitleLabelY, subTitleLabelW, subTitleLabelH);
        
        scoreY = subTitleLabelY + subTitleLabelH + kHomeInfoTop_B;
        avatarViewY = subTitleLabelY + subTitleLabelH + kHomeAvatarTopSpace_B;
    }
    if (!self.scoreImageView.hidden) {
        CGFloat subTitleLabelY = titleLabelY + titleLabelH + kReasonTop_B;
        CGFloat subTitleLabelH = kReasonHeight_B;
        CGFloat scoreImageViewW = kHomeInfoIconSize_B;
        self.scoreImageView.xmi_size = CGSizeMake(scoreImageViewW, scoreImageViewW);
        self.scoreImageView.xmi_left = titleLabelX;
        self.scoreImageView.xmi_centerY = subTitleLabelY + subTitleLabelH * 0.5f;
        [self.scoreLabel sizeToFit];
        self.scoreLabel.xmi_left = self.scoreImageView.xmi_right + kHomeRatioSize(3.0f);
        self.scoreLabel.xmi_centerY = self.scoreImageView.xmi_centerY;
        self.scoreDivideLine.xmi_size = CGSizeMake(1, kHomeRatioSize(8));
        self.scoreDivideLine.xmi_left = self.scoreLabel.xmi_right + kHomeRatioSize(8);
        self.scoreDivideLine.xmi_centerY = self.scoreLabel.xmi_centerY;
        if (!self.subTitleLabel.hidden) {
            CGFloat subTitleLabelX = self.scoreDivideLine.xmi_right + kHomeRatioSize(8);
            CGFloat subTitleLabelW = XMI_SCREEN_WIDTH - subTitleLabelX - kHomeTitle2Cover_B;
            self.subTitleLabel.xmi_left = subTitleLabelX;
            self.subTitleLabel.xmi_width = subTitleLabelW;
        }
    }
    if (!self.subTitleLabel2.hidden) {
        CGFloat subTitleLabelX = titleLabelX;
        CGFloat subTitleLabelY = self.subTitleLabel.hidden ? (titleLabelY + titleLabelH + kReasonTop_B) : avatarViewY;
        CGFloat subTitleLabelW = XMI_SCREEN_WIDTH - subTitleLabelX - kHomeTitle2Cover_B;
        CGFloat subTitleLabelH = kReasonHeight_B;
        self.subTitleLabel2.frame = CGRectMake(subTitleLabelX, subTitleLabelY, subTitleLabelW, subTitleLabelH);
        avatarViewY = subTitleLabelY + subTitleLabelH + kHomeAvatarTopSpace_B;
    }
    
    CGFloat avatarViewX = titleLabelX;
    CGFloat avatarViewW = kHomeAvatarSize;
    CGFloat avatarViewH = kHomeAvatarSize;
    if (self.relatedData.adMarkStyle == 2) {
        avatarViewW = kHomeAvatarStyle2Width;
        avatarViewH = kHomeAvatarStyle2Height;
        self.avatarView.layer.cornerRadius = 0;
    } else {
        self.avatarView.layer.cornerRadius = kHomeAvatarSize / 2;
    }
    avatarViewY = MAX(avatarViewY, self.xmi_height - kHomeAvatarBottomSpace_B - avatarViewH);
    self.avatarView.frame = CGRectMake(avatarViewX, avatarViewY, avatarViewW, avatarViewH);
    
    CGFloat closeButtonW = kDislikeContainerW;
    CGFloat closeButtonH = kDislikeContainerH;
    CGFloat closeButtonX = self.xmi_width - closeButtonW;
    CGFloat closeButtonY = avatarViewY + (avatarViewH - closeButtonH) / 2;
    self.closeButton.frame = CGRectMake(closeButtonX, closeButtonY, closeButtonW, closeButtonH);
    if (self.relatedData.adMarkStyle == 2) {
        self.closeImageView.size = CGSizeMake(kDislikeImageWStyle0, kDislikeImageWStyle0);
    } else if (self.relatedData.adMarkStyle == 1) {
        self.closeImageView.size = CGSizeMake(kDislikeImageWStyle1, kDislikeImageWStyle1);
    } else {
        self.adMarkButtonView.xmi_right = self.xmi_width - kHomeRatioSize(16);
        self.adMarkButtonView.xmi_centerY = self.closeButton.xmi_centerY;
    }
    self.closeImageView.center = CGPointMake(closeButtonW * 0.5f, closeButtonH * 0.5f);
    self.adMarkButtonView.hidden = self.relatedData.adMarkStyle != 0;
    self.closeButton.hidden = !self.adMarkButtonView.hidden;

    [self.nameLabel sizeToFit];
    CGFloat nameLabelX = avatarViewX + avatarViewW + kHomeAvatarNameGapX;
    CGFloat nameLabelY = avatarViewY;
    CGFloat nameLabelW = MIN(self.nameLabel.xmi_width + 20, closeButtonX - nameLabelX - 10);
    CGFloat nameLabelH = avatarViewH;
    self.nameLabel.frame = CGRectMake(nameLabelX, nameLabelY, nameLabelW, nameLabelH);
    
    CGFloat userButtonX = avatarViewX;
    CGFloat userButtonY = avatarViewY - 2;
    CGFloat userButtonW = nameLabelX + nameLabelW - userButtonX;
    CGFloat userButtonH = avatarViewH + 4;
    self.userButton.frame = CGRectMake(userButtonX, userButtonY, userButtonW, userButtonH);
        
    CGFloat lineX = titleLabelX;
    CGFloat lineH = kOnePixelsLineHeight;
    CGFloat lineY = self.xmi_height - lineH;
    CGFloat lineW = XMI_SCREEN_WIDTH - lineX;
    self.divideLine.frame = CGRectMake(lineX, lineY, lineW, lineH);
}

- (void)refreshWithData:(XMIAdRelatedData *)adData
{
    [self updateFontAndColor];
    [super refreshWithData:adData];
}

- (void)updateFontAndColor
{
    self.nameLabel.textColor = XMI_COLOR_DynamicFromRGB(0x666666, 0x8D8D91);
    self.nameLabel.font = XMI_AD_PingFangFont(kHomeRatioSize(12));
    self.subTitleLabel.font = kSingleIntroFont_B;
    self.subTitleLabel.textColor = kSingleIntroColor_B;
    self.subTitleLabel2.font = kSingleIntroFont_B;
    self.subTitleLabel2.textColor = kSingleIntroColor_B;
    self.scoreDivideLine.backgroundColor = kSingleIntroColor_B;
    self.scoreLabel.font = kHomeInfoFont_B;
    self.scoreLabel.textColor = kHomeInfoColor_B;
    self.closeButton.imageView.tintColor = kSingleCloseColor;
}

+ (YYTextLayout *)getTitleTextLayout:(NSString *)title
                         adViewWidth:(CGFloat)adViewWidth
                             isTrack:(BOOL)isTrack
{
    NSMutableAttributedString *str = [[NSMutableAttributedString alloc] initWithString:title?:@""];
    str.yy_font = kSingleTitleFont_B;
    str.yy_color = kSingleTitleColor_B;
    
    CGFloat maxWidth = XMI_SCREEN_WIDTH - kHomeSingleTitleX - kTitleRightMargin;
    YYTextContainer *titleContainer      = [YYTextContainer containerWithSize:(CGSizeMake(maxWidth, 999))];
    titleContainer.maximumNumberOfRows   = 2;
    [titleContainer setTruncationType:(YYTextTruncationTypeEnd)];
    YYTextLayout *layout = [YYTextLayout layoutWithContainer:titleContainer text:str];
    return layout;
}

+ (CGFloat)getAdViewHeight:(XMIAdRelatedData *)adData
{
    id trackInfo = adData.trackInfoMapModel;
    XMIExpressAdTrackInfoMapModel *trackInfoModel;
    if ([trackInfo isKindOfClass:[XMIExpressAdTrackInfoMapModel class]]) {
        trackInfoModel = (XMIExpressAdTrackInfoMapModel *)trackInfo;
    }
    
    CGFloat titleHeight = ceilf(adData.titleTextLayout.textBoundingSize.height);
    
    CGFloat calHeight = kCoverTop_B + titleHeight;
    if (trackInfoModel.subtitle.length > 0) {
        calHeight = calHeight + kReasonTop_B + kReasonHeight_B;
    }
    
    BOOL displaySub2 = NO;
    if ([trackInfoModel.promoteType isEqualToString:@"TRACK"]) {
        if (trackInfoModel.playCount.length || trackInfoModel.duration.length) {
            displaySub2 = YES;
        }
    } else if ([trackInfoModel.promoteType isEqualToString:@"LIVE"]) {
        if (trackInfoModel.hotness.length) {
            displaySub2 = YES;
        }
    }
    
    if (displaySub2) {
        calHeight = calHeight + kHomeInfoTop_B + kHomeInfoHeight_B;
    }
    calHeight = calHeight + kHomeAvatarTopSpace_B + kHomeAvatarSize + kHomeAvatarBottomSpace_B;
    CGFloat height = MAX(kCoverTop_B * 2 + kCoverSize, calHeight);
    return height;
}

- (void)p_setCloseButtonBackImage:(XMIAdRelatedData *)adData
{
    UIImage *normalImage = nil;
    UIImage *darkImage = nil;
    if (self.relatedData.adMarkStyle == 2) {
        normalImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        darkImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close_dark"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        self.closeImageView.image = [UIImage xm_imageWithLight:normalImage dark:darkImage];
    } else if (self.relatedData.adMarkStyle == 1) {
        normalImage = [[XMICommonUtils imageNamed:@"home_feed_more_ic"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        darkImage = [[XMICommonUtils imageNamed:@"home_feed_more_ic_dark"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        NSURL *markUrl = [NSURL URLWithString:adData.adMark];
        @weakify(self);
        [self.closeImageView sd_setImageWithURL:markUrl placeholderImage:[UIImage xm_imageWithLight:normalImage dark:darkImage] options:SDWebImageRetryFailed completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
            if (image) {
                UIImage *tempImage = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
                weak_self.closeImageView.image = tempImage;
                weak_self.closeImageView.tintColor = kSingleCloseColor;
            }
        }];
    } else {
        if ([XMIAdHelper adDataIsOperation:self.relatedData]) {
            [self.adMarkButtonView updateAdMarkText:@""];
        } else {
            NSURL *markUrl = [NSURL URLWithString:adData.adMark];
            NSURL *darkAdMarkUrl = [NSURL URLWithString:adData.darkAdMark];
            [self.adMarkButtonView updateAdMarkURL:markUrl adMarkDarkURL:darkAdMarkUrl placeholder:[XMICommonUtils imageNamed:@"admark_text"]];
        }
    }
    self.closeImageView.tintColor = kSingleCloseColor;
    ((XMIAdButton *)self.closeButton).hitTestEdgeOutsets = [adData closeAreaPaddingWithDefaultPadding:UIEdgeInsetsMake(7, 7, 7, 7)];
    self.adMarkButtonView.hitTestEdgeOutsets = [adData closeAreaPaddingWithDefaultPadding:UIEdgeInsetsMake(13, 12, 13, 12)];
}


@end

#define kCoverSize_C kHomeRatioSize(70)
#define kSingleTitleFont_C XMI_AD_PingFangMediumFont(kHomeRatioSize(14))
#define kSingleTitleColor_C XMI_COLOR_DynamicFromRGB(0x131313, 0xDCDCDC)

#define kCoverTop_C kHomeRatioSize(16)
#define kHomeSingleStyleMargins_C 16

#define kHomeTitle2Cover_C kHomeRatioSize(21)
#define kTitleRightMargin_C kHomeRatioSize(67)
#define kHomeSingleTitleX_C (kHomeSingleStyleMargins_C + kCoverSize_C + kHomeTitle2Cover_C)

#define kHomeInfoTop_C kHomeRatioSize(4)
#define kHomeInfoHeight_C kHomeRatioSize(18)
#define kHomeInfoFont_C XMI_AD_PingFangFont(kHomeRatioSize(12))
#define kHomeInfoColor_C XMI_COLOR_RGB(0xF97373)

#define kReasonTop_C kHomeRatioSize(0)
#define kReasonHeight_C kHomeRatioSize(17)

#define kHomeAvatarSize_C kHomeRatioSize(16)
#define kHomeAvatarTopSpace_C kHomeRatioSize(7)
#define kHomeAvatarBottomSpace_C kHomeRatioSize(16)

#define kHomeInfoIconSize_C kHomeRatioSize(12)

#define kSingleIntroFont_C XMI_AD_PingFangFont(kHomeRatioSize(12))
#define kSingleIntroColor_C XMI_COLOR_DynamicFromRGB(0x8D8D91, 0x66666B)

#define kHomeInfoIcon2Text_C kHomeRatioSize(3)
#define kPlayIconSize_C kHomeRatioSize(24)
#define kPlayIconBottom_C kHomeRatioSize(4)

@interface XMIOwnMixFeedGuessYouLikeAdViewC ()

@property (nonatomic, strong) UIImageView *closeImageView;
@property (nonatomic, strong) XMIAdMarkButtonView *adMarkButtonView;
@property (nonatomic, strong) UIImageView *ellipseImageView;
//产品要求图片上面，需要描透明的边
@property(strong, nonatomic)UIImageView *borderImageView;
@property (nonatomic, strong) UIImageView *coverImageMask;
@property (nonatomic, strong) UIView *trackBorderView;
@end

@implementation XMIOwnMixFeedGuessYouLikeAdViewC

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        UILongPressGestureRecognizer *longPress = [[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(longPressAction:)];
        [self addGestureRecognizer:longPress];
    }
    return self;
}

- (UIImageView *)closeImageView
{
    if (!_closeImageView) {
        UIImage *normalImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        UIImage *darkImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close_dark"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        _closeImageView = [[UIImageView alloc] initWithImage:[UIImage xm_imageWithLight:normalImage dark:darkImage]];
        _closeImageView.tintColor = kSingleCloseColor;;
        [self.closeButton addSubview:_closeImageView];
        [self.closeButton setImage:nil forState:UIControlStateNormal];
    }
    return _closeImageView;
}

- (XMIAdMarkButtonView *)adMarkButtonView {
    if (!_adMarkButtonView) {
        _adMarkButtonView = [[XMIAdMarkButtonView alloc] initWithType:XMIAdMarkTypeArrow];
        _adMarkButtonView.adMarkUIMode = XMIAdMarkUIModeSocialHomePageGuessYouLike;
        [self addSubview:_adMarkButtonView];
        @weakify(self)
        _adMarkButtonView.clickAction = ^{
            @strongify(self)
            [self adViewDidClickedClose:self];
        };
    }
    return _adMarkButtonView;
}

- (UIImageView *)ellipseImageView {
    if (!_ellipseImageView) {
        _ellipseImageView = [[UIImageView alloc] init];
        _ellipseImageView.image = [UIImage xm_dynamicImageWithNormalImage:[XMICommonUtils imageNamed:@"home_album_cover_ellipse"] darkImage:[XMICommonUtils imageNamed:@"home_album_cover_ellipse_dark"]];
        [self.coverView.superview insertSubview:_ellipseImageView belowSubview:self.coverView];
    }
    return _ellipseImageView;
}

- (UIImageView *)borderImageView {
    if (!_borderImageView) {
        _borderImageView = [[UIImageView alloc] initWithFrame:self.coverView.bounds];
        _borderImageView.image = [UIImage xm_dynamicImageWithNormalImage:[XMICommonUtils imageNamed:@"home_album_cover_border"] darkImage:[XMICommonUtils imageNamed:@"home_album_cover_border_dark"]];
        [self.coverView addSubview:_borderImageView];
    }
    return _borderImageView;
}

- (UIView *)trackBorderView
{
    if (!_trackBorderView) {
        _trackBorderView = [[UIView alloc] init];
        [self.coverView.superview insertSubview:_trackBorderView belowSubview:self.coverView];
        _trackBorderView.backgroundColor = XMI_COLOR_DynamicFromRGBA(0xE2E3E8, 0.8f, 0xE2E3E8, 0.2f);
        _trackBorderView.userInteractionEnabled = NO;
        _trackBorderView.size = CGSizeMake(self.coverView.width + kHomeRatioSize(4), self.coverView.height + kHomeRatioSize(4));
        _trackBorderView.layer.cornerRadius = _trackBorderView.size.width * 0.5f;
        _trackBorderView.center = self.coverView.center;

    }
    return _trackBorderView;
}


- (UIImageView *)coverImageMask
{
    if (!_coverImageMask) {
        _coverImageMask = [[UIImageView alloc] init];
        _coverImageMask.image = [XMICommonUtils imageNamed:@"home_album_cover_mask"];
    }
    return _coverImageMask;
}

- (UIView *)dislikePointView {
    if (!self.adMarkButtonView.hidden) {
        return self.adMarkButtonView;
    } else if (!self.closeButton.hidden) {
        return self.closeButton;
    } else {
        return self.coverView;
    }
}

- (void)longPressAction:(UILongPressGestureRecognizer *)gesture {
    if (gesture.state == UIGestureRecognizerStateBegan) {
        BOOL isExposed = [self xmi_isExposed:self.rootViewController.view radio:0.95];
        if (isExposed) {
            [XMUtility shakeFeedback];
            [self adViewDidClickedClose:self];
        }
    }
}

- (void)startSetUpUIWithModel:(id)model
{
    [super startSetUpUIWithModel:model];
    XMIAdRelatedData *relatedData = self.relatedData;
    id trackInfo = relatedData.trackInfoMapModel;
    if ([trackInfo isKindOfClass:[XMIExpressAdTrackInfoMapModel class]]) {
        XMIExpressAdTrackInfoMapModel *trackInfoModel = (XMIExpressAdTrackInfoMapModel *)trackInfo;
       
        if ([trackInfoModel.promoteType isEqualToString:@"ALBUM"]) {
            //todo:cd图 
            self.coverView.layer.cornerRadius = 0;
            self.coverView.maskView = self.coverImageMask;
            self.ellipseImageView.hidden = NO;
            self.borderImageView.hidden = NO;
            _trackBorderView.hidden = YES;
            self.playIcon.hidden = YES;
        } else {
            self.coverView.layer.cornerRadius =  self.coverView.width * 0.5f;
            self.coverView.maskView = nil;
            self.ellipseImageView.hidden = YES;
            self.playIcon.hidden = YES;
          
            _borderImageView.hidden = YES;
            self.trackBorderView.hidden = NO;
        }
        if ([trackInfoModel.promoteType isEqualToString:@"TRACK"]) {
            self.playIcon.hidden = NO;
            long long promoteId = [trackInfoModel.promoteId longLongValue];
            if (promoteId == self.playerPromoteId && self.isplaying) {
                self.playIconImageView.image = [XMICommonUtils imageNamed:@"btn_pause_btn_n_fill_n_28"];
            } else {
                self.playIconImageView.image = [XMICommonUtils imageNamed:@"btn_play_btn_inside_fill_n_28"];
            }
            self.playIcon.contentMode = UIViewContentModeScaleAspectFill;
        }
    }
    
    self.userButton.accessibilityLabel = [NSString stringWithFormat:@"主播%@",self.nameLabel.text];
    
    self.divideLine.hidden = relatedData.hideDivideLine;
    [[self.playIcon viewWithTag:10000] setHidden:YES];
    self.playIcon.layer.shadowColor = XMI_COLOR_RGBA(0x000000, 0.16f).CGColor;
    self.playIcon.layer.shadowRadius = XMIAdPic(4.0f);
    self.playIcon.layer.shadowOffset = CGSizeMake(XMIAdPic(2.0f), XMIAdPic(-2.0f));
    [self p_refreshLayout];
}

- (void)p_refreshLayout {
    XMIAdRelatedData *relatedData = self.relatedData;
    
    CGFloat coverViewY = kCoverTop_C;
    CGFloat coverViewW = kCoverSize_C;
    CGFloat coverViewH = kCoverSize_C;
    CGFloat coverViewX = kHomeSingleStyleMargins_C;
    self.coverView.frame = CGRectMake(coverViewX, coverViewY, coverViewW, coverViewH);
    CGFloat playIconW = kPlayIconSize_C;
    CGFloat playIconH = kPlayIconSize_C;
    self.playIcon.size = CGSizeMake(playIconW, playIconH);
    self.playIcon.center = CGPointMake(coverViewW * 0.5f, coverViewH * 0.5f);
    self.playIconImageView.frame = self.playIcon.bounds;
    self.playIconImageView.autoresizingMask = UIViewAutoresizingNone;
    [[[[self.playIcon subviews] firstObject] layer] setCornerRadius:kPlayIconSize_C * 0.5f];
    CGFloat ellipseWidth = ceil(kHomeRatioSize(82));
    CGFloat ellipseHeight = kCoverSize_C + kHomeRatioSize(2);
    _ellipseImageView.frame = CGRectMake(self.coverView.left, self.coverView.top - kHomeRatioSize(2), ellipseWidth, ellipseHeight);
    _coverImageMask.frame = self.coverView.bounds;
    _borderImageView.frame = self.coverView.bounds;
    _trackBorderView.size = CGSizeMake(coverViewW + kHomeRatioSize(4), coverViewH + kHomeRatioSize(4));
    _trackBorderView.layer.cornerRadius = _trackBorderView.size.width * 0.5f;
    _trackBorderView.center = self.coverView.center;
    CGFloat titleLabelY = coverViewY;
    CGFloat titleLabelX = kHomeSingleTitleX_C;
    
    CGFloat titleLabelW = MAX(
                              ceilf(relatedData.titleTextLayout.textBoundingSize.width),
                              XMSCREEN_WIDTH - kHomeSingleTitleX_C - kTitleRightMargin_C
                              );;
    CGFloat titleLabelH = relatedData.titleTextLayout.textBoundingSize.height;
    self.titleLabel.frame = CGRectMake(titleLabelX, titleLabelY - kHomeRatioSize(3), titleLabelW, titleLabelH);
    
 
    CGFloat scoreY = titleLabelY + titleLabelH + kHomeInfoTop_C;
    CGFloat avatarViewY = titleLabelY + titleLabelH + kHomeAvatarTopSpace_C;
   
    if (!self.subTitleLabel.hidden) {
        CGFloat subTitleLabelX = titleLabelX;
        CGFloat subTitleLabelY = titleLabelY + titleLabelH + kReasonTop_C;
       
        CGFloat subTitleLabelW = XMI_SCREEN_WIDTH - subTitleLabelX - kHomeTitle2Cover_C;
        CGFloat subTitleLabelH = kReasonHeight_C;
        self.subTitleLabel.frame = CGRectMake(subTitleLabelX, subTitleLabelY, subTitleLabelW, subTitleLabelH);
        
        scoreY = subTitleLabelY + subTitleLabelH + kHomeInfoTop_C;
        avatarViewY = subTitleLabelY + subTitleLabelH + kHomeAvatarTopSpace_C;
    }
    if (!self.subTitleLabel2.hidden) {
        CGFloat subTitleLabelX = titleLabelX;
        CGFloat subTitleLabelY =  self.subTitleLabel.hidden ? (titleLabelY + titleLabelH + kReasonTop_C) : self.subTitleLabel.bottom + kReasonTop_C;
        CGFloat subTitleLabelW = XMI_SCREEN_WIDTH - subTitleLabelX - kHomeTitle2Cover_C;
        CGFloat subTitleLabelH = kReasonHeight_C;
        self.subTitleLabel2.frame = CGRectMake(subTitleLabelX, subTitleLabelY, subTitleLabelW, subTitleLabelH);
        avatarViewY = subTitleLabelY + subTitleLabelH + kHomeAvatarTopSpace_C;
    }
//    if (!self.scoreImageView.hidden) {
//        CGFloat subTitleLabelY = titleLabelY + titleLabelH + kReasonTop_C;
//        CGFloat subTitleLabelH = kReasonHeight_C;
//        CGFloat scoreImageViewW = kHomeInfoIconSize_C;
//        self.scoreImageView.xmi_size = CGSizeMake(scoreImageViewW, scoreImageViewW);
//        self.scoreImageView.xmi_left = titleLabelX;
//        self.scoreImageView.xmi_centerY = subTitleLabelY + subTitleLabelH * 0.5f;
//        [self.scoreLabel sizeToFit];
//        self.scoreLabel.xmi_left = self.scoreImageView.xmi_right + kHomeRatioSize(3.0f);
//        self.scoreLabel.xmi_centerY = self.scoreImageView.xmi_centerY;
//        self.scoreDivideLine.xmi_size = CGSizeMake(1, kHomeRatioSize(8));
//        self.scoreDivideLine.xmi_left = self.scoreLabel.xmi_right + kHomeRatioSize(8);
//        self.scoreDivideLine.xmi_centerY = self.scoreLabel.xmi_centerY;
//        if (!self.subTitleLabel.hidden) {
//            CGFloat subTitleLabelX = self.scoreDivideLine.xmi_right + kHomeRatioSize(8);
//            CGFloat subTitleLabelW = XMI_SCREEN_WIDTH - subTitleLabelX - kHomeTitle2Cover_C;
//            self.subTitleLabel.xmi_left = subTitleLabelX;
//            self.subTitleLabel.xmi_width = subTitleLabelW;
//        }
//    }
    
    CGFloat avatarViewX = titleLabelX;
    CGFloat avatarViewW = kHomeAvatarSize_C;
    CGFloat avatarViewH = kHomeAvatarSize_C;
    self.avatarView.layer.cornerRadius = kHomeAvatarSize_C / 2;
    avatarViewY = MAX(avatarViewY, self.xmi_height - kHomeAvatarBottomSpace_C - avatarViewH);
    self.avatarView.frame = CGRectMake(avatarViewX, avatarViewY, avatarViewW, avatarViewH);
    
    CGFloat closeButtonW = kDislikeContainerW;
    CGFloat closeButtonH = kDislikeContainerH;
    CGFloat closeButtonX = self.xmi_width - closeButtonW;
    CGFloat closeButtonY = avatarViewY + (avatarViewH - closeButtonH) / 2;
    self.closeButton.frame = CGRectMake(closeButtonX, closeButtonY, closeButtonW, closeButtonH);
    if (self.relatedData.adMarkStyle == 2) {
        self.closeImageView.size = CGSizeMake(kDislikeImageWStyle0, kDislikeImageWStyle0);
    } else if (self.relatedData.adMarkStyle == 1) {
        self.closeImageView.size = CGSizeMake(kDislikeImageWStyle1, kDislikeImageWStyle1);
    } else {
        self.adMarkButtonView.xmi_right = self.xmi_width - kHomeRatioSize(16);
        self.adMarkButtonView.xmi_centerY = self.closeButton.xmi_centerY;
    }
    self.closeImageView.center = CGPointMake(closeButtonW * 0.5f, closeButtonH * 0.5f);
    self.adMarkButtonView.hidden = self.relatedData.adMarkStyle != 0;
    self.closeButton.hidden = !self.adMarkButtonView.hidden;

    [self.nameLabel sizeToFit];
    CGFloat nameLabelX = avatarViewX + avatarViewW + kHomeAvatarNameGapX;
    CGFloat nameLabelY = avatarViewY;
    CGFloat nameLabelW = MIN(self.nameLabel.xmi_width + 20, closeButtonX - nameLabelX - 10);
    CGFloat nameLabelH = avatarViewH;
    self.nameLabel.frame = CGRectMake(nameLabelX, nameLabelY, nameLabelW, nameLabelH);
    
    CGFloat userButtonX = avatarViewX;
    CGFloat userButtonY = avatarViewY - 2;
    CGFloat userButtonW = nameLabelX + nameLabelW - userButtonX;
    CGFloat userButtonH = avatarViewH + 4;
    self.userButton.frame = CGRectMake(userButtonX, userButtonY, userButtonW, userButtonH);
        
    CGFloat lineX = titleLabelX;
    CGFloat lineH = kOnePixelsLineHeight;
    CGFloat lineY = self.xmi_height - lineH;
    CGFloat lineW = XMI_SCREEN_WIDTH - lineX;
    self.divideLine.frame = CGRectMake(lineX, lineY, lineW, lineH);
}

- (void)refreshWithData:(XMIAdRelatedData *)adData
{
    [self updateFontAndColor];
    [super refreshWithData:adData];
}

- (void)updateFontAndColor
{
    self.nameLabel.textColor = XMI_COLOR_DynamicFromRGBA(0x131313, 0.6f, 0xDCDCDC, 0.6f);
    self.nameLabel.font = XMI_AD_PingFangFont(kHomeRatioSize(12));
    self.subTitleLabel.font = kSingleIntroFont_C;
    self.subTitleLabel.textColor = kSingleIntroColor_C;
    self.subTitleLabel2.font = kSingleIntroFont_C;
    self.subTitleLabel2.textColor = kSingleIntroColor_C;
    self.scoreDivideLine.backgroundColor = kSingleIntroColor_C;
    self.scoreLabel.font = kHomeInfoFont_C;
    self.scoreLabel.textColor = kHomeInfoColor_C;
    self.closeButton.imageView.tintColor = kSingleCloseColor;
}

+ (YYTextLayout *)getTitleTextLayout:(NSString *)title
                         adViewWidth:(CGFloat)adViewWidth
                             isTrack:(BOOL)isTrack
{
    NSMutableAttributedString *str = [[NSMutableAttributedString alloc] initWithString:title?:@""];
    str.yy_font = kSingleTitleFont_C;
    str.yy_color = kSingleTitleColor_C;
    
    CGFloat maxWidth = XMI_SCREEN_WIDTH - kHomeSingleTitleX - kTitleRightMargin;
    YYTextContainer *titleContainer      = [YYTextContainer containerWithSize:(CGSizeMake(maxWidth, 999))];
    titleContainer.maximumNumberOfRows   = 2;
    [titleContainer setTruncationType:(YYTextTruncationTypeEnd)];
    YYTextLayout *layout = [YYTextLayout layoutWithContainer:titleContainer text:str];
    return layout;
}

+ (CGFloat)getAdViewHeight:(XMIAdRelatedData *)adData
{
    id trackInfo = adData.trackInfoMapModel;
    XMIExpressAdTrackInfoMapModel *trackInfoModel;
    if ([trackInfo isKindOfClass:[XMIExpressAdTrackInfoMapModel class]]) {
        trackInfoModel = (XMIExpressAdTrackInfoMapModel *)trackInfo;
    }
    
    CGFloat titleHeight = ceilf(adData.titleTextLayout.textBoundingSize.height);
    
    CGFloat calHeight = kCoverTop_C + titleHeight;
    if (trackInfoModel.subtitle.length > 0) {
        calHeight = calHeight + kReasonTop_C + kReasonHeight_C;
    }
    
    BOOL displaySub2 = NO;
    if ([trackInfoModel.promoteType isEqualToString:@"TRACK"]) {
        if (trackInfoModel.playCount.length || trackInfoModel.duration.length) {
            displaySub2 = YES;
        }
    } else if ([trackInfoModel.promoteType isEqualToString:@"LIVE"]) {
        if (trackInfoModel.hotness.length) {
            displaySub2 = YES;
        }
    }
    
    if (displaySub2) {
        calHeight = calHeight + kHomeInfoTop_C + kHomeInfoHeight_C;
    }
    calHeight = calHeight + kHomeAvatarTopSpace_C + kHomeAvatarSize_C + kHomeAvatarBottomSpace_C;
    CGFloat height = MAX(kCoverTop_C * 2 + kCoverSize_C, calHeight);
    return height;
}

- (void)p_setCloseButtonBackImage:(XMIAdRelatedData *)adData
{
    UIImage *normalImage = nil;
    UIImage *darkImage = nil;
    if (self.relatedData.adMarkStyle == 2) {
        normalImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        darkImage = [[XMICommonUtils imageNamed:@"home_feed_ad_close_dark"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        self.closeImageView.image = [UIImage xm_imageWithLight:normalImage dark:darkImage];
    } else if (self.relatedData.adMarkStyle == 1) {
        normalImage = [[XMICommonUtils imageNamed:@"home_feed_more_ic"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        darkImage = [[XMICommonUtils imageNamed:@"home_feed_more_ic_dark"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        NSURL *markUrl = [NSURL URLWithString:adData.adMark];
        @weakify(self);
        [self.closeImageView sd_setImageWithURL:markUrl placeholderImage:[UIImage xm_imageWithLight:normalImage dark:darkImage] options:SDWebImageRetryFailed completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
            if (image) {
                UIImage *tempImage = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
                weak_self.closeImageView.image = tempImage;
                weak_self.closeImageView.tintColor = kSingleCloseColor;
            }
        }];
    } else {
        if ([XMIAdHelper adDataIsOperation:self.relatedData]) {
            [self.adMarkButtonView updateAdMarkText:@""];
        } else {
            NSURL *markUrl = [NSURL URLWithString:adData.adMark];
            NSURL *darkAdMarkUrl = [NSURL URLWithString:adData.darkAdMark];
            [self.adMarkButtonView updateAdMarkURL:markUrl adMarkDarkURL:darkAdMarkUrl placeholder:[XMICommonUtils imageNamed:@"admark_text"]];
        }
    }
    self.closeImageView.tintColor = kSingleCloseColor;
    ((XMIAdButton *)self.closeButton).hitTestEdgeOutsets = [adData closeAreaPaddingWithDefaultPadding:UIEdgeInsetsMake(7, 7, 7, 7)];
    self.adMarkButtonView.hitTestEdgeOutsets = [adData closeAreaPaddingWithDefaultPadding:UIEdgeInsetsMake(13, 12, 13, 12)];
}

- (void)playerStatusDidChange:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    id trackInfo = self.relatedData.trackInfoMapModel;
    if ([trackInfo isKindOfClass:[XMIExpressAdTrackInfoMapModel class]]) {
        long long trackId = [[userInfo valueForKey:@"trackId"] longLongValue];
        BOOL playing = [[userInfo valueForKey:@"playing"] boolValue];
        
        XMIExpressAdTrackInfoMapModel *trackInfoModel = (XMIExpressAdTrackInfoMapModel *)trackInfo;
        long long promoteId = [trackInfoModel.promoteId longLongValue];
        self.playerPromoteId = trackId;
        self.isplaying = playing;
        if (trackId != promoteId) {
            return;
        }
        if (playing) {
            self.playIconImageView.image = [XMICommonUtils imageNamed:@"btn_pause_btn_n_fill_n_28"];
        } else {
            self.playIconImageView.image = [XMICommonUtils imageNamed:@"btn_play_btn_inside_fill_n_28"];
        }
    }
}

@end
