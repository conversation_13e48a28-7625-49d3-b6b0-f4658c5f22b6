//
//  XMIBUMixFeedCardAdView.m
//  XMAd
//
//  Created by xmly on 2022/8/9.
//

#import "XMIBUMixFeedCardAdView.h"
#import <BUAdSDK/BUNativeExpressAdView.h>
#import <BUAdSDK/BUNativeAd.h>
#import <BUAdSDK/BUNativeAdRelatedView.h>
#import "XMAdDownloadMediaManager.h"
#import "XMIAdRelatedData.h"
#import "XMICommonUtils.h"
#import "XMIAdMacro.h"
#import "XMIBUConverter.h"
#import "XMIAnimatedImageView.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import <XMCategories/UIImage+XMDynamic.h>
#import "UIView+XMIUtils.h"
#import "XMIAdButton.h"
@interface XMIBUMixFeedCardAdView()<BUVideoAdViewDelegate, BUNativeAdDelegate>
@property (nonatomic, strong) BUNativeExpressAdView *adView;
@property (nonatomic, strong) BUNativeAd *adData;
@property (nonatomic, strong) BUNativeAdRelatedView *relatedObject;
@end

@implementation XMIBUMixFeedCardAdView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.clipsToBounds = YES;
        self.relatedObject = [[BUNativeAdRelatedView alloc] init];
    }
    return self;
}

- (void)refreshWithData:(XMIAdRelatedData *)adData {
    if (adData.loadingStatus == XMIAdRelatedLoadingStatusNormal) {
        UIImage *normalImage = [XMICommonUtils imageNamed:@"bkg_mix_feed"];
        UIImage *darkImage = [XMICommonUtils imageNamed:@"bkg_mix_feed_dark"];
        UIImage *placeholderImage = [UIImage xm_imageWithLight:normalImage dark:darkImage];
        self.coverImageView.image = placeholderImage;
        self.adMark.hidden = self.closeButton.hidden = self.btnLabel.hidden = YES;
        return;
    }
    self.relatedData = adData;
    self.adData = (BUNativeAd *)adData.originData;
    if (!self.adData || ![self.adData isKindOfClass:BUNativeAd.class]) {
        return;
    }
    
    self.adMark.hidden = self.closeButton.hidden = self.btnLabel.hidden = NO;
    
    BUNativeAdRelatedView *relatedView = (BUNativeAdRelatedView *)self.relatedObject;
    
    self.adData.delegate = self;

    [self setToAnimationOrigin];
    
    [self p_setCoverImageWithBUData:self.adData];
    self.infoLabel.text = self.videoInfoLabel.text = self.adData.data.AdTitle;
    self.btnLabel.attributedText = [self p_getBtnTextWithImage:[XMICommonUtils imageNamed:@"ad_native_arrow_white"] whiteStyle:YES];
    
    [self p_refreshLayout];
    
    
    UIImage *adMarkImage = relatedView.logoADImageView.image;
    if (!adMarkImage) {
        adMarkImage = [XMICommonUtils imageNamed:@"pic_ad_mark_2"];
    } else {
        self.adMark.image = adMarkImage;
        CGFloat width = 14 * adMarkImage.size.width/adMarkImage.size.height;
        self.adMark.frame = CGRectMake(self.bounds.size.width - width, 0, width, 13);
        self.adMark.xmi_right = self.closeButton.xmi_left - 10;
        self.adMark.xmi_centerY = self.closeButton.xmi_centerY;
    }
    
    
    BUFeedADMode adMode = self.adData.data.imageMode;
    relatedView.videoAdView.hidden = YES;
    [relatedView refreshData:self.adData];
    UIView *buVideoView = relatedView.videoAdView;
    if (buVideoView != nil && self.relatedData.showstyle == XMIAdStyleHomeVideo) {
        relatedView.videoAdView.delegate = self;
        buVideoView.hidden = NO;
        if (!buVideoView.superview) {
            buVideoView.frame = self.contentView.bounds;
            [self.contentView insertSubview:buVideoView belowSubview:self.greyView];
        }
    }
        
    [self.adData registerContainer:self withClickableViews:@[self]];
    [self detectExpose:YES];
}

- (void)p_refreshLayout {
    [super p_refreshLayout];
    
}

- (void)p_setCoverImageWithBUData:(BUNativeAd *)buAd {
    BUImage *buImage = buAd.data.imageAry.firstObject;
    UIImage *normalImage = [XMICommonUtils imageNamed:@"bkg_mix_feed"];
    UIImage *darkImage = [XMICommonUtils imageNamed:@"bkg_mix_feed_dark"];
    UIImage *placeholderImage = [UIImage xm_imageWithLight:normalImage dark:darkImage];
    [self.coverImageView sd_setImageWithURL:[NSURL URLWithString:buImage.imageURL] placeholderImage:placeholderImage completed:nil];
}

- (int)getAdShowType {
    XMIAdShowType showType = XMIAdShowTypeImage;
    if (self.adData == nil) {
        return showType;
    }
    if (self.adData.data.imageMode == BUFeedVideoAdModeImage
        || self.adData.data.imageMode == BUFeedVideoAdModePortrait
        || self.adData.data.imageMode == BUFeedADModeSquareVideo) {
        showType = XMIAdShowTypeVideo;
    }
    else if (self.adData.data.imageMode == BUFeedADModeLargeImage) {
        showType = XMIAdShowTypeImage;
    } else if (self.adData.data.imageMode == BUFeedADModeGroupImage) {
        showType = XMIAdShowTypeImage;
    }
    return showType;
}


- (void)startSetUpUIWithModel:(id)model
{
    
    BUNativeAd *nativeAd = nil;
    if ([model isKindOfClass:BUNativeAd.class]) {
        nativeAd = (BUNativeAd *)model;
    }
    if (!nativeAd) {
        return;
    }
    
}

- (NSAttributedString *)p_getBtnTextWithImage:(UIImage *)image whiteStyle:(BOOL)isWhiteStyle {
    BUNativeAd *buDate = self.adData;
    NSString *btnText = buDate.data.buttonText;
    if (btnText.length > 4) {
        btnText = [NSString stringWithFormat:@"%@", [btnText substringWithRange:NSMakeRange(0, 4)]];
    }
    NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:btnText.length > 0 ? btnText : @"了解详情"];
    
    NSTextAttachment *spaceAttach = [[NSTextAttachment alloc] init];
    spaceAttach.bounds = CGRectMake(0, 0, 2, 0);
    NSAttributedString *spaceAttachAttr = [NSAttributedString attributedStringWithAttachment:spaceAttach];
    [attributedStr appendAttributedString:spaceAttachAttr];
    [attributedStr addAttributes:@{NSForegroundColorAttributeName:isWhiteStyle ? XMI_COLOR_RGB(0xFFFFFF) : XMI_COLOR_RGB(0xFC5832)} range:NSMakeRange(0, attributedStr.length)];
    
    NSTextAttachment *attach = [NSTextAttachment new];
    attach.image = image;
    attach.bounds = CGRectMake(0, -1, attach.image.size.width, attach.image.size.height);
    NSAttributedString *str = [NSAttributedString attributedStringWithAttachment:attach];
    [attributedStr appendAttributedString:str];
    [attributedStr addAttributes:@{NSFontAttributeName:XMI_AD_PingFangFont(13)} range:NSMakeRange(0, attributedStr.length)];
    return attributedStr;
}


#pragma mark - BUNativeAdDelegate
- (void)nativeAdDidLoad:(BUNativeAd *)nativeAd view:(UIView *_Nullable)view {
    XMILog(@"nativeAdDidLoad:view:");
}

- (void)nativeAd:(BUNativeAd *)nativeAd didFailWithError:(NSError *_Nullable)error {
    XMILog(@"nativeAd:didFailWithError:");
}

- (void)nativeAdDidBecomeVisible:(BUNativeAd *)nativeAd {
    XMILog(@"nativeAdDidBecomeVisible");
    [self adViewWillShow];
}

- (void)nativeAdDidCloseOtherController:(BUNativeAd *)nativeAd interactionType:(BUInteractionType)interactionType {
    XMILog(@"nativeAdDidCloseOtherController");
    [self adViewDetailControllerDidClosed];
}

- (void)nativeAdDidClick:(BUNativeAd *)nativeAd withView:(UIView *_Nullable)view {
    XMILog(@"nativeAdDidClick:withView:");
    [self adViewDidClick:view];
}

- (void)nativeAd:(BUNativeAd *_Nullable)nativeAd dislikeWithReason:(NSArray<BUDislikeWords *> *_Nullable)filterWords {
    XMILog(@"nativeAd:dislikeWithReason:");
    NSMutableArray *reasonArray = [[NSMutableArray alloc] init];
    for (int i = 0; i < filterWords.count; i++) {
        BUDislikeWords *dWord = filterWords[i];
        if (dWord.name != nil) {
            [reasonArray addObject:dWord.name];
        }
    }
    [self adViewDislikeWithReason:reasonArray];
}

- (void)nativeAd:(BUNativeAd *_Nullable)nativeAd adContainerViewDidRemoved:(UIView *)adContainerView {
    [self adViewDidRemoved];
}

#pragma mark - BUVideoAdViewDelegate
- (void)videoAdView:(BUVideoAdView *)videoAdView didLoadFailWithError:(NSError *_Nullable)error {
    XMILog(@"BUVideoAdView:didLoadFailWithError:");
}

- (void)playerReadyToPlay:(BUVideoAdView *)videoAdView {
    XMILog(@"playerReadyToPlay");
}

- (void)videoAdView:(BUVideoAdView *)videoAdView stateDidChanged:(BUPlayerPlayState)playerState {
    XMILog(@"videoAdView:stateDidChanged:");
    XMIPlayerPlayState state = [XMIBUConverter playStateFromBUPlayState:playerState];
    [self adViewPlayerStateChanged:state];
}

- (void)playerDidPlayFinish:(BUVideoAdView *)videoAdView {
    XMILog(@"playerDidPlayFinish");
    [self adViewPlayerDidPlayFinish:nil];
}

- (void)videoAdViewDidClick:(BUVideoAdView *)videoAdView {
    XMILog(@"videoAdViewDidClick");
    [self adViewDidClick:videoAdView.superview];
}

- (void)videoAdViewFinishViewDidClick:(BUVideoAdView *)videoAdView {
    XMILog(@"videoAdViewFinishViewDidClick");
}

- (void)videoAdViewDidCloseOtherController:(BUVideoAdView *)videoAdView interactionType:(BUInteractionType)interactionType {
    XMILog(@"videoAdViewDidCloseOtherController:interactionType:");
    [self adViewDetailControllerDidClosed];
}

- (void)dealloc
{
    _relatedObject = nil;
}
@end
