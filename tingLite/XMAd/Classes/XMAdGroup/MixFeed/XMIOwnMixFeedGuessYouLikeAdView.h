//
//  XMIExpressAdMixGuessYouLikeView.h
//  XMAd
//
//  Created by xmly on 2022/5/13.
//

#import "XMIExpressAdView.h"

@class XMIAdModel;

NS_ASSUME_NONNULL_BEGIN

@class XMIAnimatedImageView,XMIAdButton,YYLabel;
@interface XMIOwnMixFeedGuessYouLikeAdView : XMIExpressAdView

@property (nonatomic, strong) XMIAdButton *closeButton;
@property (nonatomic, strong) UIButton *userButton;

@property (nonatomic, strong) XMIAnimatedImageView *coverView;
@property (nonatomic, strong) XMIAnimatedImageView *avatarView;

@property (nonatomic, strong) UIView *playIcon;
@property (nonatomic, strong) UIImageView *playIconImageView;

@property (nonatomic, strong) YYLabel *titleLabel;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) UILabel *subTitleLabel;
@property (nonatomic, strong) UILabel *subTitleLabel2;
@property (nonatomic, strong) UIView *divideLine;
@property (nonatomic, strong) UIView *scoreDivideLine;
@property (nonatomic, strong) UIImageView *tagImageView;

@property (nonatomic, strong) UIImageView *scoreImageView;
@property (nonatomic, strong) UILabel *scoreLabel;

// 封面按钮播放状态，辅助字段
@property (nonatomic, assign) long long playerPromoteId;
@property (nonatomic, assign) BOOL isplaying;

/// 刷新adModel的size等
+ (void)startRefreshSizeWithAdModel:(XMIAdModel *)adModel;

- (void)setupCoverImageWithData:(XMIAdRelatedData *)relatedData;

- (void)startSetUpUIWithModel:(id)model;

@end

@interface XMIOwnMixFeedGuessYouLikeAdViewB : XMIOwnMixFeedGuessYouLikeAdView

@end

@interface XMIOwnMixFeedGuessYouLikeAdViewC : XMIOwnMixFeedGuessYouLikeAdView

@end

///请不要在这里新增猜你喜欢view by xiaodong
NS_ASSUME_NONNULL_END
