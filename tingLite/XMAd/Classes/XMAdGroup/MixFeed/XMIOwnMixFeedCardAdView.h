//
//  XMIOwnMixFeedCardAdView.h
//  XMAd
//
//  Created by xmly on 2022/5/17.
//

#import "XMIExpressAdView.h"
@class XMIAdNewVideoPlayer, XMIAnimatedImageView, XMIAdButton;
typedef NS_ENUM(NSInteger, XMIOwnMixFeedCardAdViewType){
    XMHomeRecAdCellModelTypeDefault = 0,
    XMIOwnMixFeedCardAdViewTypeGuessYouLike = 1,
    XMIOwnMixFeedCardAdViewTypeSmallCard = 2,
    XMIOwnMixFeedCardAdViewTypeLargeCard = 3
};

NS_ASSUME_NONNULL_BEGIN

@interface XMIOwnMixFeedCardAdView : XMIExpressAdView
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) XMIAnimatedImageView *coverImageView;
@property (nonatomic, strong) UILabel *infoLabel;
@property (nonatomic, strong) UILabel *videoInfoLabel;
@property (nonatomic, strong) UILabel *btnLabel;
@property (nonatomic, strong) UIImageView *adMark;
@property (nonatomic, strong) XMIAdButton *closeButton;
@property (nonatomic, strong, nullable) XMIAdNewVideoPlayer *videoPlayer;
@property (nonatomic, strong) UIView *videoView;
@property (nonatomic, strong) UIImageView *bottomMask;
@property (nonatomic, strong) UIView *greyView;

- (void)setToAnimationOrigin;
- (void)p_refreshLayout;

+ (void)startRefreshSizeWithRelatedData:(XMIAdRelatedData *)relatedData
                            adViewWidth:(CGFloat)adViewWidth;
@end

NS_ASSUME_NONNULL_END
