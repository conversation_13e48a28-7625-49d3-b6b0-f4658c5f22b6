//
//  XMIGDTMixFeedCardAdView.m
//  XMAd
//
//  Created by xmly on 2022/8/17.
//

#import "XMIGDTMixFeedCardAdView.h"
#import <GDTMobSDK/GDTUnifiedNativeAdDataObject.h>
#import <GDTMobSDK/GDTUnifiedNativeAdView.h>
#import "XMAdDownloadMediaManager.h"
#import "XMIAdRelatedData.h"
#import "XMICommonUtils.h"
#import "XMIAdMacro.h"
#import "XMIBUConverter.h"
#import "XMIAnimatedImageView.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import <XMCategories/UIImage+XMDynamic.h>
#import "UIView+XMIUtils.h"
#import "XMIAdButton.h"
#import "XMIGDTConverter.h"
@interface XMIGDTMixFeedCardAdView()<GDTUnifiedNativeAdViewDelegate, GDTMediaViewDelegate>

@property (nonatomic, strong) GDTUnifiedNativeAdDataObject *adData;
@property (nonatomic, strong) GDTUnifiedNativeAdView *relatedView;
@property (nonatomic, assign) BOOL isUnClicked;
@end

@implementation XMIGDTMixFeedCardAdView


- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.clipsToBounds = YES;
        self.relatedView = [[GDTUnifiedNativeAdView alloc] initWithFrame:self.bounds];
        [self.contentView insertSubview:self.relatedView aboveSubview:self.coverImageView];
        self.relatedView.delegate = self;
    }
    return self;
}

- (void)refreshWithData:(XMIAdRelatedData *)adData {
    if (adData.loadingStatus == XMIAdRelatedLoadingStatusNormal) {
        UIImage *normalImage = [XMICommonUtils imageNamed:@"bkg_mix_feed"];
        UIImage *darkImage = [XMICommonUtils imageNamed:@"bkg_mix_feed_dark"];
        UIImage *placeholderImage = [UIImage xm_imageWithLight:normalImage dark:darkImage];
        self.coverImageView.image = placeholderImage;
        self.adMark.hidden = self.closeButton.hidden = self.btnLabel.hidden = YES;
        return;
    }
    self.relatedData = adData;
    self.adData = (GDTUnifiedNativeAdDataObject *)adData.originData;
    if (!self.adData || ![self.adData isKindOfClass:GDTUnifiedNativeAdDataObject.class]) {
        return;
    }
    
    self.adMark.hidden = self.closeButton.hidden = self.btnLabel.hidden = NO;
    

    
    self.relatedView.viewController = adData.rootViewController;
    [self.relatedView unregisterDataObject];

    [self setToAnimationOrigin];
    
    
    UIImage *normalImage = [XMICommonUtils imageNamed:@"bkg_mix_feed"];
    UIImage *darkImage = [XMICommonUtils imageNamed:@"bkg_mix_feed_dark"];
    UIImage *placeholderImage = [UIImage xm_imageWithLight:normalImage dark:darkImage];
    [self.adData bindImageViews:@[self.coverImageView] placeholder:placeholderImage];
    
    
    self.infoLabel.text = self.videoInfoLabel.text = self.adData.title;
    self.btnLabel.attributedText = [self p_getBtnTextWithImage:[XMICommonUtils imageNamed:@"ad_native_arrow_white"] whiteStyle:YES];
    
    [self p_refreshLayout];
    
    self.relatedView.mediaView.hidden = YES;
    
    UIView *gdtVideoView = self.relatedView.mediaView;
    if (gdtVideoView != nil && self.relatedData.showstyle == XMIAdStyleHomeVideo) {
        self.relatedView.mediaView.delegate = self;
        gdtVideoView.hidden = NO;
        if (!gdtVideoView.superview) {
            gdtVideoView.frame = self.contentView.bounds;
            [self.contentView insertSubview:gdtVideoView belowSubview:self.greyView];
        }
    }
    
    [self.relatedView registerDataObject:self.adData clickableViews:@[self]];
    
    UIView *logoView = self.relatedView.logoView;
    if (logoView) {
        CGFloat logoWidth = 13 * kGDTLogoImageViewDefaultWidth/kGDTLogoImageViewDefaultHeight;
        logoView.frame = CGRectMake(0, 0, logoWidth, 13);
        logoView.xmi_right = self.closeButton.xmi_left - 10;
        logoView.xmi_centerY = self.closeButton.xmi_centerY;
        if (logoView.superview) {
            [self.contentView addSubview:logoView];
        }
    }
    
    
    [self detectExpose:YES];
}

- (void)p_refreshLayout {
    [super p_refreshLayout];
    
}

- (int)getAdShowType {
    XMIAdShowType showType = XMIAdShowTypeImage;
    if (self.adData == nil) {
        return showType;
    }
    if (self.adData.isVideoAd) {
        showType = XMIAdShowTypeVideo;
    }
    else{
        showType = XMIAdShowTypeImage;
    }
    return showType;
}



- (NSAttributedString *)p_getBtnTextWithImage:(UIImage *)image whiteStyle:(BOOL)isWhiteStyle {
    NSString *btnText = self.adData.buttonText;
    if (btnText.length > 4) {
        btnText = [NSString stringWithFormat:@"%@", [btnText substringWithRange:NSMakeRange(0, 4)]];
    }
    NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:btnText.length > 0 ? btnText : @"了解详情"];
    
    NSTextAttachment *spaceAttach = [[NSTextAttachment alloc] init];
    spaceAttach.bounds = CGRectMake(0, 0, 2, 0);
    NSAttributedString *spaceAttachAttr = [NSAttributedString attributedStringWithAttachment:spaceAttach];
    [attributedStr appendAttributedString:spaceAttachAttr];
    [attributedStr addAttributes:@{NSForegroundColorAttributeName:isWhiteStyle ? XMI_COLOR_RGB(0xFFFFFF) : XMI_COLOR_RGB(0xFC5832)} range:NSMakeRange(0, attributedStr.length)];
    
    NSTextAttachment *attach = [NSTextAttachment new];
    attach.image = image;
    attach.bounds = CGRectMake(0, -1, attach.image.size.width, attach.image.size.height);
    NSAttributedString *str = [NSAttributedString attributedStringWithAttachment:attach];
    [attributedStr appendAttributedString:str];
    [attributedStr addAttributes:@{NSFontAttributeName:XMI_AD_PingFangFont(13)} range:NSMakeRange(0, attributedStr.length)];
    return attributedStr;
}

#pragma mark - GDTUnifiedNativeAdViewDelegate
/**
 广告曝光回调

 @param unifiedNativeAdView GDTUnifiedNativeAdView 实例
 */
- (void)gdt_unifiedNativeAdViewWillExpose:(GDTUnifiedNativeAdView *)unifiedNativeAdView {
    XMILog(@"gdt_unifiedNativeAdViewWillExpose");
}

/**
 广告点击回调

 @param unifiedNativeAdView GDTUnifiedNativeAdView 实例
 */
- (void)gdt_unifiedNativeAdViewDidClick:(GDTUnifiedNativeAdView *)unifiedNativeAdView {
    XMILog(@"gdt_unifiedNativeAdViewDidClick");
    if (!self.isUnClicked) {
        self.isUnClicked = YES;
        __weak typeof(self) weakSelf = self;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            weakSelf.isUnClicked = NO;
            [weakSelf adViewDidClick:unifiedNativeAdView.superview];
        });
    }
}

/**
 广告详情页关闭回调

 @param unifiedNativeAdView GDTUnifiedNativeAdView 实例
 */
- (void)gdt_unifiedNativeAdDetailViewClosed:(GDTUnifiedNativeAdView *)unifiedNativeAdView {
    XMILog(@"gdt_unifiedNativeAdDetailViewClosed");
    [self adViewDetailControllerDidClosed];
    
}

/**
 当点击应用下载或者广告调用系统程序打开时调用
 
 @param unifiedNativeAdView GDTUnifiedNativeAdView 实例
 */
- (void)gdt_unifiedNativeAdViewApplicationWillEnterBackground:(GDTUnifiedNativeAdView *)unifiedNativeAdView {
    XMILog(@"gdt_unifiedNativeAdViewApplicationWillEnterBackground");
}

/**
 广告详情页面即将展示回调

 @param unifiedNativeAdView GDTUnifiedNativeAdView 实例
 */
- (void)gdt_unifiedNativeAdDetailViewWillPresentScreen:(GDTUnifiedNativeAdView *)unifiedNativeAdView {
    XMILog(@"gdt_unifiedNativeAdDetailViewWillPresentScreen");
    [self adViewWillPresentScreen];
}

/**
 视频广告播放状态更改回调

 @param unifiedNativeAdView GDTUnifiedNativeAdView 实例
 @param status 视频广告播放状态
 @param userInfo 视频广告信息
 */
- (void)gdt_unifiedNativeAdView:(GDTUnifiedNativeAdView *)unifiedNativeAdView playerStatusChanged:(GDTMediaPlayerStatus)status userInfo:(NSDictionary *)userInfo {
    XMILog(@"gdt_unifiedNativeAdView:playerStatusChanged:");
    XMIPlayerPlayState state = [XMIGDTConverter playStateFromGDTPlayState:status];
    [self adViewPlayerStateChanged:state];
}

#pragma mark - GDTMediaViewDelegate
/**
 用户点击 MediaView 回调，当 GDTVideoConfig userControlEnable 设为 YES，用户点击 mediaView 会回调。
 
 @param mediaView 播放器实例
 */
- (void)gdt_mediaViewDidTapped:(GDTMediaView *)mediaView {
    XMILog(@"gdt_mediaViewDidTapped");
}

/**
 播放完成回调

 @param mediaView 播放器实例
 */
- (void)gdt_mediaViewDidPlayFinished:(GDTMediaView *)mediaView {
    XMILog(@"gdt_mediaViewDidPlayFinished");
    [self adViewPlayerDidPlayFinish:nil];
}
- (void)dealloc
{
    _relatedView = nil;
}
@end
