//
//  XMIExpressAdTrackInfoMapModel.h
//  XMAd
//
//  Created by xmly on 2022/5/19.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XMIExpressAdTrackInfoMapModel : NSObject

@property(nonatomic, strong) NSString *promoteType;
@property(nonatomic, strong) NSString *promoteId;

@property(nonatomic, strong) NSString *broadcasterId;

@property(nonatomic, strong) NSString *iconUrl;

@property(nonatomic, strong) NSString *title;
@property(nonatomic, strong) NSString *subtitle;

@property(nonatomic, strong) NSString *broadcasterName;
@property(nonatomic, strong) NSString *broadcasterPic;
@property(nonatomic, strong) NSString *broadcasterPicDark;

@property(nonatomic, strong) NSString *playCount;
@property(nonatomic, strong) NSString *subscribeCount;
@property(nonatomic, strong) NSString *hotness;

@property(nonatomic, strong) NSString *albumScore;
@property(nonatomic, strong) NSString *duration;
@end

NS_ASSUME_NONNULL_END
