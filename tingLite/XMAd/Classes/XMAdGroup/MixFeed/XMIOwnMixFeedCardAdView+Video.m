//
//  XMIOwnMixFeedCardAdView+Video.m
//  XMAd
//
//  Created by xmly on 2022/6/22.
//

#import "XMIOwnMixFeedCardAdView+Video.h"
#import "XMIAdNewVideoPlayer.h"
#import "XMIAdDefines.h"
#import "XMIAdMacro.h"
#import "XMIAdError.h"
#import "UIView+XMIUtils.h"
#import "XMIAdRelatedData.h"
#import "XMIAdReportHelper.h"
#import "XMIAnimatedImageView.h"
#import "XMIAdHelper.h"

@interface XMIOwnMixFeedCardAdView ()<XMIAdVideoPlayerDelegate>

@end

@implementation XMIOwnMixFeedCardAdView (Video)

- (XMIAdNewVideoPlayer *)buildVideoPlayer {
    
    XMIAdNewVideoPlayer *videoPlayer = [XMIAdNewVideoPlayer createVideoPlayer];
    videoPlayer.delegate = self;
    videoPlayer.volume = 0.0;
    videoPlayer.repeat = YES;
    videoPlayer.view.alpha = 0.0;
    return videoPlayer;
}

#pragma mark - XMIAdVideoPlayerDelegate
- (void)player:(XMIAdNewVideoPlayer *)player playStateDidChanged:(XMIAdPlayerPlayState)state {
    NSString *identifier = [self.relatedData getIdentifier];
    if (![player.identifier isEqualToString:identifier]) {
        return;
    }
    if (state == XMIAdPlayerStatePlaying) {
        player.view.alpha = 1.0;
    }
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdView:playerStateChanged:)]) {
        [self.delegate expressAdView:self playerStateChanged:(XMIPlayerPlayState)state];
    }
}

- (void)player:(XMIAdNewVideoPlayer *)player failWithError:(NSError *)error {
    NSString *identifier = [self.relatedData getIdentifier];
    if (![player.identifier isEqualToString:identifier]) {
        return;
    }
    [self.videoPlayer stop];
    self.videoView.hidden = YES;
    XMILog(@"-- play fail, %@", error);
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdView:playerDidPlayFinish:)]) {
        [self.delegate expressAdView:self playerDidPlayFinish:error];
    }
}

- (void)player:(XMIAdNewVideoPlayer *)player playTimeDidChanged:(CGFloat)currentTime {
    NSString *identifier = [self.relatedData getIdentifier];
    if (![player.identifier isEqualToString:identifier]) {
        return;
    }
//    XMILog(@"-- progress %.2f", currentTime);
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdView:playTimeDidChanged:)]) {
        [self.delegate expressAdView:self playTimeDidChanged:currentTime];
    }
}

- (void)player:(XMIAdNewVideoPlayer *)player playDidFinish:(NSError *)error {
    NSString *identifier = [self.relatedData getIdentifier];
    if (![player.identifier isEqualToString:identifier]) {
        return;
    }
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdView:playerDidPlayFinish:)]) {
        [self.delegate expressAdView:self playerDidPlayFinish:error];
    }
}

- (void)startVideoToPlay:(XMIAdRelatedData *)relatedData
{

    if (!relatedData.videoUrl) {
        return;
    }
    [XMIAdHelper showDebugMessageWhenPlayVideo:relatedData];
    // 在配置项adAlwaysPlayVideo为false，且(当前在流量下，且无视频缓存时)，不去播放视频
    if (![XMIAdHelper canPlayVideoOnWIFIOrCached:relatedData]) {
        return;
    }
    
    self.videoView.hidden = NO;
    
    NSString *identifier = [relatedData getIdentifier];
    if (![self.videoPlayer.identifier isEqualToString:identifier]) {
        self.videoPlayer.identifier = identifier;
        [self.videoPlayer startPlayWithURL:[NSURL URLWithString:relatedData.videoUrl] playerView:self.videoView];
    }
    self.videoView.frame = self.coverImageView.bounds;
    if (!self.videoPlayer.isPlaying) {
        [self.videoPlayer play];
    }
}

- (void)cleanVideoPlayer
{
    [self.videoPlayer stop];
    self.videoView.hidden = YES;
    self.videoPlayer = nil;
}
@end
