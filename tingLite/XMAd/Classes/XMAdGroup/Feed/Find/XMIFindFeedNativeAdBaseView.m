//
//  XMIFindFeedNativeAdBaseView.m
//  XMAd
//
//  Created by cuiyuanz<PERSON> on 2022/3/1.
//

#import "XMIFindFeedNativeAdBaseView.h"
#import "XMIAdMacro.h"
#import "XMIAdButton.h"
#import "XMICommonUtils.h"
#import "UIView+XMIUtils.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import "XMIAdDataCenter.h"
#import "UIView+XMIUtils.h"
#import "XMIAdShakeTipView.h"

@interface XMIFindFeedNativeAdBaseView ()

@property (nonatomic, strong) XMIAdShakeTipView *shakeTipView;

@end

@implementation XMIFindFeedNativeAdBaseView

#pragma mark - lazy load

- (UILabel *)titleLabel
{
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
    }
    return _titleLabel;
}

- (UILabel *)descLabel
{
    if (!_descLabel) {
        _descLabel = [[UILabel alloc] init];
    }
    return _descLabel;
}




//- (UIButton *)closeButton
//{
//    if (!_closeButton) {
//        XMIAdButton *closeButton = [[XMIAdButton alloc] initWithFrame:CGRectMake(0, 0, 30, 30)];
//        closeButton.hitTestEdgeOutsets = UIEdgeInsetsMake(10, 10, 10, 10);
//        _closeButton = closeButton;
//        [_closeButton addTarget:self action:@selector(closeButtonClicked:) forControlEvents:UIControlEventTouchUpInside];
//        [_closeButton setImage:[XMICommonUtils imageNamed:@"ad_can_close_find"] forState:UIControlStateNormal];
//        [_closeButton sizeToFit];
//        [self.contentView addSubview:_closeButton];
//        closeButton.accessibilityLabel = @"关闭";
//    }
//    return _closeButton;
//}

- (UIImageView *)coverImageView
{
    if (!_coverImageView) {
        _coverImageView = [[UIImageView alloc] init];
        [self.contentView insertSubview:_coverImageView atIndex:0];
        //需要动态切换，改色需要同步下面👇🏻 traitCollectionDidChange 方法
        _coverImageView.layer.borderColor = XMI_COLOR_DynamicFromRGB(0xe8e8e8, 0x272727).CGColor;
        _coverImageView.layer.borderWidth = kADOnePixelsLineHeight;
    }
    return _coverImageView;
}

- (XMIAdMarkButtonView *)adMarkButtonView {
    if (!_adMarkButtonView) {
        _adMarkButtonView = [[XMIAdMarkButtonView alloc] initWithType:XMIAdMarkTypeArrow];
        _adMarkButtonView.accessibilityLabel = @"广告";
        [self.contentView addSubview:_adMarkButtonView];
        @weakify(self)
        _adMarkButtonView.sizeDidChangeAction = ^{
            @strongify(self)
            [self adMarkButtonViewSizeDidChange];
        };
        _adMarkButtonView.clickAction = ^{
            @strongify(self)
            [self adMarkButtonViewDidClick];
        };
    }
    return _adMarkButtonView;
}

- (UILabel *)btnLabel
{
    if (!_btnLabel) {
        _btnLabel = [[UILabel alloc] init];
        _btnLabel.accessibilityTraits = UIAccessibilityTraitButton;
        [self.contentView addSubview:_btnLabel];
    }
    return _btnLabel;
}

#pragma mark - adview protocol

- (void)updateTitle:(NSString *)title
{
    self.titleLabel.text = title;
}

- (void)updateDescription:(NSString *)Description
{
    self.descLabel.text = Description;
}


- (NSArray<UIView *> *)clickableViews
{
    return @[self.contentView];
}

- (UIView *)dislikePointView {
    return self.adMarkButtonView;
}

#pragma mark - internal


- (void)updateImageURLs:(NSArray<NSString *> *)imageURLs
{
    if (imageURLs.count == 0) {
        self.coverImageView.image = [XMICommonUtils imageNamed:@"ad_bkg_default"];
        [self coverImageViewDidLoad];
        return;
    }
    NSString *imageURLString = [imageURLs firstObject];
    if (imageURLString.length == 0) {
        self.coverImageView.image = [XMICommonUtils imageNamed:@"ad_bkg_default"];
        [self coverImageViewDidLoad];
        return;
    }
    @weakify(self)
    [self.coverImageView sd_setImageWithURL:[NSURL URLWithString:imageURLString] placeholderImage:[XMICommonUtils imageNamed:@"ad_bkg_default"] completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
        @strongify(self)
        if (!image || error || ![imageURL.absoluteString isEqualToString:imageURLString]) {
            self.coverImageView.image = [XMICommonUtils imageNamed:@"ad_bkg_default"];
        }
        [self coverImageViewDidLoad];
    }];
}

- (void)updateClosePadding:(UIEdgeInsets)closePaddding
{
    if (!UIEdgeInsetsEqualToEdgeInsets(closePaddding, UIEdgeInsetsZero)) {
        [self.adMarkButtonView setHitTestEdgeOutsets:[XMIAdButton closeAreaPaddingWithDefaultPadding:UIEdgeInsetsMake(10, 10, 10, 10) extraPadding:closePaddding]];
    }
}

#pragma mark - public

- (void)adMarkButtonViewSizeDidChange {
    
}

- (void)adMarkButtonViewDidClick {
    [self closeAdViewClick];
}

- (void)updateCloseAndMarkUI
{

}

- (void)closeButtonClicked:(id)sender
{
    [self closeAdViewClick];
}

- (void)coverImageViewDidLoad
{
    
}

- (BOOL)isVideoAdView
{
    return NO;
}

- (void)dealloc
{
    
}

- (BOOL)supportRealExpose
{
    return YES;
}

- (void)updateShakeEnabled:(BOOL)shakeEnabled
{
    if (shakeEnabled) {
        self.shakeTipView.hidden = NO;
    } else {
        _shakeTipView.hidden = YES;
    }
}

- (UIView *)shakeView
{
    return _shakeTipView;
}

- (XMIAdShakeTipView *)shakeTipView
{
    if (!_shakeTipView) {
        _shakeTipView = [XMIAdShakeTipView shakeTipViewWithTitle:[XMIAdDataCenter adCommonShakeTips]];
        [self.contentView addSubview:_shakeTipView];
    }
    return _shakeTipView;
}

- (void)traitCollectionDidChange:(UITraitCollection *)previousTraitCollection {
    [super traitCollectionDidChange:previousTraitCollection];
    if (@available(iOS 13.0, *)) {
        if ([self.traitCollection hasDifferentColorAppearanceComparedToTraitCollection:previousTraitCollection]) {
            _coverImageView.layer.borderColor = XMI_COLOR_DynamicFromRGB(0xe8e8e8, 0x272727).CGColor;
        }
    }
}

@end
