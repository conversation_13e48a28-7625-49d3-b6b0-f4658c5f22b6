//
//  XMIFindFeedNativeAdBaseCell.m
//  XMAd
//
//  Created by xiaodong2.zhang on 2024/7/24.
//

#import "XMIFindFeedNativeAdBaseCell.h"
#import "XMIFindFeedNativeAdImageListBaseView.h"
#import <XMCommonUtil/XMUtility.h>

@interface XMIFindFeedNativeAdBaseCell ()

@property (nonatomic, strong) UILongPressGestureRecognizer *longPress;

@end

@implementation XMIFindFeedNativeAdBaseCell

- (void)setupGestureRecognizer {
    if (!self.longPress) {
        self.longPress = [[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(longPressAction:)];
        [self.contentView addGestureRecognizer:self.longPress];
    }
}

- (void)longPressAction:(UILongPressGestureRecognizer *)gesture {
    if (gesture.state == UIGestureRecognizerStateBegan) {
        [XMUtility shakeFeedback];
        if (self.animatedDislikeHandler) {
            self.animatedDislikeHandler(self.indexPath);
        }
    }
}

- (void)didReceiveDidScrollNotification:(NSNotification *)notification {
    [self checkExpose];
}

- (void)didMoveToWindow {
    [super didMoveToWindow];
    [self checkExpose];
}

- (void)checkExpose {
    UIView *parentView = self.superview.superview.superview;
    if (parentView) {
        BOOL pause = NO;
        if ([parentView isKindOfClass:[XMIFindFeedNativeAdImageListBaseView class]]) {
            pause = ((XMIFindFeedNativeAdImageListBaseView *)parentView).cellPause;
        }
        if (!self.exposed) {
            self.exposed = !pause && [self xmi_isExposed:parentView radio:0.01f];
        }
        if (self.animatingImage) {
            self.animating = !pause && [self xmi_isExposed:parentView radio:0.5f];
        }
    }
}

- (BOOL)xmi_isExposed:(UIView *)toView
                radio:(CGFloat)radio {
    // 还未添加到window上
    if (!self.superview) {
        return NO;
    }
    // 在后台
    if ([UIApplication sharedApplication].applicationState != UIApplicationStateActive) {
        return NO;
    }
    // 宽或高为0
    int w = (int)self.frame.size.width;
    int h = (int)self.frame.size.height;
    if (w == 0 || h == 0) {
        return NO;
    }
    CGRect rect = [self convertRect:self.bounds toView:toView];

    int toViewHeight = toView.frame.size.width;
    if (toViewHeight == 0) {
        return NO;
    }

    if (-rect.origin.x >= w*(1-radio)) {
        return NO;
    }

    if (rect.origin.x + w*radio >= toViewHeight) {
        return NO;
    }

    return YES;
}

@end
