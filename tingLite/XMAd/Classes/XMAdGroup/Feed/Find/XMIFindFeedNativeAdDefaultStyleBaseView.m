//
//  XMIFindFeedNativeAdDefaultStyleBaseView.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/2/28.
//

#define kBottomHeight [XMIAdHelper isSocialHome] ? kHomeRatioSize(64) : 68

#import "XMIFindFeedNativeAdDefaultStyleBaseView.h"
#import "UIView+XMIUtils.h"
#import "XMICommonUtils.h"
#import "XMIAdSourceLabel.h"
#import "XMIAdMacro.h"
#import "XMIAdHelper.h"
#import <XMUIKit/XMUIKit.h>
#import <XMCategories/XMCategory.h>
#import "XMIAdManager.h"

#define kContentViewCornerRadius kHomeRatioSize([XMIAdManager findNativeSocialShowStyle] ? 4 : 12)

@interface XMIFindFeedNativeAdDefaultStyleBaseView ()

@property (nonatomic, strong) XMIAdSourceLabel *sourceLabel;

@property (nonatomic, strong) UILabel *tagLabel;

@property (nonatomic, strong) UIImageView *bottomMask;

@property (nonatomic, copy) NSString *btnText;

@property (nonatomic, assign) BOOL hasShowedAnimation;

@property (nonatomic, strong) UIView *greyView;

@property (nonatomic, strong) UIView *bkgView;

@end

@implementation XMIFindFeedNativeAdDefaultStyleBaseView

- (CGFloat)imageHeightWithImageWidth:(CGFloat)width
{
    return [[self class] imageHeightWithImageWidth:width];
}

+ (CGFloat)imageHeightWithImageWidth:(CGFloat)width
{
    CGFloat imageHeight = 0;
    // 1496 * 400
    if([XMICommonUtils isDeviceIPad])
    {
        imageHeight = width * (400.0 / 1496);
    }
    // 1182 * 426
    else
    {
        imageHeight = width * (9 / 16.0);
    }
    return ceil(imageHeight);
}

- (CGFloat)backgroundHeightWithImageWidth:(CGFloat)width
{
    return width*9/16.0;
}

- (void)sizeToFit
{
    UIEdgeInsets edge = self.edge;
    CGFloat imgWidth = self.xmi_width - edge.left - edge.right;
    CGFloat imgHeight = [self imageHeightWithImageWidth:imgWidth];
    CGFloat bkgHeight = [self backgroundHeightWithImageWidth:imgWidth];
    self.contentView.frame = CGRectMake(edge.left, edge.top + kContentViewEdgeTop, imgWidth, bkgHeight);
    self.bkgView.frame = self.contentView.bounds;
    self.coverImageView.frame = CGRectMake(0, 0, imgWidth, imgHeight);
    self.bottomMask.frame = CGRectMake(0, _hasShowedAnimation ? (imgHeight - kBottomHeight) : imgHeight, imgWidth, kBottomHeight);
    self.sourceLabel.bottom = self.coverImageView.bottom;
    self.sourceLabel.left = self.coverImageView.left;
    [self updateCloseAndMarkUI];
    [self updateTextLayout];
    self.xmi_height = self.contentView.xmi_bottom + edge.bottom + kContentViewEdgeTop;
    self.shakeView.centerX = self.coverImageView.centerX;
    self.shakeView.bottom = imgHeight - XMIAdPic(40);
}

- (void)updateTextLayout
{
    CGFloat titleLeftSpace = kHomeRatioSize(10);
    [self.btnLabel sizeToFit];
    self.btnLabel.xmi_width +=  ([XMIAdHelper isSocialHome] ? kHomeRatioSize(16) : 20);
    self.btnLabel.xmi_height =  ([XMIAdHelper isSocialHome] ? kHomeRatioSize(28) : 26);
    self.btnLabel.xmi_right = self.bkgView.xmi_right - ([XMIAdHelper isSocialHome] ? kHomeRatioSize(12) : 10);
    self.btnLabel.xmi_bottom = self.bkgView.xmi_bottom - ([XMIAdHelper isSocialHome] ? kHomeRatioSize(12) : 12);
    self.btnLabel.layer.cornerRadius = self.btnLabel.xmi_height * 0.5f;
    self.btnLabel.layer.masksToBounds = YES;
    [self.titleLabel sizeToFit];
    self.titleLabel.xmi_left = titleLeftSpace;
    if ([self isVideoAdView] && ![XMIAdHelper isSocialHome]) {
        return;
    }
    self.titleLabel.xmi_width = self.btnLabel.xmi_left - titleLeftSpace - 20;
    [self.bottomMask addSubview:self.titleLabel];
    self.titleLabel.center = CGPointMake(self.titleLabel.center.x, self.bottomMask.xmi_top * 0.5f + 23);
    if (self.tagLabel.text.length > 0) {
        self.titleLabel.xmi_top = 26;
        [self.tagLabel sizeToFit];
        self.tagLabel.xmi_width = MIN(self.tagLabel.xmi_width, self.bkgView.xmi_width - titleLeftSpace*2);
        self.tagLabel.xmi_left = titleLeftSpace;
        self.tagLabel.xmi_top = 47;
    } else {
        if ([XMIAdHelper isSocialHome]) {
            self.titleLabel.xmi_centerY = kHomeRatioSize(6) + self.bottomMask.height * 0.5f;
        } else {
            self.titleLabel.xmi_top = 32;
        }
    }
}

- (void)updateCloseAndMarkUI
{
    [super updateCloseAndMarkUI];
    CGFloat closeMargin = [XMIAdHelper isSocialHome] ? kHomeRatioSize(8) : 8;
    CGFloat closeRight = self.contentView.xmi_width - closeMargin;
    self.adMarkButtonView.xmi_right = closeRight;
    self.adMarkButtonView.xmi_top = closeMargin;
}

- (void)updateBtnLabelWithImage:(UIImage *)image whiteStyle:(BOOL)isWhiteStyle {
    if (self.btnText.length > 6) {
        self.btnText = [NSString stringWithFormat:@"%@...", [self.btnText substringWithRange:NSMakeRange(0, 6)]];
    }
    if ([XMIAdHelper isSocialHome]) {
        self.btnLabel.text = self.btnText;
        self.btnLabel.textColor = XMI_COLOR_RGB(0x131313);
        self.btnLabel.font = XMI_AD_PingFangMediumFont(xmUIFont(12));
        return;
    }
    NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:self.btnText.length > 0 ? self.btnText : @"立即查看"];
    
    NSTextAttachment *spaceAttach = [[NSTextAttachment alloc] init];
    spaceAttach.bounds = CGRectMake(0, 0, 2, 0);
    NSAttributedString *spaceAttachAttr = [NSAttributedString attributedStringWithAttachment:spaceAttach];
    [attributedStr appendAttributedString:spaceAttachAttr];
    [attributedStr addAttributes:@{NSForegroundColorAttributeName:isWhiteStyle ? XMI_COLOR_RGB(0xFFFFFF) : XMI_COLOR_RGB(0xFF4444)} range:NSMakeRange(0, attributedStr.length)];
    
    NSTextAttachment *attach = [NSTextAttachment new];
    attach.image = image;
    attach.bounds = CGRectMake(0, -1, attach.image.size.width, attach.image.size.height);
    NSAttributedString *str = [NSAttributedString attributedStringWithAttachment:attach];
    [attributedStr appendAttributedString:str];
    [attributedStr addAttributes:@{NSFontAttributeName:XMI_AD_PingFangFont(12)} range:NSMakeRange(0, attributedStr.length)];
    self.btnLabel.attributedText = attributedStr;
}

- (void)updateCustomUI
{
    [super updateCustomUI];
    self.bkgView.backgroundColor = XMI_COLOR_DynamicFromRGB(0xffffff, 0x1e1e1e);
    if ([self isVideoAdView]  && ![XMIAdHelper isSocialHome]) {
        self.titleLabel.font = XMI_AD_PingFangFont(13);
    } else {
        self.titleLabel.font = XMI_AD_PingFangMediumFont(15);
    }
    self.titleLabel.textColor = XMI_COLOR_RGB(0xFFFFFF);
    self.descLabel.font = [UIFont fontWithName:@"HelveticaNeue" size:12];
    self.descLabel.textColor = XMI_COLOR_DynamicFromRGB(0x999999, 0x888888);
    self.btnLabel.backgroundColor = [XMIAdHelper isSocialHome] ?  XMI_COLOR_RGB(0xFFFFFF) :  XMI_COLOR_RGB(0xFF4444);
    self.btnLabel.textAlignment = NSTextAlignmentCenter;
    self.tagLabel.layer.shadowColor = XMI_COLOR_RGB(0x666666).CGColor;
    self.tagLabel.layer.shadowOpacity = 3;
    self.tagLabel.layer.shadowRadius = 1;
    [self.tagLabel.layer setShadowOffset:CGSizeMake(0, 0)];
    self.adMarkButtonView.adMarkUIMode = [XMIAdHelper isSocialHome] ? XMIAdMarkUIModeSocialHomePage : XMIAdMarkUIModeDark;
    [self.adMarkButtonView setHitTestEdgeOutsets:UIEdgeInsetsMake(8, 14, 18, 8)];
    if ([self isVideoAdView]  && ![XMIAdHelper isSocialHome]) {
        _bottomMask.hidden = YES;
        self.greyView.hidden = NO;
        return;
    }
    self.bottomMask.hidden = NO;
    _greyView.hidden = YES;
    self.clipsToBounds = YES;
    self.descLabel.hidden = YES;
}

- (void)updateBtnText:(NSString *)btnText
{
    if (btnText.length > 4) {
        btnText = [btnText substringToIndex:4];
    }
    if (btnText.length == 0) {
        btnText = @"了解详情";
    }
    self.btnText = btnText;
    [self updateBtnLabelWithImage:[XMICommonUtils imageNamed:@"ad_native_arrow_white"] whiteStyle:YES];
}

- (void)updateAdTags:(NSArray *)tags
{
    if ([XMIAdHelper isSocialHome]) {
        self.tagLabel.text = nil;
        return;
    }
    NSMutableString *tagStr = [[NSMutableString alloc] init];
    [tags enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj isKindOfClass:NSString.class]) {
            NSString *tag = (NSString *)obj;
            if (tag.length > 0) {
                if (tagStr.length > 0) {
                    [tagStr appendString:@" · "];
                }
                [tagStr appendString:tag];
            }
        }
    }];
    self.tagLabel.text = tagStr;
}

- (void)updateHasShowedAnimation:(BOOL)hasShowedAnimation
{
    _hasShowedAnimation = hasShowedAnimation;
    if ([XMIAdHelper isSocialHome]) {
        self.bottomMask.alpha = 1;
        self.bottomMask.xmi_bottom = self.coverImageView.xmi_bottom;
        self.btnLabel.alpha = 1.0f;
        self.titleLabel.alpha = 1.0f;
        return;
    }
    if ([self isVideoAdView]) {
        if (hasShowedAnimation) {
            self.greyView.alpha = 0;
            self.btnLabel.alpha = 1;
            self.titleLabel.alpha = 0;
            self.btnLabel.xmi_bottom = self.bkgView.xmi_bottom - 7;
            self.greyView.xmi_width = self.btnLabel.xmi_width;
            self.greyView.xmi_right = self.btnLabel.xmi_right;
        } else {
            self.btnLabel.alpha = 0;
            self.titleLabel.alpha = 1.0f;
            self.greyView.alpha = 0;
            self.greyView.frame = CGRectMake(10, 0, self.bkgView.xmi_width - 20, ([XMIAdHelper isSocialHome] ? kHomeRatioSize(28) : 26));
        }
    } else {
        if (hasShowedAnimation) {
            self.bottomMask.alpha = 1;
            self.bottomMask.xmi_bottom = self.coverImageView.xmi_bottom;
            self.btnLabel.alpha = 1.0f;
            self.titleLabel.alpha = 0;
        } else {
            self.bottomMask.alpha = 0;
            self.bottomMask.xmi_top = self.coverImageView.xmi_bottom;
            self.btnLabel.alpha = 0;
        }
    }
}

- (void)showAnimation
{
    if ([XMIAdHelper isSocialHome]) {
        [self showAnimationStyleSocial];
        
    } else if ([self isVideoAdView]) {
        [self showAnimationStyleVideo];
    } else {
        [self showAnimationStyleImage];
    }
    if ([self.delegate respondsToSelector:@selector(feedAdViewDidShowAnimation:)]) {
        [self.delegate feedAdViewDidShowAnimation:self];
    }
}

- (void) showAnimationStyleSocial
{
    self.bottomMask.alpha = 1;
    self.bottomMask.xmi_bottom = self.coverImageView.xmi_bottom;
    self.btnLabel.alpha = 1;
}

- (void)showAnimationStyleImage
{
    self.bottomMask.alpha = 0;
    self.bottomMask.xmi_top = self.coverImageView.xmi_bottom;
    self.titleLabel.alpha = 1.0f;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [UIView animateWithDuration:0.25 animations:^{
            self.bottomMask.alpha = 1;
            self.bottomMask.xmi_bottom = self.coverImageView.xmi_bottom;
        } completion:^(BOOL finished) {
            [self showBtnLabel];
            [self performSelector:@selector(hideBottomMark) withObject:nil afterDelay:8.0];
        }];
    });
}

- (void)showBtnLabel {
    [UIView animateWithDuration:0.5 animations:^{
        self.btnLabel.alpha = 1;
    } completion:^(BOOL finished) {
        
    }];
}

- (void)hideBottomMark {
    [UIView animateWithDuration:0.5 animations:^{
        self.bottomMask.alpha = 0;
    } completion:^(BOOL finished) {
        
    }];
}

- (void)showAnimationStyleVideo
{
    [self.contentView insertSubview:self.greyView belowSubview:self.btnLabel];
    [self.greyView addSubview:self.titleLabel];
    self.titleLabel.xmi_width = self.btnLabel.xmi_left - self.greyView.xmi_left - self.titleLabel.xmi_left - ([XMIAdHelper isSocialHome] ? kHomeRatioSize(17) : 5);
    self.titleLabel.xmi_centerY = self.greyView.xmi_height / 2;
    self.titleLabel.alpha = 1.0f;
    self.btnLabel.alpha = 0;
    self.btnLabel.backgroundColor = [UIColor clearColor];
    self.btnLabel.textColor = XMI_COLOR_RGBA(0xFFFFFF, 0.9f);
    self.btnLabel.xmi_bottom = self.bkgView.xmi_bottom - 7;
    
    self.greyView.xmi_bottom = self.bkgView.xmi_bottom;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [UIView animateWithDuration:0.4 animations:^{
            self.greyView.alpha = 1;
            self.greyView.xmi_bottom = self.coverImageView.xmi_bottom - 7;
            
            self.btnLabel.alpha = 1;
        } completion:^(BOOL finished) {
            [self performSelector:@selector(changeBtnLabel) withObject:nil afterDelay:0.3];
            [self performSelector:@selector(hideGreyView) withObject:nil afterDelay:5.0];
        }];
    });
}

- (void)changeBtnLabel {
    [UIView animateWithDuration:0.2 animations:^{
        self.btnLabel.alpha = 0;
    } completion:^(BOOL finished) {
        self.btnLabel.backgroundColor = XMI_COLOR_RGB(0xFF4444);
        self.btnLabel.textColor = XMI_COLOR_RGB(0xFFFFFF);
        [self updateBtnLabelWithImage:[XMICommonUtils imageNamed:@"ad_native_arrow_white"] whiteStyle:YES];
        
        [UIView animateWithDuration:0.3 animations:^{
            self.btnLabel.alpha = 1;
        } completion:^(BOOL finished) {

        }];
    }];
}

- (void)hideGreyView {
    [UIView animateWithDuration:0.3 animations:^{
        self.titleLabel.alpha = 0;
        self.greyView.xmi_width = self.btnLabel.xmi_width;
        self.greyView.xmi_right = self.coverImageView.xmi_width - 10;
    } completion:^(BOOL finished) {
        
    }];
}


- (void)didExposeAdView
{
   [super didExposeAdView];
   if (!_hasShowedAnimation) {
       _hasShowedAnimation = YES;
       [self showAnimation];
   }
}




- (XMIAdSourceLabel *)sourceLabel
{
    if (!_sourceLabel) {
        _sourceLabel = [XMIAdSourceLabel labelWithSoundPatchStyle];
        [self.contentView addSubview:_sourceLabel];
        if (@available(iOS 11.0, *)) {
            _sourceLabel.layer.cornerRadius = 6;
            _sourceLabel.layer.masksToBounds = YES;
            _sourceLabel.layer.maskedCorners = kCALayerMaxXMaxYCorner;
        }
    }
    return _sourceLabel;
}

- (UILabel *)tagLabel
{
    if (!_tagLabel) {
        _tagLabel = [[UILabel alloc] init];
        [self.bottomMask addSubview:_tagLabel];
        _tagLabel.font = XMI_AD_PingFangFont(10);
        _tagLabel.textColor = XMI_COLOR_RGB(0xFFFFFF);
    }
    return _tagLabel;
}

- (UIImageView *)bottomMask
{
    if (!_bottomMask) {
        _bottomMask = [[UIImageView alloc] initWithImage:[self bottomMaskImage]];
        [_bottomMask addSubview:self.titleLabel];
        [_bottomMask addSubview:self.descLabel];
        [_bottomMask addSubview:self.tagLabel];
        [self.coverImageView addSubview:self.bottomMask];
    }
    return _bottomMask;
}

- (UIView *)greyView {
    if (!_greyView) {
        _greyView = [[UIView alloc] initWithFrame:CGRectZero];
        _greyView.backgroundColor = XMI_COLOR_RGBA(0x000000, 0.5f);
        _greyView.layer.cornerRadius = 13;
        _greyView.layer.masksToBounds = YES;
        _greyView.alpha = 0;
    }
    return _greyView;
}

- (void)updateInScreenSource:(XMIInScreenSource)inScreenSource materialProvideSource:(NSString *)materialProvideSource
{
    [self.sourceLabel updateWithInScreenSource:inScreenSource materialProvideSource:materialProvideSource];
}

- (UIImage *)bottomMaskImage {
    
    UIImage *img = [XMICommonUtils imageFromCacheNamed:@"findFeedBottomMask" drawing:^UIImage * _Nonnull{
        CGFloat height = ceil(kBottomHeight);
        UIGraphicsBeginImageContext(CGSizeMake(1, height));
        CAGradientLayer *layer = [[CAGradientLayer alloc] init];
        layer.frame = CGRectMake(0, 0, 1, height);
        layer.colors = @[(__bridge id)XMI_COLOR_RGBA(0x000000, 0).CGColor, (__bridge id)XMI_COLOR_RGBA(0x000000, [XMIAdHelper isSocialHome] ? 0.7f : 0.5f).CGColor];
        layer.startPoint = CGPointMake(0.5, 0);
        layer.endPoint = CGPointMake(0.5, 1);
        [layer renderInContext:UIGraphicsGetCurrentContext()];
        UIImage *i = UIGraphicsGetImageFromCurrentImageContext();
        UIGraphicsEndImageContext();
        return i;
    }];
    return img;
}

- (UIView *)bkgView
{
    if (!_bkgView) {
        _bkgView = [[UIView alloc] initWithFrame:self.contentView.bounds];
        [self.contentView insertSubview:_bkgView atIndex:0];
        self.contentView.layer.cornerRadius = kContentViewCornerRadius;
        self.contentView.clipsToBounds = YES;
    }
    return _bkgView;
}

- (BOOL)isVideoAdView
{
    return NO;
}

@end
