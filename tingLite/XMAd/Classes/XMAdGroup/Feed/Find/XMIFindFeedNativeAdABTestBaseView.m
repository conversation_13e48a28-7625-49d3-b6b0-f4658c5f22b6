//
//  XMIFindFeedNativeAdAvatarStyleBaseView.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON>z<PERSON> on 2023/2/28.
//

#import "XMIFindFeedNativeAdABTestBaseView.h"
#import "XMIAdMacro.h"
#import "XMICommonUtils.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import "UIView+XMIUtils.h"
#import "XMIAdManager.h"
#import "XMIAdSourceLabel.h"

@interface XMIFindFeedNativeAdABTestBaseView ()

@property (nonatomic, assign, readonly, class) XMIAdFindNativeShowStyle showStyle;

@property (nonatomic, strong) UIView *topLine;

@property (nonatomic, strong) UIView *bottomLine;

@property (nonatomic, strong) XMIAdSourceLabel *sourceLabel;

@end

@implementation XMIFindFeedNativeAdABTestBaseView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        
    }
    return self;
}

- (UIImageView *)avatarImageView
{
    if (XMIFindFeedNativeAdABTestBaseView.showStyle != 1) {
        return nil;
    }
    if (!_avatarImageView) {
        _avatarImageView = [[UIImageView alloc] init];
        _avatarImageView.layer.cornerRadius = 4;
        _avatarImageView.layer.masksToBounds = YES;
        [self.contentView addSubview:_avatarImageView];
    }
    return _avatarImageView;
}

- (XMIAdSourceLabel *)sourceLabel
{
    if (!_sourceLabel) {
        _sourceLabel = [XMIAdSourceLabel labelWithSoundPatchStyle];
        [self.contentView addSubview:_sourceLabel];
    }
    return _sourceLabel;
}

- (void)updateIconUrl:(NSString *)iconUrl
{
    if (XMIFindFeedNativeAdABTestBaseView.showStyle != 1) {
        return;
    }
    UIImage *placeholderImage =  [XMICommonUtils imageNamed:@"avatar_default_rectangle"];
    if (iconUrl.length > 0) {
        [self.avatarImageView sd_setImageWithURL:[NSURL URLWithString:iconUrl] placeholderImage:placeholderImage];
    } else {
        self.avatarImageView.image = placeholderImage;
    }
}

- (void)updateBtnText:(NSString *)btnText
{
    if (btnText.length == 0) {
        btnText = @"了解详情";
    }
    self.btnLabel.text = btnText;
}

- (void)updateCustomUI
{
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.descLabel];
    [self.contentView addSubview:self.btnLabel];
    self.titleLabel.textColor = XMIAdUIColor(xmUI_titleColor);
    self.titleLabel.font = XMI_AD_PingFangFont(XMIAdFont(16));
    self.titleLabel.numberOfLines = 1;
    self.btnLabel.font = XMI_AD_PingFangMediumFont(XMIAdFont(12));
    self.btnLabel.textColor = XMI_COLOR_RGB(0xFFFFFF);
    self.btnLabel.backgroundColor = XMIAdUIColor(xmUI_xmRed);
    self.btnLabel.textAlignment = NSTextAlignmentCenter;
    self.descLabel.font = XMI_AD_PingFangFont(XMIAdFont(12));
    self.descLabel.textColor = XMIAdUIColor(xmUI_mediumTextColor);
    self.topLine.backgroundColor = XMIAdUIColor(xmUI_lineColor_2_darkFill_1);
    self.bottomLine.backgroundColor = XMIAdUIColor(xmUI_lineColor_2_darkFill_1);
    self.coverImageView.layer.cornerRadius = 4;
    self.coverImageView.layer.masksToBounds = YES;
    switch (XMIFindFeedNativeAdABTestBaseView.showStyle) {
        case XMIAdFindNativeShowStyleAvatar:
            self.contentView.layer.cornerRadius = 0;
            self.contentView.backgroundColor = [UIColor clearColor];
            self.adMarkButtonView.adMarkUIMode = XMIAdMarkUIModeLight;
            break;
        case XMIAdFindNativeShowStyleBigCard:
            self.contentView.layer.cornerRadius = 6;
            self.contentView.layer.masksToBounds = YES;
            self.contentView.backgroundColor = xmUICBColor(grayBackgroundColor, darkFill_1);
            self.adMarkButtonView.adMarkUIMode = XMIAdMarkUIModeDark;
            break;
            
        default:
            break;
    }
    [self.adMarkButtonView setHitTestEdgeOutsets:UIEdgeInsetsMake(13, 8, 13, 16)];
}

- (void)sizeToFit
{
    [super sizeToFit];
    switch (XMIFindFeedNativeAdABTestBaseView.showStyle) {
        case XMIAdFindNativeShowStyleAvatar:
        {
            self.avatarImageView.xmi_left = self.edge.left;
            self.avatarImageView.xmi_top = self.edge.top + XMIAdHSpace(8);
            self.avatarImageView.xmi_size = CGSizeMake(XMIAdPic(80), XMIAdPic(80));

            [self.titleLabel sizeToFit];
            self.titleLabel.xmi_width = MIN(self.titleLabel.xmi_width, XMI_SCREEN_WIDTH - XMIAdHSpace(142) - self.edge.left - self.edge.right);
            self.titleLabel.xmi_left = self.avatarImageView.xmi_right + XMIAdHSpace(12);
            self.titleLabel.xmi_centerY = xmUIHSpace(8) + self.avatarImageView.top;
            self.adMarkButtonView.xmi_centerY = self.titleLabel.xmi_centerY;
            self.adMarkButtonView.xmi_right = XMI_SCREEN_WIDTH - self.edge.right;
            CGFloat imageWidth = XMI_SCREEN_WIDTH - XMIAdHSpace(92) - self.edge.left - self.edge.right;
            CGFloat imageHeight = imageWidth * 9 / 16;
            self.coverImageView.frame = CGRectMake(self.titleLabel.xmi_left, self.edge.top + XMIAdFont(16) + XMIAdHSpace(18), imageWidth, imageHeight);
            self.btnLabel.xmi_size = CGSizeMake(XMIAdPic(72), XMIAdPic(26));
            self.btnLabel.layer.cornerRadius = self.btnLabel.xmi_height * 0.5f;
            self.btnLabel.layer.masksToBounds = YES;
            self.btnLabel.xmi_top = self.coverImageView.xmi_bottom + XMIAdHSpace(8);
            self.btnLabel.xmi_right = XMI_SCREEN_WIDTH - self.edge.right;
            self.descLabel.xmi_size = CGSizeMake(XMIAdPic(170), XMIAdPic(14));
            self.descLabel.xmi_centerY = self.btnLabel.xmi_centerY;
            self.descLabel.xmi_left = self.coverImageView.xmi_left;
            self.xmi_size = CGSizeMake(XMI_SCREEN_WIDTH, self.btnLabel.xmi_bottom + self.edge.bottom + XMIAdHSpace(8));
            self.topLine.frame = CGRectMake(self.coverImageView.xmi_left, 0, XMI_SCREEN_WIDTH - self.coverImageView.xmi_left, kADOnePixelsLineHeight);
            self.bottomLine.frame = CGRectMake(self.coverImageView.xmi_left, self.xmi_height - kADOnePixelsLineHeight, XMI_SCREEN_WIDTH - self.coverImageView.xmi_left, kADOnePixelsLineHeight);
            self.contentView.frame = self.bounds;
            [self updateCloseAndMarkUI];
        }
            break;
        case XMIAdFindNativeShowStyleBigCard:
        {
            [self.titleLabel sizeToFit];
            self.titleLabel.xmi_width = MIN(self.titleLabel.xmi_width, XMI_SCREEN_WIDTH - XMIAdHSpace(86) - self.edge.left - self.edge.right);
            self.titleLabel.xmi_left = XMIAdHSpace(16);
            self.titleLabel.centerY = XMIAdVSpace(16) + XMIAdFont(8);
            CGFloat imageWidth = XMI_SCREEN_WIDTH - XMIAdHSpace(32) - self.edge.left - self.edge.right;
            CGFloat imageHeight = imageWidth * 9 / 16;
            self.coverImageView.frame = CGRectMake(self.titleLabel.xmi_left, XMIAdFont(16) + XMIAdVSpace(32), imageWidth, imageHeight);
            self.btnLabel.xmi_size = CGSizeMake(XMIAdPic(72), XMIAdPic(26));
            self.btnLabel.layer.cornerRadius = self.btnLabel.xmi_height * 0.5f;
            self.btnLabel.layer.masksToBounds = YES;
            self.btnLabel.xmi_top = self.coverImageView.xmi_bottom + XMIAdHSpace(14);
            self.btnLabel.xmi_right = self.coverImageView.xmi_right;
            self.descLabel.xmi_size = CGSizeMake(XMIAdPic(230), XMIAdPic(14));
            self.descLabel.xmi_centerY = self.btnLabel.xmi_centerY;
            self.descLabel.xmi_left = self.coverImageView.xmi_left;
            self.adMarkButtonView.xmi_centerY = self.titleLabel.xmi_centerY;
            self.adMarkButtonView.xmi_right = self.coverImageView.right;
            self.xmi_size = CGSizeMake(XMI_SCREEN_WIDTH, self.btnLabel.xmi_bottom + XMIAdHSpace(14) + self.edge.top + self.edge.bottom);
            self.contentView.frame = CGRectMake(self.edge.left, self.edge.top, self.width - self.edge.left - self.edge.right, self.height - self.edge.top - self.edge.bottom);
            [self updateCloseAndMarkUI];
        }
            break;
        default:
            break;
    }
    self.shakeView.centerX = self.coverImageView.centerX;
    self.shakeView.bottom = self.coverImageView.bottom - XMIAdPic(15);
    self.sourceLabel.bottom = self.coverImageView.bottom;
    self.sourceLabel.left = self.coverImageView.left;
}

- (void)updateInScreenSource:(XMIInScreenSource)inScreenSource materialProvideSource:(NSString *)materialProvideSource
{
    [self.sourceLabel updateWithInScreenSource:inScreenSource materialProvideSource:materialProvideSource];
}

+ (CGFloat)calAdHeight:(id)adData withAdWidth:(CGFloat)adWidth
{
    switch (XMIFindFeedNativeAdABTestBaseView.showStyle) {
        case XMIAdFindNativeShowStyleAvatar:
            {
                CGFloat imageWidth = adWidth - XMIAdHSpace(92);
                CGFloat imageHeight = imageWidth * 9 / 16;
                CGFloat height = XMIAdFont(16) + XMIAdHSpace(18)+ imageHeight + XMIAdPic(26) + XMIAdHSpace(16);
                return height;
            }
            break;
        case XMIAdFindNativeShowStyleBigCard:
        {
            CGFloat imageWidth = adWidth - XMIAdHSpace(32);
            CGFloat imageHeight = imageWidth * 9 / 16;
            CGFloat height = XMIAdFont(16) + XMIAdHSpace(32)+ imageHeight + XMIAdPic(26) + XMIAdHSpace(28);
            return height;
        }
            break;
            
        default:
            break;
    }
    return 0;
}

+ (XMIAdFindNativeShowStyle)showStyle
{
    return [XMIAdManager findNativeShowStyle];
}

- (UIView *)topLine
{
    if (!_topLine) {
        _topLine = [[UIView alloc] init];
        [self.contentView addSubview:_topLine];
    }
    return _topLine;
}
- (UIView *)bottomLine
{
    if (!_bottomLine) {
        _bottomLine = [[UIView alloc] init];
        [self.contentView addSubview:_bottomLine];
    }
    return _bottomLine;
}

- (void)updateAdTags:(NSArray *)tags
{
    if (tags.count == 0) {
        return;
    }
    NSMutableString *tagStr = [[NSMutableString alloc] init];
    [tags enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj isKindOfClass:NSString.class]) {
            NSString *tag = (NSString *)obj;
            if (tag.length > 0) {
                if (tagStr.length > 0) {
                    [tagStr appendString:@" · "];
                }
                [tagStr appendString:tag];
            }
        }
    }];
    self.descLabel.text = tagStr;
}

@end
