//
//  XMIFindFeedNativeAdImageListBaseView.m
//  XMAd
//
//  Created by xia<PERSON><PERSON><PERSON>.z<PERSON> on 2024/7/10.
//

#import "XMIFindFeedNativeAdImageListBaseView.h"
#import "UIView+XMIUtils.h"
#import "XMIAdSourceLabel.h"
#import "XMIAdHelper.h"
#import "XMIAdError.h"
#import "XMIAdRelatedData.h"
#import "XMIBounceMoreView.h"
#import <XMUIKit/XMUIKit.h>
#import <XMCommonUtil/XMUtility.h>
#import <XMCategories/XMCategory.h>
#import <XMAd/XMIAdMarkButtonView.h>
#import <KVOController/KVOController.h>
#import "XMIFindFeedNativeAdItemCell.h"
#import "XMICommonUtils.h"


#define kMarginLeft 16.f
#define kImageMarginRight 32.f + kHomeRatioSize(16.f)
#define kTitleTop kHomeRatioSize(12.f)
#define kTitleImageSpacing kHomeRatioSize(38.f)
#define kItemCellSpacing kHomeRatioSize(10.f)
#define kImageBottom kHomeRatioSize(10.f)

#define kTitleFont XMI_AD_PingFangSemiboldFont(kHomeRatioSize(16))

@interface XMIFindFeedNativeAdImageListBaseView () <UICollectionViewDelegate,UICollectionViewDataSource>

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UICollectionView *cView;
@property (nonatomic, strong) XMIBounceMoreView *moreView;
@property (nonatomic, strong) XMIAdMarkButtonView *adMarkButtonView;
@property (nonatomic, assign) BOOL triggerMore;

@end

@implementation XMIFindFeedNativeAdImageListBaseView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didBecomeActiveNotification) name:UIApplicationDidBecomeActiveNotification object:nil];
    }
    return self;
}

- (void)sizeToFit {
    self.contentView.frame = CGRectMake(0, kTitleTop + kTitleImageSpacing, XMSCREEN_WIDTH, [self itemCellSize].height);
    self.cView.frame = self.contentView.bounds;

    [self updateTextLayout];
    [self updateCloseAndMarkUI];
    self.xmi_height = self.contentView.xmi_bottom + kImageBottom;
}

- (void)updateCloseAndMarkUI {
    self.adMarkButtonView.xmi_right = self.contentView.xmi_width - 16;
    self.adMarkButtonView.xmi_centerY = self.titleLabel.xmi_centerY;
}

- (void)updateTextLayout {
    [self.titleLabel sizeToFit];
    self.titleLabel.xmi_left = kMarginLeft;
    self.titleLabel.xmi_width = XMSCREEN_WIDTH - kMarginLeft - 70;
    self.titleLabel.xmi_top = kTitleTop;
    [self addSubview:self.titleLabel];
}

- (void)didBecomeActiveNotification {
    if ([self.delegate respondsToSelector:@selector(feedAdViewFrameDidChange:)]) {
        [self.delegate feedAdViewFrameDidChange:self];
    }
}

#pragma mark - lazy load

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
    }
    return _titleLabel;
}

- (XMIAdMarkButtonView *)adMarkButtonView {
    if (!_adMarkButtonView) {
        _adMarkButtonView = [[XMIAdMarkButtonView alloc] initWithType:XMIAdMarkTypeArrow];
        _adMarkButtonView.accessibilityLabel = @"广告";
        [self addSubview:_adMarkButtonView];
        @weakify(self)
        _adMarkButtonView.clickAction = ^{
            @strongify(self)
            [self adMarkButtonViewDidClick];
        };
    }
    return _adMarkButtonView;
}

- (UICollectionView *)cView {
    if (!_cView) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        layout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
        layout.minimumLineSpacing = kItemCellSpacing;
        layout.minimumInteritemSpacing = 0;
        _cView = [[UICollectionView alloc] initWithFrame:CGRectMake(0, kTitleTop + kTitleImageSpacing, XMSCREEN_WIDTH, [self itemCellSize].height) collectionViewLayout:layout];
        _cView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        [_cView registerClass:XMIFindFeedNativeAdBaseCell.class
   forCellWithReuseIdentifier:NSStringFromClass(XMIFindFeedNativeAdBaseCell.class)];
        [_cView registerClass:XMIFindFeedNativeAdItemCell.class
   forCellWithReuseIdentifier:NSStringFromClass(XMIFindFeedNativeAdItemCell.class)];
        
        _cView.delegate = self;
        _cView.dataSource = self;
        _cView.contentInset = UIEdgeInsetsMake(0, kMarginLeft, 0, kImageMarginRight);
        _cView.showsHorizontalScrollIndicator = NO;
        _cView.alwaysBounceHorizontal = YES;
        _cView.backgroundColor = UIColor.clearColor;
        
        XMWeakObject(self)
        [self.KVOController observe:_cView
                            keyPath:@"contentSize"
                            options:(NSKeyValueObservingOptionNew)
                              block:^(id  _Nullable observer,
                                      id  _Nonnull object,
                                      NSDictionary<NSString *,id> * _Nonnull change) {
            XMStrongObject(self)
            [self checkExpose];
            [self refreshMoreLayout];
        }];
        [_cView addSubview:self.moreView];
        [self.contentView addSubview:_cView];
    }
    return _cView;
}

- (XMIBounceMoreView *)moreView {
    if (!_moreView) {
        _moreView = [[XMIBounceMoreView alloc] initWithFrame:CGRectZero];
    }
    return _moreView;
}

#pragma mark - XMIFeedAdViewProtocol

- (void)updateTitle:(NSString *)title {
    self.titleLabel.text = title;
}

- (UIView *)dislikePointView {
    return self.adMarkButtonView;
}

- (void)updateClosePadding:(UIEdgeInsets)closePaddding {
    if (!UIEdgeInsetsEqualToEdgeInsets(closePaddding, UIEdgeInsetsZero)) {
        [self.adMarkButtonView setHitTestEdgeOutsets:[XMIAdButton closeAreaPaddingWithDefaultPadding:UIEdgeInsetsMake(10, 10, 10, 10) extraPadding:closePaddding]];
    }
}

- (void)updateCustomUI {
    [super updateCustomUI];
    self.titleLabel.font = kTitleFont;
    self.titleLabel.textColor = XMI_COLOR_DynamicFromRGB(0x2C2C3C, 0xffffff);
    [self.adMarkButtonView setHitTestEdgeOutsets:UIEdgeInsetsMake(8, 14, 18, 8)];
}

- (BOOL)supportRealExpose {
    return YES;
}

- (void)closeSubAdView:(NSInteger)index  {
    NSMutableArray *items = [self itemCellArray];
    if (items.count <= 1) {
        //异常
        return;
    } else if (index < items.count ) {
        [items removeObjectAtIndex:index];
        [self.cView deleteItemsAtIndexPaths:@[[NSIndexPath indexPathForItem:index inSection:0]]];
        [self refreshMoreLayout];

        dispatch_after(dispatch_time(DISPATCH_TIME_NOW,
                                     (int64_t)(0.5 * NSEC_PER_SEC)),
                       dispatch_get_main_queue(), ^{
            [self.cView reloadData];
        });
    }
}

- (void)pause {
    self.cellPause = YES;
    [self checkExpose];
}

- (void)replay {
    self.cellPause = NO;
    [self checkExpose];
}

- (void)checkExpose {
    NSArray<XMIFindFeedNativeAdBaseCell *> *visibleCells = [self.cView visibleCells];
    [visibleCells enumerateObjectsUsingBlock:^(XMIFindFeedNativeAdBaseCell * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        [obj checkExpose];
    }];
}

#pragma mark - XMIFeedAdViewCustomRenderProtocol

- (void)customRenderWithAdData:(id)adData {
    if (![adData isKindOfClass:[XMIAdRelatedData class]]) {
        [self failRenderWithError:[XMIAdError emptyDataError]];
        return;
    }
    
    self.cellPause = YES;
    self.adData = adData;
    [self.cView reloadData];
    [self refreshMoreLayout];
    
    if ([XMIAdHelper adDataIsOperation:self.adData] && self.adData.compositeAds.count == 0) {
        [self.adMarkButtonView updateAdMarkImage:xmPureImage(@"ic_more_n_n_line_regular_24") size:CGSizeMake(kHomeRatioSize(24), kHomeRatioSize(24))];
        self.adMarkButtonView.adMarkUIMode = XMIAdMarkUIModeLightClear;
        self.adMarkButtonView.adMarkView.tintColor = colorDynamicFromRGBA(0x8D8D91, 0.6f, 0x8D8D91, 0.6f);
        [self.adMarkButtonView updateAdMarkType:XMIAdMarkTypeNone];
    } else {
        [self.adMarkButtonView updateAdMarkType:XMIAdMarkTypeArrow];
        self.adMarkButtonView.adMarkUIMode = XMIAdMarkUIModeWhiteHomePage;
        NSURL *markUrl = [NSURL URLWithString:self.adData.adMark];
        [self.adMarkButtonView updateAdMarkURL:markUrl adMarkDarkURL:markUrl placeholder:[XMICommonUtils imageNamed:@"admark_text"]];
    }
}

// MARK: - UICollectionViewDelegate & UICollectionViewDataSource

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return [self itemCellArray].count;
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    id itemData = [[self itemCellArray] objectMaybeAtIndex:indexPath.item];
    
    XMIFindFeedNativeAdBaseCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([self itemCellClass]) forIndexPath:indexPath];
    cell.adData = self.adData;
    cell.indexPath = indexPath;
    cell.itemData = itemData;
    XMWeakObject(self)
    [cell setAnimatedDislikeHandler:^(NSIndexPath * _Nonnull indexPath) {
        XMStrongObject(self)
        [self closeAdViewClickWithUserInfo:@{@"subAdIndex": @(indexPath.item)}];
    }];
    return cell;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    return [self itemCellSize];
}

- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate {
    BOOL canTriggerMore = self.triggerMore && !self.moreView.hidden && !UIAccessibilityIsVoiceOverRunning();
    if (canTriggerMore) {
        [self tapAdViewWithPoint:CGPointZero userInfo:nil];
    }
}

- (void)refreshMoreLayout {
    CGFloat offsetX = self.cView.contentOffset.x;
    CGFloat cSizeWidth = self.cView.contentSize.width;
    CGFloat scrollWidth = self.cView.width;
    
    CGFloat overSpace = offsetX + scrollWidth - MAX(cSizeWidth, scrollWidth) - self.cView.contentInset.right;
    CGFloat limitOver = (kAdMoreMaxWidth - kAdMoreWidth);
    self.triggerMore = NO;
    
    BOOL beforeStatus = self.moreView.isLookMore;
    
    if (overSpace > limitOver) {
        self.triggerMore = YES;
        self.moreView.isLookMore = YES;
    }
    else {
        self.moreView.isLookMore = NO;
    }
    
    if (!self.moreView.hidden && beforeStatus == NO && self.moreView.isLookMore) {
        [XMUtility shakeFeedback];
    }
 
    CGFloat moreW = MIN(kAdMoreMaxWidth, MAX(kAdMoreWidth + overSpace, kAdMoreWidth));
    CGFloat moreX = MAX(cSizeWidth + self.cView.contentInset.right, offsetX + scrollWidth) - moreW;
    CGFloat moreH = kAdMoreHeight;
    CGFloat moreY = (self.cView.height - moreH) / 2;
    self.moreView.frame = CGRectMake(moreX, moreY, moreW, moreH);
}

#pragma mark - public

- (CGSize)itemCellSize {
    return CGSizeZero;
}

- (Class)itemCellClass {
    return [XMIFindFeedNativeAdBaseCell class];
}

- (NSMutableArray *)itemCellArray {
    return [@[] mutableCopy];
}

- (void)adMarkButtonViewDidClick {
    [self closeAdViewClick];
}

- (void)setMoreViewHidden:(BOOL)hidden {
    self.moreView.hidden = hidden;
    self.cView.contentInset = UIEdgeInsetsMake(0, kMarginLeft, 0, hidden? kMarginLeft : kImageMarginRight);
}

@end
