//
//  XMIFindFeedNativeAdItemCell.m
//  XMAd
//
//  Created by xiaodong2.zhang on 2024/7/24.
//

#import "XMIFindFeedNativeAdItemCell.h"
#import "XMIAdHelper.h"
#import "UIView+XMIUtils.h"
#import "XMIAdMacro.h"
#import "XMIAdRespData.h"
#import "XMIAdRelatedData.h"
#import <XMYYImage/YYWebImage.h>
#import "XMICommonUtils.h"
#import "XMIAdReporter+AD.h"
#import "XMIAdManager.h"

#define kImageTitleFont [XMIAdManager findNativeSocialShowStyle] ? XMI_AD_PingFangMediumFont(kHomeRatioSize(13)) :  XMI_AD_PingFangFont(kHomeRatioSize(14))
#define kImageTitleFont1_1 XMI_AD_PingFangMediumFont(kHomeRatioSize(13))
#define kImageTitleTextColor [XMIAdManager findNativeSocialShowStyle] ? XMI_COLOR_DynamicFromRGB(0x2C2C3C, 0xDCDCDC) : XMI_COLOR_DynamicFromRGB(0x393942, 0xDCDCDC)
#define kTitleLeft 8.f
#define kTitleWithIconLeft 16.f
#define kTitleBottom 8.f
#define kImageCornerRadius kHomeRatioSize([XMIAdManager findNativeSocialShowStyle] ? 4 : 8)

@interface XMIFindFeedNativeAdItemCell ()

@property (nonatomic, strong) YYAnimatedImageView *imageView;
@property (nonatomic, strong) YYAnimatedImageView *iconView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIView *adMaskView;

@end

@implementation XMIFindFeedNativeAdItemCell
@synthesize exposed = _exposed, itemData = _itemData, animating = _animating;

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self configViews];
    }
    return self;
}

- (void)configViews {
    self.layer.cornerRadius = kImageCornerRadius;
    self.clipsToBounds = YES;
}

#pragma mark - lazy load

- (YYAnimatedImageView *)imageView {
    if (!_imageView) {
        _imageView = [[YYAnimatedImageView alloc] initWithFrame:CGRectMake(0, 0, self.xmi_width, self.xmi_height)];
        _imageView.layer.cornerRadius = kImageCornerRadius;
        _imageView.clipsToBounds = YES;
        _imageView.contentMode = UIViewContentModeScaleAspectFill;
        [self insertSubview:_imageView atIndex:0];
    }
    return _imageView;
}

- (YYAnimatedImageView *)iconView {
    if (!_iconView) {
        _iconView = [[YYAnimatedImageView alloc] initWithFrame:CGRectMake(0, 0, kHomeRatioSize(12), kHomeRatioSize(12))];
        _iconView.contentMode = UIViewContentModeScaleAspectFill;
        [self addSubview:_iconView];
    }
    return _iconView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        [self addSubview:_titleLabel];
        _titleLabel.font = kImageTitleFont;
        _titleLabel.textAlignment = NSTextAlignmentLeft;
        _titleLabel.lineBreakMode = NSLineBreakByTruncatingTail;
        _titleLabel.numberOfLines = 1;
    }
    return _titleLabel;
}

- (UIView *)adMaskView {
    if (!_adMaskView) {
        _adMaskView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.xmi_width * 2, 36.f)];
        _adMaskView.userInteractionEnabled = NO;
        CAGradientLayer *gradientLayer = [CAGradientLayer layer];
        gradientLayer.locations = @[@0, @1];
        gradientLayer.startPoint = CGPointMake(0.5, 0);
        gradientLayer.endPoint = CGPointMake(0.5, 1);
        gradientLayer.colors = @[(__bridge id)XMI_COLOR_RGBA(0x000000, 0).CGColor, (__bridge id)XMI_COLOR_RGBA(0x000000, 0.5).CGColor];
        gradientLayer.frame = _adMaskView.bounds;
        [_adMaskView.layer addSublayer:gradientLayer];
        [self insertSubview:_adMaskView belowSubview:self.titleLabel];
    }
    return _adMaskView;
}

#pragma mark - public

- (void)setItemData:(id)itemData {
    _itemData = itemData;
    self.animatingImage = NO;
    self.animating = NO;
    if ([_itemData isKindOfClass:[XMIAdBeanComposite class]]) {
        _exposed = YES;
        [self updateImageURL:((XMIAdBeanComposite *)_itemData).picUrl videoUrl:((XMIAdBeanComposite *)_itemData).videoUrl];
        [self updateTitle:((XMIAdBeanComposite *)_itemData).title iconURL:nil];
    }
    if ([_itemData isKindOfClass:[XMIAdRelatedData class]]) {
        _exposed = ((XMIAdRelatedData *)_itemData).isExposed;
        [self setupGestureRecognizer];
        [self updateImageURL:((XMIAdRelatedData *)_itemData).cover videoUrl:((XMIAdRelatedData *)_itemData).videoUrl];
        [self updateTitle:((XMIAdRelatedData *)_itemData).name iconURL:((XMIAdRelatedData *)_itemData).iconUrl];
    }
}

- (void)updateImageURL:(NSString *)imageURL videoUrl:(NSString *)videoUrl {
    if (imageURL.length == 0 && videoUrl.length == 0) {
        self.imageView.image = nil;
        return;
    }
    
    if (self.adData.showstyle == XMIAdStyleHomeListImage1_1|
        self.adData.showstyle == XMIAdStyleHomeCompositeBanner1_1) {
        self.imageView.size = CGSizeMake(self.xmi_width, self.xmi_width);
        self.titleLabel.font = kImageTitleFont1_1;
    } else {
        self.imageView.size = CGSizeMake(self.xmi_width, self.xmi_height);
        self.titleLabel.font = kImageTitleFont;
    }
    XMWeakObject(self)
    [self.imageView yy_setImageWithURL:[NSURL URLWithString:imageURL] placeholder:[XMICommonUtils imageNamed:@"ad_bkg_default"] options:YYWebImageOptionAvoidSetImage completion:^(UIImage * _Nullable image, NSURL * _Nonnull url, YYWebImageFromType from, YYWebImageStage stage, NSError * _Nullable error) {
        XMStrongObject(self)
        image = image ? : [XMICommonUtils imageNamed:@"ad_bkg_default"];
        if (videoUrl.length) {
            self.animatingImage = YES;
            [self.imageView yy_setImageWithURL:[NSURL URLWithString:videoUrl] placeholder:image options:0 completion:^(UIImage * _Nullable image, NSURL * _Nonnull url, YYWebImageFromType from, YYWebImageStage stage, NSError * _Nullable error) {
                [self checkExpose];
            }];
        } else {
            self.imageView.image = image;
        }
    }];
}

- (void)updateTitle:(NSString *)title iconURL:(NSString *)iconURL {
    self.titleLabel.text = title;
    if (self.adData.showstyle == XMIAdStyleHomeListImage1_1 ||
        self.adData.showstyle == XMIAdStyleHomeCompositeBanner1_1) {
        self.titleLabel.textColor = kImageTitleTextColor;
        self.titleLabel.left = iconURL.length ? kTitleWithIconLeft : 0;
    } else {
        self.titleLabel.textColor = XMI_COLOR_RGB(0xFFFFFF);
        self.titleLabel.left = iconURL.length ? kTitleWithIconLeft + 4 : kTitleLeft;
    }
    CGSize size = [self.titleLabel sizeThatFits:CGSizeMake(self.xmi_width - 6 - self.titleLabel.left, MAXFLOAT)];
    self.titleLabel.xmi_size = CGSizeMake(MIN(size.width, self.xmi_width - 6 - self.titleLabel.left), size.height);
    if (self.adData.showstyle == XMIAdStyleHomeListImage1_1 ||
        self.adData.showstyle == XMIAdStyleHomeCompositeBanner1_1) {
        self.titleLabel.bottom = self.height - 5;
    } else {
        self.titleLabel.bottom = self.height - kTitleBottom;
    }
    self.maskView.hidden = title.length == 0 || self.adData.showstyle == XMIAdStyleHomeListImage1_1 ||
    self.adData.showstyle == XMIAdStyleHomeCompositeBanner1_1;
    self.maskView.left = 0;
    self.maskView.top = self.xmi_height - self.maskView.height;
    
    if (iconURL.length == 0) {
        self.iconView.image = nil;
    } else {
        [self.iconView yy_setImageWithURL:[NSURL URLWithString:iconURL] placeholder:nil];
        if (self.adData.showstyle == XMIAdStyleHomeListImage1_1 ||
            self.adData.showstyle == XMIAdStyleHomeCompositeBanner1_1) {
            self.iconView.left = 0;
        } else {
            self.iconView.left = 4;
        }
        self.iconView.centerY = self.titleLabel.centerY;
    }
}

- (void)setExposed:(BOOL)exposed {
    if (_exposed != exposed && exposed) {
        _exposed = YES;
        if ([self.itemData isKindOfClass:[XMIAdRelatedData class]]) {
            if (!((XMIAdRelatedData *)_itemData).isExposed) {
                ((XMIAdRelatedData *)_itemData).isExposed = YES;
                [XMIAdReporter exposeReportWithAd:self.itemData andView:self];
            }
        }
    }
}

- (void)setAnimating:(BOOL)animating {
    if (animating && !self.imageView.animating) {
        self.imageView.currentAnimatedImageIndex = 0;
        [self.imageView startAnimating];
    } else if (!animating && self.imageView.animating) {
        [self.imageView stopAnimating];
    }
}

@end
