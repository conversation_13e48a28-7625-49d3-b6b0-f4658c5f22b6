//
//  XMIFindFeedNativeAdBaseView.h
//  XMAd
//
//  Created by cuiyuanz<PERSON> on 2022/3/1.
//

#import <XMAd/XMIFeedAdBaseView.h>
#import <XMAd/XMIAdMarkButtonView.h>

NS_ASSUME_NONNULL_BEGIN

@interface XMIFindFeedNativeAdBaseView : XMIFeedAdBaseView

@property (nonatomic,strong) UILabel *titleLabel;

@property (nonatomic,strong) UILabel *descLabel;

@property (nonatomic, strong) UILabel *btnLabel;

//@property (nonatomic, strong) UIButton *closeButton;

@property (nonatomic, strong) UIImageView *coverImageView;

@property (nonatomic, strong) XMIAdMarkButtonView *adMarkButtonView;

- (void)updateCloseAndMarkUI;

- (void)coverImageViewDidLoad;

- (UIView *)shakeView;

- (void)adMarkButtonViewSizeDidChange;

- (void)adMarkButtonViewDidClick;

@end

NS_ASSUME_NONNULL_END
