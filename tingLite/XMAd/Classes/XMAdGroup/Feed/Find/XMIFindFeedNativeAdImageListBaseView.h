//
//  XMIFindFeedNativeAdImageListBaseView.h
//  XMAd
//
//  Created by xiaodong2.zhang on 2024/7/10.
//

#import <XMAd/XMIFeedAdBaseView.h>
#import "XMIAdRelatedData.h"

NS_ASSUME_NONNULL_BEGIN

@class XMIBounceMoreView;
@interface XMIFindFeedNativeAdImageListBaseView : XMIFeedAdBaseView<XMIFeedAdViewCustomRenderProtocol>

@property (nonatomic, strong) XMIAdRelatedData *adData;
@property (nonatomic, assign) BOOL cellPause;

- (CGSize)itemCellSize;

- (Class)itemCellClass;

- (NSMutableArray *)itemCellArray;

- (void)setMoreViewHidden:(BOOL)hidden;

@end

NS_ASSUME_NONNULL_END
