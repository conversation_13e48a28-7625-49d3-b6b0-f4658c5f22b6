//
//  XMIFeedAdGXBaseView.m
//  XMAd
//
//  Created by cuiyuanzhe on 2025/4/7.
//

#import "XMIFeedAdGXBaseView.h"
#import <GaiaXiOS/GaiaXiOS.h>
#import <XMCategories/XMCategory.h>
#import <XMAd/XMIAdRelatedData.h>
#import <YYModel/YYModel.h>
#import <XMAd/XMIAdRespData.h>
#import <XMAd/XMIAdHelper.h>
#define kGXTopMargin 16

@interface XMIFeedAdGXBaseView ()<XMIFeedAdViewCustomRenderProtocol>

@property (nonatomic, strong) UIView *gxAdView;

@property (nonatomic, weak) XMIAdRelatedData *data;

@property (nonatomic, assign) BOOL canPlay;

@end

@implementation XMIFeedAdGXBaseView

- (void)customRenderWithAdData:(id)adData
{
    if (![adData isKindOfClass:[XMIAdRelatedData class]] || self.data == adData) {
        return;
    }
    XMIAdRelatedData *data = (XMIAdRelatedData *)adData;
    self.data = adData;
    NSMutableArray *composite = [NSMutableArray arrayWithCapacity:data.composite.count];
    NSString *adMark = nil;
    if ([XMIAdHelper adDataIsOperation:data]) {
        adMark = @"";
    } else {
        if ([[data.adUserType uppercaseString] isEqualToString:@"RTB"]) {
            adMark = @"推广";
        } else {
            adMark = @"广告";
        }
    }
    for (XMIAdBeanComposite *bean in data.composite) {
        NSMutableDictionary *dic = [[bean yy_modelToJSONObject] mutableCopy];
        dic[@"adMark"] = adMark;
        if (![bean.title isKindOfClass:[NSString class]] || bean.title.length == 0) {
            dic[@"title"] = data.name;
        }
        if (![bean.picUrl isKindOfClass:[NSString class]] || bean.picUrl.length == 0) {
            dic[@"picUrl"] = data.cover;
        }
        [composite addObject:dic];
    }
    NSMutableDictionary *mDic = [NSMutableDictionary dictionary];
    mDic[@"composite"] = composite;
    mDic[@"showStyle"] = @(data.showstyle);
    mDic[@"reset"] = @(YES);
    mDic[@"canPlay"] = @(self.canPlay);
    GXTemplateData *templateData = [[GXTemplateData alloc] init];
    templateData.data = mDic;
    [TheGXTemplateEngine bindData:templateData onView:self.gxAdView];
}



- (void)layoutSubviews
{
    [super layoutSubviews];
    self.contentView.frame = self.bounds;
    self.gxAdView.left = 0;
    self.gxAdView.top = kGXTopMargin;
}


+ (CGFloat)calAdHeight:(id)adData withAdWidth:(CGFloat)adWidth
{
    CGFloat imageWidth = XMSCREEN_WIDTH;
    NSString *identifier = @"XMAdSlide";  //todo:动态化
    GXTemplateItem *templateItem = [[GXTemplateItem alloc] init];
    templateItem.bizId = @"AD";
    templateItem.templateId = identifier;
    CGSize size = [TheGXTemplateEngine sizeWithTemplateItem:templateItem measureSize:CGSizeMake(imageWidth, NAN)];
    return size.height + kGXTopMargin;
}

- (UIView *)gxAdView
{
    if (!_gxAdView) {
        NSString *identifier = @"XMAdSlide";  //todo:动态化
        GXTemplateItem *templateItem = [[GXTemplateItem alloc] init];
        templateItem.bizId = @"AD";
        templateItem.templateId = identifier;
        _gxAdView = [TheGXTemplateEngine creatViewByTemplateItem:templateItem measureSize:CGSizeMake(XMSCREEN_WIDTH, NAN)];
        if (_gxAdView) {
            [self.contentView addSubview:_gxAdView];
            _gxAdView.left = 0;
            _gxAdView.top = kGXTopMargin;
        }
    }
    return _gxAdView;
}

- (void)pause
{
    self.canPlay = NO;
    [self updatePlayStatus];
}

- (void)replay
{
    self.canPlay = YES;
    [self updatePlayStatus];
}

- (void)updatePlayStatus
{
    NSMutableDictionary *mDic = [NSMutableDictionary dictionary];
    mDic[@"canPlay"] = @(self.canPlay);
    GXTemplateData *templateData = [[GXTemplateData alloc] init];
    templateData.data = mDic;
    [TheGXTemplateEngine bindData:templateData onView:self.gxAdView];
}

- (BOOL)supportRealExpose
{
    return YES;
}

@end
