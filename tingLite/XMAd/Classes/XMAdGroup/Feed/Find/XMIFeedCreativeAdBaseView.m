//
//  XMIFeedCreativeAdBaseView.m
//  XMAd
//
//  Created by cuiyuanzhe on 2022/3/7.
//

#import "XMIFeedCreativeAdBaseView.h"
#import "XMIAdButton.h"
#import "XMICommonUtils.h"
#import "XMIAdMacro.h"
#import "UIView+XMIUtils.h"
#import <XMWebImage/UIImageView+WebCache.h>

@interface XMIFeedCreativeAdBaseView ()

@property (strong, nonatomic) UIImageView *bgImageView;

@property (strong, nonatomic) XMIAdButton *closeButton;

@end

@implementation XMIFeedCreativeAdBaseView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.scrollAreaInWindow = CGRectNull;
        self.clipsToBounds = YES;;
    }
    return self;
}

#pragma mark - lazy load

- (XMIAdButton *)closeButton
{
    if (!_closeButton) {
        _closeButton = [[XMIAdButton alloc] initWithFrame:CGRectMake(0, 0, 30, 30)];
        _closeButton.hitTestEdgeOutsets = UIEdgeInsetsMake(10, 10, 10, 10);
        UIImage *closeNormal = [XMICommonUtils imageNamed:@"ad_can_close_white"];
        [_closeButton setImage:closeNormal forState:UIControlStateNormal];
        [_closeButton addTarget:self action:@selector(closeButtonAction) forControlEvents:UIControlEventTouchUpInside];
        [_closeButton sizeToFit];
        [self.contentView addSubview:_closeButton];
        
    }
    return _closeButton;
}

#pragma mark - super methods
- (void)didMoveToWindow {
    [super didMoveToWindow];
    if (!self.window) {
        return;
    }
    if (CGRectIsNull(self.scrollAreaInWindow)) {
        UIScrollView *scrollView = nil;
        UIView *superview = self.superview;
        for (NSInteger i = 0; i < 10; i++) {
            if ([superview isKindOfClass:[UIScrollView class]]) {
                scrollView = (UIScrollView *)superview;
                break;
            }
            superview = superview.superview;
            if (!superview) {
                break;
            }
        }
        if (!scrollView) {
            self.scrollAreaInWindow = [self.superview convertRect:self.superview.bounds toView:self.window];
        } else {
            self.scrollAreaInWindow = [self.window convertRect:scrollView.bounds fromView:scrollView];
        }
        
    }
    [self updateUIWhenScrolled];
}

#pragma mark - property settings

- (void)setScrollAreaInWindow:(CGRect)scrollAreaInWindow {
    _scrollAreaInWindow = scrollAreaInWindow;
    [self updateUI];
}

#pragma mark - view settings
- (void)updateUI {
    // ImageView
    self.bgImageView.frame = CGRectMake(0, 0, self.scrollAreaInWindow.size.width, self.scrollAreaInWindow.size.height);
    [self updateUIWhenScrolled];
    
    self.closeButton.xmi_right = XMI_SCREEN_WIDTH - 12;
    self.closeButton.xmi_top = 8;
    // 3-2
}

- (void)updateUIWhenScrolled {
    if (!self.window)
        return;
    CGRect rectInWindow = [self convertRect:self.frame toView:nil];
    if (!CGRectIntersectsRect(rectInWindow, [UIScreen mainScreen].bounds))
        return;
    CGFloat imageTop = 0;
    // 图片可以滚动
    if (rectInWindow.origin.y > self.scrollAreaInWindow.origin.y) {
        imageTop = - (rectInWindow.origin.y - self.scrollAreaInWindow.origin.y);
    }
    self.bgImageView.xmi_top = imageTop;
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    self.contentView.frame = self.bounds;
}

#pragma mark - adview delegate

- (void)updateImageURLs:(NSArray<NSString *> *)imageURLs
{
    if (imageURLs.count == 0) {
        self.bgImageView.image = nil;
        return;
    }
    [self.bgImageView sd_setImageWithURL:[NSURL URLWithString:[imageURLs firstObject]]];
}

- (void)updateClosePadding:(UIEdgeInsets)closePaddding
{
    self.closeButton.hitTestEdgeOutsets = [XMIAdButton closeAreaPaddingWithDefaultPadding:UIEdgeInsetsMake(10, 10, 10, 10) extraPadding:closePaddding];
}

- (NSArray<UIView *> *)clickableViews
{
    return @[self.contentView];
}

- (void)closeButtonAction
{
    [self closeAdViewClick];
}

- (UIImageView *)bgImageView
{
    if (!_bgImageView) {
        _bgImageView = [[UIImageView alloc] init];
        [self.contentView insertSubview:_bgImageView atIndex:0];
        [_bgImageView setContentMode:UIViewContentModeScaleAspectFill];
        _bgImageView.clipsToBounds = YES;
    }
    return _bgImageView;
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
