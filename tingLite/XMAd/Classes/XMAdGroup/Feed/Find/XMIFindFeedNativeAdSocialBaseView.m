//
//  XMIFindFeedNativeAdSocialBaseView.m
//  XMAd
//
//  Created by 张晓东 on 2024/3/7.
//

#import "XMIFindFeedNativeAdSocialBaseView.h"
#import "UIView+XMIUtils.h"
#import "XMICommonUtils.h"
#import "XMIAdSourceLabel.h"
#import "XMIAdMacro.h"
#import <XMUIKit/XMImageGenerator.h>
#import "XMIAdHelper.h"
#import "XMIAdManager.h"

#define kTitleTopMargin self.edge.top + kHomeRatioSize(8.f)
#define kTitleHeight kHomeRatioSize(22.f)
#define kTitleBottomMargin kHomeRatioSize(16.f)
#define kContainViewTopMargin (kTitleTopMargin + kTitleHeight + kTitleBottomMargin)
#define kContainViewLeftMargin 16.f
#define kContainViewBottomMargin kHomeRatioSize(13.f)
#define kContainViewRightMargin 16.f
#define kContentViewCornerRadius kHomeRatioSize([XMIAdManager findNativeSocialShowStyle] ? 4 : 12)

@interface XMIFindFeedNativeAdSocialBaseView ()

@property (nonatomic, strong) XMIAdSourceLabel *sourceLabel;

//@property (nonatomic, strong) CAGradientLayer *gradientLayer;

//@property (nonatomic, strong) UIView *bottomMask;

@end

@implementation XMIFindFeedNativeAdSocialBaseView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.contentView.layer.cornerRadius = kContentViewCornerRadius;
        self.contentView.clipsToBounds = YES;
    }
    return self;
}

+ (CGFloat)calAdHeight:(id)adData withAdWidth:(CGFloat)adWidth
{
    CGFloat imageWidth = XMSCREEN_WIDTH  - kContainViewLeftMargin - kContainViewRightMargin;
    CGFloat imageHeight = [self imageHeightWithImageWidth:imageWidth];
    CGFloat height = kTitleHeight + kTitleBottomMargin + imageHeight + kContainViewBottomMargin;
    return height;
}

+ (CGFloat)imageHeightWithImageWidth:(CGFloat)width
{
    CGFloat imageHeight = 0;
    // 1496 * 400
    if([XMICommonUtils isDeviceIPad])
    {
        imageHeight = width * (400.0 / 1496);
    }
    // 1182 * 426
    else
    {
        imageHeight = width * (9 / 16.0);
    }
    return ceil(imageHeight);
}

- (void)sizeToFit
{
    CGFloat imgWidth = self.xmi_width - kContainViewLeftMargin - kContainViewRightMargin;
    CGFloat imgHeight = [XMIFindFeedNativeAdSocialBaseView imageHeightWithImageWidth:imgWidth];
    self.contentView.frame = CGRectMake(kContainViewLeftMargin, kContainViewTopMargin, imgWidth, imgHeight);
//    self.bottomMask.frame = CGRectMake(0, self.contentView.height - 55 , imgWidth, 55);
//    self.gradientLayer.frame = self.bottomMask.bounds;
    self.coverImageView.frame = self.contentView.bounds;
    self.sourceLabel.bottom = self.coverImageView.bottom;
    self.sourceLabel.left = self.coverImageView.left;
    [self updateCloseAndMarkUI];
    [self updateTextLayout];
    self.xmi_height = self.contentView.xmi_bottom + kContainViewBottomMargin;
}

- (void)updateTextLayout
{
    self.btnLabel.xmi_width = kHomeRatioSize(64);
    self.btnLabel.xmi_height = kHomeRatioSize(28);
    self.btnLabel.xmi_right = self.coverImageView.right - 12;
    self.btnLabel.xmi_bottom = self.coverImageView.bottom - 12;
    self.btnLabel.layer.cornerRadius = kHomeRatioSize(14);
    self.btnLabel.layer.masksToBounds = YES;
    self.titleLabel.frame = CGRectMake(kContainViewLeftMargin, kTitleTopMargin, self.contentView.xmi_width, kTitleHeight);
    [self addSubview:self.titleLabel];
}

- (void)updateCloseAndMarkUI
{
    [super updateCloseAndMarkUI];
    CGFloat closeMargin = 8;
    CGFloat closeRight = self.contentView.xmi_width - closeMargin;
    self.adMarkButtonView.xmi_right = closeRight;
    self.adMarkButtonView.xmi_top = closeMargin;
}

- (void)updateCustomUI
{
    [super updateCustomUI];
    self.titleLabel.font = XMI_AD_PingFangSemiboldFont(kHomeRatioSize(16));
    self.titleLabel.textColor = XMI_COLOR_DynamicFromRGB(0x2C2C3C, 0xffffff);
    self.btnLabel.backgroundColor = XMI_COLOR_RGBA(0xFFFFFF,0.9);
    self.btnLabel.textColor = XMI_COLOR_RGB(0x131313);
    self.btnLabel.font = XMI_AD_PingFangSemiboldFont(kHomeRatioSize(12));
    self.btnLabel.textAlignment = NSTextAlignmentCenter;
    self.adMarkButtonView.adMarkUIMode = XMIAdMarkUIModeSocialHomePage;
    [self.adMarkButtonView setHitTestEdgeOutsets:UIEdgeInsetsMake(8, 14, 18, 8)];
    self.coverImageView.contentMode = UIViewContentModeScaleAspectFill;
//    self.bottomMask.frame = CGRectMake(0, self.contentView.height - 55 , self.contentView.width, 55);
    if ([self isVideoAdView]) {
        [self doVideoRender];
    }
}

- (void)doVideoRender {
//    self.gradientLayer.colors = @[(__bridge id)colorFromRGBA(0x000000, 0).CGColor, (__bridge id)colorFromRGBA(0x000000, 0.7).CGColor];
}

- (void)updateBtnText:(NSString *)btnText
{
    if (btnText.length > 4) {
        btnText = [btnText substringToIndex:4];
    }
    if (btnText.length == 0) {
        btnText = @"了解详情";
    }
    self.btnLabel.text = btnText;
}

- (void)didExposeAdView
{
   [super didExposeAdView];
}

- (XMIAdSourceLabel *)sourceLabel
{
    if (!_sourceLabel) {
        _sourceLabel = [XMIAdSourceLabel labelWithSoundPatchStyle];
        [self.contentView addSubview:_sourceLabel];
    }
    return _sourceLabel;
}

//- (UIView *)bottomMask {
//    if (!_bottomMask) {
//        _bottomMask = [[UIView alloc] init];
//        _bottomMask.userInteractionEnabled = NO;
//        CAGradientLayer *gradientLayer = [CAGradientLayer layer];
//        gradientLayer.locations = @[@0, @1];
//        gradientLayer.startPoint = CGPointMake(0.5, 0);
//        gradientLayer.endPoint = CGPointMake(0.5, 1);
//        gradientLayer.frame = _bottomMask.bounds;
//        _gradientLayer = gradientLayer;
//        [_bottomMask.layer addSublayer:gradientLayer];
//        [self.contentView insertSubview:_bottomMask aboveSubview:self.coverImageView];
//    }
//    return _bottomMask;
//}

- (void)updateInScreenSource:(XMIInScreenSource)inScreenSource materialProvideSource:(NSString *)materialProvideSource
{
    [self.sourceLabel updateWithInScreenSource:inScreenSource materialProvideSource:materialProvideSource];
}

//- (void)updateGradientLayerWithImage:(UIImage *)image {
//    image = [image scaleToSize:CGSizeMake(image.size.width / 6, image.size.height / 6)];
//    UIColor *bottomColor = [[self themeColorWithImage:image rect:CGRectMake(0, image.size.height - 10, image.size.width, image.size.height)] xm_colorWithSaturability:0.46 brightness:0.6];
//    self.gradientLayer.colors = @[(__bridge id)colorFromRGBA(bottomColor.rgbHex, 0).CGColor, (__bridge id)colorFromRGBA(bottomColor.rgbHex, 0.7).CGColor];
//}

- (UIColor *)themeColorWithImage:(UIImage *)image rect:(CGRect)rect {
    CGFloat scale = image.scale;
    rect.origin.x *= scale;
    rect.origin.y *= scale;
    rect.size.width *= scale;
    rect.size.height *= scale;

    UIColor *color = [XMImageGenerator themeColorsFromImage:image atRect:rect].firstObject;
    color = [color colorWithAlphaComponent:1];
    return color;
}

- (BOOL)isVideoAdView {
    return NO;
}

//- (void)coverImageViewDidLoad
//{
//    if (![self isVideoAdView]) {
//        [self updateGradientLayerWithImage:self.coverImageView.image];
//    }
//}

@end
