//
//  XMIFindWindowAdBaseView.m
//  XMAd
//
//  Created by cuiyuanzhe on 2022/3/7.
//

#import "XMIFindWindowAdBaseView.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import "XMICommonUtils.h"
#import "XMIAdMacro.h"
#import "UIView+XMIUtils.h"
#import "XMIAdButton.h"
#import "XMIFeedAdRenderer.h"
#import "XMIAdError.h"
#import <XMCategories/UIImage+XMCommon.h>

@interface XMIFeedShopView : XMIFeedAdBaseView

@property (nonatomic, strong) UIImageView *imageView;

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, weak) UILabel *moreLabel1;

@property (nonatomic, weak) UIView *lineView;

@property (nonatomic, weak) UILabel *moreLabel2;

- (void)setupMoreView;

@end

@interface XMIFindWindowAdBaseView ()<XMIFeedAdRendererDelegate>

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, copy) NSArray *adModels;

@property (nonatomic, strong) XMIAdButton *closeButton;

@property (nonatomic, strong) UIScrollView *scrollView;

@property (nonatomic, copy) NSMutableArray<XMIFeedAdRenderer *> *renderers;

@end

@implementation XMIFindWindowAdBaseView
{
    XMIAdButton *_closeButton;
}
@dynamic closeButton;
#pragma mark - lazy load

- (UILabel *)titleLabel
{
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        [self addSubview:_titleLabel];
        _titleLabel.font = XMI_AD_PingFangMediumFont(18);
        _titleLabel.textColor = XMI_COLOR_RGB(0x333333);
        _titleLabel.lineBreakMode = NSLineBreakByTruncatingTail;
        _titleLabel.numberOfLines = 1;
        _titleLabel.xmi_top = 15;
        _titleLabel.xmi_left = 25;
    }
    return _titleLabel;
}

- (UIScrollView *)scrollView
{
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] initWithFrame:CGRectMake(0, self.titleLabel.xmi_bottom, self.xmi_width, self.xmi_height - self.titleLabel.xmi_bottom)];
        [self addSubview:_scrollView];
        _scrollView.showsHorizontalScrollIndicator = NO;
        _scrollView.alwaysBounceHorizontal = YES;
        [_scrollView setDecelerationRate:UIScrollViewDecelerationRateFast];
    }
    return _scrollView;
}

- (UIButton *)closeButton
{
    if (!_closeButton) {
        _closeButton = [XMIAdButton buttonWithType:UIButtonTypeCustom];
        [self addSubview:_closeButton];
        _closeButton.hitTestEdgeOutsets = UIEdgeInsetsMake(10, 10, 10, 10);
        [_closeButton addTarget:self action:@selector(closeButtonClicked:) forControlEvents:UIControlEventTouchUpInside];
        [_closeButton setImage:[UIImage xm_dynamicImageWithNormalImage:[XMICommonUtils imageNamed:@"ad_can_close"] darkImage:[XMICommonUtils imageNamed:@"ad_can_close_dark"]] forState:UIControlStateNormal];
        _closeButton.xmi_size = CGSizeMake(30, 30);
    }
    return _closeButton;
}

- (NSMutableArray<XMIFeedAdRenderer *> *)renderers
{
    if (!_renderers) {
        _renderers = [NSMutableArray array];
    }
    return _renderers;
}

#pragma mark - adview protocol

- (void)updateTitle:(NSString *)title
{
    self.titleLabel.text = title;
    self.titleLabel.xmi_size = [self.titleLabel sizeThatFits:CGSizeMake(self.xmi_width - self.titleLabel.xmi_left - 100, MAXFLOAT)];
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    self.titleLabel.xmi_left = 16;
    self.titleLabel.xmi_top = 24;
    self.closeButton.xmi_right = self.xmi_width - 12;
    self.closeButton.xmi_centerY = self.titleLabel.xmi_centerY;
    self.titleLabel.xmi_width = self.closeButton.xmi_left - 10 - self.titleLabel.xmi_left;
    self.scrollView.xmi_top = self.titleLabel.xmi_bottom + 15;
    self.scrollView.xmi_width = self.xmi_width;
}

- (void)customRenderWithAdData:(id)adData
{
    [self.renderers enumerateObjectsUsingBlock:^(XMIFeedAdRenderer * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (obj.hasRendered) {
            [obj.renderedView removeFromSuperview];
        }
    }];
    if (![adData isKindOfClass:[NSArray class]]) {
        [self failRenderWithError:[XMIAdError emptyDataError]];
        return;
    }
    self.adModels = adData;
    CGFloat itemLeft = 16;
    CGFloat itemSpace = 10;
    CGFloat itemPCount = 3;
    CGFloat itemWidth = floor((self.scrollView.xmi_width - itemLeft - itemPCount * itemSpace)/(itemPCount + 0.5));
    if (XMI_SCREEN_WIDTH < 321) {
        itemWidth = floor((self.scrollView.xmi_width - itemLeft - (itemPCount - 1) * itemSpace)/itemPCount);
    }
    self.scrollView.xmi_height = 44 + itemWidth;
    [self.adModels enumerateObjectsUsingBlock:^(XMIFeedAdModel *adModel, NSUInteger idx, BOOL * _Nonnull stop) {
        XMIFeedAdRenderer *render;
        if (idx < self.renderers.count) {
            render = [self.renderers objectAtIndex:idx];
        } else {
            render = [[XMIFeedAdRenderer alloc] initWithRenderingViewClass:[XMIFeedShopView class]];
            render.delegate = self;
            [self.renderers addObject:render];
        }
        render.adModel = adModel;
        XMIFeedShopView *renderedView = (XMIFeedShopView *)[render renderedView];
        [self.scrollView addSubview:renderedView];
        renderedView.frame = CGRectMake(itemLeft + (itemWidth + itemSpace) * idx, 0, itemWidth, self.scrollView.xmi_height);
        if (idx == self.adModels.count - 1) {
            [renderedView setupMoreView];
        }
    }];
    self.scrollView.xmi_height += 7;
    self.scrollView.contentSize = CGSizeMake(itemLeft + self.adModels.count * (itemWidth + itemSpace) - itemSpace + itemLeft, self.scrollView.xmi_height);
}

- (void)feedAdRendererWillPresentScreen:(XMIFeedAdRenderer *)renderer
{
    [self willPresentScreen];
}

- (void)feedAdRendererDidPresentScreen:(XMIFeedAdRenderer *)renderer
{
    [self didPresentScreen];
}

- (void)feedAdRendererDetailControllerDidClosed:(XMIFeedAdRenderer *)renderer
{
    [self didCloseAdDetail];
}

- (void)closeButtonClicked:(id)sender
{
    [self closeAdViewClick];
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end

@implementation XMIFeedShopView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self commonInit];
    }
    
    return self;
}

- (void)commonInit {
    self.contentView.backgroundColor = [UIColor whiteColor];
    self.contentView.layer.cornerRadius = 4;
    self.contentView.layer.masksToBounds = YES;
    self.backgroundColor = XMI_COLOR_DynamicFromRGB(0xffffff, 0x1e1e1e);
    self.layer.shadowRadius = 30;
    self.layer.shadowOffset = CGSizeMake(0, 10);
    self.layer.cornerRadius = self.contentView.layer.cornerRadius;
    self.layer.shadowOpacity = 0.15f;
    self.layer.shadowColor = XMI_COLOR_RGB(0x535459).CGColor;
}

#pragma mark - lazy load

- (UIImageView *)imageView
{
    if (!_imageView) {
        _imageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, self.xmi_width, self.xmi_width)];
        _imageView.backgroundColor = XMI_COLOR_RGB(0xf3f4f5);
        [self.contentView insertSubview:_imageView atIndex:0];
    }
    return _imageView;
}

- (UILabel *)titleLabel
{
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        [self.contentView addSubview:_titleLabel];
        _titleLabel.font = XMI_AD_PingFangFont(12);
        _titleLabel.textColor = XMI_COLOR_DynamicFromRGB(0x333333, 0x888888);
        _titleLabel.lineBreakMode = NSLineBreakByTruncatingTail;
        _titleLabel.numberOfLines = 0;
    }
    return _titleLabel;
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    self.contentView.frame = self.bounds;
    self.imageView.frame = CGRectMake(0, 0, self.xmi_width, self.xmi_width);
    self.titleLabel.center = CGPointMake(self.xmi_width * 0.5f, (self.xmi_width + self.xmi_height) * 0.5f);
}

//- (void)setupWithRelatedAd:(XMIAdRelatedData *)relatedData {
//    CGFloat itemWidth = self.frame.size.width;
//    UIFont *itemFont = XMI_AD_PingFangFont(13);
//    // image
//    UIImageView *imageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, itemWidth, itemWidth)];
//    if (relatedData.cover != nil) {
//        [imageView sd_setImageWithURL:[NSURL URLWithString:relatedData.cover] placeholderImage:[XMICommonUtils imageNamed:@"ad_bkg_default"]];
//    } else {
//        imageView.image = [XMICommonUtils imageNamed:@"ad_bkg_default"];
//    }
//    [self addSubview:imageView];
//    // detail
//    UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(5, itemWidth + 14, itemWidth - 5 * 2, itemFont.lineHeight)];
//    label.font = itemFont;
//    label.textColor = XMI_COLOR_RGB(0x333333);
//    label.backgroundColor = [UIColor clearColor];
//    label.lineBreakMode = NSLineBreakByTruncatingTail;
//    label.text = relatedData.name;
//    [self addSubview:label];
//}

/**
 查看更多 特殊视图
 */
- (void)setupMoreView {
    _titleLabel.hidden = YES;
    _imageView.hidden = YES;
    CGFloat itemWidth = self.frame.size.width;
    CGFloat itemHeight = self.frame.size.height;
    UIFont *itemFont = XMI_AD_PingFangFont(12);
    // 查看更多
    UILabel *moreLabel1 = _moreLabel1 ?: [[UILabel alloc] initWithFrame:CGRectMake(0, itemHeight / 2 - itemFont.lineHeight - 3, itemWidth, itemFont.lineHeight)];
    moreLabel1.font = itemFont;
    moreLabel1.textColor = XMI_COLOR_RGB(0x666666);
    moreLabel1.textAlignment = NSTextAlignmentCenter;
    moreLabel1.text = @"查看更多";
    [self.contentView addSubview:moreLabel1];
    // 分割线
    CGFloat lineWidth = itemWidth * 0.6;
    CGFloat lineHeight = 1 / [UIScreen mainScreen].scale;
    UIView *lineView = _lineView ?: [[UIView alloc] initWithFrame:CGRectMake((itemWidth - lineWidth) / 2, itemHeight / 2, lineWidth, lineHeight)];
    lineView.backgroundColor = XMI_COLOR_RGB(0x666666);
    [self.contentView addSubview:lineView];
    // more
    UILabel *moreLabel2 = _moreLabel2 ?: [[UILabel alloc] initWithFrame:CGRectMake(0, itemHeight / 2 + 3, itemWidth, itemFont.lineHeight)];
    moreLabel2.font = itemFont;
    moreLabel2.textColor = XMI_COLOR_RGB(0x666666);
    moreLabel2.textAlignment = NSTextAlignmentCenter;
    moreLabel2.text = @"more";
    [self.contentView addSubview:moreLabel2];
    
    _moreLabel1 = moreLabel1;
    _moreLabel2 = moreLabel2;
    _lineView = lineView;
}

#pragma mark - adview protocol

- (NSArray<UIView *> *)clickableViews
{
    return @[self];
}

- (void)updateImageURLs:(NSArray<NSString *> *)imageURLs
{
    if (imageURLs.count == 0) {
        self.imageView.image = nil;
        return;
    }
    [self.imageView sd_setImageWithURL:[NSURL URLWithString:[imageURLs firstObject]] placeholderImage:[XMICommonUtils imageNamed:@"ad_bkg_default"] completed:nil];
}

- (void)updateTitle:(NSString *)title
{
    self.titleLabel.text = title;
    self.titleLabel.xmi_size = [self.titleLabel sizeThatFits:CGSizeMake(self.xmi_width - 10, MAXFLOAT)];
    self.titleLabel.center = CGPointMake(self.xmi_width * 0.5f, (self.xmi_width + self.xmi_height) * 0.5f);
}

- (void)updateCustomUI
{
    [_moreLabel1 removeFromSuperview];
    [_moreLabel2 removeFromSuperview];
    [_lineView removeFromSuperview];
}

@end
