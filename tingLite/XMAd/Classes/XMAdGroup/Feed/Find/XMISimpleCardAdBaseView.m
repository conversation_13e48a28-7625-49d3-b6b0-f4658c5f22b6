//
//  XMISimpleCardAdBaseView.m
//  XMAd
//
//  Created by cuiyuanzhe on 2023/2/23.
//

#import "XMISimpleCardAdBaseView.h"
#import "UIView+XMIUtils.h"
#import "XMIAdButton.h"
#import "XMICommonUtils.h"
#import "XMIAdDefines.h"
#import "XMIAdMacro.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import "XMIAdRelatedData.h"

@implementation XMISimpleCardAdBaseView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.edge = UIEdgeInsetsZero;
    }
    return self;
}


- (void)layoutSubviews
{
    [super layoutSubviews];
    if (self.xmi_width == 0) return;
    self.contentView.frame = self.bounds;
    self.titleLabel.xmi_top = 12;
    self.titleLabel.xmi_left = 16;
    self.titleLabel.xmi_size = [self.titleLabel sizeThatFits:CGSizeMake(self.xmi_width - 52, MAXFLOAT)];
    self.closeButton.xmi_top = 14;
    self.closeButton.xmi_right = self.xmi_width - 8;
    self.providerAvatarView.xmi_size = CGSizeMake(16, 16);
    self.providerAvatarView.xmi_left = self.titleLabel.xmi_left;
    self.providerAvatarView.xmi_top = self.titleLabel.xmi_bottom + 8.5f;
    self.providerLabel.xmi_left = self.providerAvatarView.xmi_right + 2;
    self.providerLabel.xmi_top = self.providerAvatarView.xmi_top;
    CGFloat imageWidth = self.xmi_width - 28;
    self.coverImageView.frame = CGRectMake(14, self.providerAvatarView.xmi_bottom + 8, imageWidth, imageWidth * 9.0f / 16.0f);
    self.actionButtonLabel.xmi_size = CGSizeMake(72, 26);
    self.actionButtonLabel.xmi_right = self.coverImageView.xmi_width - 8;
    self.actionButtonLabel.xmi_bottom = self.coverImageView.xmi_height - 8;
}

- (NSArray<UIView *> *)clickableViews
{
    return @[self.contentView];
}

- (UILabel *)titleLabel
{
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        [self.contentView addSubview:_titleLabel];
        _titleLabel.numberOfLines = 2;
        _titleLabel.textColor = XMI_COLOR_DynamicFromRGB(0x333333, 0xffffff);
        _titleLabel.font = XMI_AD_PingFangSemiboldFont(16);
    }
    return _titleLabel;
}

- (UIButton *)closeButton
{
    if (!_closeButton) {
        _closeButton = [XMIAdButton buttonWithType:UIButtonTypeCustom];
        [_closeButton setImage:[XMICommonUtils imageNamed:@"card_ad_close"] forState:UIControlStateNormal];
        [_closeButton addTarget:self action:@selector(closeButtonAction) forControlEvents:UIControlEventTouchUpInside];
        [self.contentView addSubview:_closeButton];
        [_closeButton sizeToFit];
    }
    return _closeButton;
}

- (UIImageView *)providerAvatarView
{
    if (!_providerAvatarView) {
        _providerAvatarView = [[UIImageView alloc] init];
        [self.contentView addSubview:_providerAvatarView];
        _providerAvatarView.layer.cornerRadius = 8;
        _providerAvatarView.layer.masksToBounds = YES;
    }
    return _providerAvatarView;
}

- (UILabel *)providerLabel
{
    if (!_providerLabel) {
        _providerLabel = [[UILabel alloc] init];
        [self.contentView addSubview:_providerLabel];
        _providerLabel.textColor = XMI_COLOR_DynamicFromRGB(0x666666, 0x8d8d91);
        _providerLabel.font = XMI_AD_PingFangFont(12);
    }
    return _providerLabel;
}

- (UIImageView *)coverImageView
{
    if (!_coverImageView) {
        _coverImageView = [[UIImageView alloc] init];
        [self.contentView addSubview:_coverImageView];
        _coverImageView.layer.cornerRadius = 4;
        _coverImageView.layer.masksToBounds = YES;
    }
    return _coverImageView;
}

- (UILabel *)actionButtonLabel
{
    if (!_actionButtonLabel) {
        _actionButtonLabel = [[UILabel alloc] init];
        [self.coverImageView addSubview:_actionButtonLabel];
        _actionButtonLabel.textAlignment = NSTextAlignmentCenter;
        _actionButtonLabel.layer.cornerRadius = 13;
        _actionButtonLabel.layer.masksToBounds = YES;
        _actionButtonLabel.font = XMI_AD_PingFangFont(12);
        _actionButtonLabel.textColor = XMI_COLOR_RGB(0xFFFFFF);
        _actionButtonLabel.backgroundColor = XMI_COLOR_RGB(0xFF4444);
    }
    return _actionButtonLabel;
}

- (void)updateTitle:(NSString *)title
{
    self.titleLabel.text = title;
    self.titleLabel.xmi_size = [self.titleLabel sizeThatFits:CGSizeMake(self.xmi_width - 10, MAXFLOAT)];
    self.titleLabel.center = CGPointMake(self.xmi_width * 0.5f, (self.xmi_width + self.xmi_height) * 0.5f);
}

- (void)updateImageURLs:(NSArray<NSString *> *)imageURLs
{
    if (imageURLs.count == 0) {
        self.coverImageView.image = [XMICommonUtils imageNamed:@"ad_bkg_default"];
        return;
    }
    [self.coverImageView sd_setImageWithURL:[NSURL URLWithString:[imageURLs firstObject]] placeholderImage:[XMICommonUtils imageNamed:@"ad_bkg_default"] completed:nil];
}

- (void)customRenderWithAdData:(id)adData
{
    if (![adData isKindOfClass:[XMIAdRelatedData class]]) {
        return;
    }
    [self.providerAvatarView sd_setImageWithURL:[NSURL URLWithString:[(XMIAdRelatedData *)adData providerAvatar]] placeholderImage:[XMICommonUtils imageNamed:@"avatar_default_rectangle"]];
    self.providerLabel.text = [(XMIAdRelatedData *)adData providerName];
    [self.providerLabel sizeToFit];
    self.providerLabel.xmi_height = 14;
    [self setNeedsLayout];
    [self layoutIfNeeded];
}

-(void)updateBtnText:(NSString *)btnText
{
    self.actionButtonLabel.text = [NSString stringWithFormat:@"%@ >",btnText.length > 0 ? btnText : @"了解详情"];
}

- (void)updateClosePadding:(UIEdgeInsets)closePaddding
{
    [(XMIAdButton *)self.closeButton setHitTestEdgeOutsets:[XMIAdButton closeAreaPaddingWithDefaultPadding:UIEdgeInsetsMake(2, 2, 2, 2) extraPadding:closePaddding]];
}


- (void)closeButtonAction
{
    [self closeAdViewClick];
}

+ (CGFloat)calAdHeight:(id)adData withAdWidth:(CGFloat)adWidth
{
    XMIAdRelatedData *data = (XMIAdRelatedData *)adData;
    NSMutableParagraphStyle *ps = [[NSMutableParagraphStyle alloc] init];
    ps.lineBreakMode = NSLineBreakByCharWrapping;
    ps.alignment = NSTextAlignmentLeft;
    NSAttributedString *attributedTitle = [[NSAttributedString alloc] initWithString:data.name attributes:@{NSFontAttributeName : XMI_AD_PingFangSemiboldFont(16), NSForegroundColorAttributeName : XMI_COLOR_DynamicFromRGB(0x333333, 0xffffff), NSParagraphStyleAttributeName : ps}];
    CGFloat titleHeight = [attributedTitle boundingRectWithSize:CGSizeMake(adWidth - 52, 46) options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading context:nil].size.height;
    return 12 + titleHeight + 13.5f + 16 + 8 + adWidth * 9.0f / 16.0f + 12;
}

- (BOOL)supportRealExpose
{
    return YES;
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
