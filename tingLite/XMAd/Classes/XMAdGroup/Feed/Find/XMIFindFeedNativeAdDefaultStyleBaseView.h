//
//  XMIFindFeedNativeAdDefaultStyleBaseView.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/2/28.
//

#import <XMAd/XMIFindFeedNativeAdBaseView.h>

#define kContentViewEdgeTop XMIAdHSpace(8)

NS_ASSUME_NONNULL_BEGIN

@interface XMIFindFeedNativeAdDefaultStyleBaseView : XMIFindFeedNativeAdBaseView

@property (nonatomic, strong, readonly) UIImageView *bottomMask;

+ (CGFloat)imageHeightWithImageWidth:(CGFloat)width;

- (CGFloat)imageHeightWithImageWidth:(CGFloat)width;

- (CGFloat)backgroundHeightWithImageWidth:(CGFloat)width;

- (void)showAnimation;

//子类实现 用于区分出现动画
- (BOOL)isVideoAdView;

- (void)updateBtnLabelWithImage:(UIImage *)image whiteStyle:(BOOL)isWhiteStyle;

@end

NS_ASSUME_NONNULL_END
