//
//  XMIFindFeedNativeAdBaseCell.h
//  XMAd
//
//  Created by xiaodong2.zhang on 2024/7/24.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class XMIAdRelatedData;
@interface XMIFindFeedNativeAdBaseCell : UICollectionViewCell

@property (nonatomic, strong) id itemData;
@property (nonatomic, strong) NSIndexPath *indexPath;
@property (nonatomic, copy  ) void(^animatedDislikeHandler)(NSIndexPath *indexPath);
@property (nonatomic, strong) XMIAdRelatedData *adData;
@property (nonatomic, assign) BOOL exposed;
@property (nonatomic, assign) BOOL animating;
@property (nonatomic, assign) BOOL animatingImage;

- (void)checkExpose;

- (void)setupGestureRecognizer;


@end

NS_ASSUME_NONNULL_END
