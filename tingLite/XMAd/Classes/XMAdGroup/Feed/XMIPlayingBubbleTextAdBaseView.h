//
//  XMIPlayingBubbleTextAdBaseView.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON>z<PERSON> on 2022/5/9.
//

#import <XMAd/XMIFeedAdBaseView.h>

NS_ASSUME_NONNULL_BEGIN
/**
 新版气泡广告样式，纯文字无图
 */

@interface XMIPlayingBubbleTextAdBaseView : XMIFeedAdBaseView<XMIFeedAdViewCustomRenderProtocol>

- (UIView *)bubbleView; //子类实现

- (CGFloat)contentWidth;

- (CGFloat)arrowHeight;

- (BOOL)shouldPlaySpreadAnimation;

@property (nonatomic, strong) UILabel *recommendLabel;//主播推荐

@property (nonatomic, strong) UIView *titleContainer;

@property (nonatomic, strong) UILabel *btnLabel;

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) UIScrollView *titleScrollView;

@property (nonatomic, strong) UIButton *closeButton;

- (void)resetScrollAnimation;

//layout后调用
- (void)updateBubbleAndTitle;


@end

NS_ASSUME_NONNULL_END
