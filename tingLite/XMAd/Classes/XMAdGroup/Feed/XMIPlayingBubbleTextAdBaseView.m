//
//  XMIPlayingBubbleTextAdBaseView.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/5/9.
//
//动效：https://lanhuapp.com/web/#/item/project/detailDetach?pid=08644c22-00ae-4917-addc-a39655774574&image_id=b33e3eaa-8b02-4e11-bd88-cf99d2649792&project_id=08644c22-00ae-4917-addc-a39655774574&fromEditor=true

#import "XMIPlayingBubbleTextAdBaseView.h"
#import "XMIAdRelatedData.h"
#import "UIView+XMIUtils.h"
#import "XMIAdMacro.h"
#import "XMIAdButton.h"
#import "XMICommonUtils.h"
#import "XMIAdHelper.h"

@interface  XMIPlayingBubbleTextAdBaseView()

@property (nonatomic, copy) NSString *title;

@property (nonatomic, strong) UIView *titleMaskView;

@property (nonatomic, assign) BOOL hasShowedAnimation;

@property (nonatomic, strong) CAShapeLayer *spreadLayer;

@property (nonatomic, assign) XMIAdShowSubStyle subShowStyle;

@end

@implementation XMIPlayingBubbleTextAdBaseView

- (UILabel *)recommendLabel
{
    if (!_recommendLabel) {
        _recommendLabel = [[UILabel alloc] init];
        _recommendLabel.font = XMI_AD_PingFangFont(8);
        _recommendLabel.textColor = XMI_COLOR_RGBA(0xFFFFFF, 0.6f);
        _recommendLabel.text = @"主播推荐";
        _recommendLabel.textAlignment = NSTextAlignmentCenter;
        _recommendLabel.layer.cornerRadius = 2;
        _recommendLabel.xmi_size = CGSizeMake(36, 11);
        _recommendLabel.layer.borderColor = _recommendLabel.textColor.CGColor;
        _recommendLabel.layer.borderWidth = kADOnePixelsLineHeight;
        [self.contentView addSubview:_recommendLabel];
    }
    return _recommendLabel;
}

- (UIView *)titleContainer
{
    if (!_titleContainer) {
        _titleContainer = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 100, 20)];
        [self.contentView addSubview:_titleContainer];
    }
    return _titleContainer;
}

- (UIScrollView *)titleScrollView
{
    if (!_titleScrollView) {
        _titleScrollView = [[UIScrollView alloc] initWithFrame:CGRectMake(0, 0, 100, 20)];
        [self.titleContainer addSubview:_titleScrollView];
        _titleScrollView.showsHorizontalScrollIndicator = NO;
        _titleScrollView.bounces = NO;
        _titleScrollView.userInteractionEnabled = NO;
    }
    return _titleScrollView;
}

- (UILabel *)titleLabel
{
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        [self.titleScrollView addSubview:_titleLabel];
        _titleLabel.font = XMI_AD_PingFangMediumFont(12);
        _titleLabel.textColor = XMI_COLOR_RGBA(0xFFFFFF, 0.7f);
    }
    return _titleLabel;
}

- (UILabel *)btnLabel
{
    if (!_btnLabel) {
        _btnLabel = [[UILabel alloc] init];
        [self.contentView addSubview:_btnLabel];
        _btnLabel.layer.cornerRadius = 9;
        _btnLabel.layer.masksToBounds = YES;
        _btnLabel.textColor = XMI_COLOR_RGB(0xFFFFFF);
        _btnLabel.font = XMI_AD_PingFangMediumFont(10);
        _btnLabel.textAlignment = NSTextAlignmentCenter;
        NSString *btnText = @"查看详情";
        _btnLabel.text = btnText;
        [_btnLabel sizeToFit];
        _btnLabel.xmi_width += 12;
        _btnLabel.xmi_height = 18;
    }
    return _btnLabel;
}

- (UIView *)titleMaskView
{
    if (!_titleMaskView) {
        _titleMaskView = [[UIView alloc] initWithFrame:self.titleContainer.bounds];
        _titleMaskView.backgroundColor = [UIColor clearColor];
        UIView *leftView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, _titleMaskView.xmi_width - 24, _titleMaskView.xmi_height)];
        [_titleMaskView addSubview:leftView];
        leftView.backgroundColor = [UIColor whiteColor];
        leftView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        UIView *rightView = [[UIView alloc] initWithFrame:CGRectMake(leftView.xmi_right, 0, 24, _titleMaskView.xmi_height)];
        [_titleMaskView addSubview:rightView];
        rightView.backgroundColor = [UIColor clearColor];
        rightView.autoresizingMask = UIViewAutoresizingFlexibleLeftMargin | UIViewAutoresizingFlexibleHeight;
        CAGradientLayer *gradientLayer = [CAGradientLayer layer];
        [rightView.layer addSublayer:gradientLayer];
        gradientLayer.frame = rightView.bounds;
        gradientLayer.colors = @[(__bridge id)XMI_COLOR_RGBA(0xFFFFFF, 1.0).CGColor, (__bridge id)XMI_COLOR_RGBA(0xFFFFFF, 0.0f).CGColor];
        gradientLayer.locations = @[@0, @1];
        gradientLayer.startPoint = CGPointMake(0, 0.5);
        gradientLayer.endPoint = CGPointMake(1, 0.5);
        [_titleMaskView aktTagObject:gradientLayer forKey:@"gradient"];
    }
    return _titleMaskView;
}

- (UIButton *)closeButton
{
    if (!_closeButton) {
        _closeButton = [XMIAdButton buttonWithType:UIButtonTypeCustom];
        UIImage *closeNormal = [XMICommonUtils imageNamed:@"bubble_ad_close"];
        CGFloat closeWidth = 8;
        if ([XMIAdHelper negativeFeedbackValueForAd] && (XMI_SCREEN_HEIGHT >= 667.0f)) {
            closeNormal = [XMICommonUtils imageNamed:@"bubble_ad_close_new"];
            closeWidth = 10;
        }
        [_closeButton setImage:closeNormal forState:UIControlStateNormal];
        [_closeButton addTarget:self action:@selector(closeButtonAction) forControlEvents:UIControlEventTouchUpInside];
        [self.contentView addSubview:_closeButton];
        _closeButton.xmi_size = CGSizeMake(closeWidth, closeWidth);
        [(XMIAdButton *)_closeButton setHitTestEdgeOutsets:UIEdgeInsetsMake(2, 2, 2, 2)];
    }
    return _closeButton;
}

- (void)closeButtonAction
{
    [self closeAdViewClick];
}


#pragma mark - adview protocol

- (void)updateTitle:(NSString *)title
{
    self.title = title;
    self.titleLabel.text = self.title;
    [self.titleLabel sizeToFit];
    self.titleLabel.xmi_left = 0;
    self.titleLabel.xmi_centerY = self.titleContainer.xmi_height * 0.5f;
    self.titleScrollView.contentSize = CGSizeMake(self.titleLabel.xmi_width, self.titleContainer.xmi_height);
}

- (void)updateBtnText:(NSString *)btnText
{
    btnText = @"查看详情";
    self.btnLabel.text = btnText;
    [self.btnLabel sizeToFit];
    self.btnLabel.xmi_width += 12;
    self.btnLabel.xmi_height = 18;
}

- (void)updateClosePadding:(UIEdgeInsets)closePaddding
{
    [(XMIAdButton *)self.closeButton setHitTestEdgeOutsets:[XMIAdButton closeAreaPaddingWithDefaultPadding:UIEdgeInsetsMake(2, 2, 2, 2) extraPadding:closePaddding]];
}

- (void)updateSubShowStyle:(XMIAdShowSubStyle)subShowStyle
{
    self.subShowStyle = subShowStyle;
}

- (void)customRenderWithAdData:(id)adData
{
    if (![adData isKindOfClass:[XMIAdRelatedData class]]) {
        return;
    }
    self.recommendLabel.hidden = ![(XMIAdRelatedData *)adData enableAnchorRec];
    [self setNeedsLayout];
    [self layoutIfNeeded];
}


- (void)didExposeAdView
{
    [super didExposeAdView];
    if (!_hasShowedAnimation) {
        _hasShowedAnimation = YES;
        [self showAnimation];
    }
}

- (void)showAnimation
{
    if ([self.delegate respondsToSelector:@selector(feedAdViewDidShowAnimation:)]) {
        [self.delegate feedAdViewDidShowAnimation:self];
    }
    if (self.contentView.layer.animationKeys.count > 0) {
        return;
    }
    [self.titleScrollView.layer removeAllAnimations];
    self.titleScrollView.contentOffset = CGPointZero;
    [self.spreadLayer removeAllAnimations];
    self.spreadLayer.hidden = YES;
    self.btnLabel.backgroundColor = XMI_COLOR_RGBA(0xFFFFFF, 0.12f);
    self.contentView.alpha = 1.0f;
    self.titleLabel.xmi_left = 0;
    self.titleLabel.text = self.title;
    [self.titleLabel sizeToFit];
    self.titleLabel.xmi_left = 0;
    self.titleLabel.xmi_centerY = self.titleContainer.xmi_height * 0.5f;
    self.contentView.frame = self.bounds;
    self.contentView.clipsToBounds = YES;
    if (self.subShowStyle == XMIAdSubShowStyleBubbleOnDraft) {
        self.contentView.xmi_left = 0;
        self.contentView.xmi_width = 0;
    } else {
        self.contentView.xmi_centerX = self.xmi_width * 0.5f;
        self.contentView.xmi_width = [self contentWidth];
        if (!self.contentView.maskView) {
            self.contentView.maskView = [[UIView alloc] init];
            self.contentView.backgroundColor = [UIColor clearColor];
            UIView *centerView = [[UIView alloc] init];
            centerView.backgroundColor = [UIColor whiteColor];
            centerView.tag = 102;
            [self.contentView.maskView addSubview:centerView];
        }
        self.contentView.maskView.frame = self.contentView.bounds;
        UIView *centerView = [self.contentView.maskView viewWithTag:102];
        centerView.xmi_size = CGSizeMake(self.contentView.xmi_width * 0.2f, self.contentView.xmi_height);
        centerView.center  = CGPointMake(self.contentView.xmi_width * 0.5f, self.contentView.xmi_height * 0.5f);
        self.contentView.alpha = 0.3f;
    }
    [UIView animateWithDuration:0.4f animations:^{
        if (self.subShowStyle == XMIAdSubShowStyleBubbleOnDraft) {
            self.contentView.xmi_width = [self contentWidth];
        } else {
            UIView *centerView = [self.contentView.maskView viewWithTag:102];
            centerView.frame = self.contentView.bounds;
        }
        self.contentView.alpha = 1.0f;
    } completion:^(BOOL finished) {
        self.contentView.maskView = nil;
        if (finished) {
            if ([self shouldPlaySpreadAnimation]) {
                [self showSpreadAnimation:3];
            } else {
                [self showBtnAnimation];
            }
            self.titleLabel.text = self.title;
            [self resetScrollAnimation];
            
        }
    }];
}

- (void)resetScrollAnimation
{
    [self.titleScrollView.layer removeAllAnimations];
    self.titleScrollView.contentOffset = CGPointZero;
    CGFloat textWidth = [self.titleLabel sizeThatFits:CGSizeMake(MAXFLOAT, MAXFLOAT)].width;
    if (textWidth > self.titleContainer.xmi_width) {
        CGFloat speed = 25.0f;
        NSString *showText = [NSString stringWithFormat:@"%@    %@    ",self.title, self.title];
        self.titleLabel.text = showText;
        CGFloat scrollLength = [self.titleLabel sizeThatFits:CGSizeMake(MAXFLOAT, MAXFLOAT)].width;
        NSTimeInterval duration = scrollLength / speed;
        self.titleLabel.text = [NSString stringWithFormat:@"%@    %@    %@",self.title, self.title, self.title];
        self.titleLabel.xmi_width = scrollLength + self.titleContainer.xmi_width;
        self.titleLabel.xmi_left = 0;
        self.titleLabel.xmi_centerY = self.titleContainer.xmi_height * 0.5f;
        self.titleScrollView.contentSize = CGSizeMake(scrollLength + self.titleContainer.xmi_width, self.titleContainer.xmi_height);
        [UIView animateWithDuration:duration delay:0 options:UIViewAnimationOptionCurveLinear animations:^{
                            [self.titleScrollView setContentOffset:CGPointMake(scrollLength, 0)];
                        } completion:^(BOOL finished) {
                            if (finished) {
                                self.titleLabel.text = self.title;
                                [self.titleLabel sizeToFit];
                                self.titleLabel.xmi_left = 0;
                                self.titleLabel.xmi_centerY = self.titleContainer.xmi_height * 0.5f;
                                [self.titleScrollView setContentOffset:CGPointZero animated:NO];
                                self.titleScrollView.contentSize = self.titleContainer.xmi_size;
                                self.titleLabel.xmi_width = self.titleContainer.xmi_width;
                            }
                        }];
    } else {
        self.titleLabel.xmi_left = 0;
        self.titleLabel.xmi_centerY = self.titleContainer.xmi_height * 0.5f;
    }
}

- (void)updateHasShowedAnimation:(BOOL)hasShowedAnimation
{
    _hasShowedAnimation = hasShowedAnimation;
    if (hasShowedAnimation) {
        [self.spreadLayer removeAllAnimations];
        self.spreadLayer.hidden = YES;
        [self.titleScrollView.layer removeAllAnimations];
        [self.contentView.layer removeAllAnimations];
        [self.bubbleView.layer removeAllAnimations];
        self.contentView.transform = CGAffineTransformIdentity;
        self.btnLabel.backgroundColor = XMI_COLOR_RGB(0xFF4444);
        self.contentView.alpha = 1.0f;
        self.titleScrollView.contentOffset = CGPointZero;
        self.titleLabel.xmi_width = self.titleContainer.xmi_width;
        self.titleLabel.xmi_left = 0;
        self.titleLabel.text = self.title;
    }
}

- (void)showBtnAnimation
{
    [UIView animateWithDuration:0.8f animations:^{
        self.btnLabel.backgroundColor = XMI_COLOR_RGB(0xFF4444);
    }];
}

- (void)showSpreadAnimation:(NSInteger)count
{
    self.contentView.clipsToBounds = NO;
    if (count <= 0) {
        [self showBtnAnimation];
        return;
    }
    if (!self.spreadLayer) {
        self.spreadLayer = [CAShapeLayer layer];
        self.spreadLayer.fillColor = XMI_COLOR_RGBA(0xFFFFFF, 0.12f).CGColor;
        self.spreadLayer.fillRule = kCAFillRuleEvenOdd;
    }
    [self.bubbleView.layer insertSublayer:self.spreadLayer atIndex:0];
    self.spreadLayer.opacity = 1;
    CGFloat spreadInterval = 6.0f;
    CGFloat arrowHeight = [self arrowHeight];
    self.spreadLayer.frame = CGRectMake(- spreadInterval, - spreadInterval + arrowHeight, self.bubbleView.xmi_width + 2 * spreadInterval, self.bubbleView.xmi_height + 2 * spreadInterval - arrowHeight);
    self.spreadLayer.hidden = NO;
    NSTimeInterval duration = 0.6f;
    UIBezierPath *path = [UIBezierPath bezierPathWithRoundedRect:CGRectMake(spreadInterval, spreadInterval, self.bubbleView.xmi_width, self.bubbleView.xmi_height - arrowHeight) cornerRadius:self.bubbleView.layer.cornerRadius];
    UIBezierPath *subPath = [UIBezierPath bezierPathWithRoundedRect:CGRectMake(spreadInterval, spreadInterval, self.bubbleView.xmi_width, self.bubbleView.xmi_height - arrowHeight) cornerRadius:self.bubbleView.layer.cornerRadius];
    [path appendPath:subPath];
    [path setUsesEvenOddFillRule:YES];
    self.spreadLayer.path = path.CGPath;
    {
        UIBezierPath *path = [UIBezierPath bezierPathWithRoundedRect:self.spreadLayer.bounds cornerRadius:self.bubbleView.layer.cornerRadius];
        UIBezierPath *subPath = [UIBezierPath bezierPathWithRoundedRect:CGRectMake(spreadInterval, spreadInterval, self.bubbleView.xmi_width, self.bubbleView.xmi_height - arrowHeight) cornerRadius:self.bubbleView.layer.cornerRadius];
        [path appendPath:subPath];
        [path setUsesEvenOddFillRule:YES];
        CABasicAnimation *anim = [CABasicAnimation animationWithKeyPath:@"path"];
        anim.fromValue = (__bridge  id)self.spreadLayer.path;
        anim.toValue = (__bridge  id)path.CGPath;
        anim.duration = duration;
        [self.spreadLayer addAnimation:anim forKey:@"spread"];
    }
    CAKeyframeAnimation *anim = [CAKeyframeAnimation animationWithKeyPath:@"opacity"];
    anim.duration = duration;
    anim.values = @[@(0), @(1.0f), @(0)];
    anim.keyTimes = @[@(0), @(0.5f), @(1.0f)];
    [self.spreadLayer addAnimation:anim forKey:@"alpha"];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(duration * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self.spreadLayer removeAllAnimations];
        self.spreadLayer.hidden = YES;
        NSInteger nextCount = count - 1;
        if (count == 0) {
            [self showBtnAnimation];
        } else {
            [self showSpreadAnimation:nextCount];
        }
    });
    
}

- (NSArray<UIView *> *)clickableViews
{
    return @[self.contentView];
}

- (UIView *)bubbleView
{
    return [[UIView alloc] init];
}

- (CGFloat)contentWidth
{
    return 100;
}

- (void)updateBubbleAndTitle
{
    self.titleMaskView.frame = self.titleContainer.bounds;
    [[self.titleMaskView aktTagObjectWithKey:@"gradient"] setFrame:CGRectMake(0, 0, 24, self.titleMaskView.height)];
    self.titleContainer.maskView = self.titleMaskView;
    self.bubbleView.frame = self.contentView.bounds;
    self.titleScrollView.frame = self.titleContainer.bounds;
    self.titleLabel.xmi_left = 0;
    self.titleLabel.xmi_centerY = self.titleContainer.xmi_height * 0.5f;
}

- (BOOL)shouldPlaySpreadAnimation
{
    return NO;
}

- (CGFloat)arrowHeight
{
    return 0;
}

@end
