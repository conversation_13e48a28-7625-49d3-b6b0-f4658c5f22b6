//
//  XMIFeedAdModel.m
//  Pods-XMAd_Example
//
//  Created by cu<PERSON>yuanz<PERSON> on 2022/2/28.
//

#import "XMIFeedAdModel.h"
#import "XMIAdNativeLogger.h"
#import "XMIFeedAdModelFactory.h"
#import "XMIAdConverter.h"
#import "XMIAdReporter+AD.h"
#import "XMICommonUtils.h"

#define XMIFeedAdVideoRetryCount 3

@interface XMIFeedAdModel ()

@property (nonatomic, assign) int64_t startMS;

@end

@implementation XMIFeedAdModel
{
    NSInteger _videoRetryCount;
}

+ (instancetype)feedAdModelWithRelatedData:(XMIAdRelatedData *)relatedData
{
    return [XMIFeedAdModelFactory adModelWithRelatedData:relatedData];
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        _videoRetryCount = 3;
    }
    return self;
}

- (void)loadAdData
{
    XMILogNativeAdInfo(@"开始读取第三方广告 dpsPositionID:%@", self.relatedData.dspPositionId);
    self.startMS = [XMICommonUtils currentMS];
}

- (NSString *)adTitle
{
    return self.relatedData.name;
}

- (NSString *)adDescription
{
    return self.relatedData.adDescription;
}

- (NSString *)adButtonText
{
    return self.relatedData.buttonText.length > 0 ? self.relatedData.buttonText : self.relatedData.clickTitle;
}

- (NSArray<NSString *> *)adImageURLs
{
    return @[self.relatedData.cover.length > 0 ? self.relatedData.cover : @""];
}

- (NSString *)adMark
{
    return self.relatedData.adMark;
}

- (NSArray<NSString *> *)adAdTags
{
    return self.relatedData.tags;
}

- (NSString *)adVideoURL
{
    return self.relatedData.videoUrl;
}

- (NSString *)modelIdentifier
{
    return [self.relatedData getIdentifier];
}

- (XMIInScreenSource)adInScreenSource
{
    return self.relatedData.inScreenSource;
}

- (NSString *)adMaterialProvideSource
{
    return self.relatedData.materialProvideSource;
}

- (BOOL)adNeedRender
{
    return self.relatedData.needRender;
}
 
- (CGSize)adRenderSize
{
    return CGSizeMake(self.relatedData.renderWidth, self.relatedData.renderHeight);
}

- (id)adData
{
    return self.relatedData;
}

- (UIView *)templateAdView
{
    return nil;
}

- (void)loadSuccess
{
    self.requestTimeCost = [XMICommonUtils currentMS] - self.startMS;
    BOOL timeOut = NO;
    if (self.loadingStatus == XMIFeedAdModelLoadingStatusTimeout) {
        timeOut = YES;
    }
    self.loadingStatus = XMIFeedAdModelLoadingStatusLoadSuccess;
    XMILogNativeAdInfo(@"读取第三方广告成功 dpsPositionID:%@", self.relatedData.dspPositionId);
    if ([self.delegate respondsToSelector:@selector(feedAdModelDidLoadDataSuccess: timeout:)]) {
        [self.delegate feedAdModelDidLoadDataSuccess:self timeout:timeOut];
    }
    [XMIAdReporter dspSDKReportLoad:self.relatedData];
    
}

- (void)loadFailed:(NSError *)error
{
    self.requestTimeCost = [XMICommonUtils currentMS] - self.startMS;
    self.loadingStatus = XMIFeedAdModelLoadingStatusLoadFailed;
    XMILogNativeAdError(@"读取第三方广告 dspPositionID: %@ eror: %@", self.relatedData.dspPositionId, error);
    if ([self.delegate respondsToSelector:@selector(feedAdModel:didLoadDataFailWithError:)]) {
        [self.delegate feedAdModel:self didLoadDataFailWithError:error];
    }
}

- (void)win:(nullable NSNumber *)auctionBidToWin {
    
}

- (void)loss:(nullable NSNumber *)auctionPrice {
    
}

- (BOOL)shouldRetryVideo
{
    if (_videoRetryCount > 0) {
        _videoRetryCount -= 1;
        return YES;
    }
    return NO;
}

- (BOOL)isVirtual
{
    return [XMIAdConverter isVirtualAD:self.relatedData.adid];
}

- (NSString *)iconUrl
{
    return self.relatedData.iconUrl;
}

- (NSString *)loadingStatusString {
    switch (self.loadingStatus) {
        case XMIFeedAdModelLoadingStatusInit:
            return @"Init";
            break;
        case XMIFeedAdModelLoadingStatusLoading:
            return @"Loading";
            break;
        case XMIFeedAdModelLoadingStatusTimeout:
            return @"Timeout";
            break;
        case XMIFeedAdModelLoadingStatusLoadSuccess:
            return @"Success";
            break;
        case XMIFeedAdModelLoadingStatusLoadFailed:
            return @"Failed";
            break;
        default:
            return @"Undefine";
            break;
    }
}

@end
