//
//  XMIFeedAdRenderer.h
//  Pods-XMAd_Example
//
//  Created by cu<PERSON><PERSON><PERSON><PERSON> on 2022/2/28.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <XMAd/XMIFeedAdViewProtocol.h>
#import <XMAd/XMIFeedAdModel.h>

NS_ASSUME_NONNULL_BEGIN

extern NSString *XMIFeedAdRendererScrollViewDidScrollNotification;
extern NSString *XMIFeedAdRendererControllerDidAppearNotification;
extern NSString *XMIFeedAdRendererControllerDidDisappearNotification;
extern NSString * const kUserInfoJumpNotSupport;

@class XMIFeedAdRenderer;

@protocol XMIFeedAdRendererDelegate <NSObject>

@optional

- (CGSize)feedAdRendererWillRender:(XMIFeedAdRenderer *)renderer;

- (void)feedAdRendererdidRender:(XMIFeedAdRenderer *)renderer;

- (void)feedAdRenderer:(XMIFeedAdRenderer *)renderer didRenderFailWithError:(NSError *)error;

- (void)feedAdRendererDidExpose:(XMIFeedAdRenderer *)renderer;

- (void)feedAdRenderer:(XMIFeedAdRenderer *)renderer adViewDidClick:(UIView<XMIFeedAdViewProtocol> *)aView withUserInfo:(nullable NSDictionary *)userInfo;

- (void)feedAdRendererDidRemoved:(XMIFeedAdRenderer *)renderer;

- (void)feedAdRendererWillPresentScreen:(XMIFeedAdRenderer *)renderer;

- (void)feedAdRendererDidPresentScreen:(XMIFeedAdRenderer *)renderer;

- (void)feedAdRendererDetailControllerDidClosed:(XMIFeedAdRenderer *)renderer;

- (void)feedAdRenderer:(XMIFeedAdRenderer *)renderer playerStateChanged:(XMIPlayerPlayState)state;

- (void)feedAdRenderer:(XMIFeedAdRenderer *)renderer playerDidPlayFinish:(NSError *_Nullable)error;

- (void)feedAdRenderer:(XMIFeedAdRenderer *)renderer dislikeWithReason:(NSArray<__kindof NSString *> *)reasons;
/// 点击关闭广告
- (void)feedAdRenderDidClickedClose:(XMIFeedAdRenderer *)renderer
                           userInfo:(nullable NSDictionary *)userInfo;

- (void)feedAdRenderDidClose:(XMIFeedAdRenderer *)renderer;

- (void)feedAdRenderer:(XMIFeedAdRenderer *)renderer clickAnchorId:(NSString *)anchorId;
@end

@interface XMIFeedAdRenderer : NSObject<XMIFeedAdViewDelegate, NSCopying>

- (instancetype)initWithRenderingViewClass:(Class)class;

@property (nonatomic, strong, readonly)Class renderingViewClass;

- (UIView<XMIFeedAdViewProtocol> *)renderedView;

@property (nonatomic, strong) XMIFeedAdModel *adModel;

@property (nonatomic, weak) UIViewController *rootViewController;

@property (nonatomic, weak) id<XMIFeedAdRendererDelegate> delegate;;

@property (nonatomic, assign, readonly) BOOL hasRendered;

@property (nonatomic, assign) XMIAdShowSubStyle subShowStyle;

@property (nonatomic, assign) BOOL onlyReportExposeOnce; //真实曝光是否只上报一次

@property (nonatomic, assign) UIEdgeInsets edge;

- (void)checkExpose;

- (void)closeAd;

- (void)closeSubAd:(NSInteger)index;

- (BOOL)isVirtual;

@end

NS_ASSUME_NONNULL_END
