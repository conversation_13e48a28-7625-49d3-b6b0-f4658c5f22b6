//
//  XMIFeedAdBaseView.m
//  XMAd
//
//  Created by <PERSON><PERSON>yuan<PERSON><PERSON> on 2022/3/1.
//

#import "XMIFeedAdBaseView.h"
#import "XMIAdNativeLogger.h"

@implementation XMIFeedAdBaseView
@synthesize delegate;

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.edge = UIEdgeInsetsMake(16, 16, 16, 16);
    }
    return self;
}

- (UIView *)contentView
{
    if (!_contentView) {
        _contentView = [[UIView alloc] init];
        [self addSubview:_contentView];
    }
    return _contentView;
}

#pragma mark - adview protocol

- (UIView *)dislikePointView {
    return self;
}

- (void)didMoveToWindow
{
    [super didMoveToWindow];
    if ([self.delegate respondsToSelector:@selector(feedAdViewDidMoveToWindow:)]) {
        [self.delegate feedAdViewDidMoveToWindow:self];
    }
}

- (void)setFrame:(CGRect)frame
{
    [super setFrame:frame];
    if ([self.delegate respondsToSelector:@selector(feedAdViewFrameDidChange:)]) {
        [self.delegate feedAdViewFrameDidChange:self];
    }
}

- (void)willExposeAdView
{
    if ([self.delegate respondsToSelector:@selector(feedAdViewWillExpose:)]) {
        [self.delegate feedAdViewWillExpose:self];
    }
}

- (void)didExposeAdView
{
    if ([self.delegate respondsToSelector:@selector(feedAdViewDidExpose:)]) {
        [self.delegate feedAdViewDidExpose:self];
    }
}

- (void)willUnExposeAdView
{
    if ([self.delegate respondsToSelector:@selector(feedAdViewWillUnExpose:)]) {
        [self.delegate feedAdViewWillUnExpose:self];
    }
}

- (void)didUnExposeAdView
{
    if ([self.delegate respondsToSelector:@selector(feedAdViewDidUnExpose:)]) {
        [self.delegate feedAdViewDidUnExpose:self];
    }
}

- (void)closeAdViewClick
{
    if ([self.delegate respondsToSelector:@selector(feedAdViewDidClickClose:userInfo:)]) {
        [self.delegate feedAdViewDidClickClose:self userInfo:nil];
    }
}

- (void)closeAdViewClickWithUserInfo:(nullable NSDictionary *)userInfo {
    if ([self.delegate respondsToSelector:@selector(feedAdViewDidClickClose:userInfo:)]) {
        [self.delegate feedAdViewDidClickClose:self userInfo:userInfo];
    }
}

- (void)closeAdView
{
    if ([self.delegate respondsToSelector:@selector(feedAdViewDidClose:)]) {
        [self.delegate feedAdViewDidClose:self];
    }
}

- (void)closeSubAdView:(NSInteger)index
{
    
}

- (void)updateCustomUI
{
    
}

- (void)didExposeDspView 
{
    if ([self.delegate respondsToSelector:@selector(feedAdViewDidExposeDspView:)]) {
        [self.delegate feedAdViewDidExposeDspView:self];
    }
}

- (void)clickAdView:(NSDictionary *)userInfo
{
    if ([self.delegate respondsToSelector:@selector(feedAdViewDidClick:withUserInfo:)]) {
        [self.delegate feedAdViewDidClick:self withUserInfo:userInfo];
    }
}

- (void)tapAdViewWithPoint:(CGPoint)tapPoint userInfo:(nullable NSDictionary *)userInfo
{
    if ([self.delegate respondsToSelector:@selector(feedAdViewHandleTap:tapPoint:userInfo:)]) {
        [self.delegate feedAdViewHandleTap:self tapPoint:tapPoint userInfo:userInfo];
    }
}

- (void)willPresentScreen
{
    if ([self.delegate respondsToSelector:@selector(feedAdViewWillPresentScreen:)]) {
        [self.delegate feedAdViewWillPresentScreen:self];
    }
}

- (void)didPresentScreen
{
    if ([self.delegate respondsToSelector:@selector(feedAdViewDidPresentScreen:)]) {
        [self.delegate feedAdViewDidPresentScreen:self];
    }
}

- (void)didCloseAdDetail
{
    if ([self.delegate respondsToSelector:@selector(feedAdViewDidCloseDetail:)]) {
        [self.delegate feedAdViewDidCloseDetail:self];
    }
}

- (void)failRenderWithError:(NSError *)error
{
    if ([self.delegate respondsToSelector:@selector(feedAdView:didFailRenderWithError:)]) {
        [self.delegate feedAdView:self didFailRenderWithError:error];
    }
}

- (void)playerStateChanged:(XMIPlayerPlayState)state
{
    if ([self.delegate respondsToSelector:@selector(playerStateChanged:)]) {
        [self.delegate feedAdView:self playerStateChanged:state];
    }
}

- (void)dislikeAdView:(NSArray *)reasons
{
    if ([self.delegate respondsToSelector:@selector(feedAdView:dislikeAdViewWithReasons:)]) {
        [self.delegate feedAdView:self dislikeAdViewWithReasons:reasons];
    }
}

- (void)removeFromSuperview
{
    [super removeFromSuperview];
    if ([self.delegate respondsToSelector:@selector(feedAdViewDidRemoveFromSuperview:)]) {
        [self.delegate feedAdViewDidRemoveFromSuperview:self];
    }
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
