//
//  XMIFeedAdModelGroup.m
//  XMAd
//
//  Created by cuiyuanzhe on 2022/3/8.
//

#import "XMIFeedAdModelGroup.h"

@implementation XMIFeedAdModelGroup

- (instancetype)init
{
    self.adModels = [NSMutableArray array];
    return self;
}

- (NSMethodSignature *)methodSignatureForSelector:(SEL)sel
{
    if (self.adModels.count > 0) {
        return [self.adModels[0] methodSignatureForSelector:sel];
    }
    return nil;
}

- (void)forwardInvocation:(NSInvocation *)invocation
{
    if (self.adModels.count > 0) {
        [invocation invokeWithTarget:self.adModels[0]];
    }
}

- (id)adData
{
    return self.adModels;
}

@end
