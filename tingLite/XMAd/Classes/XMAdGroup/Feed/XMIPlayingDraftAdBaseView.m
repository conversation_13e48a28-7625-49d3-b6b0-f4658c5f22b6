//
//  XMIPlayingDraftAdBaseView.m
//  XMAd
//
//  Created by cuiyuanzhe on 2022/3/8.
//

#import "XMIPlayingDraftAdBaseView.h"
#import "XMIAdButton.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import "UIView+XMIUtils.h"
#import "XMIAdMacro.h"
#import "XMICommonUtils.h"

@interface XMIPlayingDraftAdBaseView ()<CAAnimationDelegate>

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) UILabel *btnLabel;

@property (nonatomic, strong) XMIAdButton *closeButton;

@property (nonatomic, assign) BOOL isLargeImageStyle;

@end

@implementation XMIPlayingDraftAdBaseView
{
    BOOL _hasShowedAnimation;
}

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = XMI_COLOR_RGBA(0x000000, 0.15f);
    }
    return self;
}

#pragma mark - lazy load

- (UILabel *)titleLabel
{
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        [self.contentView addSubview:_titleLabel];
        _titleLabel.numberOfLines = 2;
    }
    return _titleLabel;
}

- (UILabel *)btnLabel
{
    if (!_btnLabel) {
        _btnLabel = [[UILabel alloc] init];
        _btnLabel.font = XMI_AD_PingFangFont(12);
        _btnLabel.textColor = XMI_COLOR_RGB(0xFFFFFF);
        _btnLabel.textAlignment = NSTextAlignmentCenter;
        _btnLabel.layer.cornerRadius = 11;
        _btnLabel.layer.masksToBounds = YES;
        _btnLabel.userInteractionEnabled = YES;
        [self addSubview:_btnLabel];
    }
    return _btnLabel;
}

- (UIImageView *)coverView
{
    if (!_coverView) {
        _coverView = [[UIImageView alloc] init];
        [self.contentView addSubview:_coverView];
        _coverView.layer.cornerRadius = 4;
        _coverView.layer.masksToBounds = YES;
        _coverView.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _coverView;
}

- (XMIAdButton *)closeButton
{
    if (!_closeButton) {
        _closeButton = [XMIAdButton buttonWithType:UIButtonTypeCustom];
        [self addSubview:_closeButton];
        [_closeButton setImage:[XMICommonUtils imageNamed:@"draft_ad_close"] forState:UIControlStateNormal];
        _closeButton.hitTestEdgeOutsets = UIEdgeInsetsMake(10, 10, 10, 10);
        [_closeButton addTarget:self action:@selector(closebuttonClicked:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _closeButton;
}

#pragma mark - adview protocol

- (void)updateImageURLs:(NSArray<NSString *> *)imageURLs
{
    UIImage *placeholderImage = [XMICommonUtils imageNamed:@"ad_bkg_default"];
    if (imageURLs.count == 0) {
        self.coverView.image = placeholderImage;
        return;
    }
    [self.coverView sd_setImageWithURL:[NSURL URLWithString:imageURLs.firstObject] placeholderImage:placeholderImage];
}

- (void)updateTitle:(NSString *)title
{
    NSMutableParagraphStyle *ps = [[NSMutableParagraphStyle alloc] init];
    ps.minimumLineHeight = 20;
    ps.alignment = NSTextAlignmentLeft;
    ps.lineBreakMode = NSLineBreakByTruncatingTail;
    self.titleLabel.attributedText = [[NSAttributedString alloc] initWithString:title attributes:@{NSFontAttributeName : XMI_AD_PingFangMediumFont(15), NSForegroundColorAttributeName : XMI_COLOR_RGB(0xFFFFFF), NSParagraphStyleAttributeName : ps}];
}

- (void)updateBtnText:(NSString *)btnText
{
    if (btnText.length > 4) {
        btnText = [btnText substringToIndex:4];
    }
    if (btnText.length == 0) {
        btnText = @"立即查看";
    }
    self.btnLabel.text = btnText;
}

- (void)updateClickableAreaType:(NSInteger)clickableAreaType
{
    BOOL enable = YES;
    switch (clickableAreaType) {
        case XMIAdClickableAreaTypeAll:
            enable = YES;
            break;
            
        case XMIAdClickableAreaTypeBtn:
            enable = NO;
            break;
            
        default:
            enable = YES;
            break;
    }
    [[self.contentView gestureRecognizers] enumerateObjectsUsingBlock:^(__kindof UIGestureRecognizer * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        obj.enabled = enable;
    }];
}

- (void)updateClosePadding:(UIEdgeInsets)closePaddding
{
    self.closeButton.hitTestEdgeOutsets = [XMIAdButton closeAreaPaddingWithDefaultPadding:UIEdgeInsetsMake(10, 10, 10, 10) extraPadding:closePaddding];
}

- (NSArray<UIView *> *)clickableViews
{
    return @[self.contentView, self.btnLabel];
}

- (void)closebuttonClicked:(id)sender
{
    [self closeAdViewClick];
}

- (void)updateCustomUI
{
    [self.btnLabel.layer removeAllAnimations];
    [NSObject cancelPreviousPerformRequestsWithTarget:self];
}

- (void)updateHasShowedAnimation:(BOOL)hasShowedAnimation
{
    _hasShowedAnimation = hasShowedAnimation;
    if (hasShowedAnimation) {
        _btnLabel.layer.borderColor = nil;
        _btnLabel.layer.borderWidth = 0;
        _btnLabel.backgroundColor = XMI_COLOR_RGB(0xFF4444);
    } else {
        _btnLabel.layer.borderColor = XMI_COLOR_RGBA(0xFFFFFF, 0.08f).CGColor;
        _btnLabel.layer.borderWidth = kADOnePixelsLineHeight;
        _btnLabel.backgroundColor = XMI_COLOR_RGBA(0xFFFFFF, 0.1f);
    }
}

- (void)updateADShowStyle:(NSInteger)showStyle
{
    self.isLargeImageStyle = (showStyle == XMIAdStyleDraftLargeImage);
}

- (void)sizeToFit
{
    BOOL smallScreen = XMI_SCREEN_HEIGHT <= 667.f;
    if (smallScreen) {
        self.bounds = CGRectMake(0, 0, XMI_SCREEN_WIDTH - 48, 68);
    } else {
        self.bounds = CGRectMake(0, 0, XMI_SCREEN_WIDTH - 48, 82);
    }
    self.titleLabel.numberOfLines = smallScreen ? 1 : 2;
    [self updateLayouts];
}

- (void)updateLayouts
{
    self.contentView.frame = self.bounds;
    self.closeButton.frame = CGRectMake(self.xmi_width - 17, 8, 9, 9);
    
    CGFloat interval = 8;
    CGFloat titleRightInterval = 29;
    CGFloat imageHeight = self.xmi_height - 2 * interval;
    
    BOOL smallScreen = XMI_SCREEN_HEIGHT <= 667.f;
    if (self.isLargeImageStyle) {
        CGFloat imageWidth = floor(imageHeight * 16.0f / 9.0f);
        self.coverView.xmi_size = CGSizeMake(imageWidth, imageHeight);
    } else {
        self.coverView.xmi_size = CGSizeMake(imageHeight, imageHeight);
    }
    self.coverView.xmi_centerY = self.contentView.xmi_height * 0.5f;
    self.coverView.xmi_left = interval;
    if (smallScreen) {
        self.titleLabel.frame = CGRectMake(self.coverView.xmi_right + interval, 9, self.xmi_width - titleRightInterval - interval - self.coverView.xmi_right, 19);
    } else {
        CGFloat titleWidth = self.xmi_width - titleRightInterval - interval - self.coverView.xmi_right;
        self.titleLabel.xmi_size = [self.titleLabel sizeThatFits:CGSizeMake(titleWidth, MAXFLOAT)];
        self.titleLabel.xmi_left = self.coverView.xmi_right + interval;
        self.titleLabel.xmi_top = self.titleLabel.xmi_height < 25 ? 20 : interval;
    }
    [self.btnLabel sizeToFit];
    self.btnLabel.xmi_width += 16;
    self.btnLabel.xmi_right = self.xmi_width - interval;
    self.btnLabel.xmi_height = 22;
    self.btnLabel.xmi_bottom = self.xmi_height - interval;
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    [self updateLayouts];
}

#pragma mark - animation

- (void)didExposeAdView
{
    [super didExposeAdView];
    if (!_hasShowedAnimation) {
        _hasShowedAnimation = YES;
        [self showAnimation];
    }
}


- (void)showAnimation
{
    if ([self.delegate respondsToSelector:@selector(feedAdViewDidShowAnimation:)]) {
        [self.delegate feedAdViewDidShowAnimation:self];
    }
    //on runloop defalut mode，滑动的时候不出动画
    [self performSelector:@selector(doBtnAnimation) withObject:nil afterDelay:1.0f];
}

- (void)doBtnAnimation
{
    CGFloat duration = 1.0f;
    [UIView animateWithDuration:duration animations:^{
        self.btnLabel.backgroundColor = XMI_COLOR_RGB(0xFF4444);
    }];
    CABasicAnimation *animation = [CABasicAnimation animationWithKeyPath:@"borderColor"];
    animation.toValue = (__bridge id _Nullable)XMI_COLOR_RGBA(0xFFFFFF, 0).CGColor;
    animation.duration = duration;
    animation.delegate = self;
    animation.removedOnCompletion = YES;
    [self.btnLabel.layer addAnimation:animation forKey:@"layerBorder"];
}

- (void)animationDidStop:(CAAnimation *)anim finished:(BOOL)flag
{
    self.btnLabel.layer.borderColor = nil;
    self.btnLabel.layer.borderWidth = 0;
}

- (void)dealloc
{
    
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
