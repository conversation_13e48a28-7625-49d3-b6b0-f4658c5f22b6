//
//  XMIFeedAdVideoPlayerProtocol.h
//  Pods-XMAd_Example
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/28.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol XMIFeedAdVideoPlayerProtocol <NSObject>
/*
重复播放次数
*/
@property (nonatomic, assign) NSInteger repeatTimes;

/*
播放
*/
- (void)play;
/**
暂停
*/
- (void)pause;
/**
停止
*/
- (void)stop;
/**
time 单位秒
*/
- (void)seek:(CGFloat)time;
/**
重播，等价于seek:0
*/
- (void)replay;
/**
是否正在播放
*/
- (BOOL)isPlaying;

@end

NS_ASSUME_NONNULL_END
