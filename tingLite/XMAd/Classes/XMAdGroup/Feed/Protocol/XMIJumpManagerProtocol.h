//
//  XMIJumpManagerProtocol.h
//  XMAd
//
//  Created by cu<PERSON><PERSON>z<PERSON> on 2022/3/3.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class XMIAdRelatedData;
@protocol XMIJumpManagerProtocol;

@protocol XMIJumpManagerDelegate <NSObject>

@optional
- (void)jumpManager:(id<XMIJumpManagerProtocol>)jumpManager adWillPresentScreen:(XMIAdRelatedData *)adData;
- (void)jumpManager:(id<XMIJumpManagerProtocol>)jumpManager adDidPresentScreen:(XMIAdRelatedData *)adData;
- (void)jumpManager:(id<XMIJumpManagerProtocol>)jumpManager adDetailControllerDidClosed:(XMIAdRelatedData *)adData;

@end

@protocol XMIJumpManagerProtocol <NSObject>

@property (nonatomic, weak) id<XMIJumpManagerDelegate> delegate;
@property (nonatomic, weak) UIViewController *rootViewController;

/**
 广告点击跳转
 @param adData :广告数据
 */
- (BOOL)doJumpWithAd:(XMIAdRelatedData *)adData;


@end

NS_ASSUME_NONNULL_END
