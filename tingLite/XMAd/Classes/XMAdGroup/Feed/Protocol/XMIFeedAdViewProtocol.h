//
//  XMIFeedAdViewProtocol.h
//  Pods-XMAd_Example
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/28.
//

#import <Foundation/Foundation.h>
#import <XMAd/XMIFeedAdVideoPlayerProtocol.h>
#import <XMAd/XMIAdDefines.h>

NS_ASSUME_NONNULL_BEGIN

@protocol XMIFeedAdViewProtocol;

@protocol XMIFeedAdViewDelegate <NSObject>

@optional

/*
 UIView didMoveToWindow
 */
- (void)feedAdViewDidMoveToWindow:(UIView<XMIFeedAdViewProtocol> *)feedAdView;

/*
 UIView didMoveToWindow
 */
- (void)feedAdViewFrameDidChange:(UIView<XMIFeedAdViewProtocol> *)feedAdView;

/*
 将要曝光
 */
- (void)feedAdViewWillExpose:(UIView<XMIFeedAdViewProtocol> *)feedAdView;

/*
 曝光后
 */
- (void)feedAdViewDidExpose:(UIView<XMIFeedAdViewProtocol> *)feedAdView;

/*
 将要从屏幕上消失
 */
- (void)feedAdViewWillUnExpose:(UIView<XMIFeedAdViewProtocol> *)feedAdView;

/*
 曝从屏幕上消失后
 */
- (void)feedAdViewDidUnExpose:(UIView<XMIFeedAdViewProtocol> *)feedAdView;

/*
 曝从屏幕上消失后
 */
- (void)feedAdViewDidRemoveFromSuperview:(UIView<XMIFeedAdViewProtocol> *)feedAdView;

/*
即将弹出控制器
 */
- (void)feedAdViewWillPresentScreen:(UIView<XMIFeedAdViewProtocol> *)feedAdView;

/*
 弹出控制器后
 */
- (void)feedAdViewDidPresentScreen:(UIView<XMIFeedAdViewProtocol> *)feedAdView;

/*
 关闭详情页
 */
- (void)feedAdViewDidCloseDetail:(UIView<XMIFeedAdViewProtocol> *)feedAdView;

/*
dsp曝光事件上报
 */
- (void)feedAdViewDidExposeDspView:(UIView<XMIFeedAdViewProtocol> *)feedAdView;

/*
点击事件上报
 */
- (void)feedAdViewDidClick:(UIView<XMIFeedAdViewProtocol> *)feedAdView withUserInfo:(nullable NSDictionary *)userInfo;

/*
自定义跳转
 */
- (void)feedAdViewHandleTap:(UIView<XMIFeedAdViewProtocol> *)feedAdView
                   tapPoint:(CGPoint)tapPoint
                   userInfo:(nullable NSDictionary *)userInfo;

/*
点击关闭
 */
- (void)feedAdViewDidClickClose:(UIView<XMIFeedAdViewProtocol> *)feedAdView
                       userInfo:(nullable NSDictionary *)userInfo;

/*
点击不感兴趣
 */
- (void)feedAdView:(UIView<XMIFeedAdViewProtocol> *)feedAdView dislikeAdViewWithReasons:(NSArray *)reasons;

/*
关闭
 */
- (void)feedAdViewDidClose:(UIView<XMIFeedAdViewProtocol> *)feedAdView;

/*
播放器状态变化
 */
- (void)feedAdView:(UIView<XMIFeedAdViewProtocol> *)feedAdView playerStateChanged:(XMIPlayerPlayState)state;

/*
播放器时间变化
 */
- (void)feedAdView:(UIView<XMIFeedAdViewProtocol> *)feedAdView playTimeDidChanged:(CGFloat)currentTime;

/*
渲染失败
 */
- (void)feedAdView:(UIView<XMIFeedAdViewProtocol> *)feedAdView didFailRenderWithError:(NSError *)error;

/*
出现动画
 */
- (void)feedAdViewDidShowAnimation:(UIView<XMIFeedAdViewProtocol> *)feedAdView;

/*
视频加载失败是否需要重试
 */
- (BOOL)feedAdViewShouldRetryVideo:(UIView<XMIFeedAdViewProtocol> *)feedAdView;

/*
点击了作者
 */
- (void)anchorItemClicked:(UIView<XMIFeedAdViewProtocol> *)feedAdView anchorId:(nullable NSString *)anchorId;

@end

@protocol XMIFeedAdViewProtocol <NSObject>

@required

@property (nonatomic, weak) id<XMIFeedAdViewDelegate> delegate;

@property (nonatomic, assign) UIEdgeInsets edge;

- (void)willExposeAdView;

- (void)didExposeAdView;

- (void)didExposeDspView;

- (void)dislikeAdView:(NSArray *)reasons;

- (void)closeAdViewClick;

- (void)closeAdViewClickWithUserInfo:(nullable NSDictionary *)userInfo;

- (void)closeAdView;

- (void)closeSubAdView:(NSInteger)index;

- (void)clickAdView:(NSDictionary *)userInfo;

- (void)tapAdViewWithPoint:(CGPoint)tapPoint userInfo:(nullable NSDictionary *)userInfo;

- (void)willUnExposeAdView;

- (void)didUnExposeAdView;

- (void)willPresentScreen;

- (void)didPresentScreen;

- (void)didCloseAdDetail;

- (void)failRenderWithError:(NSError *)error;

@optional

- (UIView *)dislikePointView;

- (void)didNewExposeAdView;

- (void)replay;

- (void)play;

- (void)stop;

- (void)pause;

- (void)resume;

- (BOOL)isPlaying;

- (void)playerStateChanged:(XMIPlayerPlayState)state;

- (void)updateTitle:(NSString *)title;

- (void)updateDescription:(NSString *)description;

- (void)updateBtnText:(NSString *)btnText;

- (void)updateImageURLs:(NSArray<NSString *> *)imageURLs;

- (void)updateAdMark:(NSString *)adMark;

- (void)updateAdTags:(NSArray *)tags;

- (void)updateVideoURL:(NSString *)videoURL indentifier:(NSString *)identifier;

- (void)updateInScreenSource:(XMIInScreenSource)inScreenSource materialProvideSource:(NSString *)materialProvideSource;

- (void)updateNeedRender:(CGFloat)needRender renderSize:(CGSize)renderSize;

- (void)updateHasShowedAnimation:(BOOL)hasShowedAnimation;

- (void)updateClosePadding:(UIEdgeInsets)closePaddding;

- (void)updateClickableAreaType:(NSInteger)clickableAreaType;

- (void)updateCustomUI;

- (void)updateRootViewController:(UIViewController *)rootViewController;

- (NSArray<UIView *> *)clickableViews;

+ (CGFloat)calAdHeight:(id)adData withAdWidth:(CGFloat)adWidth;

- (void)updateUIWhenScrolled;

- (void)updateADShowStyle:(NSInteger)showStyle;

- (BOOL)supportRealExpose;

- (void)updateSubShowStyle:(XMIAdShowSubStyle)subShowStyle;

- (void)updateIconUrl:(NSString *)iconUrl;

- (void)updateShakeEnabled:(BOOL)shakeEnabled;

- (void)reloadUI;

@end

@protocol XMIFeedAdViewCustomRenderProtocol <NSObject>

- (void)customRenderWithAdData:(id)adData; //自行解析adData

@end

@protocol XMIFeedAdViewArtistProtocol <NSObject>

@optional

- (void)updateArtistNameText:(NSString *)artistNameText;

- (void)updateArtistAvatar:(NSString *)avatarURL;

- (void)setArrowCenterX:(CGFloat)arrowCenterX;

- (void)hideAvatar:(BOOL)hide;

@end

@protocol XMIFeedCreativeAdViewProtocol <NSObject>

- (void)setScrollAreaInWindow:(CGRect)scrollAreaInWindow;

@end

NS_ASSUME_NONNULL_END
