//
//  XMIFeedAdViewFactory.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/4.
//

#import "XMIFeedAdViewFactory.h"
#import "XMIFeedAdBaseView.h"
#import "XMIAdConverter.h"

@implementation XMIFeedAdViewFactory

+ (Class)feedAdViewClasslWithShowStyle:(XMIAdShowStyle)showstyle adType:(XMIAdType)adType
{
    return [self feedAdViewClasslWithShowStyle:showstyle adType:adType subShowStyle:XMIAdSubShowSubStyleNone];
}

+ (Class)feedAdViewClasslWithShowStyle:(XMIAdShowStyle)showstyle adType:(XMIAdType)adType subShowStyle:(XMIAdShowSubStyle)subShowStyle
{
    NSString *provider = [XMIAdConverter providerFromAdType:adType];
    
    if (provider.length > 0) {
        NSString *clsName = [NSString stringWithFormat:@"XMI%@FeedAdViewFactory", provider];
        __typeof(self) cls = NSClassFromString(clsName);
        if (cls != nil) {
            return [cls feedAdViewClasslWithShowStyle:showstyle adType:adType subShowStyle:subShowStyle];
        }
    }
    return [XMIFeedAdBaseView class];
}

@end
