//
//  XMIFeedAdRenderer.m
//  Pods-XMAd_Example
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/28.
//

#import "XMIFeedAdRenderer.h"
#import <objc/runtime.h>
#import "UIView+XMIUtils.h"
#import "XMIAdError.h"
#import "UIView+XMIUtils.h"
#import "XMIAdReporter+AD.h"
#import "XMIAdNativeLogger.h"
#import "XMIJumpManagerFactory.h"
#import "XMIJumpManagerProtocol.h"
#import "XMIAdConverter.h"
#import "XMIAdManager.h"
#import <XMConfigCenter/XMConfigCenter.h>

NSString * XMIFeedAdRendererScrollViewDidScrollNotification = @"XMIFeedAdRendererScrollViewDidScrollNotification";
NSString * XMIFeedAdRendererControllerDidAppearNotification = @"XMIFeedAdRendererControllerDidAppearNotification";
NSString * XMIFeedAdRendererControllerDidDisappearNotification = @"XMIFeedAdRendererControllerDidDisappearNotification";

@interface XMIFeedAdRenderer ()<XMIFeedAdModelDelegate, XMIJumpManagerDelegate>

@property (nonatomic, strong)Class renderingViewClass;

@property (nonatomic, strong) id<XMIJumpManagerProtocol> jumpManager;

@property (nonatomic, weak) UIScrollView *scrollView;

@end

@implementation XMIFeedAdRenderer
{
    UIView<XMIFeedAdViewProtocol> *_renderingView;
    BOOL _exposed;
    BOOL _newExposed;
    BOOL _newExposed50;
}

- (instancetype)initWithRenderingViewClass:(Class)class
{
    self = [self init];
    if (self) {
        NSAssert([class conformsToProtocol:@protocol(XMIFeedAdViewProtocol)], @"必须遵从协议!");
        self.renderingViewClass = class;
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didReceiveDidScrollNotification:) name:XMIFeedAdRendererScrollViewDidScrollNotification object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didReceiveDidAppearNotification:) name:XMIFeedAdRendererControllerDidAppearNotification object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didReceiveDidDisappearNotification:) name:XMIFeedAdRendererControllerDidDisappearNotification object:nil];
    }
    return self;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.onlyReportExposeOnce = NO;
        _edge = UIEdgeInsetsMake(16, 16, 16, 16);
    }
    return self;
}

- (UIView<XMIFeedAdViewProtocol> *)renderedView
{
    if (!_renderingView) {
        _hasRendered = YES;
        _renderingView = [[self.renderingViewClass alloc] initWithFrame:CGRectZero];
        _renderingView.delegate = self;
        if ([_renderingView respondsToSelector:@selector(clickableViews)]) {
            NSArray<UIView *> *views = [_renderingView clickableViews];
            [views enumerateObjectsUsingBlock:^(UIView * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                UITapGestureRecognizer* tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleTapGesture:)];
                if ([obj respondsToSelector:@selector(gestureRecognizerShouldBegin:)]) {
                    tap.delegate = obj;
                }
                [obj addGestureRecognizer:tap];
            }];
        }
        if ([_renderingView respondsToSelector:@selector(setEdge:)]) {
            [_renderingView setEdge:_edge];
        }
        if (_adModel
            && ![self isVirtual] // 如果是虚拟广告，不渲染
            && _adModel.loadingStatus == XMIFeedAdModelLoadingStatusLoadSuccess) {
            [self refreshUI];
        }
    }
    return _renderingView;
}

- (id<XMIJumpManagerProtocol>)jumpManager
{
    if (!_jumpManager) {
        _jumpManager = [XMIJumpManagerFactory jumpManagerByAdType:self.adModel.relatedData.adtype];
        _jumpManager.delegate = self;
        _jumpManager.rootViewController = self.rootViewController;
    }
    return _jumpManager;
}

- (void)setAdModel:(XMIFeedAdModel *)adModel
{
    _adModel = adModel;
    _exposed = NO;
    _adModel.delegate = self;
    if (!self.rootViewController) {
        self.rootViewController = adModel.rootViewController;
    }
    [_adModel loadAdData];
    if (_renderingView && _adModel.loadingStatus == XMIFeedAdModelLoadingStatusLoadSuccess) {
        [self refreshUI];
    }
    if (adModel && _renderingView.superview) {
        [self checkExpose];
    }
}

- (void)refreshUI
{
    CGSize size = CGSizeMake(_adModel.adWidth, _adModel.adHeight);
    if (CGSizeEqualToSize(size, CGSizeZero)) {
        if ([self.delegate respondsToSelector:@selector(feedAdRendererWillRender:)]) {
            size = [self.delegate feedAdRendererWillRender:self];
            _adModel.adWidth = size.width;
            _adModel.adHeight = size.height;
        }
    }
    if ([_renderingView respondsToSelector:@selector(updateADShowStyle:)]) {
        [_renderingView updateADShowStyle:_adModel.relatedData.showstyle];
    }
    if ([_renderingView respondsToSelector:@selector(updateSubShowStyle:)]) {
        [_renderingView updateSubShowStyle:self.subShowStyle];
    }
    if ([_renderingView respondsToSelector:@selector(updateTitle:)]) {
        [_renderingView updateTitle:[_adModel adTitle]];
    }
    if ([_renderingView respondsToSelector:@selector(updateDescription:)]) {
        [_renderingView updateDescription:[_adModel adDescription]];
    }
    if ([_renderingView respondsToSelector:@selector(updateBtnText:)]) {
        [_renderingView updateBtnText:[_adModel adButtonText]];
    }
    if ([_renderingView respondsToSelector:@selector(updateImageURLs:)]) {
        [_renderingView updateImageURLs:[_adModel adImageURLs]];
    }
    if ([_renderingView respondsToSelector:@selector(updateAdMark:)]) {
        [_renderingView updateAdMark:[_adModel adMark]];
    }
    if ([_renderingView respondsToSelector:@selector(updateAdTags:)]) {
        [_renderingView updateAdTags:[_adModel adAdTags]];
    }
    if ([_renderingView respondsToSelector:@selector(updateVideoURL:indentifier:)]) {
        [_renderingView updateVideoURL:[_adModel adVideoURL] indentifier:[_adModel modelIdentifier]];
    }
    if ([_renderingView respondsToSelector:@selector(updateInScreenSource:materialProvideSource:)]) {
        [_renderingView updateInScreenSource:[_adModel adInScreenSource] materialProvideSource:[_adModel adMaterialProvideSource]];
    }
    if ([_renderingView respondsToSelector:@selector(updateClosePadding:)]) {
        [_renderingView updateClosePadding:_adModel.relatedData.closeAreaPaddingEdge];
    }
    if ([_renderingView respondsToSelector:@selector(updateIconUrl:)]) {
        [_renderingView updateIconUrl:[_adModel iconUrl]];
    }
    if (!CGSizeEqualToSize(CGSizeMake(self.adModel.adWidth, self.adModel.adHeight), CGSizeZero)) {
        _renderingView.xmi_size = CGSizeMake(self.adModel.adWidth, self.adModel.adHeight);
    }
    if ([_renderingView respondsToSelector:@selector(updateNeedRender:renderSize:)]) {
        [_renderingView updateNeedRender:[_adModel adNeedRender] renderSize:[_adModel adRenderSize]];
    }
    if ([_renderingView respondsToSelector:@selector(updateRootViewController:)]) {
        [_renderingView updateRootViewController:_rootViewController];
    }
    if ([_renderingView respondsToSelector:@selector(updateClickableAreaType:)]) {
        [_renderingView updateClickableAreaType:_adModel.relatedData.clickableAreaType];
    }
    if ([_renderingView respondsToSelector:@selector(updateShakeEnabled:)]) {
        [_renderingView updateShakeEnabled:_adModel.relatedData.enableShake];
    }
    if ([_renderingView respondsToSelector:@selector(updateCustomUI)]) {
        [_renderingView updateCustomUI];
    }
    if ([_renderingView respondsToSelector:@selector(customRenderWithAdData:)]) {
        [(id<XMIFeedAdViewCustomRenderProtocol>)_renderingView customRenderWithAdData:[_adModel adData]];
    }
    [_renderingView sizeToFit];
    if ([_renderingView respondsToSelector:@selector(updateHasShowedAnimation:)]) {
        [_renderingView updateHasShowedAnimation:[_adModel hasShowedAnimation]];
    }
    self.adModel.adWidth = _renderingView.xmi_width;
    self.adModel.adHeight = _renderingView.xmi_height;
}

#pragma mark - feedadview delegate

- (void)feedAdViewDidMoveToWindow:(UIView<XMIFeedAdViewProtocol> *)feedAdView
{
    UIView *superview = feedAdView.superview;
    for (NSInteger i = 0; i < 10; i++) {
        if ([super isKindOfClass:[UIScrollView class]]) {
            self.scrollView = (UIScrollView *)superview;
            break;
        }
        superview = superview.superview;
        if (!superview) {
            break;;
        }
    }
    [self checkExpose];
}

- (void)feedAdViewFrameDidChange:(UIView<XMIFeedAdViewProtocol> *)feedAdView
{
    if (feedAdView.window) {
        [self checkExpose];
    }
}

- (void)feedAdViewDidClick:(UIView<XMIFeedAdViewProtocol> *)feedAdView withUserInfo:(NSDictionary *)userInfo {
    //跳转上报
    XMILogNativeAdInfo(@"FeedAd Did Click, %@", self.adModel.relatedData);
    // 点击上报
    XMIAdRelatedData *adData = self.adModel.relatedData;
    XMIAdRelatedData *realItem = userInfo[@"realClickItem"];
    if ([realItem isKindOfClass:[XMIAdRelatedData class]]) {
        adData = realItem;
    }
    [XMIAdReporter clickReportWithAd:adData andView:feedAdView andUserInfo:userInfo];
    if ([self.delegate respondsToSelector:@selector(feedAdRenderer:adViewDidClick:withUserInfo:)]) {
        [self.delegate feedAdRenderer:self adViewDidClick:feedAdView withUserInfo:userInfo];
    }
    [XMIAdReporter dspSDKReportClick:adData];
    
}

- (void)feedAdViewHandleTap:(UIView<XMIFeedAdViewProtocol> *)feedAdView
                   tapPoint:(CGPoint)tapPoint
                   userInfo:(nullable NSDictionary *)userInfo {
    NSMutableDictionary *dic = [[NSMutableDictionary alloc] init];
    if (userInfo) {
        [dic addEntriesFromDictionary:userInfo];
    }
    // 防止除0问题
    if (_renderingView.xmi_width > 0 && _renderingView.xmi_height > 0) {
        NSString *absX = [NSString stringWithFormat:@"%d", (int)tapPoint.x];
        NSString *absY = [NSString stringWithFormat:@"%d", (int)tapPoint.y];
        NSString *x = [NSString stringWithFormat:@"%.2f", _renderingView.xmi_width];
        NSString *y = [NSString stringWithFormat:@"%.2f", _renderingView.xmi_height];
        dic[@"absX"] = absX;
        dic[@"absY"] = absY;
        dic[@"x"] = x;
        dic[@"y"] = y;
    }
    //sdk广告不做跳转处理
    if (self.adModel.relatedData.adtype == XMIAdTypeXM) {
        XMIAdRelatedData *adData = self.adModel.relatedData;
        if (userInfo) {
            XMIAdRelatedData *realItem = userInfo[@"realClickItem"];
            if ([realItem isKindOfClass:[XMIAdRelatedData class]]) {
                adData = realItem;
            }
        }
        BOOL jumpSupport = [self.jumpManager doJumpWithAd:adData];
        if (!jumpSupport) {
            if ([[XMIAdManager sharedInstance].delegate respondsToSelector:@selector(managerHandleSDKNotSupportedAdJump:)]) {
                //处理不了交给主站跳转
                [[XMIAdManager sharedInstance].delegate managerHandleSDKNotSupportedAdJump:adData];
            }
            dic[kUserInfoJumpNotSupport] = @(YES);
        }
    } else {
        NSLog(@"not support");
    }
  
    [_renderingView clickAdView:dic];
}

- (void)feedAdViewDidClickClose:(nonnull UIView<XMIFeedAdViewProtocol> *)feedAdView 
                       userInfo:(nullable NSDictionary *)userInfo {
    if ([self.delegate respondsToSelector:@selector(feedAdRenderDidClickedClose:userInfo:)]) {
        [self.delegate feedAdRenderDidClickedClose:self userInfo:userInfo];
    }
    XMILogNativeAdInfo(@"FeedAd Click Close, %@", self.adModel.relatedData);
}

- (void)feedAdViewDidClose:(UIView<XMIFeedAdViewProtocol> *)feedAdView
{
    XMILogNativeAdInfo(@"FeedAd Did Close, %@", self.adModel.relatedData);
    if ([self.delegate respondsToSelector:@selector(feedAdRenderDidClose:)]) {
        [self.delegate feedAdRenderDidClose:self];
    }
}

- (void)feedAdViewWillExpose:(UIView<XMIFeedAdViewProtocol> *)feedAdView
{
    if ([feedAdView respondsToSelector:@selector(play)]) {
        [feedAdView play];
    }
}

- (void)feedAdViewDidUnExpose:(UIView<XMIFeedAdViewProtocol> *)feedAdView
{
    XMILogNativeAdInfo(@"FeedAd Did UnExpose, %@", self.adModel.relatedData);
    if ([feedAdView respondsToSelector:@selector(stop)]) {
        [feedAdView stop];
    }
}

- (void)feedAdViewDidExpose:(nonnull UIView<XMIFeedAdViewProtocol> *)feedAdView { 
    if (self.adModel.relatedData.isExposed && self.onlyReportExposeOnce) {
        return;
    }
    self.adModel.relatedData.isExposed = YES;
    
    // 曝光上报
    XMILogNativeAdInfo(@"FeedAd Did Expose, %@", self.adModel.relatedData);
    if ([self.delegate respondsToSelector:@selector(feedAdRendererDidExpose:)]) {
        [self.delegate feedAdRendererDidExpose:self];
    }

    BOOL supportRealExpose = NO;
    if ([feedAdView respondsToSelector:@selector(supportRealExpose)]) {
        supportRealExpose = [feedAdView supportRealExpose];
    }
    if (supportRealExpose) {
        [XMIAdReporter exposeReportWithAd:self.adModel.relatedData andView:feedAdView];
    }
}

- (void)feedAdViewDidNewExpose:(nonnull UIView<XMIFeedAdViewProtocol> *)feedAdView radio:(NSInteger)radio {
    if (self.adModel.relatedData.isNewExposed &&
        self.adModel.relatedData.isNewExposed50 &&
        self.onlyReportExposeOnce) {
        return;
    }
    if (radio == 50) {
        self.adModel.relatedData.isNewExposed50 = YES;
    } else if (radio != 1){
        self.adModel.relatedData.isNewExposed = YES;
    }
    BOOL supportRealExpose = NO;
    if ([feedAdView respondsToSelector:@selector(supportRealExpose)]) {
        supportRealExpose = [feedAdView supportRealExpose];
    }
    if (supportRealExpose && self.adModel.relatedData) {
        [XMIAdReporter exposeNewReportValidAds:@[self.adModel.relatedData] radio:radio];
    }
}

- (void)feedAdViewDidRemoveFromSuperview:(UIView<XMIFeedAdViewProtocol> *)feedAdView
{
    XMILogNativeAdInfo(@"FeedAd Did Remove From Superview, %@", self.adModel.relatedData);
    if ([feedAdView respondsToSelector:@selector(stop)]) {
        [feedAdView stop];
    }
    _exposed = NO;
    if ([self.delegate respondsToSelector:@selector(feedAdRendererDidRemoved:)]) {
        [self.delegate feedAdRendererDidRemoved:self];
    }
}

- (void)feedAdView:(UIView<XMIFeedAdViewProtocol> *)feedAdView didFailRenderWithError:(NSError *)error
{
    XMILogNativeAdInfo(@"FeedAd Did Render Fail, %@, error:%@", self.adModel.relatedData, error);
    if([self.delegate respondsToSelector:@selector(feedAdRenderer:didRenderFailWithError:)]){
        [self.delegate feedAdRenderer:self didRenderFailWithError:error];
    }
}

- (void)feedAdView:(UIView<XMIFeedAdViewProtocol> *)feedAdView playerStateChanged:(XMIPlayerPlayState)state
{
    XMILogNativeAdInfo(@"VIDEO PLAY STATE CHANGED:%zd", state);
}

- (void)feedAdView:(UIView<XMIFeedAdViewProtocol> *)feedAdView playTimeDidChanged:(CGFloat)currentTime {
    self.adModel.relatedData.finishSeconds = currentTime;
}

- (void)feedAdViewDidShowAnimation:(UIView<XMIFeedAdViewProtocol> *)feedAdView
{
    self.adModel.hasShowedAnimation = YES;
}

- (BOOL)feedAdViewShouldRetryVideo:(UIView<XMIFeedAdViewProtocol> *)feedAdView
{
    return [self.adModel shouldRetryVideo];
}

- (void)feedAdViewWillPresentScreen:(UIView<XMIFeedAdViewProtocol> *)feedAdView
{
    XMILogNativeAdInfo(@"AD WILL PRESETN SCREEN");
    if ([self.delegate respondsToSelector:@selector(feedAdRendererWillPresentScreen:)]) {
        [self.delegate feedAdRendererWillPresentScreen:self];
    }
}

- (void)feedAdViewDidPresentScreen:(UIView<XMIFeedAdViewProtocol> *)feedAdView
{
    XMILogNativeAdInfo(@"AD DID PRESETN SCREEN");
    if ([self.delegate respondsToSelector:@selector(feedAdRendererDidPresentScreen:)]) {
        [self.delegate feedAdRendererDidPresentScreen:self];
    }
}

- (void)feedAdViewDidCloseDetail:(UIView<XMIFeedAdViewProtocol> *)feedAdView
{
    XMILogNativeAdInfo(@"AD DID CLOSE DETAIL");
    if ([self.delegate respondsToSelector:@selector(feedAdRendererDetailControllerDidClosed:)]) {
        [self.delegate feedAdRendererDetailControllerDidClosed:self];
    }
}

- (void)feedAdViewDidExposeDspView:(UIView<XMIFeedAdViewProtocol> *)feedAdView {
    if (self.adModel.relatedData) {
        [XMIAdReporter exposeDspShowAds:@[self.adModel.relatedData] sdkShow:1];
    }
}

- (void)feedAdView:(UIView<XMIFeedAdViewProtocol> *)feedAdView dislikeAdViewWithReasons:(NSArray *)reasons
{
    if ([self.delegate respondsToSelector:@selector(feedAdRenderer:dislikeWithReason:)]) {
        [self.delegate feedAdRenderer:self dislikeWithReason:reasons];
    }
}

- (void)anchorItemClicked:(UIView<XMIFeedAdViewProtocol> *)feedAdView anchorId:(NSString *)anchorId {
    if ([self.delegate respondsToSelector:@selector(feedAdRenderer:clickAnchorId:)]) {
        [self.delegate feedAdRenderer:self clickAnchorId:anchorId];
    }
}

#pragma mark - notification

- (void)didReceiveDidScrollNotification:(NSNotification *)notification
{
    if ([_renderingView respondsToSelector:@selector(updateUIWhenScrolled)]) {
        [_renderingView updateUIWhenScrolled];
    }
    [self checkExpose];
}

- (void)didReceiveDidAppearNotification:(NSNotification *)notification
{
    [self checkExpose];
}

- (void)didReceiveDidDisappearNotification:(NSNotification *)notification
{
    if (!_renderingView.superview) {
        return;
    }
    if ([_renderingView respondsToSelector:@selector(pause)]) {
        [_renderingView pause];
    }
}

- (void)handleTapGesture:(UITapGestureRecognizer *)gesture
{
    [self feedAdViewHandleTap:_renderingView tapPoint:[gesture locationInView:_renderingView] userInfo:nil];
}


- (id)copyWithZone:(NSZone *)zone {
    XMIFeedAdRenderer *copy = [[[self class] allocWithZone:zone] init];
    copy.renderingViewClass = self.renderingViewClass;
    copy.delegate = self.delegate;
    copy.adModel = self.adModel;
    copy.rootViewController = self.rootViewController;
    return copy;
}

- (void)dealloc
{
    _renderingView.delegate = nil;
    [_renderingView removeFromSuperview];
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - model load delegate

- (void)feedAdModel:(nonnull XMIFeedAdModel *)adModel didLoadDataFailWithError:(nonnull NSError *)error {
    if ([self.delegate respondsToSelector:@selector(feedAdRenderer:didRenderFailWithError:)]) {
        [self.delegate feedAdRenderer:self didRenderFailWithError:error];
    }
}

- (void)feedAdModelDidLoadDataSuccess:(nonnull XMIFeedAdModel *)adModel timeout:(BOOL)isTimeout {
    if (_renderingView) {
        [self refreshUI];
    }
}

#pragma mark - jump delegate

- (void)jumpManager:(id<XMIJumpManagerProtocol>)jumpManager adWillPresentScreen:(XMIAdRelatedData *)adData
{
    [self.renderedView willPresentScreen];
}
- (void)jumpManager:(id<XMIJumpManagerProtocol>)jumpManager adDidPresentScreen:(XMIAdRelatedData *)adData
{
    [self.renderedView didPresentScreen];
}
- (void)jumpManager:(id<XMIJumpManagerProtocol>)jumpManager adDetailControllerDidClosed:(XMIAdRelatedData *)adData
{
    [self.renderedView didCloseAdDetail];
}

#pragma mark - public method

- (void)checkExpose
{
    BOOL isExposed = NO;
    BOOL isNewExposed = NO;
    BOOL isNewExposed50 = NO;
    CGFloat radio = (CGFloat)[XMConfigCenter.sharedConfigCenter getIntValueWithGroup:@"ad" andItem:@"adNewExposeRadio" defaultValue:30];
    if (!_renderingView || !_renderingView.superview || _renderingView.superview.bounds.size.height == 0 || !_renderingView.window) {
        isExposed = NO;
    } else {
        isExposed =[_renderingView xmi_isExposed: self.rootViewController.view.window ? self.rootViewController.view:_renderingView.window radio:0.01f];
        if (self.scrollView) {
            isExposed = isExposed && [_renderingView xmi_isExposed:self.scrollView radio:0.01f];
        }
        isNewExposed =[_renderingView xmi_isExposed: self.rootViewController.view.window ? self.rootViewController.view:_renderingView.window radio:radio/100];
        if (self.scrollView) {
            isNewExposed = isNewExposed && [_renderingView xmi_isExposed:self.scrollView radio:radio/100];
        }
        isNewExposed50 =[_renderingView xmi_isExposed: self.rootViewController.view.window ? self.rootViewController.view:_renderingView.window radio:0.5];
        if (self.scrollView) {
            isNewExposed50 = isNewExposed && [_renderingView xmi_isExposed:self.scrollView radio:0.5];
        }
    }
    if (isExposed != _exposed) {
        _exposed = isExposed;
        if (isExposed) {
            [_renderingView willExposeAdView];
            [_renderingView didExposeAdView];
            if ([_renderingView respondsToSelector:@selector(replay)]) {
                [_renderingView replay];
            }
            [self feedAdViewDidNewExpose:_renderingView radio:1];
        } else {
            [_renderingView willUnExposeAdView];
            [_renderingView didUnExposeAdView];
            if ([_renderingView respondsToSelector:@selector(pause)]) {
                [_renderingView pause];
            }
        }
    }
    
    if (isNewExposed != _newExposed) {
        _newExposed = isNewExposed;
        [self feedAdViewDidNewExpose:_renderingView radio:radio];
    }
    if (isNewExposed50 != _newExposed50) {
        _newExposed50 = isNewExposed50;
        [self feedAdViewDidNewExpose:_renderingView radio:50];
    }
}

- (void)closeAd
{
    if (_renderingView) {
        [_renderingView closeAdView];
    } else {
        if ([self.delegate respondsToSelector:@selector(feedAdRenderDidClose:)]) {
            [self.delegate feedAdRenderDidClose:self];
        }
    }
}

- (void)closeSubAd:(NSInteger)index {
    if (_renderingView) {
        [_renderingView closeSubAdView:index];
    }
}

- (BOOL)isVirtual
{
    return [XMIAdConverter isVirtualAD:self.adModel.relatedData.adid];
}

- (void)setEdge:(UIEdgeInsets)edge
{
    _edge = edge;
    if (_renderingView && [_renderingView respondsToSelector:@selector(setEdge:)]) {
        [_renderingView setEdge:edge];
    }
}

@end
