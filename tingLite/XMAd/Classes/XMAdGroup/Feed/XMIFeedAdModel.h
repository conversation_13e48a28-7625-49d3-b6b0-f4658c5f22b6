//
//  XMIFeedAdModel.h
//  Pods-XMAd_Example
//
//  Created by <PERSON><PERSON><PERSON>z<PERSON> on 2022/2/28.
//

#import <XMAd/XMIAdRelatedData.h>

typedef enum : NSUInteger {
    XMIFeedAdModelLoadingStatusInit,
    XMIFeedAdModelLoadingStatusLoading,
    XMIFeedAdModelLoadingStatusTimeout,
    XMIFeedAdModelLoadingStatusLoadSuccess,
    XMIFeedAdModelLoadingStatusLoadFailed,
} XMIFeedAdModelLoadingStataus;

NS_ASSUME_NONNULL_BEGIN

@class XMIFeedAdModel;

@protocol XMIFeedAdModelDelegate <NSObject>

- (void)feedAdModelDidLoadDataSuccess:(XMIFeedAdModel *)adModel timeout:(BOOL)isTimeout;

- (void)feedAdModel:(XMIFeedAdModel *)adModel didLoadDataFailWithError:(NSError *)error;

@end

@interface XMIFeedAdModel : NSObject

/*
 工厂方法
 */
+ (instancetype)feedAdModelWithRelatedData:(XMIAdRelatedData *)relatedData;

/// 高度
@property (nonatomic, assign) CGFloat adHeight;

/// 宽度
@property (nonatomic, assign) CGFloat adWidth;

@property(nonatomic, strong, nullable)XMIAdRelatedData *relatedData;


/*
用于处理事件presentViewController
 */
@property (nonatomic, weak) UIViewController *rootViewController;

@property (nonatomic,assign) BOOL hasShowedAnimation;

@property (nonatomic, assign) XMIFeedAdModelLoadingStataus loadingStatus;

@property (nonatomic, weak) id<XMIFeedAdModelDelegate> delegate;

@property (nonatomic, assign) int64_t requestTimeCost;

- (void)loadAdData;

- (NSString *)adTitle;

- (NSString *)adDescription;

- (NSString *)adButtonText;

- (NSArray<NSString *> *)adImageURLs;

- (NSString *)adMark;

- (NSArray<NSString *> *)adAdTags;

- (NSString *)adVideoURL;

- (NSString *)modelIdentifier;

- (XMIInScreenSource)adInScreenSource;

- (NSString *)adMaterialProvideSource;

- (BOOL)adNeedRender;
 
- (CGSize)adRenderSize;

- (id)adData;

- (UIView *)templateAdView;

- (BOOL)shouldRetryVideo;

//inner method
- (void)loadSuccess;

- (void)loadFailed:(NSError *)error;

- (BOOL)isVirtual;

- (NSString *)iconUrl;

- (NSString *)loadingStatusString;

/// 当竞价成功调用此方法
- (void)win:(nullable NSNumber *)auctionBidToWin;

/// 当竞价失败调用此方法
- (void)loss:(nullable NSNumber *)auctionPrice;

@end

NS_ASSUME_NONNULL_END
