//
//  XMIFeedAdModelFactory.m
//  XMAd
//
//  Created by cuiyuanzhe on 2022/3/4.
//

#import "XMIFeedAdModelFactory.h"
#import "XMIAdConverter.h"
#import "XMIAdHelper.h"

@implementation XMIFeedAdModelFactory

+ (XMIFeedAdModel *)adModelWithRelatedData:(XMIAdRelatedData *)relatedData
{
    [XMIAdHelper initSdkWithAdType:relatedData.adtype];
    NSString *provider = [XMIAdConverter providerFromAdType:relatedData.adtype];
    
    if (provider.length > 0) {
        NSString *clsName = [NSString stringWithFormat:@"XMI%@FeedAdModelFactory", provider];
        __typeof(self) cls = NSClassFromString(clsName);
        if (cls != nil) {
            return [cls adModelWithRelatedData:relatedData];
        }
    }
    XMIFeedAdModel *model = [[XMIFeedAdModel alloc] init];
    model.relatedData = relatedData;
    return model;
}

@end
