//
//  XMIAdManager.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/6/29.
//

#import <Foundation/Foundation.h>
#import "XMIAdDefines.h"

NS_ASSUME_NONNULL_BEGIN

@protocol XMIAdManagerDelegate;
@class XMIAdRelatedData;
@interface XMIAdManager : NSObject

/**
 服务环境
 不设置，默认为生产环境
 */
@property (nonatomic, assign) XMIAdEnvironment environment;


/// 是否是debug环境
@property (nonatomic, assign) BOOL isDebug;


/**
 代理
 用来获取一些必要配置，比如uid、token、自定义UA等
 */
@property (nonatomic, weak) id<XMIAdManagerDelegate> delegate;

/**
 初始化SDK
 */
+ (void)initWithAppID:(NSString *)appID completeBlock:(void (^)(bool success))completeBlock;

/**
 manager单例
 */
+ (instancetype)sharedInstance;

/**
 广告供应方(SDK)初始化
 @param provider : 穿山甲-XMIAdProviderBU, 广点通-XMIAdProviderGDT
 @param appID :后台分配的appID，用来唯一标识接入方
 @return 是否成功，如果相应provider模块不存在可能会失败
 */
+ (BOOL)initAdProvider:(NSString *)provider withAppID:(NSString *)appID;

/**
 获取应用ID
 */
+ (NSString *)appID;

/**
 SDK版本号
 */
+ (NSString *)sdkVersion;

/**
 internal method
 */
- (NSString *)getUid;

/**
 debug时的log level
 */
+ (void)setNativeLoglevel:(XMIAdNativeLogLevel)level;

/**
 中插音贴间隔时间等信息
 */
+ (NSDictionary *)trackVentConfig;

/*
 首页信息流样式ab实验
 */
+ (XMIAdFindNativeShowStyle)findNativeShowStyle;

+ (BOOL)findNativeSocialShowStyle;

/*
 基于positionName获取slotId
 */
+ (NSString *)getAdSlotIdByPositionName:(NSString *)positionName;

//caid所需字段
+ (NSDictionary *)phoneInfos;

+ (NSString *)customCookieValue;

- (BOOL)isRNHome;

@end


@protocol XMIAdManagerDelegate <NSObject>

@optional
/**
 获取用户ID
 */
- (nullable NSString *)managerGetUid;
/**
 获取用户token
 */
- (nullable NSString *)managerGetToken;
/**
 获取自定义设备ID，以替代SDK默认设备ID生成方案
 */
- (NSString *)managerGetCustomDeviceId;
/**
 获取自定义UA，如果实现该代理方法，广告所有请求将默认使用此UA
 */
- (NSString *)managerGetCustomUserAgent;
/**
 获取渠道，默认 "ios-b1"
 */
- (NSString *)managerGetChannel;
/**
 手机号区号，默认 "86"
 */
- (NSString *)managerGetPhoneCountryCode;
/**
 自定义请求header，会覆盖SDK内置的同名header。请不要覆盖cookie
 */
- (NSDictionary *)managerGetCustomHeaders;
/**
 自定义请求body，会覆盖SDK内置的同名key。
 */
- (NSDictionary *)managerGetCustomParamters;
/**
 自定义一些cookie，会覆盖SDK内置同名cookie key
 */
- (NSDictionary *)managerGetCustomCookie;
/**
 获取idfa
 */
- (NSString *)managerGetStystemIDFA;
/**
 当前idfa获取的类型
 */
- (NSInteger)managerGetStystemIDFALimit;
/**
 获取微信openSDK版本号
 */
- (NSString *)managerGetWxOpenSDKVersion;
/**
 自定义web容器，必须是实现了XMIWebVCProtocol协议的UIViewController子类
 */
- (Class)managerGetCustomWebVCClass;
/**
 获取渠道id
 */
- (NSString *)managerGetChannelId;
/**
 正在播放的专辑id
 */
- (NSString *)managerGetAlbumId;
/**
 正在播放的声音id
 */
- (NSString *)managerGetTrackId;
/**
 是否正在审核，主站特殊上报需要
 */
- (BOOL)managerGetIsAppleReview;
/**
 播音合规弹窗样式3
 */
- (void)showToastWithInfo:(NSString *)info;

/**
 SDK无法进行的跳转，需要接入方外部自己处理
 比如 跳转app内某个自定义页面、商城、微信小程序
 */
/**
 跳转商城
 */
- (void)managerJumpMall:(XMIAdRelatedData *)relatedData fromController:(UIViewController *)controller;
- (void)managerJumpGameCenter:(XMIAdRelatedData *)relatedData fromController:(UIViewController *)controller;
- (void)managerJumpWeixin:(XMIAdRelatedData *)relatedData fromController:(UIViewController *)controller;

/// 是否要处理处理有关iting的跳转
- (BOOL)managerJumpWebInApp:(XMIAdRelatedData *)relatedData;

/**
 SDK是否需要初始化xlog
 喜马系列app基本都已集成xlog，不需要sdk再进行初始化，sdk默认不进行初始化，除非该方法返回YES
 */
- (BOOL)managerShouldInitXlog;

/// 是否使用老的XMAdApi中的跳转方式
- (id)getConfigCenterWithKey:(NSString *)key;

- (NSString *)managerGetNetType;

- (NSInteger)managerFindNativeVersion;

- (void)managerSetupAdIfNeededWithAdPlatform:(XMIAdSource)platform;

/**
 额外上报参数
 */
- (NSDictionary *)managerGetExtraReportParamsForAdPosition:(NSString *)postionName;

/**
 sdk不能跳转的处理
 */
- (void)managerHandleSDKNotSupportedAdJump:(XMIAdRelatedData *)relatedData;

/// 广告ab拦截
/// @param positionNames 广告位名称数组，如果是n合一可能只除去其中某些广告位的广告物料，需要传给server处理
/// @param returnValue 是否被拦截，如果返回no不发出网络请求。
- (BOOL)managerShouldRequestAdWithSlotId:(long long)slotId params:(nullable NSDictionary *)params forbidStyles:(NSArray *__autoreleasing *)styles;

/**
 专辑播放器播放专辑声音 YES 播放专辑声音  NO 暂停声音
 */
- (void)xmplayerPlay:(BOOL)play;

- (NSInteger)managerGetFindNativeShowStyle;

- (NSInteger)managerGetFindNativeSocialShowStyle;

// 基于主站的逻辑获取IPv6
- (NSString *)managerGetIPv6Address;

- (NSTimeInterval)managerGetInstallReportInterval;

- (NSArray *)managerGetInstalledConfigList;

- (void)managerApmLogMessage:(NSString *)message;

- (BOOL)managerGetPddInstallState;

- (NSString *)managerGetAppInstallList;

- (void)loadRewardVideoAdIfLinkTypeIs105:(XMIAdRelatedData *)adData completion:(void (^)(BOOL success))completionHandler;

- (BOOL)isSocialHome;

- (void)managerSettingCurrentAdData:(id)adData;

- (NSTimeInterval)managerGetRewardAdTimeout;

- (NSString *)managerGetTicketWithBusiness:(NSString *)business scene:(NSString *)scene;

- (void)managerPreLoadAdItemRealLinkWithDatas:(NSArray *)datas;

- (NSInteger)managerGetPreLoadAdItemResult:(XMIAdRelatedData *)adData;

- (BOOL)managerSchemeManagerCanOpenURL:(NSURL *)url;

- (NSDictionary *)managerGetInstallDetectionStrategy;

- (BOOL)isRNHome;

@end

NS_ASSUME_NONNULL_END
