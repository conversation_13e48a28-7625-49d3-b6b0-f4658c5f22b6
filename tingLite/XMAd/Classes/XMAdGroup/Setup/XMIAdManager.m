//
//  XMIAdManager.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/6/29.
//

#import "XMIAdManager.h"
#import <XMXlog/XMXlogManager.h>
#import <XMXlog/XMXlogManager+iTing.h>
#import "XMIAdDataCenter.h"
#import "XMIAdHeader.h"
#import "XMIAdNativeLogger.h"
#import <XMCategories/XMCategory.h>
#import "XMICommonUtils.h"
#import "XMIAdInstallReporter.h"
#import <XMAdGaiaX/XMAdGXTemplateSource.h>

@interface XMIAdManager ()

/**
 应用ID
 */
@property (nonatomic, copy) NSString *appID;

@property (nonatomic, copy) NSString *versionString;

@end

@implementation XMIAdManager

+ (instancetype)sharedInstance {
    static id _sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _sharedInstance = [[self alloc] init];
    });
    
    return _sharedInstance;
}

+ (void)initWithAppID:(NSString *)appID completeBlock:(void (^)(bool success))completeBlock {
    [[XMIAdManager sharedInstance] setupWithAppID:appID completeBlock:completeBlock];
}

+ (BOOL)initAdProvider:(NSString *)provider withAppID:(NSString *)appID {
    NSString *clsName = [NSString stringWithFormat:@"XMI%@AdSDKManager", provider];
    Class cls = NSClassFromString(clsName);
    if (cls == nil) {
        return NO;
    }
    SEL selector = NSSelectorFromString(@"initWithAppID:");
    
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Warc-performSelector-leaks"
    [cls performSelector:selector withObject:appID];
#pragma clang diagnostic pop
    
    return YES;
}

+ (NSString *)appID {
    return [XMIAdManager sharedInstance].appID;
}

+ (NSString *)sdkVersion {
    return [XMIAdManager sharedInstance].versionString;
}

- (NSString *)getUid {
    NSString *uid = nil;
    if (self.delegate && [self.delegate respondsToSelector:@selector(managerGetUid)]) {
        uid = [self.delegate managerGetUid];
    }
    
    return uid;
}

- (void)setupWithAppID:(NSString *)appID completeBlock:(void (^)(bool success))completeBlock{
    self.appID = appID;
    //初始化版本号
    [self initVersionString];
    // 初始化adx
    [self initAdxWithCompleteBlock:completeBlock];
    // 配置xlog
    [self setupXlog];
    //安装上报
    [[XMIAdInstallReporter sharedInstance] beginInstallReport];
    [[XMAdGXTemplateSource sharedInstance] setupTemplates];
}

- (void)initVersionString
{
    NSString *version = XMIAdLocalSDKVersion;
    Class cls = NSClassFromString(@"XMAudioFacMHPolo");
    if (cls && [cls respondsToSelector:@selector(sharedManager)]) {
        id instance = [cls performSelector:@selector(sharedManager)];
        if ([instance respondsToSelector:@selector(patchedIds)]) {
            NSArray *patchedIds = [instance valueForKey:@"patchedIds"];
            if (patchedIds.count > 0) {
                NSMutableArray *components = [[version componentsSeparatedByString:@"."] mutableCopy];
                if (components.count > 0) {
                    components[components.count - 1] = [NSString stringWithFormat:@"%zd", [components[components.count - 1] integerValue] + patchedIds.count];
                    version = [components componentsJoinedByString:@"."];
                }
            }
        }
    }
    
    self.versionString = version;
}

- (void)initAdxWithCompleteBlock:(void (^)(bool success))completeBlock {
    [XMIAdDataCenter initAdx:^(NSError * _Nullable error) {
        if (error != nil) {
            // TODO:
        }
        if (completeBlock) {
            completeBlock(error == nil);
        }
    }];
}

- (void)setupXlog {
    BOOL shouldInitXlog = NO;
    if (self.delegate && [self.delegate respondsToSelector:@selector(managerShouldInitXlog)]) {
        shouldInitXlog = [self.delegate managerShouldInitXlog];
    }
    if (!shouldInitXlog) {
        return;
    }
    
    [[XMXlogManager shareInstance] prepareLogUploader];
    if (self.environment == XMIAdEnvironmentTest) {
        [[XMXlogManager shareInstance] setEnv:ENV_TEST];
    } else if (self.environment == XMIAdEnvironmentUAT) {
        [[XMXlogManager shareInstance] setEnv:ENV_UAT];
    }
    XMIAdHeader *adHeader = [XMIAdHeader sharedInstance];
    [[XMXlogManager shareInstance] startLogWithAppId:self.appID deviceId:adHeader.deviceId channel:adHeader.channel];
}

+ (void)setNativeLoglevel:(XMIAdNativeLogLevel)level
{
#ifdef DEBUG
    [XMIAdNativeLogger setNativeLoglevel:level];
#endif
}

+ (NSDictionary *)trackVentConfig {
    return [XMIAdDataCenter trackVentConfig];
}

+ (XMIAdFindNativeShowStyle)findNativeShowStyle
{
    //可能刷新
//    static NSInteger showStyle = 0;
//    static dispatch_once_t onceToken;
//    dispatch_once(&onceToken, ^{
//        if ([[XMIAdManager sharedInstance].delegate respondsToSelector:@selector(managerGetFindNativeShowStyle)]) {
//            showStyle =
//        }
//    });
    return  [[XMIAdManager sharedInstance].delegate managerGetFindNativeShowStyle];
}

+ (BOOL)findNativeSocialShowStyle {
    return [[XMIAdManager sharedInstance].delegate managerGetFindNativeSocialShowStyle];
}

/*
 基于positionName
 */
+ (NSString *)getAdSlotIdByPositionName:(NSString *)positionName {
    long long slotIds = [XMIAdDataCenter getSlotIdByPositionName:positionName];
    return [NSString stringWithFormat:@"%lld", slotIds];
}

+ (NSDictionary *)phoneInfos
{
    NSMutableDictionary *info = [NSMutableDictionary dictionary];
    info[@"bootTimeInSec"] = [XMIAdHeader getBootTimeInSec];
    info[@"countryCode"] = [XMIAdHeader getCountryCode];
    info[@"deviceName"] = [[XMICommonUtils deviceName] hashString];
    info[@"systemVersion"] = [XMICommonUtils systemVersion];
    info[@"machine"] = [XMIAdHeader getHWMachine];
    info[@"carrierInfo"] = [XMIAdHeader getCarrierInfo];
    info[@"language"] = [XMIAdHeader getLanguage2];
    info[@"memory"] = [XMIAdHeader getMemory];
    info[@"disk"] = [XMIAdHeader getDisk];
    info[@"model"] = [XMIAdHeader getHWModel];
    info[@"sysFileTime"] = [XMIAdHeader getOsUpdateTime];
    info[@"timeZone"] = [XMIAdHeader getTimeZoneInSec];
    info[@"mnt_id"] = [XMIAdHeader getMntId];
    return info;
}

+ (NSString *)customCookieValue
{
    return [[XMIAdHeader sharedInstance] cookieValue];
}

- (BOOL)isRNHome
{
    if ([self.delegate respondsToSelector:@selector(isRNHome)]) {
        return [self.delegate isRNHome];
    }
    return NO;
}

@end

NSString * const XMIAdProviderXM = @"Own";
NSString * const XMIAdProviderBU = @"BU";
NSString * const XMIAdProviderGDT = @"GDT";
NSString * const XMIAdProviderJAD = @"JAD";
NSString * const XMIAdProviderBAIDU = @"BAIDU";

NSString * const XMIAdLocalSDKVersion = @"1.2.83";
