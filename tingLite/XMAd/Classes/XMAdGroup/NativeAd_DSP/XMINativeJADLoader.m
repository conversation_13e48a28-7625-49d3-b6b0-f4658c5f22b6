//
//  XMINativeJADLoader.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/8/23.
//

#import "XMINativeJADLoader.h"
#import "XMIAdRelatedData.h"
#import <JADYun/JADNativeSize.h>
#import <JADYun/JADNativeAdSlot.h>
#import <JADYun/JADNativeAd.h>
#import "XMIAdMacro.h"
#import "UIView+XMIUtils.h"
#import "XMIAdError.h"
#import "XMIAdHelper.h"

@interface XMINativeJADLoader ()<JADNativeAdDelegate>
@property (nonatomic, strong) JADNativeAd *jadData;
@end

@implementation XMINativeJADLoader
{
    UIView *_customView;
}
- (instancetype)init {
    self = [super init];
    if (self) {
        
    }
    return self;
}

- (void)loadAdWithData:(XMIAdRelatedData *)originData {
    [super loadAdWithData:originData];
    
    self.originData = originData;
    NSString *dspPositionId = originData.dspPositionId;
    
    JADNativeSize *size = [[JADNativeSize alloc] init];
    size.width = XMIAdPic(135) * [UIScreen mainScreen].scale;
    size.height = XMIAdPic(76) * [UIScreen mainScreen].scale;
    
    JADNativeAdSlot *slot  = [[JADNativeAdSlot alloc] init];
    slot.slotID = dspPositionId;
    slot.imgSize = size;
    if (originData.showstyle == XMIAdStyleMyPageFeedBannerVideo) {
        slot.type = JADSlotTypeFeedVideo;
    } else {
        slot.type = JADSlotTypeFeed;
    }
    [self.jadData unregisterView];
    self.jadData = [[JADNativeAd alloc] initWithSlot:slot];
    self.jadData.delegate = self;
    [self.jadData loadAdData];
    
}

//MARK: - BUNativeAdsManagerDelegate
- (void)jadNativeAdDidLoadSuccess:(JADNativeAd *)nativeAd {
    if (!self.originData) return;
    if (nativeAd.data.count == 0) {
        [self loadFailed:[XMIAdError emptyDataError]];
        return;
    }
    [self jadNativeAdDataMapToRelateData:self.originData jadNativeData:nativeAd];
    [self loadSuccess];
}

- (void)jadNativeAdDidLoadFailure:(JADNativeAd *)nativeAd error:(NSError *)error {
    [self.jadData unregisterView];
    self.jadData = nil;
    [self loadFailed:error];
    [XMIAdHelper apmLogMessage:[NSString stringWithFormat:@"jad error: position=%lld, code=%zd,message =%@", self.originData.positionId, error.code, error.localizedDescription]];
}

//MARK: - BUNativeAdDelegate

- (void)jadNativeAdDataMapToRelateData:(XMIAdRelatedData *)originData jadNativeData:(JADNativeAd *)jadData {
    NSString *dspPositionId = originData.dspPositionId;
    NSString *slotId = jadData.adSlot.slotID;
    if ([dspPositionId isEqualToString:slotId]) {
        long long positionId = originData.positionId;
        JADNativeAdData *data = [jadData.data firstObject];
        if (positionId == 295 || positionId == 308) {
            originData.cover = [data.adImages lastObject];
        } else {
            originData.cover = [data.adImages firstObject];
        }
        originData.name = data.adTitle;
        originData.adDescription = data.adDescription;
        originData.videoUrl = data.adVideoUrl;
    }
}

- (void)dspAdsRegisterContainer:(__kindof UIView *)containerView
             withClickableViews:(NSArray<__kindof UIView *> *_Nullable)clickableViews
              withClosableViews:(NSArray<__kindof UIView *> *_Nullable)closableViews {
    self.jadData.rootViewController = self.rootViewController;
    [self.jadData registerContainer:containerView withClickableViews:clickableViews withClosableViews:closableViews];
}


- (void)jadNativeAdDidExposure:(JADNativeAd *)nativeAd {
    if (self.delegate && [self.delegate respondsToSelector:@selector(dspAdsNativeAdDidExposure:)]) {
        [self.delegate dspAdsNativeAdDidExposure:self.originData];
    }
}

- (void)jadNativeAdDidClick:(JADNativeAd *)nativeAd withView:(UIView *)view {
    if (self.delegate && [self.delegate respondsToSelector:@selector(dspAdsNativeAdDidClick:withView:)]) {
        [self.delegate dspAdsNativeAdDidClick:self.originData withView:view];
    }
    if ([self.delegate respondsToSelector:@selector(dspAdsManager:nativeAdWillOpenDetailPage:)]) {
        [self.delegate dspAdsManager:self nativeAdWillOpenDetailPage:self.originData];
    }
}

- (void)jadNativeAdDidCloseOtherController:(JADNativeAd *)nativeAd interactionType:(JADInteractionType)interactionType {
    if ([self.delegate respondsToSelector:@selector(dspAdsManager:nativeAdDidCloseDetailPage:)]) {
        [self.delegate dspAdsManager:self nativeAdDidCloseDetailPage:self.originData];
    }
}

- (void)jadNativeAdDidClose:(JADNativeAd *)nativeAd withView:(UIView *)view
{
    if ([self.delegate respondsToSelector:@selector(dspAdsNativeAdDidClickClose:withView:)]) {
        [self.delegate dspAdsNativeAdDidClickClose:self.originData withView:view];
    }
}

- (void)dealloc
{
    [self.jadData unregisterView];
}

@end
