//
//  XMINativeBULoader.m
//  XMAd
//
//  Created by xmly on 2023/6/13.
//

#import "XMINativeBULoader.h"
#import "XMIAdRelatedData.h"
#import <BUAdSDK/BUNativeAdsManager.h>
#import <BUAdSDK/BUNativeAdRelatedView.h>
#import "XMIAdMacro.h"
#import "UIView+XMIUtils.h"
#import "XMIAdHelper.h"
#import "XMIAdNativeLogger.h"

@interface XMINativeBULoader ()<BUNativeAdsManagerDelegate, BUNativeAdDelegate, BUVideoAdViewDelegate>
@property (nonatomic, strong) BUNativeAd *buData;
@property (nonatomic, strong) BUNativeAdRelatedView *relatedView;
@end

@implementation XMINativeBULoader
{
    UIView *_customView;
}
- (instancetype)init {
    self = [super init];
    if (self) {
        
    }
    return self;
}

- (void)loadAdWithData:(XMIAdRelatedData *)originData {
    [super loadAdWithData:originData];
    self.originData = originData;
    NSString *dspPositionId = originData.dspPositionId;
    
   
    BUAdSlot *slot1 = [[BUAdSlot alloc] init];
    slot1.ID = dspPositionId;
    slot1.AdType = BUAdSlotAdTypeFeed;
    slot1.position = BUAdSlotPositionTop;
    slot1.imgSize = [BUSize sizeBy:BUProposalSize_Feed228_150];
    BUNativeAdsManager *nad = [[BUNativeAdsManager alloc] initWithSlot:slot1];
    nad.delegate = self;
    // 重要，防止manager提前释放
    [self.dspAdManagers addObject:nad];
    [nad loadAdDataWithCount:1];
}

- (void)dspAdsRegisterContainer:(__kindof UIView *)containerView
             withClickableViews:(NSArray<__kindof UIView *> *_Nullable)clickableViews
              withClosableViews:(NSArray<__kindof UIView *> *_Nullable)closableViews {
    self.buData.rootViewController = self.rootViewController;
    [self.buData registerContainer:containerView withClickableViews:clickableViews];
}

- (void)win:(nullable NSNumber *)auctionBidToWin {
    BUNativeAd *ad = self.buData;
    if (ad && auctionBidToWin) {
        [ad win:auctionBidToWin];
    }
}

- (void)loss:(nullable NSNumber *)auctionPrice {
    BUNativeAd *ad = self.buData;
    if (ad && auctionPrice && self.loadingStatus == XMINativeBaseLoaderLoadingStatusSuccess) {
        [ad loss:auctionPrice lossReason:nil winBidder:nil];
    }
}

//MARK: - BUNativeAdsManagerDelegate
- (void)nativeAdsManagerSuccessToLoad:(BUNativeAdsManager *)adsManager nativeAds:(NSArray<BUNativeAd *> *)nativeAdDataArray  {
    if (!self.originData) return;
    // 自渲染广告，将SDK返回数据映射给adx广告model
    BUNativeAd *nativeAd = nativeAdDataArray.firstObject;
    self.buData = nativeAd;
    self.buData.delegate = self;
    
    [self buNativeAdDataMapToRelateData:self.originData buNativeData:nativeAd];
    if (_relatedView) {
        [_relatedView refreshData:nativeAd];
    }
    [self loadSuccess];
}

- (void)nativeAdsManager:(BUNativeAdsManager *)adsManager didFailWithError:(NSError *)error {
    self.buData = nil;
    [self loadFailed:error];
    [XMIAdHelper apmLogMessage:[NSString stringWithFormat:@"bu error: position=%lld, code=%zd,message =%@", self.originData.positionId, error.code, error.localizedDescription]];
}

//MARK: - BUNativeAdDelegate

- (void)buNativeAdDataMapToRelateData:(XMIAdRelatedData *)originData buNativeData:(BUNativeAd *)buData {
    NSString *dspPositionId = originData.dspPositionId;
    NSString *slotId = buData.adslot.ID;
    if (originData.isMobileRtb) {
        // 转换价格，单位为元，原始为分
        NSDictionary *dic = buData.data.mediaExt;
        NSInteger originPrice = [dic integerMaybeForKey:@"price"];
        originData.price = originPrice * 1.0 / 100;
        XMILogNativeInfo(@"AD_LOG_NATIVE_RTB",@"CSJ 请求dsp原生广告成功 🎉🎉🎉 【%lld(%ld, %@)】price = %.6f 元 sdk返回原始价格 %ld 分", originData.adid, originData.adtype, originData.dspPositionId, originData.price, originPrice);
    }
    if ([dspPositionId isEqualToString:slotId]) {
        long long positionId = originData.positionId;
        switch (positionId) {
            case 14:
            { // 下挂接dsp，代码待优化
                BUImage *buImage = buData.data.imageAry.firstObject;
                originData.cover = buImage.imageURL;
                if (buData.data.icon.imageURL.length) {
                    originData.cover = buData.data.icon.imageURL;
                }
                originData.name = buData.data.AdDescription;
                originData.adDescription = buData.data.source;
                NSDictionary *mediaExt = buData.data.mediaExt;
                if (!originData.adDescription.length) {
                    NSString *soft_ad_source = [mediaExt valueForKey:@"soft_ad_source"];
                    originData.adDescription = soft_ad_source;
                }
                if (!originData.adDescription.length) {
                    originData.adDescription = buData.data.AdTitle;
                }
            }
                break;
            case 15:
            case 16:
            case 271:
            {
                BUImage *buImage = buData.data.imageAry.firstObject;
                originData.cover = buImage.imageURL;
                if (!originData.cover) {
                    originData.cover = buData.data.icon.imageURL;
                }
                originData.name = buData.data.AdDescription;
                originData.clickTitle = buData.data.buttonText;
            }
                break;
            case 295:
            case 308:
            {
                originData.cover = buData.data.icon.imageURL;
                originData.name = buData.data.AdTitle;
                originData.adDescription = buData.data.AdDescription;
                originData.clickTitle = buData.data.buttonText;
            }
                break;
            default:
                break;
        }
    }
}

- (void)nativeAdDidLoad:(BUNativeAd *)nativeAd {
    
}

- (void)nativeAd:(BUNativeAd *)nativeAd didFailWithError:(NSError *)error {
    [XMIAdHelper apmLogMessage:[NSString stringWithFormat:@"bu ad error: position=%lld, code=%zd,message =%@", self.originData.positionId, error.code, error.localizedDescription]];
}

- (void)nativeAdDidBecomeVisible:(BUNativeAd *)nativeAd {
    if (self.delegate && [self.delegate respondsToSelector:@selector(dspAdsNativeAdDidExposure:)]) {
        [self.delegate dspAdsNativeAdDidExposure:self.originData];
    }
}

- (void)nativeAdDidClick:(BUNativeAd *)nativeAd withView:(UIView *)view {
    if (self.delegate && [self.delegate respondsToSelector:@selector(dspAdsNativeAdDidClick:withView:)]) {
        [self.delegate dspAdsNativeAdDidClick:self.originData withView:view];
    }
    if ([self.delegate respondsToSelector:@selector(dspAdsManager:nativeAdWillOpenDetailPage:)]) {
        [self.delegate dspAdsManager:self nativeAdWillOpenDetailPage:self.originData];
    }
}

- (void)nativeAdDidCloseOtherController:(BUNativeAd *)nativeAd interactionType:(BUInteractionType)interactionType {
    if ([self.delegate respondsToSelector:@selector(dspAdsManager:nativeAdDidCloseDetailPage:)]) {
        [self.delegate dspAdsManager:self nativeAdDidCloseDetailPage:self.originData];
    }
}

//MARK: - BUVideoAdViewDelegate
- (void)videoAdView:(BUVideoAdView *)videoAdView stateDidChanged:(BUPlayerPlayState)playerState {
    
}

- (void)videoAdView:(BUVideoAdView *)videoAdView didLoadFailWithError:(NSError *)error {
    
}

- (void)playerDidPlayFinish:(BUVideoAdView *)videoAdView {
    
}

// WARNING:如果是视频广告，点击走的这里，不走nativeAdDidClick:withView:
- (void)videoAdViewDidClick:(BUVideoAdView *)videoAdView {
    if (self.delegate && [self.delegate respondsToSelector:@selector(dspAdsNativeAdDidClick:withView:)]) {
        [self.delegate dspAdsNativeAdDidClick:self.originData withView:videoAdView];
    }
    if ([self.delegate respondsToSelector:@selector(dspAdsManager:nativeAdWillOpenDetailPage:)]) {
        [self.delegate dspAdsManager:self nativeAdWillOpenDetailPage:self.originData];
    }
}

- (void)videoAdViewFinishViewDidClick:(BUVideoAdView *)videoAdView {
    
}

- (void)videoAdViewDidCloseOtherController:(BUVideoAdView *)videoAdView interactionType:(BUInteractionType)interactionType {
    if ([self.delegate respondsToSelector:@selector(dspAdsManager:nativeAdDidCloseDetailPage:)]) {
        [self.delegate dspAdsManager:self nativeAdDidCloseDetailPage:self.originData];
    }
}

- (void)videoAdView:(BUVideoAdView *)videoAdView rewardDidCountDown:(NSInteger)countDown {
    
}

//MARK: - Helper
- (UIView *)customView
{
    if (!self.buData || self.originData.positionId == 295 || self.originData.positionId == 308) {
        return nil;
    }
    if (!_customView) {
        _customView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, XMI_SCREEN_WIDTH, 400)];
    }
    switch (self.buData.data.imageMode) {
        case BUFeedVideoAdModeImage:
        case BUFeedADModeSquareVideo:
        case BUFeedADModeUnionSplashVideo:
        case BUFeedVideoAdModePortrait:
            if (self.relatedView.videoAdView) {
                [_customView insertSubview:self.relatedView.videoAdView atIndex:0];
                self.relatedView.videoAdView.frame = _customView.bounds;
                self.relatedView.videoAdView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
            }
            break;
            
        default:
            [_relatedView.videoAdView removeFromSuperview];
            break;
    }
    if (self.relatedView.logoADImageView) {
        [self.relatedView.logoADImageView removeConstraints:self.relatedView.logoADImageView.constraints];
        [_customView addSubview:self.relatedView.logoADImageView];
        [self layoutCustomView];
    }
    return _customView;
}

- (void)layoutCustomView
{
    CGFloat width = 58*0.65f;
    CGFloat height = 22*0.65f;
    [self.relatedView.logoADImageView sizeToFit];
    if (self.relatedView.logoADImageView.xmi_width == 0 || self.relatedView.logoADImageView.xmi_height == 0) {
        self.relatedView.logoADImageView.size = CGSizeMake(width, height);
    } else {
        CGFloat scale = self.relatedView.logoADImageView.xmi_width / self.relatedView.logoADImageView.xmi_height;
        height = XMIAdPic(14);
        self.relatedView.logoADImageView.size = CGSizeMake(height * scale, height);
    }
    if (self.originData.positionId == 271) {
        self.relatedView.logoADImageView.xmi_centerY = XMIAdPic(15);
        self.relatedView.logoADImageView.xmi_right = _customView.xmi_width - XMIAdPic(26);
    } else if(self.originData.positionId == 15 || self.originData.positionId == 16) {
        self.relatedView.logoADImageView.hidden = YES;
    }
    self.relatedView.logoADImageView.autoresizingMask = UIViewAutoresizingFlexibleLeftMargin;
}

- (BUNativeAdRelatedView *)relatedView
{
    if (!_relatedView) {
        _relatedView = [[BUNativeAdRelatedView alloc] init];
        _relatedView.videoAdView.delegate = self;
        if (self.buData) {
            [_relatedView refreshData:self.buData];
        }
    }
    return _relatedView;
}

- (BOOL)isVideo
{
    switch (self.buData.data.imageMode) {
        case BUFeedVideoAdModeImage:
        case BUFeedADModeSquareVideo:
        case BUFeedADModeUnionSplashVideo:
        case BUFeedVideoAdModePortrait:
            return YES;
            break;
            
        default:
            return NO;
            break;
    }
}

- (BOOL)isDownloadAd
{
    return self.buData.data.interactionType == BUInteractionTypeDownload;
}

@end
