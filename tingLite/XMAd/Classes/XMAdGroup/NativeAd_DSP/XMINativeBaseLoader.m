//
//  XMINativeBaseLoader.m
//  XMAd
//
//  Created by xmly on 2023/6/13.
//

#import "XMINativeBaseLoader.h"
#import "XMICommonUtils.h"

@interface XMINativeBaseLoader ()

@property (nonatomic, assign) int64_t startMS;

@end


@implementation XMINativeBaseLoader
- (instancetype)init {
    self = [super init];
    if (self) {
        _dspAdManagers = [[XMIAdLimitedMutableArray alloc] initWithCapacity:3];
    }
    return self;
}

- (void)loadAdWithData:(XMIAdRelatedData *)originData {
    self.startMS = [XMICommonUtils currentMS];
}

- (void)dspAdsRegisterContainer:(__kindof UIView *)containerView
             withClickableViews:(NSArray<__kindof UIView *> *_Nullable)clickableViews
              withClosableViews:(NSArray<__kindof UIView *> *_Nullable)closableViews {
    
}

- (UIView *)customView
{
    return nil;
}

- (BOOL)isVideo
{
    return NO;
}

- (void)win:(nullable NSNumber *)auctionBidToWin {
    
}

- (void)loss:(nullable NSNumber *)auctionPrice {
    
}

- (void)loadSuccess {
    self.requestTimeCost = [XMICommonUtils currentMS] - self.startMS;
    if (self.delegate && [self.delegate respondsToSelector:@selector(dspAdsManagerSuccessToLoad:nativeAds:)]) {
        [self.delegate dspAdsManagerSuccessToLoad:self nativeAds:@[self.originData]];
    }
}

- (void)loadFailed:(NSError *)error {
    self.requestTimeCost = [XMICommonUtils currentMS] - self.startMS;
    if (self.delegate && [self.delegate respondsToSelector:@selector(dspAdsManager:nativeAd:didFailWithError:)]) {
        [self.delegate dspAdsManager:self nativeAd:self.originData didFailWithError:error];
    }
}

- (NSString *)loadingStatusString {
    switch (self.loadingStatus) {
        case XMINativeBaseLoaderLoadingStatusInit:
            return @"Init";
            break;
        case XMINativeBaseLoaderLoadingStatusLoading:
            return @"Loading";
            break;
        case XMINativeBaseLoaderLoadingStatusSuccess:
            return @"Success";
            break;
        case XMINativeBaseLoaderLoadingStatusFailed:
            return @"Failed";
            break;
        default:
            return @"Undefine";
            break;
    }
}

@end


@implementation XMIAdLimitedMutableArray
{
    NSMutableArray *_innerArray;
}


- (instancetype)initWithCapacity:(NSUInteger)numItems {
    self = [super init];
    if (self) {
        _capacity = numItems;
        _innerArray = [[NSMutableArray alloc] initWithCapacity:numItems];
    }
    return self;
}

- (NSUInteger)count {
    return [_innerArray count];
}

- (id)objectAtIndex:(NSUInteger)index {
    return [_innerArray objectAtIndex:index];
}

- (void)addObject:(id)anObject {
    if ([_innerArray count] >= self.capacity) {
        [_innerArray removeObjectAtIndex:0];
    }
    [_innerArray addObject:anObject];
}

- (void)insertObject:(id)anObject atIndex:(NSUInteger)index {
    if ([_innerArray count] >= self.capacity) {
        [_innerArray removeObjectAtIndex:0];
    }
    [_innerArray insertObject:anObject atIndex:index];
}

- (void)removeLastObject {
    [_innerArray removeLastObject];
}

- (void)removeObjectAtIndex:(NSUInteger)index {
    [_innerArray removeObjectAtIndex:index];
}

- (void)replaceObjectAtIndex:(NSUInteger)index withObject:(id)anObject {
    [_innerArray replaceObjectAtIndex:index withObject:anObject];
}

@end
