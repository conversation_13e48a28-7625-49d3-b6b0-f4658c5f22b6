//
//  XMINativeGDTLoader.m
//  XMAd
//
//  Created by xmly on 2023/6/13.
//

#import "XMINativeGDTLoader.h"
#import "XMIAdRelatedData.h"
#import <GDTMobSDK/GDTUnifiedNativeAd.h>
#import "XMIAdMacro.h"
#import "UIView+XMIUtils.h"
#import "XMIAdHelper.h"
#import "XMIAdNativeLogger.h"

@interface XMINativeGDTLoader ()<GDTUnifiedNativeAdDelegate, GDTUnifiedNativeAdViewDelegate>
@property (nonatomic, strong) GDTUnifiedNativeAdDataObject *gdtData;
@property (nonatomic, strong) GDTUnifiedNativeAdView *unifiedNativeAdView;
@property (nonnull, strong) GDTUnifiedNativeAd *loadingAd;
@end

@implementation XMINativeGDTLoader

- (instancetype)init {
    self = [super init];
    if (self) {
        
    }
    return self;
}

- (void)loadAdWithData:(XMIAdRelatedData *)originData {
    [super loadAdWithData:originData];
    self.originData = originData;
    NSString *dspPositionId = originData.dspPositionId;
    
    GDTUnifiedNativeAd *unifiedNativeAd = [[GDTUnifiedNativeAd alloc] initWithPlacementId:dspPositionId];
    unifiedNativeAd.delegate = self;
    self.loadingAd = unifiedNativeAd;
    [unifiedNativeAd loadAd];
}

- (void)dspAdsRegisterContainer:(__kindof UIView *)containerView
             withClickableViews:(NSArray<__kindof UIView *> *_Nullable)clickableViews
              withClosableViews:(NSArray<__kindof UIView *> *_Nullable)closableViews {
    self.unifiedNativeAdView.frame = containerView.bounds;
    self.unifiedNativeAdView.viewController = self.rootViewController;
    if (self.originData.positionId != 295 && self.originData.positionId != 308) {
        [containerView insertSubview:self.unifiedNativeAdView atIndex:0];
    }
    [self.unifiedNativeAdView registerDataObject:self.gdtData clickableViews:clickableViews];
    [self layoutLogoView];
}

- (void)win:(nullable NSNumber *)auctionBidToWin {
    GDTUnifiedNativeAdDataObject *ad = self.gdtData;
    if (ad && auctionBidToWin) {
        [ad sendWinNotificationWithInfo:@{GDT_M_W_E_COST_PRICE : auctionBidToWin}];
    }
}

- (void)loss:(nullable NSNumber *)auctionPrice {
    GDTUnifiedNativeAdDataObject *ad = self.gdtData;
    if (ad && auctionPrice && self.loadingStatus == XMINativeBaseLoaderLoadingStatusSuccess) {
        [ad sendLossNotificationWithInfo:@{GDT_M_L_WIN_PRICE : auctionPrice}];
    }
}

//MARK: - GDTUnifiedNativeAdDelegate
- (void)gdt_unifiedNativeAdLoaded:(NSArray<GDTUnifiedNativeAdDataObject *> * _Nullable)unifiedNativeAdDataObjects error:(NSError * _Nullable)error {
    if (!self.originData) return;
    
    if (error) {
        [self loadFailed:error];
        [XMIAdHelper apmLogMessage:[NSString stringWithFormat:@"gdt error: position=%lld, code=%zd,message =%@", self.originData.positionId, error.code, error.localizedDescription]];
        return;
    }
    
    GDTUnifiedNativeAdDataObject *nativeAd = unifiedNativeAdDataObjects.firstObject;
    if (![nativeAd isAdValid]) {
        [self loadFailed:error];
        return;
    }
    self.gdtData = nativeAd;
    [self gdtNativeAdDataMapToRelateData:self.originData gdtNativeData:nativeAd];
    [self loadSuccess];
}

- (void)gdtNativeAdDataMapToRelateData:(XMIAdRelatedData *)originData gdtNativeData:(GDTUnifiedNativeAdDataObject *)gdtData {
    long long positionId = originData.positionId;
    if (originData.isMobileRtb) {
        // 转换价格，单位为元，原始为分
        float originPrice = gdtData.eCPM;
        originData.price = originPrice * 1.0 / 100;
        XMILogNativeInfo(@"AD_LOG_NATIVE_RTB",@"GDT 请求dsp原生广告成功 🎉🎉🎉 【%lld(%ld, %@)】price = %.6f 元 sdk返回原始价格 %.1f 分", originData.adid, originData.adtype, originData.dspPositionId, originData.price, originPrice);
    }
    switch (positionId) {
        case 14:
        { // 下挂接dsp，代码待优化
            GDTUnifiedNativeAdView *unifiedNativeAdView = [GDTUnifiedNativeAdView new];
            unifiedNativeAdView.logoView.hidden = YES;
            unifiedNativeAdView.userInteractionEnabled = NO;
            unifiedNativeAdView.delegate = self;
           
            self.unifiedNativeAdView = unifiedNativeAdView;
            
            originData.cover = gdtData.imageUrl;
            if (gdtData.iconUrl.length) {
                originData.cover = gdtData.iconUrl;
            }
            originData.name = gdtData.desc;
            originData.adDescription = gdtData.title;
        }
            break;
        case 15:
        case 16:
        case 271:
        {
            _mute = gdtData.videoConfig.videoMuted;
            GDTUnifiedNativeAdView *unifiedNativeAdView = [[GDTUnifiedNativeAdView alloc] initWithFrame:CGRectMake(0, 0, XMI_SCREEN_WIDTH, 400)];
//            unifiedNativeAdView.userInteractionEnabled = NO;
            unifiedNativeAdView.delegate = self;
           
            self.unifiedNativeAdView = unifiedNativeAdView;
            
            originData.cover = gdtData.imageUrl;
            originData.name = gdtData.desc;
            originData.clickTitle = gdtData.buttonText;
            [self layoutLogoView];
        }
            break;
        case 295:
        case 308:
        {
            _mute = gdtData.videoConfig.videoMuted;
            GDTUnifiedNativeAdView *unifiedNativeAdView = [[GDTUnifiedNativeAdView alloc] initWithFrame:CGRectMake(0, 0, 48, 48)];
//            unifiedNativeAdView.userInteractionEnabled = NO;
            unifiedNativeAdView.delegate = self;
           
            self.unifiedNativeAdView = unifiedNativeAdView;
            
            originData.cover = gdtData.iconUrl;
            originData.name = gdtData.title;
            originData.adDescription = gdtData.desc;
            originData.clickTitle = gdtData.buttonText;
            [self layoutLogoView];
        }
        default:
            break;
    }


}

- (void)gdt_unifiedNativeAdViewWillExpose:(GDTUnifiedNativeAdView *)unifiedNativeAdView {
    NSLog(@"gdt will expose");
    if (self.delegate && [self.delegate respondsToSelector:@selector(dspAdsNativeAdDidExposure:)]) {
        [self.delegate dspAdsNativeAdDidExposure:self.originData];
    }
}

- (void)gdt_unifiedNativeAdViewDidClick:(GDTUnifiedNativeAdView *)unifiedNativeAdView {
    if (self.delegate && [self.delegate respondsToSelector:@selector(dspAdsNativeAdDidClick:withView:)]) {
        [self.delegate dspAdsNativeAdDidClick:self.originData withView:unifiedNativeAdView];
    }
}

- (void)gdt_unifiedNativeAdDetailViewWillPresentScreen:(GDTUnifiedNativeAdView *)unifiedNativeAdView
{
    if ([self.delegate respondsToSelector:@selector(dspAdsManager:nativeAdWillOpenDetailPage:)]) {
        [self.delegate dspAdsManager:self nativeAdWillOpenDetailPage:self.originData];
    }
}

- (void)gdt_unifiedNativeAdDetailViewClosed:(GDTUnifiedNativeAdView *)unifiedNativeAdView
{
    if ([self.delegate respondsToSelector:@selector(dspAdsManager:nativeAdDidCloseDetailPage:)]) {
        [self.delegate dspAdsManager:self nativeAdDidCloseDetailPage:self.originData];
    }
}

- (UIView *)customView
{
    return self.unifiedNativeAdView;
}

- (void)layoutLogoView
{
    CGFloat width = 58*0.65f;
    CGFloat height = 22*0.65f;
    self.unifiedNativeAdView.logoView.frame = CGRectMake(self.unifiedNativeAdView.xmi_width - XMIAdPic(26) - width, XMIAdPic(15) - height * 0.5f, width, height);
    self.unifiedNativeAdView.logoView.autoresizingMask = UIViewAutoresizingFlexibleLeftMargin;
    if (self.originData.positionId == 15 || self.originData.positionId == 16) {
        self.unifiedNativeAdView.logoView.hidden = YES;
    }
}

- (void)setMute:(BOOL)mute
{
    _mute = mute;
    [self.unifiedNativeAdView.mediaView muteEnable:mute];
}

- (BOOL)isVideo
{
    return self.gdtData.isVideoAd;
}

- (BOOL)isDownloadAd
{
    return self.gdtData.isAppAd;
}

@end
