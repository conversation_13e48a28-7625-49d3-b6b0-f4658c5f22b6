//
//  XMINativeBaseLoader.h
//  XMAd
//
//  Created by xmly on 2023/6/13.
//

#import <Foundation/Foundation.h>
@class XMINativeBaseLoader, XMIAdRelatedData, XMIAdLimitedMutableArray;
NS_ASSUME_NONNULL_BEGIN

typedef enum : NSUInteger {
    XMINativeBaseLoaderLoadingStatusInit,
    XMINativeBaseLoaderLoadingStatusLoading,
    XMINativeBaseLoaderLoadingStatusSuccess,
    XMINativeBaseLoaderLoadingStatusFailed,
    XMINativeBaseLoaderLoadingStatusTimeout,
} XMINativeBaseLoaderLoadingStatus;

@protocol XMINativeDSPLoaderProtocol <NSObject>

@optional
- (void)dspAdsManagerSuccessToLoad:(XMINativeBaseLoader *)adsLoader nativeAds:(NSArray<XMIAdRelatedData *> *)adArray;

- (void)dspAdsManager:(XMINativeBaseLoader *)adsLoader nativeAd:(XMIAdRelatedData *)adData didFailWithError:(NSError *_Nullable)error;

- (void)dspAdsNativeAdDidExposure:(XMIAdRelatedData *)adData;

- (void)dspAdsNativeAdDidClick:(XMIAdRelatedData *)adData withView:(UIView *_Nullable)view;

- (void)dspAdsManager:(XMINativeBaseLoader *)adsLoader nativeAdWillOpenDetailPage:(XMIAdRelatedData *)adData;

- (void)dspAdsManager:(XMINativeBaseLoader *)adsLoader nativeAdDidCloseDetailPage:(XMIAdRelatedData *)adData;

- (void)dspAdsNativeAdDidClickClose:(XMIAdRelatedData *)adData withView:(UIView *_Nullable)view;

@end

@interface XMINativeBaseLoader : NSObject
@property (nonatomic, weak) id<XMINativeDSPLoaderProtocol> delegate;

@property (nonatomic, assign) XMINativeBaseLoaderLoadingStatus loadingStatus;

@property (nonatomic, strong) XMIAdLimitedMutableArray *dspAdManagers;

@property (nonatomic, weak) UIViewController *rootViewController;

@property (nonatomic, strong) XMIAdRelatedData *originData;

@property (nonatomic, assign) int64_t requestTimeCost;

@property (nonatomic, assign) BOOL isCache;

- (void)loadAdWithData:(XMIAdRelatedData *)originData;

- (void)dspAdsRegisterContainer:(__kindof UIView *)containerView
       withClickableViews:(NSArray<__kindof UIView *> *_Nullable)clickableViews
        withClosableViews:(NSArray<__kindof UIView *> *_Nullable)closableViews;

- (UIView *)customView;

- (BOOL)isVideo;

- (void)win:(nullable NSNumber *)auctionBidToWin;

- (void)loss:(nullable NSNumber *)auctionPrice;

- (void)loadSuccess;

- (void)loadFailed:(NSError *)error;

- (NSString *)loadingStatusString;

@end


@interface XMIAdLimitedMutableArray : NSMutableArray

@property (nonatomic, assign) NSUInteger capacity; // 数组最大容量

@end



NS_ASSUME_NONNULL_END
