//
//  XMINativeBaiduLoader.m
//  XMAd
//
//  Created by xiaodong2.zhang on 2024/11/25.
//

#import "XMINativeBaiduLoader.h"
#import <BaiduMobAdSDK/BaiduMobAdNative.h>
#import <BaiduMobAdSDK/BaiduMobAdNativeAdObject.h>
#import <BaiduMobAdSDK/BaiduMobAdNativeVideoView.h>
#import "XMIAdRelatedData.h"
#import "XMIAdMacro.h"
#import "UIView+XMIUtils.h"
#import "XMIAdError.h"
#import "XMIAdHelper.h"
#import "XMIAdNativeLogger.h"

@interface XMINativeBaiduLoader ()<BaiduMobAdNativeAdDelegate, BaiduMobAdNativeInterationDelegate, BaiduMobAdNativeVideoViewDelegate>
@property (nonatomic, strong) BaiduMobAdNative *nativeAd;
@property (nonatomic, strong) BaiduMobAdNativeAdObject *sdkObject;

@property (nonatomic, weak) BaiduMobAdNativeVideoView *videoView;
@end

@implementation XMINativeBaiduLoader
{
    UIView *_customView;
}
- (void)loadAdWithData:(XMIAdRelatedData *)originData {
    [super loadAdWithData:originData];
    
    self.originData = originData;
    NSString *dspPositionId = originData.dspPositionId;
    
    self.nativeAd = [[BaiduMobAdNative alloc] init];
    self.nativeAd.adDelegate = self;
    self.nativeAd.publisherId = kXMIBaiduSDKAppId;
    self.nativeAd.adUnitTag = dspPositionId;
    self.nativeAd.timeout = 10;
    [self.nativeAd requestNativeAds];
}

- (void)dealloc
{
    
}

- (void)trackImpression:(UIView *)view {
    [self.sdkObject trackImpression:view];
}

- (UIView *)customView {
    if (!_customView) {
        _customView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, XMI_SCREEN_WIDTH, 400)];
    }
    BaiduMobAdSmartFeedStyleType style_type = self.sdkObject.style_type;
    if (style_type == FeedType_VIDEO_TOP_TITLE) {
        BaiduMobAdNativeVideoView *videoView = [[BaiduMobAdNativeVideoView alloc] initWithFrame:_customView.bounds andObject:self.sdkObject];
        _videoView = videoView;
        [videoView setVideoMute:YES];
        videoView.videoDelegate = self;
        videoView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        [_customView addSubview:videoView];
    } else {
        
    }
    _customView.userInteractionEnabled = NO;
    return _customView;
}

//MARK: - trans

- (void)nativeAdDataMapToRelateData:(XMIAdRelatedData *)originData sdkObject:(BaiduMobAdNativeAdObject *)sdkObject {
    self.sdkObject = sdkObject;
    self.sdkObject.interationDelegate = self;
    if (originData.isMobileRtb) {
        // 转换价格，单位为元，原始为分
        float originPrice = [[sdkObject getECPMLevel] floatValue];
        originData.price = originPrice * 1.0 / 100;
        XMILogNativeInfo(@"AD_LOG_NATIVE_RTB",@"BAIDU 请求dsp原生广告成功 🎉🎉🎉 【%lld(%ld, %@)】price = %.6f 元 sdk返回原始价格 %f 分", originData.adid, originData.adtype, originData.dspPositionId, originData.price, originPrice);
    }
    originData.cover = sdkObject.mainImageURLString;
    originData.name = sdkObject.title;
    originData.adDescription = sdkObject.text;
    originData.iconUrl = sdkObject.iconImageURLString;
}

- (void)dspAdsRegisterContainer:(__kindof UIView *)containerView
             withClickableViews:(NSArray<__kindof UIView *> *_Nullable)clickableViews
              withClosableViews:(NSArray<__kindof UIView *> *_Nullable)closableViews {
    [self.sdkObject setPresentAdViewController:self.rootViewController];

    [clickableViews enumerateObjectsUsingBlock:^(__kindof UIView * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapClickableViews:)];
        [obj addGestureRecognizer:tap];
    }];
}

- (void)tapClickableViews:(UITapGestureRecognizer *)tap {
    [self.sdkObject handleClick:tap.view];
}

//MARK: - BaiduMobAdNativeAdDelegate
/**
 * 广告请求成功
*/
- (void)nativeAdObjectsSuccessLoad:(NSArray *)nativeAds nativeAd:(BaiduMobAdNative *)nativeAd {
    if (!self.originData) return;
    if (nativeAds.count == 0) {
        [self loadFailed:[XMIAdError emptyDataError]];
        return;
    }
    [self nativeAdDataMapToRelateData:self.originData sdkObject:nativeAds.firstObject];
    [self loadSuccess];
}

/**
*  广告请求失败
*/
- (void)nativeAdsFailLoadCode:(NSString *)errCode
                      message:(NSString *)message
                     nativeAd:(BaiduMobAdNative *)nativeAd
                     adObject:(BaiduMobAdNativeAdObject *)adObject {
    self.nativeAd = nil;
    NSError *error = [NSError errorWithDomain:@"BaiduaD" code:errCode.integerValue userInfo:@{NSLocalizedDescriptionKey : message?:@""}];
    [self loadFailed:error];
    [XMIAdHelper apmLogMessage:[NSString stringWithFormat:@"baidu error: position=%lld, code=%@,message =%@", self.originData.positionId, errCode, message]];
}

//MARK: - BaiduMobAdNativeInterationDelegate

- (void)nativeAdExposure:(UIView *)nativeAdView nativeAdDataObject:(BaiduMobAdNativeAdObject *)object {
    if (self.delegate && [self.delegate respondsToSelector:@selector(dspAdsNativeAdDidExposure:)]) {
        [self.delegate dspAdsNativeAdDidExposure:self.originData];
    }
}

- (void)nativeAdClicked:(UIView *)nativeAdView nativeAdDataObject:(BaiduMobAdNativeAdObject *)object {
    if (self.delegate && [self.delegate respondsToSelector:@selector(dspAdsNativeAdDidClick:withView:)]) {
        [self.delegate dspAdsNativeAdDidClick:self.originData withView:nil];
    }
}

/**
 视频准备开始播放
 
 @param videoView self
 */
- (void)nativeVideoAdDidStartPlaying:(BaiduMobAdNativeVideoView *)videoView {
//    NSLog(@"BAIDU_VIDEO nativeVideoAdDidStartPlaying");
}

/**
 视频暂停播放
 
 @param videoView self
 */
- (void)nativeVideoAdDidPause:(BaiduMobAdNativeVideoView *)videoView {
//    NSLog(@"BAIDU_VIDEO nativeVideoAdDidPause");
}

/**
 视频重播
 
 @param videoView self
 */
- (void)nativeVideoAdDidReplay:(BaiduMobAdNativeVideoView *)videoView {
//    NSLog(@"BAIDU_VIDEO nativeVideoAdDidReplay");
}

/**
 视频播放完成

 @param videoView self
 */
- (void)nativeVideoAdDidComplete:(BaiduMobAdNativeVideoView *)videoView {
//    NSLog(@"BAIDU_VIDEO nativeVideoAdDidComplete");
}

/**
 视频播放失败

 @param videoView self
 */
- (void)nativeVideoAdDidFailed:(BaiduMobAdNativeVideoView *)videoView {
//    NSLog(@"BAIDU_VIDEO nativeVideoAdDidFailed");
}

/**
 视频首帧播放
 
 @param videoView self
 */
- (void)nativeVideoAdDidReadyForDisplay:(BaiduMobAdNativeVideoView *)videoView {
//    NSLog(@"BAIDU_VIDEO nativeVideoAdDidReadyForDisplay");
}


- (void)baiduVideoReplayIfNeed {
    if (self.videoView && !self.videoView.isPlaying) {
        [self.videoView play];
//        NSLog(@"BAIDU_VIDEO 正式调用播放 ▶️▶️▶️");
    }
}
@end
