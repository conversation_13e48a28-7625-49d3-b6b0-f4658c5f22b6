//
//  XMINativeAdLoader.h
//  XMAd
//
//  Created by xiaodong2.zhang on 2024/8/27.
//

#import <Foundation/Foundation.h>
#import "XMIAdRelatedData.h"

NS_ASSUME_NONNULL_BEGIN

@protocol XMINativeDSPLoaderProtocol;
@class XMINativeBaseLoader;
@interface XMINativeAdLoader : NSObject

- (void)fillAdItems:(NSArray<XMIAdRelatedData *> *)adItems
            timeout:(CGFloat)timeout
           delegate:(id<XMINativeDSPLoaderProtocol>)delegate
 rootViewController:(UIViewController *)rootViewController
         completion:(void(^)(NSArray<XMINativeBaseLoader *> *results))completion;

@end

NS_ASSUME_NONNULL_END
