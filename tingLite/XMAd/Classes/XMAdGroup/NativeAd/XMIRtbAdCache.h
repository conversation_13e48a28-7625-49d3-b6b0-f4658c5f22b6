//
//  XMIRtbAdCache.h
//  XMAdApi
//
//  Created by xiaodong2.zhang on 2025/2/21.
//

#import <Foundation/Foundation.h>
#import <XMCategories/XMCategory.h>

NS_ASSUME_NONNULL_BEGIN

@class XMINativeBaseLoader;
@interface XMIRtbAdCache : NSObject

DECLARE_SINGLETON_METHOD(XMIRtbAdCache, sharedCache);

- (XMINativeBaseLoader *)cacheForItem:(XMINativeBaseLoader *)adItem winPrice:(float)winPrice;

- (BOOL)addCacheWithItem:(XMINativeBaseLoader *)adItem;

//- (void)removeCacheOfItemIfNeeded:(XMINativeBaseLoader *)adItem;

- (void)checkCacheWithItem:(XMINativeBaseLoader *)adItem;

@end

NS_ASSUME_NONNULL_END
