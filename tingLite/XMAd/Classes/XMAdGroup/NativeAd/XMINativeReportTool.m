//
//  XMINativeReportTool.m
//  XMAd
//
//  Created by xmly on 2023/5/24.
//

#import "XMINativeReportTool.h"
#import "XMIAdReporter+AD.h"
@implementation XMINativeReportTool
+ (void)exposeAds:(NSArray<XMIAdRelatedData *> *)adDatas {
    [XMIAdReporter exposeReportValidAds:adDatas];
}

+ (void)realExposeAds:(XMIAdRelatedData *)adData view:(UIView *)view
{
    [XMIAdReporter exposeReportWithAd:adData andView:view];
}

@end
