//
//  XMINativeAd.m
//  XMAd
//
//  Created by xmly on 2023/5/23.
//

#import "XMINativeAd.h"
#import "XMINativeAdSlot.h"
#import "XMIAdDataCenter.h"
#import "XMIAdPreviewManager.h"
#import "XMICommonUtils.h"
#import "XMIAdMacro.h"
#import "XMIAdError.h"
#import "XMIAdReporter+AD.h"
#import "UIView+XMIUtils.h"
#import "XMIJumpManagerFactory.h"
#import "XMIAdManager.h"
#import "XMIExpressAdView.h"
#import "XMIAdHelper.h"
#import "XMIAdRespData.h"
#import "NSObject+XMIModel.h"
#import "XMINativeAdLoader.h"
#import "XMINativeBULoader.h"
#import "XMINativeBaiduLoader.h"
#import "XMINativeGDTLoader.h"
#import "XMINativeJADLoader.h"

#import <KVOController/KVOController.h>
@interface XMINativeAd ()<XMIJumpManagerDelegate, XMINativeDSPLoaderProtocol>
{
    NSMutableArray<UIView *> *_clickableContentViews; // 可点击视图数组
    NSMutableArray<UIView *> *_closeContentViews;  // 可关闭视图数组
}
@property (nonatomic, copy, readonly) NSString *adPositionID; // 广告位ID
@property (nonatomic, copy, readonly) NSString *adPositionName; // 广告位名称

@property (nonatomic, strong) id<XMIAdRequestProtocol> request;

@property (nonatomic, strong) UIView *adView;

@property (nonatomic, strong) id<XMIJumpManagerProtocol> jumpManager;

@property (nonatomic, strong) XMINativeBULoader *buLoader;
@property (nonatomic, strong) XMINativeGDTLoader *gdtLoader;
@property (nonatomic, strong) XMINativeJADLoader *jadLoader;
@property (nonatomic, strong) XMINativeAdLoader *adLoader;
@property (nonatomic, strong) XMINativeBaiduLoader *baiduLoader;

@property (nonatomic, strong) XMIAdRelatedData *singleAdData;

@property (nonatomic, copy) void (^loadCompletion)(BOOL);

@end

@implementation XMINativeAd
@dynamic dspMute;

- (void)dealloc {
    _request = nil;
    self.loadCompletion = nil;
}

- (instancetype)initWithSlot:(XMINativeAdSlot *)slot {
    if (self = [super init]) {
        _adPositionID = slot.slotID;
        _adPositionName = slot.positionName;
        _data = [NSArray array];
        _clickableContentViews = [NSMutableArray array];
        _closeContentViews = [NSMutableArray array];
        _reportThirdExpose = YES;
        _cleanDataAfterLoadFailed = YES;
    }
    return self;
}

- (void)loadAdDatawithCount:(NSInteger)count {
    [self loadAdDatawithCount:count extra:nil];
}

- (void)loadAdDatawithCount:(NSInteger)count extra:(NSDictionary *)extra
{
    [self loadAdDatawithCount:count extra:extra completion:nil];
}

- (void)loadAdDatawithCount:(NSInteger)count extra:(NSDictionary *)extra completion:(nonnull void (^)(BOOL))completion {
    NSString *positionName = self.adPositionName;
    /// 加入预览数据
    long long slotId = [XMIAdDataCenter getSlotIdByPositionName:positionName];
    XMIAdRespAdData *prevData = [XMIAdPreviewManager getPreviewResponseWith:slotId];
    if (prevData) {
        NSArray<XMIAdRelatedData *> *preDatas = [XMIAdDataCenter adDataFromResponse:prevData];
        NSMutableArray *tempArray = [NSMutableArray arrayWithCapacity:0];
        [tempArray addObjectsFromArray:preDatas];
        self.loadCompletion = completion;
        [self didLoadData:tempArray andUseTime:0];
        return;
    }
    
    NSString *originAdDataKey = @"originAdDataKey";
    NSDictionary *originAdData = [extra valueForKey:originAdDataKey];
    if (originAdData) {
        XMIAdRespAdData *respData = [XMIAdRespAdData xmi_modelWithJSON:originAdData];
        NSArray<XMIAdRelatedData *> *preDatas = [XMIAdDataCenter adDataFromResponse:respData];
        NSMutableArray *tempArray = [NSMutableArray arrayWithCapacity:0];
        [tempArray addObjectsFromArray:preDatas];
        self.loadCompletion = completion;
        [self didLoadData:tempArray andUseTime:0];
        return;
    }
    
    NSMutableDictionary *props = [NSMutableDictionary dictionary];
    [props addEntriesFromDictionary:extra];
    long long startMs = [XMICommonUtils currentTimestamp];
    @weakify(self)
    id<XMIAdRequestProtocol> request = [XMIAdDataCenter adDataRequestWithPositionName:positionName props:props dataHandler:^(NSArray<XMIAdRelatedData *> * _Nullable adArray, NSError * _Nullable error) {
        XMILog(@"%@", error);
        long long endMs = [XMICommonUtils currentTimestamp];
        long long useTime = endMs - startMs;
        
        @strongify(self)
        if (!self) {
            if (completion) {
                completion(NO);
            }
            return;
        }
        
        if (error != nil) {
            // TODO: 打印日志
            [self didLoadFailWithError:error andUseTime:useTime sendReport:YES];
            if (completion) {
                completion(NO);
            }
            return;
        }
        
        if (adArray.count < 1) {
            [self didLoadFailWithError:[XMIAdError emptyDataError] andUseTime:useTime sendReport:YES];
            if (completion) {
                completion(NO);
            }
            return;
        }
        self.loadCompletion = completion;
        [self didLoadData:adArray andUseTime:useTime];
    }];
    self.request = request;
    [request start];
}

- (void)registerContainer:(__kindof UIView *)containerView
       withClickableViews:(NSArray<__kindof UIView *> *_Nullable)clickableViews
        withClosableViews:(NSArray<__kindof UIView *> *_Nullable)closableViews {
    self.adView = containerView;
    _clickableContentViews = [NSMutableArray array];
    _closeContentViews = [NSMutableArray array];
    [_clickableContentViews addObjectsFromArray:clickableViews];
    [_closeContentViews addObjectsFromArray:closableViews];
    [self addViewClickActions];
    [self addViewCloseAction];
    
    // dsp 注册相关view
    BOOL gdtAd = self.singleAdData.adtype == XMIAdTypeGDT;
    BOOL buAd = self.singleAdData.adtype == XMIAdTypeBU;
    BOOL jadAd = self.singleAdData.adtype == XMIAdTypeJAD;
    BOOL baiduAd = self.singleAdData.adtype == XMIAdTypeBAIDU;
    if (gdtAd) {
        self.gdtLoader.rootViewController = self.rootViewController;
        [self.gdtLoader dspAdsRegisterContainer:containerView withClickableViews:clickableViews withClosableViews:closableViews];
    } else if (buAd) {
        self.buLoader.rootViewController = self.rootViewController;
        [self.buLoader dspAdsRegisterContainer:containerView withClickableViews:clickableViews withClosableViews:closableViews];
    } else if (jadAd) {
        self.jadLoader.rootViewController = self.rootViewController;
        [self.jadLoader dspAdsRegisterContainer:containerView withClickableViews:clickableViews withClosableViews:closableViews];
    } else if (baiduAd) {
        self.baiduLoader.rootViewController = self.rootViewController;
        [self.baiduLoader dspAdsRegisterContainer:containerView withClickableViews:clickableViews withClosableViews:closableViews];
    }
}

// 百度的设计，必须手动调用一下
- (void)trackImpression:(UIView *)view {
    if (self.baiduLoader) {
        [self.baiduLoader trackImpression:view];
    }
}

//
- (void)baiduVideoReplayIfNeed {
    if (self.baiduLoader) {
        [self.baiduLoader baiduVideoReplayIfNeed];
    }
}

- (void)addViewClickActions {
    NSArray *clickViews = _clickableContentViews;
    for (UIView *clickView in clickViews) {
        
        NSArray *gestures = clickView.gestureRecognizers;
        for (UIGestureRecognizer *ges in gestures) {
            [clickView removeGestureRecognizer:ges];
        }
        
        clickView.userInteractionEnabled = YES;
        if (self.singleAdData.dsp) {// 当前请求到的是dsp广告，那么不添加手势
            continue;
        }
        UITapGestureRecognizer *adTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(adClick:)];
        [clickView addGestureRecognizer:adTap];
    }
}

- (void)addViewCloseAction {
    NSArray *closeViews = _closeContentViews;
    for (UIView *closeView in closeViews) {
        NSArray *gestures = closeView.gestureRecognizers;
        for (UIGestureRecognizer *ges in gestures) {
            [closeView removeGestureRecognizer:ges];
        }
        closeView.userInteractionEnabled = YES;
//        if (self.singleAdData.dsp) {// 当前请求到的是dsp广告，那么不添加手势
//            continue;
//        }
        UITapGestureRecognizer *adTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(adClose:)];
        [closeView addGestureRecognizer:adTap];
    }
}

- (void)unregisterView {
    
}

- (void)adClick:(UITapGestureRecognizer *)tap {
    XMIAdRelatedData *adData = self.data.firstObject;
    if (adData) {
        NSDictionary *userInfo = @{};
        if (self.delegate && [self.delegate respondsToSelector:@selector(xmiNativeAdDidClick:withView:)]) {
            userInfo = [self.delegate xmiNativeAdDidClick:self withView:tap.view];
        }
        
        
        UIView *tapView = tap.view;
        [XMIAdReporter clickReportWithAd:adData andView:tapView andUserInfo:userInfo];
        NSMutableDictionary *dic = [[NSMutableDictionary alloc] init];
        // 防止除0问题
        if (tapView.xmi_width > 0 && tapView.xmi_height > 0) {
            CGPoint point = [tap locationInView:tapView];
            NSString *absX = [NSString stringWithFormat:@"%d", (int)point.x];
            NSString *absY = [NSString stringWithFormat:@"%d", (int)point.y];
            NSString *x = [NSString stringWithFormat:@"%.2f", tapView.xmi_width];
            NSString *y = [NSString stringWithFormat:@"%.2f", tapView.xmi_height];
            dic[@"absX"] = absX;
            dic[@"absY"] = absY;
            dic[@"x"] = x;
            dic[@"y"] = y;
        }
        BOOL jumpSupport = [self.jumpManager doJumpWithAd:adData];
        if (!jumpSupport) {
            if ([[XMIAdManager sharedInstance].delegate respondsToSelector:@selector(managerHandleSDKNotSupportedAdJump:)]) {
                //处理不了交给主站跳转
                [[XMIAdManager sharedInstance].delegate managerHandleSDKNotSupportedAdJump:adData];
            }
            dic[kUserInfoJumpNotSupport] = @(YES);
        }
    }
}

- (void)adClose:(UITapGestureRecognizer *)tap {
    if (self.delegate && [self.delegate respondsToSelector:@selector(xmiNativeAdDidClose:withView:)]) {
        [self.delegate xmiNativeAdDidClose:self withView:tap.view];
    }
}

- (id<XMIJumpManagerProtocol>)jumpManager
{
    if (!_jumpManager) {
        XMIAdRelatedData *adData = self.data.firstObject;
        if (!adData) {return  nil;}
        _jumpManager = [XMIJumpManagerFactory jumpManagerByAdType:adData.adtype];
        _jumpManager.delegate = self;
        _jumpManager.rootViewController = self.rootViewController;
    }
    return _jumpManager;
}

//MARK: - XMINativeAdDelegate
- (void)didLoadData:(NSArray<XMIAdRelatedData *> *)adArray andUseTime:(long long)useTime {
    BOOL adxRtbEnable = NO;
    NSString *abPassThroughParams = nil;
    for (XMIAdRelatedData *relatedData in adArray) {
        if (relatedData.isMobileRtb) {
            adxRtbEnable = YES;
            abPassThroughParams = relatedData.abPassThroughParams;
            break;
        }
    }
    if (adxRtbEnable) {
        @weakify(self)
        CGFloat timeout = 3.f;
        if (abPassThroughParams.length) {
            NSData *jsonData = [abPassThroughParams dataUsingEncoding:NSUTF8StringEncoding];
            NSError *err;
            NSDictionary *dict = [NSJSONSerialization JSONObjectWithData:jsonData
                                                                 options:NSJSONReadingMutableContainers
                                                                   error:&err];
            if (!err && [dict isKindOfClass:[NSDictionary class]]) {
                if ([dict objectForKey:@"dspTimeout"]) {
                    timeout = (CGFloat)[dict safeIntegerValueForKey:@"dspTimeout"] / 1000.f;
                }
            }
        }
        self.adLoader = [XMINativeAdLoader new];
        [self.adLoader fillAdItems:adArray
                           timeout:timeout
                          delegate:self
                rootViewController:self.rootViewController
                        completion:^(NSArray<XMINativeBaseLoader *> * _Nonnull results) {
            @strongify(self)
            XMINativeBaseLoader *loader = results.firstObject;
            self.singleAdData = loader.originData;
            if (self.singleAdData) {
                if ([loader isKindOfClass:[XMINativeBULoader class]]) {
                    self->_buLoader = (XMINativeBULoader *)loader;
                }
                if ([loader isKindOfClass:[XMINativeGDTLoader class]]) {
                    self->_gdtLoader = (XMINativeGDTLoader *)loader;
                }
                if ([loader isKindOfClass:[XMINativeJADLoader class]]) {
                    self->_jadLoader = (XMINativeJADLoader *)loader;
                }
                if ([loader isKindOfClass:[XMINativeBaiduLoader class]]) {
                    self->_baiduLoader = (XMINativeBaiduLoader *)loader;
                }
                self->_data = @[self.singleAdData];
                if (self.delegate && [self.delegate respondsToSelector:@selector(xmiNativeAdDidLoadSuccess:)]) {
                    [self.delegate xmiNativeAdDidLoadSuccess:self];
                }
                if (self.loadCompletion) {
                    self.loadCompletion(YES);
                    self.loadCompletion = nil;
                }
            } else {
                [self didLoadFailWithError:[XMIAdError emptyDataError] andUseTime:-1 sendReport:YES];
                if (self.loadCompletion) {
                    self.loadCompletion(NO);
                    self.loadCompletion = nil;
                }
            }
        }];
        return;
    }
    _data = adArray;
    XMIAdRelatedData *adData = adArray.firstObject;
    _singleAdData = adData;
    BOOL isSdkData = adData.dsp;
    if (isSdkData) {// 如果返回的数据中有SDK广告，去请求SDK，等SDK都回调完毕后，再回调
        [self loadSdkDataWithAdxData:adData];
    } else {
        if (self.delegate && [self.delegate respondsToSelector:@selector(xmiNativeAdDidLoadSuccess:)]) {
            [self.delegate xmiNativeAdDidLoadSuccess:self];
        }
        if (self.loadCompletion) {
            self.loadCompletion(YES);
            self.loadCompletion = nil;
        }
    }
}

- (void)didLoadFailWithError:(NSError *)error andUseTime:(long long)useTime sendReport:(BOOL)sendReport
{
    if (self.cleanDataAfterLoadFailed) {
        _data = [NSArray array];
        _singleAdData = nil;
    }
    if (self.delegate && [self.delegate respondsToSelector:@selector(xmiNativeAdDidLoadFailure:error:)]) {
        [self.delegate xmiNativeAdDidLoadFailure:self error:error];
    }
}

//MARK: - DSP-Ad
- (void)loadSdkDataWithAdxData:(XMIAdRelatedData *)adData {
    BOOL gdtAd = adData.adtype == XMIAdTypeGDT;
    BOOL buAd = adData.adtype == XMIAdTypeBU;
    BOOL jadAd = self.singleAdData.adtype == XMIAdTypeJAD;
    BOOL baiduAd = self.singleAdData.adtype == XMIAdTypeBAIDU;
    [XMIAdHelper initSdkWithAdType:adData.adtype];
    if (gdtAd) {
        [self.gdtLoader loadAdWithData:adData];
    }
    else if (buAd) {
        [self.buLoader loadAdWithData:adData];
    }
    else if (jadAd) {
        [self.jadLoader loadAdWithData:adData];
    }
    else if (baiduAd) {
        [self.baiduLoader loadAdWithData:adData];
    }
}

- (XMINativeBULoader *)buLoader {
    if (!_buLoader) {
        _buLoader = [[XMINativeBULoader alloc] init];
        _buLoader.delegate = self;
    }
    return _buLoader;
}

- (XMINativeGDTLoader *)gdtLoader {
    if (!_gdtLoader) {
        _gdtLoader = [[XMINativeGDTLoader alloc] init];
        _gdtLoader.delegate = self;
    }
    return _gdtLoader;
}

- (XMINativeJADLoader *)jadLoader {
    if (!_jadLoader) {
        _jadLoader = [[XMINativeJADLoader alloc] init];
        _jadLoader.delegate = self;
    }
    return _jadLoader;
}

- (XMINativeBaiduLoader *)baiduLoader {
    if (!_baiduLoader) {
        _baiduLoader = [[XMINativeBaiduLoader alloc] init];
        _baiduLoader.delegate = self;
    }
    return _baiduLoader;
}

//MARK: - XMINativeDSPLoaderProtocol
- (void)dspAdsManagerSuccessToLoad:(XMINativeBaseLoader *)adsLoader nativeAds:(NSArray<XMIAdRelatedData *> *)adArray {
    if (self.delegate && [self.delegate respondsToSelector:@selector(xmiNativeAdDidLoadSuccess:)]) {
        [self.delegate xmiNativeAdDidLoadSuccess:self];
    }
    if (self.loadCompletion) {
        self.loadCompletion(YES);
        self.loadCompletion = nil;
    }
}

- (void)dspAdsManager:(XMINativeBaseLoader *)adsLoader nativeAd:(XMIAdRelatedData *)adData didFailWithError:(NSError *)error {
    NSInteger dataIndex = [self.data indexOfObject:adData];
    if (dataIndex == NSNotFound) {
        _data = [NSArray array];
        if (self.delegate && [self.delegate respondsToSelector:@selector(xmiNativeAdDidLoadFailure:error:)]) {
            [self.delegate xmiNativeAdDidLoadFailure:self error:error];
        }
        if (self.loadCompletion) {
            self.loadCompletion(NO);
            self.loadCompletion = nil;
        }
    } else {
        NSMutableArray *tempDatas = [NSMutableArray arrayWithArray:self.data];
        [tempDatas removeObjectMaybeAtIndex:dataIndex];
        if (tempDatas.count == 0) {
            [self didLoadFailWithError:[XMIAdError emptyDataError] andUseTime:-1 sendReport:YES];
            if (self.loadCompletion) {
                self.loadCompletion(NO);
                self.loadCompletion = nil;
            }
        } else {
            [self didLoadData:tempDatas andUseTime:-1];
        }
    }
}

- (void)dspAdsNativeAdDidExposure:(XMIAdRelatedData *)adData {
    if (self.reportThirdExpose) {
        [XMIAdReporter exposeReportValidAds:@[adData]];
    }
    [XMIAdReporter exposeDspShowAds:@[adData] sdkShow:1];
    if (self.delegate && [self.delegate respondsToSelector:@selector(xmiNativeAdDidExposure:)]) {
        [self.delegate xmiNativeAdDidExposure:self];
    }
}

- (void)dspAdsNativeAdDidClick:(XMIAdRelatedData *)adData withView:(UIView *)view {
    NSDictionary *userInfo = @{};
    if (self.delegate && [self.delegate respondsToSelector:@selector(xmiNativeAdDidClick:withView:)]) {
        userInfo = [self.delegate xmiNativeAdDidClick:self withView:view];
    }
    [XMIAdReporter clickReportWithAd:adData andView:view andUserInfo:userInfo];
}

- (void)dspAdsManager:(XMINativeBaseLoader *)adsLoader nativeAdWillOpenDetailPage:(XMIAdRelatedData *)adData
{
    if ([self.delegate respondsToSelector:@selector(xmiNativeAdWillOpenDspDetailPage:)]) {
        [self.delegate xmiNativeAdWillOpenDspDetailPage:adsLoader.isVideo];
    }
}

- (void)dspAdsManager:(XMINativeBaseLoader *)adsLoader nativeAdDidCloseDetailPage:(XMIAdRelatedData *)adData
{
    if ([self.delegate respondsToSelector:@selector(xmiNativeAdDidCloseDspDetailPage:)]) {
        [self.delegate xmiNativeAdDidCloseDspDetailPage:adsLoader.isVideo];
    }
}

- (void)dspAdsNativeAdDidClickClose:(XMIAdRelatedData *)adData withView:(UIView * _Nullable)view
{
    if (self.delegate && [self.delegate respondsToSelector:@selector(xmiNativeAdDidClose:withView:)]) {
        [self.delegate xmiNativeAdDidClose:self withView:view];
    }
}


- (UIView *)customView
{
    BOOL gdtAd = _singleAdData.adtype == XMIAdTypeGDT;
    BOOL buAd = _singleAdData.adtype == XMIAdTypeBU;
    BOOL baiduAd = _singleAdData.adtype == XMIAdTypeBAIDU;
    if (gdtAd) {
        return self.gdtLoader.customView;
    } else if(buAd) {
        return self.buLoader.customView;
    } else if (baiduAd) {
        return self.baiduLoader.customView;
    }
    return nil;
}

- (BOOL)canMute
{
    return _singleAdData.adtype == XMIAdTypeGDT && [self.gdtLoader isVideo];
}

- (void)setDspMute:(BOOL)dspMute
{
    _gdtLoader.mute = dspMute;
}

- (BOOL)dspMute
{
    return _gdtLoader.mute;
}

- (BOOL)isDspDownloadAd
{
    if (_singleAdData.adtype == XMIAdTypeBU) {
        return [_buLoader isDownloadAd];;
    } else if (_singleAdData.adtype == XMIAdTypeGDT) {
        return [_gdtLoader isDownloadAd];
    }
    return NO;
}

@end
