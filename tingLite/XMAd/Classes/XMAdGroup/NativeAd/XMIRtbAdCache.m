//
//  XMIRtbAdCache.m
//  XMAdApi
//
//  Created by xiaodong2.zhang on 2025/2/21.
//

#import "XMIRtbAdCache.h"
#import <XMUIKit/xm_vsync.h>
#import <XMConfigCenter/XMConfigCenter.h>
#import "XMIAdNativeLogger.h"
#import "XMIAdRelatedData.h"
#import "XMINativeBaseLoader.h"
#import "XMIAdABTest.h"

#ifdef DEBUG
#define XMIRtbAdCacheLog(fmt, ... ) XMILogNativeInfo(@"AD_LOG_CACHE", fmt, ##__VA_ARGS__)
#else
#define XMIRtbAdCacheLog(format, ...)
#endif


#define kXMIRtbAdCacheItemBugKey @"kXMRtbAdCacheItemBugKey"

@interface XMIRtbAdCacheItem : NSObject

@property (assign, nonatomic) NSTimeInterval invaildTS;

@property (strong, nonatomic) XMINativeBaseLoader *adItem;

@end

@implementation XMIRtbAdCacheItem


@end

@interface XMIRtbAdCache()

@property (strong, nonatomic) NSMutableDictionary<NSString *, NSMutableArray<XMIRtbAdCacheItem *> *> *caches;

@property (nonatomic, assign) BOOL openCache;
@property (nonatomic, assign) NSInteger rtbCacheMaxSize;
@property (strong, nonatomic) NSDictionary<NSString *, NSNumber *> *cacheTimeConfig;


@end

@implementation XMIRtbAdCache

DEFINE_SINGLETON_METHOD(XMIRtbAdCache, sharedCache);

- (instancetype)init {
    self = [super init];
    if (self) {
        NSDictionary *config = [XMIAdABTest getJsonObjectWithKey:@"rtbCacheConfigiOS" defaultObject:@{}];
        self.openCache = [config boolMaybeForKey:@"openCache"];
        self.cacheTimeConfig =  [config dictionaryMaybeForKey:@"cacheTime"];
        
        self.rtbCacheMaxSize = [[XMConfigCenter sharedConfigCenter] getIntValueWithGroup:@"ad" andItem:@"rtbCacheMaxSizeiOS" defaultValue:5];
#if DEBUG
        if (![[NSUserDefaults standardUserDefaults] objectForKey:kXMIRtbAdCacheItemBugKey]) {
            [[NSUserDefaults standardUserDefaults] setInteger:0 forKey:kXMIRtbAdCacheItemBugKey];
        }
#endif
    }
    return self;
}

- (NSMutableDictionary<NSString *,NSMutableArray<XMIRtbAdCacheItem *> *> *)caches {
    if (!_caches) {
        _caches = [NSMutableDictionary dictionary];
    }
    return _caches;
}

- (XMINativeBaseLoader *)cacheForItem:(XMINativeBaseLoader *)adItem winPrice:(float)winPrice {
    if (!self.openCache) return nil;
    
    NSString *key = [XMIRtbAdCache cacheKeyForItem:adItem];
    if (key.length <= 0) {
        return nil;
    }
    
    NSMutableArray<XMIRtbAdCacheItem *> *cacheQueue = self.caches[key];
    if (cacheQueue.count) {
        [self removeExpiredItemsInQueue:cacheQueue];
    }
    if (!cacheQueue.count) {
        return nil;
    }
#if DEBUG
    [cacheQueue enumerateObjectsUsingBlock:^(XMIRtbAdCacheItem *item, NSUInteger idx, BOOL *stop) {
        BOOL expired = item.invaildTS <= xm_vsync_get_current_time();
        XMIRtbAdCacheLog(@"缓存 %lld(%ld, %@) price:%.2f 是否失效:%@", item.adItem.originData.adid, item.adItem.originData.adtype, item.adItem.originData.dspPositionId, item.adItem.originData.price, @(expired));
    }];
#endif
    
    // 返回最优广告（如果有的话）
    XMIRtbAdCacheItem *higherItem = cacheQueue.firstObject;
    if (higherItem.adItem.originData.price > winPrice) {
        //同步cache
        higherItem.adItem.originData.responseId = adItem.originData.responseId;
        [cacheQueue removeObject:higherItem];
        return higherItem.adItem;
    } else {
        return nil;
    }
}

- (BOOL)addCacheWithItem:(XMINativeBaseLoader *)adItem {
    if (!self.openCache) return NO;
    if (!adItem ) return NO;
    if (!adItem.originData.isMobileRtb) return NO;
    if (adItem.originData.adtype == XMIAdTypeXM) return NO;
    if (adItem.loadingStatus != XMINativeBaseLoaderLoadingStatusSuccess) return NO;
    if (adItem.originData.bidMinPrice > 0 && adItem.originData.price < adItem.originData.bidMinPrice) return NO;
    
    NSTimeInterval cacheTime = [self cacheTimeForItem:adItem];
    if (cacheTime <= 0) return NO;

    NSString *key = [XMIRtbAdCache cacheKeyForItem:adItem];
    if (key.length <= 0) return NO;
    
    // 获取缓存队列
    NSMutableArray<XMIRtbAdCacheItem *> *cacheQueue = self.caches[key];
    if (!cacheQueue) {
        cacheQueue = [NSMutableArray array];
        self.caches[key] = cacheQueue;
    }
    
    
#if DEBUG
    //DEBUG for TEST 随机加钱
    NSInteger count = [[NSUserDefaults standardUserDefaults] integerForKey:kXMIRtbAdCacheItemBugKey];
    if (count > 0) {
        adItem.originData.price += (double)(arc4random_uniform(count))/100.f;
    }
#endif
    
    // 插入前判断（当缓存已满时）
    if (cacheQueue.count >= self.rtbCacheMaxSize) {
        XMIRtbAdCacheItem *lowestItem = cacheQueue.lastObject;
        // 新广告价格不高于最低价则不插入
        if (adItem.originData.price <= lowestItem.adItem.originData.price) {
            return NO;
        }
        
        // 移除最低价元素腾出空间
        [cacheQueue removeObject:lowestItem];
        
        XMIRtbAdCacheLog(@"淘汰最低价项 %lld(%ld, %@) price:%.2f",lowestItem.adItem.originData.adid, lowestItem.adItem.originData.adtype, lowestItem.adItem.originData.dspPositionId, lowestItem.adItem.originData.price);
    }
    
    //插入新元素（按价格排序插入合适位置）
    XMIRtbAdCacheItem *newCache = [[XMIRtbAdCacheItem alloc] init];
    newCache.adItem = adItem;
    newCache.invaildTS = xm_vsync_get_current_time() + cacheTime;
    [self insertItem:newCache intoSortedQueue:cacheQueue];
    
    XMIRtbAdCacheLog(@"成功插入 %lld(%ld, %@) price:%.2f 当前缓存数:%lu", adItem.originData.adid, adItem.originData.adtype, adItem.originData.dspPositionId, adItem.originData.price, (unsigned long)cacheQueue.count);
    return YES;
}

//- (void)removeCacheOfItemIfNeeded:(XMINativeBaseLoader *)adItem {
//    if (!adItem) {
//        return;
//    }
//
//    NSString *key = [XMIRtbAdCache cacheKeyForItem:adItem];
//    if (key.length <= 0) {
//        return;
//    }
//
//    NSMutableArray<XMIRtbAdCacheItem *> *arr = self.caches[key];
//    [arr enumerateObjectsWithOptions:(NSEnumerationReverse) usingBlock:^(XMIRtbAdCacheItem * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
//        if (obj.adItem == adItem
//            || (adItem.cacheInfo.originItemInThirdpartAdCache && adItem.cacheInfo.originItemInThirdpartAdCache == obj.adItem)
//            || (adItem.cacheInfo.preRequestItem && adItem.cacheInfo.preRequestItem == obj.adItem)) {
//            [arr removeObjectAtIndex:idx];
//            *stop = YES;
//        }
//    }];
//}

- (void)checkCacheWithItem:(XMINativeBaseLoader *)adItem {
    if (!self.openCache) return;
    if (!adItem ) return;
    
    NSString *key = [XMIRtbAdCache cacheKeyForItem:adItem];
    if (key.length <= 0) return;
    
    // 获取缓存队列
    NSMutableArray<XMIRtbAdCacheItem *> *cacheQueue = self.caches[key];
    if (cacheQueue) {
        [self removeExpiredItemsInQueue:cacheQueue];
    }
}

#pragma mark - Helper Methods

- (void)removeExpiredItemsInQueue:(NSMutableArray<XMIRtbAdCacheItem *> *)queue {
    NSTimeInterval currentTS = xm_vsync_get_current_time();
    
    NSMutableIndexSet *expiredIndexes = [NSMutableIndexSet indexSet];
    [queue enumerateObjectsUsingBlock:^(XMIRtbAdCacheItem *item, NSUInteger idx, BOOL *stop) {
        if (item.invaildTS <= currentTS) {
            [expiredIndexes addIndex:idx];
        }
    }];
    
    if (expiredIndexes.count > 0) {
        [queue removeObjectsAtIndexes:expiredIndexes];
        XMIRtbAdCacheLog(@"清理过期项，移除数量：%lu", expiredIndexes.count);
    }
}

- (void)insertItem:(XMIRtbAdCacheItem *)newItem
   intoSortedQueue:(NSMutableArray<XMIRtbAdCacheItem *> *)queue {
    NSUInteger insertIndex = 0;
    for (; insertIndex < queue.count; insertIndex++) {
        if (newItem.adItem.originData.price > queue[insertIndex].adItem.originData.price) {
            break;
        }
    }
    [queue insertObject:newItem atIndex:insertIndex];
}

+ (NSString *)cacheKeyForItem:(XMINativeBaseLoader *)adItem {
    return @(adItem.originData.slotId).stringValue;
}

- (NSTimeInterval)cacheTimeForItem:(XMINativeBaseLoader *)adItem {
    NSArray<NSString *> *priorityKeys =
    @[[NSString stringWithFormat:@"%ld_%lld",adItem.originData.adtype, adItem.originData.positionId],
      [NSString stringWithFormat:@"%ld_*",adItem.originData.adtype],
      @"*"];
    
    // 按优先级顺序查找
    for (NSString *key in priorityKeys) {
        NSNumber *value = [self.cacheTimeConfig numberMaybeForKey:key];
        if (value != nil) {
            return [value integerValue];
        }
    }
    return 0;
}

@end
