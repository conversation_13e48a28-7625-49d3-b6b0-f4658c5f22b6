//
//  XMINativeAdLoader.m
//  XMAd
//
//  Created by xiaodong2.zhang on 2024/8/27.
//

#import "XMINativeAdLoader.h"
#import "XMIAdNativeLogger.h"
#import "XMIAdReporter.h"
#import "XMIAdManager.h"
#import <XMCategories/NSArray+XMCommon.h>
#import "XMINativeBULoader.h"
#import "XMINativeGDTLoader.h"
#import "XMINativeJADLoader.h"
#import "XMINativeBaiduLoader.h"
#import "XMIAdHelper.h"
#import "XMIRtbAdCache.h"
#import <XMConfigCenter/XMConfigCenter.h>

@interface XMINativeNativeLoader : XMINativeBaseLoader

@end

@implementation XMINativeNativeLoader

- (void)loadAdWithData:(XMIAdRelatedData *)originData {
    self.loadingStatus = XMINativeBaseLoaderLoadingStatusSuccess;
    self.originData = originData;
}

@end

#define XMINativeAdLog(fmt, ... ) XMILogNativeInfo(@"AD_LOG_NATIVE_RTB", fmt, ##__VA_ARGS__)

@interface XMINativeAdLoader ()<XMINativeDSPLoaderProtocol>

@property (strong, nonatomic) NSMutableArray<XMINativeBaseLoader *> *adModelQueue;

@property (strong, nonatomic) NSMutableArray<XMINativeBaseLoader *> *adModelResultList;

@property (strong, nonatomic) NSMutableArray<XMIAdRelatedData *> *adItems;

@property (nonatomic, weak) UIViewController *rootViewController;

@property (copy, nonatomic) void(^completion)(NSArray<XMINativeBaseLoader *> *results);

@property (assign, nonatomic) CGFloat timeout;

@property (assign, nonatomic) BOOL finish;

@property (assign, nonatomic) NSInteger rtbAdWaitCount;

@property (assign, nonatomic) NSInteger adTimeoutCount;

@property (nonatomic, weak) id<XMINativeDSPLoaderProtocol> delegate;

@end

@implementation XMINativeAdLoader

- (void)dealloc {
    XMINativeAdLog(@"");
}

- (void)fillAdItems:(NSArray<XMIAdRelatedData *> *)adItems
            timeout:(CGFloat)timeout
           delegate:(id<XMINativeDSPLoaderProtocol>)delegate
 rootViewController:(UIViewController *)rootViewController
         completion:(void(^)(NSArray<XMINativeBaseLoader *> *results))completion {
    if (!adItems || !completion) {
        if (completion) {
            completion(nil);
        }
        return;
    }
    self.delegate = delegate;
    self.completion = completion;
    self.adItems = [adItems mutableCopy];
    self.timeout = timeout;
    self.rootViewController = rootViewController;
    [self startFill];
}

- (instancetype)init {
    self = [super init];
    if (self != nil) {
        self.adModelQueue = [NSMutableArray array];
        self.adModelResultList = [NSMutableArray array];
    }
    return self;
}

- (void)startFill {
    XMINativeAdLog(@"当前dsp列表中- 包含  客户端竞价广告");
    [self.adItems enumerateObjectsUsingBlock:^(XMIAdRelatedData *relatedData, NSUInteger idx, BOOL * _Nonnull stop) {
        BOOL isSdkData = relatedData.dsp;
        if (!isSdkData) {
            XMINativeNativeLoader *loader = [XMINativeNativeLoader new];
            [loader loadAdWithData:relatedData];
            [self.adModelQueue addObject:loader];
            [self.adModelResultList addObject:loader];
        } else {
            [self loadSdkDataWithAdxData:relatedData];
            if (relatedData.isMobileRtb) {
                self.rtbAdWaitCount ++;
            }
        }
    }];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(self.timeout * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self finishWithTimeout:YES];
    });
}

- (void)loadSdkDataWithAdxData:(XMIAdRelatedData *)adData {
    BOOL gdtAd = adData.adtype == XMIAdTypeGDT;
    BOOL buAd = adData.adtype == XMIAdTypeBU;
    BOOL jadAd = adData.adtype == XMIAdTypeJAD;
    BOOL baiduAd = adData.adtype == XMIAdTypeBAIDU;
    [XMIAdHelper initSdkWithAdType:adData.adtype];
    XMINativeBaseLoader *loader = nil;
    if (gdtAd) {
        loader = [[XMINativeGDTLoader alloc] init];
    } else if (buAd) {
        loader = [[XMINativeBULoader alloc] init];
    } else if (jadAd) {
        loader = [[XMINativeJADLoader alloc] init];
    } else if (baiduAd) {
        loader = [[XMINativeBaiduLoader alloc] init];
    }
    if (loader) {
        loader.delegate = self;
        [loader loadAdWithData:adData];
        [self.adModelQueue addObject:loader];
    }
}

- (void)addAdModelToResultArray:(XMINativeBaseLoader *)adModel {
    if (adModel.loadingStatus == XMINativeBaseLoaderLoadingStatusSuccess) {
        if (self.finish) {
            XMINativeAdLog(@"dsp广告请求超时 【%lld(%ld, %@)】", adModel.originData.adid, adModel.originData.adtype, adModel.originData.dspPositionId);
            adModel.loadingStatus = XMINativeBaseLoaderLoadingStatusTimeout;
            self.adTimeoutCount ++;
            [self.adModelResultList addObject:adModel];
            [[XMIRtbAdCache sharedCache] checkCacheWithItem:adModel];
            [[XMIRtbAdCache sharedCache] addCacheWithItem:adModel];
            return;
        }
        XMINativeAdLog(@"dsp广告请求完成，加到比价列表 【%lld(%ld, %@)】", adModel.originData.adid, adModel.originData.adtype, adModel.originData.dspPositionId);
    } else {
        XMINativeAdLog(@"dsp广告请求失败，丢弃 【%lld(%ld, %@)】, status = %@", adModel.originData.adid, adModel.originData.adtype, adModel.originData.dspPositionId, adModel.loadingStatusString);
    }
    if (adModel.originData.isMobileRtb) {
        self.rtbAdWaitCount --;
    }
    
    [self.adModelResultList addObject:adModel];
    // 所有的SDK 客户端rtb都已经返回才能进入最终比价环节
    if (self.rtbAdWaitCount != 0) {
        XMINativeAdLog(@"还剩余 【%ld】客户端rtb广告，继续等待",self.rtbAdWaitCount);
        return;
    }
    
    for (XMINativeBaseLoader *currentItem in self.adModelQueue) {
        if (currentItem.originData.isMobileRtb) {
            continue;
        }
        if (currentItem.loadingStatus == XMINativeBaseLoaderLoadingStatusSuccess) {
            ////adx第一位固价已返回,可以进入最终比价环节
            break;
        } else if (currentItem.loadingStatus == XMINativeBaseLoaderLoadingStatusFailed)  {
            ////adx第一位固价已确认失败，查看下一位状态
            continue;
        } else if (currentItem.loadingStatus == XMINativeBaseLoaderLoadingStatusInit)  {
            XMINativeAdLog(@"还剩余【%lld(%ld,%@)】dsp固价广告加载中，继续等待",currentItem.originData.adid, currentItem.originData.adtype, currentItem.originData.dspPositionId);
            return;
        }
    }

    [self finishWithTimeout:NO];
}

- (void)finishWithTimeout:(BOOL)timeout {
    if (self.finish) {
        return;
    }
    self.finish = YES;
    [self.adModelResultList sortUsingComparator:^NSComparisonResult(XMINativeBaseLoader *adModel1, XMINativeBaseLoader* adModel2) {
        if (adModel1.originData.rankLevel < adModel2.originData.rankLevel) {
            return NSOrderedDescending;
        } else if (adModel1.originData.rankLevel == adModel2.originData.rankLevel) {
            if (adModel1.originData.price < adModel2.originData.price) {
                return NSOrderedDescending;
            }
        }
        return NSOrderedSame;
    }];
    
    XMINativeAdLog(@"开始遍历物料");
    for (XMINativeBaseLoader *printObject in self.adModelResultList) {
        XMINativeAdLog(@"adItem【%lld(%ld, %@)】, price = %.4f, rankLevel = %ld, status = %@ ", printObject.originData.adid, printObject.originData.adtype, printObject.originData.dspPositionId, printObject.originData.price, printObject.originData.rankLevel, printObject.loadingStatusString);
    }
    XMINativeAdLog(@"结束遍历物料");
    
    XMINativeBaseLoader *willShowItem = nil;
    for (XMINativeBaseLoader *tempAdModel in self.adModelResultList) {
        if (willShowItem == nil && tempAdModel.loadingStatus == XMINativeBaseLoaderLoadingStatusSuccess) {
            if (tempAdModel.originData.bidMinPrice > 0 && tempAdModel.originData.price < tempAdModel.originData.bidMinPrice) {
                //底价过滤
                XMINativeAdLog(@"dsp广告底价未满足，丢弃 【%lld(%ld, %@)】", tempAdModel.originData.adid, tempAdModel.originData.adtype, tempAdModel.originData.dspPositionId);
            } else {
                //命中
                willShowItem = tempAdModel;
                break;
            }
        }
    }
    XMINativeAdLog(@"-------  实时物料比价------- 是否超时:%@ 竞胜物料:【%lld(%ld, %@) price = %.4f】",@(timeout), willShowItem.originData.adid, willShowItem.originData.adtype, willShowItem.originData.dspPositionId, willShowItem.originData.price);
    
    //和缓存比价
    NSMutableArray *bidAdArray = [self.adModelResultList mutableCopy];
    XMINativeBaseLoader *winCacheItem = [[XMIRtbAdCache sharedCache] cacheForItem:willShowItem winPrice:willShowItem.originData.price];
    if (winCacheItem) {
        willShowItem = winCacheItem;
        willShowItem.delegate = self; //重新绑定
        willShowItem.isCache = YES;
        [bidAdArray addObject:willShowItem];
        XMINativeAdLog(@"-------  缓存物料比价竞胜------- 是否超时:%@ 最终竞胜物料:【%lld(%ld, %@) price = %.4f】",@(timeout), willShowItem.originData.adid, willShowItem.originData.adtype, willShowItem.originData.dspPositionId, willShowItem.originData.price);
    }

    if (willShowItem) {
        [self reportDSPAdClientBiddingInfoWithItem:willShowItem allItems:self.adModelResultList];
    }
    willShowItem.originData.rtbSuccess = YES;
    [self adXlogReportWithMobileRtbAds:bidAdArray timeout:timeout logtype:@"mobileRtbAds"];
    if (self.completion) {
        self.completion(willShowItem ? @[willShowItem] : @[]);
        self.completion = nil;
    }
    
    if (timeout) {
        //客户端实时竞价（包含超时）上报
        NSInteger reportTime = [XMConfigCenter.sharedConfigCenter getIntValueWithGroup:@"ad" andItem:@"mobileRtbFullAdsTime" defaultValue:10];
        if (reportTime > self.timeout && self.adModelResultList.count != self.adModelQueue.count) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)((reportTime - self.timeout) * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self reportFullAds];
            });
        }
    }
}

- (void)reportDSPAdClientBiddingInfoWithItem:(XMINativeBaseLoader *)willShowItem allItems:(NSArray *)allItems {
    NSMutableArray *lossItems = [NSMutableArray array];
    for (XMINativeBaseLoader *adItem in allItems) {
        if (adItem != willShowItem) {
            [lossItems addObject:adItem];
        }
    }
    
    float ratio = 100;
    float winPrice = willShowItem.originData.price * ratio;
    NSNumber *winNum = [NSNumber numberWithFloat:winPrice];
    // 竞胜物料上报
    [willShowItem win:winNum];
    
    // 竞败物料上报
    if (lossItems.count) {
        [[XMIRtbAdCache sharedCache] checkCacheWithItem:lossItems.firstObject];
    }
    for (XMINativeBaseLoader *lossItem in lossItems) {
        [lossItem loss:winNum];
        [[XMIRtbAdCache sharedCache] addCacheWithItem:lossItem];
    }
}

- (void)adXlogReportWithMobileRtbAds:(NSArray<XMINativeBaseLoader *> *)adModelList timeout:(BOOL)timeout logtype:(NSString *)logtype {
    XMINativeBaseLoader *firstItem = adModelList.firstObject;
    if (firstItem.originData.responseId >= 0) {
        NSMutableDictionary *dic = [NSMutableDictionary dictionary];
        dic[@"responseId"] = @(firstItem.originData.responseId);
        dic[@"positionId"] = @(firstItem.originData.positionId);
        dic[@"sdkVersion"] = [XMIAdManager sdkVersion];
        NSString *bundleReleaseVersion = [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleShortVersionString"];
        dic[@"version"] = bundleReleaseVersion ?: @"";
        
        NSMutableArray *mobileRtbList = [NSMutableArray array];
        
        for (XMINativeBaseLoader *model in adModelList) {
            NSMutableDictionary *mobileRtbDic = [NSMutableDictionary dictionary];
            mobileRtbDic[@"adid"] = [NSString stringWithFormat:@"%lld", model.originData.adid];
            mobileRtbDic[@"price"] = model.originData.priceEncrypt;
            mobileRtbDic[@"adUniqId"] = [NSString stringWithFormat:@"%lld", model.originData.adUniqId];
            
            int dspResult = -1;
            if (model.originData.dsp) {
                if (model.loadingStatus == XMINativeBaseLoaderLoadingStatusSuccess) {
                    dspResult = 1;
                } else if (model.loadingStatus == XMINativeBaseLoaderLoadingStatusFailed) {
                    dspResult = 0;
                } else {
                    dspResult = 3;
                }
            }
            mobileRtbDic[@"dspResult"] = @(dspResult);
            
            mobileRtbDic[@"rtbResult"] = model.originData.rtbSuccess ? @1 : @0;
            mobileRtbDic[@"isCache"] = model.isCache ? @1 : @0;
            mobileRtbDic[@"rtbPrice"] = model.originData.priceEncrypt;
            mobileRtbDic[@"adSource"] = [NSString stringWithFormat:@"%ld", model.originData.adtype];
            mobileRtbDic[@"slotId"] = model.originData.dspPositionId ?  : @"";
                        
            NSInteger sdkCallbackStatus = -10000;
            NSString *sdkCallbackMsg = model.originData.dsp ? @"" : @"xmly-该物料为adx物料";
            
            if (model.originData.dsp) {
                if (model.loadingStatus == XMINativeBaseLoaderLoadingStatusSuccess) {
                    sdkCallbackStatus = 10000;
                    sdkCallbackMsg = [NSString stringWithFormat:@"xmly-SDK请求成功-%@", model.originData.rtbSuccess ? @"竞胜": @"竞败"];
                } else if (model.loadingStatus == XMINativeBaseLoaderLoadingStatusFailed) {
                    sdkCallbackStatus = -1;
                    sdkCallbackMsg = @"请求失败";
                } else {
                    sdkCallbackStatus = -99999;
                    sdkCallbackMsg = @"xmly-sdk在规定时间内未返回";
                }
            }

            mobileRtbDic[@"timeCost"] = @(model.requestTimeCost);
            mobileRtbDic[@"sdkCallbackStatus"] = @(sdkCallbackStatus);
            mobileRtbDic[@"sdkCallbackMsg"] = sdkCallbackMsg;
            [mobileRtbList addObject:mobileRtbDic];
        }
        
        NSString *mobileRtbListStr = [mobileRtbList jsonString];
        dic[@"mobileRtbReportList"] = mobileRtbListStr ?: @"";
        [[XMIAdReporter sharedInstance] reportParams:dic withSubtype:logtype];
    }
}

- (void)reportFullAds {
    if (self.adTimeoutCount > 0) {
        [self adXlogReportWithMobileRtbAds:self.adModelResultList timeout:YES logtype:@"mobileRtbFullAds"];
    }
}

//MARK: - XMINativeDSPLoaderProtocol

- (void)dspAdsManagerSuccessToLoad:(XMINativeBaseLoader *)adsLoader nativeAds:(NSArray<XMIAdRelatedData *> *)adArray {
    if (![self.adItems containsObject:adArray.firstObject]) {
        return;
    }
    adsLoader.loadingStatus = XMINativeBaseLoaderLoadingStatusSuccess;
    [self addAdModelToResultArray:adsLoader];
}

- (void)dspAdsManager:(XMINativeBaseLoader *)adsLoader nativeAd:(XMIAdRelatedData *)adData didFailWithError:(NSError *)error {
    if (![self.adItems containsObject:adData]) {
        return;
    }
    adsLoader.loadingStatus = XMINativeBaseLoaderLoadingStatusFailed;
    [self addAdModelToResultArray:adsLoader];
}

- (void)dspAdsNativeAdDidExposure:(XMIAdRelatedData *)adData {
    if (self.delegate && [self.delegate respondsToSelector:@selector(dspAdsNativeAdDidExposure:)]) {
        [self.delegate dspAdsNativeAdDidExposure:adData];
    }
}

- (void)dspAdsNativeAdDidClick:(XMIAdRelatedData *)adData withView:(UIView *)view {
    if ([self.delegate respondsToSelector:@selector(dspAdsNativeAdDidClick:withView:)]) {
        [self.delegate dspAdsNativeAdDidClick:adData withView:view];
    }
}

- (void)dspAdsManager:(XMINativeBaseLoader *)adsLoader nativeAdWillOpenDetailPage:(XMIAdRelatedData *)adData {
    if ([self.delegate respondsToSelector:@selector(dspAdsManager:nativeAdWillOpenDetailPage:)]) {
        [self.delegate dspAdsManager:adsLoader nativeAdWillOpenDetailPage:adData];
    }
}

- (void)dspAdsManager:(XMINativeBaseLoader *)adsLoader nativeAdDidCloseDetailPage:(XMIAdRelatedData *)adData {
    if ([self.delegate respondsToSelector:@selector(dspAdsManager:nativeAdDidCloseDetailPage:)]) {
        [self.delegate dspAdsManager:adsLoader nativeAdDidCloseDetailPage:adData];
    }
}

- (void)dspAdsNativeAdDidClickClose:(XMIAdRelatedData *)adData withView:(UIView * _Nullable)view {
    if (self.delegate && [self.delegate respondsToSelector:@selector(dspAdsNativeAdDidClickClose:withView:)]) {
        [self.delegate dspAdsNativeAdDidClickClose:adData withView:view];
    }
}

@end
