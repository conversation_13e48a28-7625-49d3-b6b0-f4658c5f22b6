//
//  XMINativeAd.h
//  XMAd
//
//  Created by xmly on 2023/5/23.
//

#import <Foundation/Foundation.h>
@class XMINativeAdSlot, XMIAdRelatedData;
@protocol XMINativeAdDelegate;
NS_ASSUME_NONNULL_BEGIN

@interface XMINativeAd : NSObject
/**
 * @brief 广告位
 * @discussion 广告位描述信息  slot包含id,type等
 */
@property (strong, nonatomic, readwrite, nullable) XMINativeAdSlot *adSlot;

/**
 * @brief 返回数据
 * @discussion 广告加载返回的数据
 */
@property (strong, atomic, readonly, nullable) NSArray<XMIAdRelatedData *> *data;

/**
 * @brief 自渲染代理
 * @discussion 代理，里面包含了给开发者的回调事件
 */
@property (weak, nonatomic, readwrite, nullable) id <XMINativeAdDelegate> delegate;

/**
 * @brief 控制器
 * @discussion 处理事件的控制器；
 * 弹出落地页广告的控制器。必选。
 * required.
 */
@property (weak, nonatomic, readwrite) UIViewController *rootViewController;


/**
 * @brief 是否三方曝光上报
 * @discussion 在收到三方曝光回调时是否上报adRealTime
 * required.
 */
@property (assign, nonatomic, readwrite) BOOL reportThirdExpose;

/**
  @brief 初始化
  @param slot ：广告位描述信息，包括 slotID，adType...
  @return JADNativeAd
  @discussion 通过 ad slot 初始化 native ad
 */
- (instancetype)initWithSlot:(XMINativeAdSlot *)slot;

/**
 * @brief 注册视图
 * @param containerView  必选 required. container view of the native ad.
 * @param clickableViews 可选 optional. Array of views that are clickable.
 * @param closableViews 可选 optional. Array of views that are closable.
 * @discussion 注册点击视图与关闭视图
 *
 * Register clickable views in native ads view.
 */
- (void)registerContainer:(__kindof UIView *)containerView
       withClickableViews:(NSArray<__kindof UIView *> *_Nullable)clickableViews
        withClosableViews:(NSArray<__kindof UIView *> *_Nullable)closableViews;


/**
 * @brief 触发广告曝光
 * @discussion 目前仅Baidu sdk
 *
 * Register clickable views in native ads view.
 */
- (void)trackImpression:(UIView *)view;
/**
 * @brief 触发视频广告继续播放
 * @discussion 目前仅Baidu sdk
 *
 * Register clickable views in native ads view.
 */
- (void)baiduVideoReplayIfNeed;

/**
 * @brief 注销视图
 * @discussion 注销自渲染注册的点击视图与关闭视图
 *
 * Unregister ad view from the native ad.
 */
- (void)unregisterView;

/**
 * @brief 加载广告
 * @discussion 加载广告数据
 *
 * Actively request nativeAd data.
 */
- (void)loadAdDatawithCount:(NSInteger)count;
- (void)loadAdDatawithCount:(NSInteger)count extra:(NSDictionary *_Nullable)extra;
- (void)loadAdDatawithCount:(NSInteger)count extra:(NSDictionary *_Nullable)extra completion:(void(^)(BOOL success))completion;

- (nullable UIView *)customView;

- (BOOL)canMute;

- (BOOL)isDspDownloadAd;

@property (nonatomic, assign) BOOL dspMute;

//加载失败后清空数据
@property (nonatomic, assign) BOOL cleanDataAfterLoadFailed;

@end

@protocol XMINativeAdDelegate <NSObject>

@optional

/**
 * @brief 加载成功回调
 * @discussion 广告数据：加载成功
 *
 * This method is called when native ad material loaded successfully.
 */
- (void)xmiNativeAdDidLoadSuccess:(XMINativeAd *)nativeAd;

/**
 * @brief 加载失败回调
 * @param error : the reason of error
 * @discussion 广告数据：加载失败
 *
 * This method is called when native ad materia failed to load.
 */
- (void)xmiNativeAdDidLoadFailure:(XMINativeAd *)nativeAd error:(NSError *__nullable)error;

/**
 * @brief 有效曝光成功回调
 * @discussion  广告视图：有效曝光
 *
 * This method is called when native ad slot has been shown.
 */
- (void)xmiNativeAdDidExposure:(XMINativeAd *)nativeAd;

/**
 * @brief 点击事件回调
 * @discussion 广告视图：点击
 *
 * This method is called when native ad is clicked.
 */
- (NSDictionary *)xmiNativeAdDidClick:(XMINativeAd *)nativeAd withView:(UIView *_Nullable)view;

/**
 * @brief 关闭事件回调
 * @discussion 广告视图：关闭
 *
 * This method is called when native ad is closed.
 */
- (void)xmiNativeAdDidClose:(XMINativeAd *)nativeAd withView:(UIView *_Nullable)view;

/**
 * @brief 跳转页关闭回调
 * @param interactionType ： open AppStore in app or open the webpage
 * @discussion 当跳转落地页关闭时回调
 *
 * This method is called when another controller has been closed.
 */
//- (void)xmiNativeAdDidCloseOtherController:(XMINativeAd *)nativeAd
//                           interactionType:(JADInteractionType)interactionType;

@optional

/**
 * @brief 倒计时回调
 * @discussion 广告视图：开屏倒计时时间回调
 *
 * This method is called when splash native ad is delay action.
 */
- (void)xmiNativeAdForSplash:(XMINativeAd *)nativeAd countDown:(int)countDown;


- (void)xmiNativeAdWillOpenDspDetailPage:(BOOL)isVideo;

- (void)xmiNativeAdDidCloseDspDetailPage:(BOOL)isVideo;

@end


NS_ASSUME_NONNULL_END
