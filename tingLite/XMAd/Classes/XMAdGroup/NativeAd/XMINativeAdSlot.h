//
//  XMINativeAdSlot.h
//  XMAd
//
//  Created by xmly on 2023/5/23.
//

#import <Foundation/Foundation.h>
@class XMINativeSize;
NS_ASSUME_NONNULL_BEGIN

@interface XMINativeAdSlot : NSObject
/// required. 代码位ID The unique identifier of a native ad.
@property (copy, nonatomic) NSString *slotID;
// slotID 和 positionName 二选一，目前主APP使用的是 positionName
@property (copy, nonatomic) NSString *positionName;


/// required. 广告尺寸 Image size.
@property (strong, nonatomic) XMINativeSize *imgSize;
@end

NS_ASSUME_NONNULL_END
