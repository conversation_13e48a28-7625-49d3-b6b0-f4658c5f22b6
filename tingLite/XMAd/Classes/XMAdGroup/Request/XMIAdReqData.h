//
//  XMIAdReqData.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/15.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XMIAdReqBaseData : NSObject

@property (nonatomic, copy) NSString *appid;
@property (nonatomic, copy) NSString *appVersion;
@property (nonatomic, copy) NSString *sdkVersion;
@property (nonatomic, copy) NSString *xt;
/**
 系统更新时间 如"1628013495.066016"
 这个字段和t3获取到的时间实现方式完全不一样，t3为阿里的方式，这个为拼多多的方式
 */
@property (nonatomic, copy) NSString *osUpdateTime;
// 真实的idfa数据
@property (nonatomic, copy) NSString *systemIDFA;
// idafa获取的类型，参考XMQueryIDFAType
@property (nonatomic, assign) NSInteger idfaLimit;
// 微信opensdk的版本号
@property (nonatomic, copy) NSString *opensdk_ver;
// 拼多多的安装状态
@property (nonatomic, assign) BOOL pddInstallState;


@property (nonatomic, copy) NSString *deviceBirthTime;

@property (nonatomic, copy) NSString *uid;
@end

/**
 adx初始化请求 /adx/init
 */
@interface XMIAdReqInitData : XMIAdReqBaseData

//caid用

/**
 启动秒数
 */

@property (nonatomic, copy) NSString *bootTimeInSec;
/**
 运营商信息
 */

@property (nonatomic, copy) NSString *carrierInfo;

/**
 渠道id
 */
@property (nonatomic, copy) NSString *channelid;

/**
 语言 如 "zh-Hans-CN" 和language取法不同
 */
@property (nonatomic, copy) NSString *language;

/**
 物理内存容量，单位为bytes
 */
@property (nonatomic, copy) NSString *memory;

/**
 硬盘容量，单位为bytes
 */
@property (nonatomic, copy) NSString *disk;

/**
 当前时区到格林尼治时间的偏移秒数
 */
@property (nonatomic, copy) NSString *timeZone;

@property (nonatomic, copy) NSString *mnt_id;

@end

// ------------------------------------------------------------

/**
 adx广告请求 /adx/ad
 */
@interface XMIAdReqAdData : XMIAdReqBaseData

@property (nonatomic, copy) NSString *userAgent;
@property (nonatomic, copy) NSString *slotIds;
@property (nonatomic, copy) NSString *sdks;
@property (nonatomic, copy) NSString *adSdkToken;
@property (nonatomic, copy) NSString *sdkInfo;
@property (nonatomic, copy) NSDictionary *props;
@end

NS_ASSUME_NONNULL_END
