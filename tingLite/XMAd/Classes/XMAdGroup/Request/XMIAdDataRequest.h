//
//  XMIAdDataRequest.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/23.
//

#import <Foundation/Foundation.h>
#import <XMAd/XMIAdRequestProtocol.h>

NS_ASSUME_NONNULL_BEGIN

@class XMIAdRespAdData;
@class XMIAdReqAdData;

typedef void(^XMIAdDataRequestSuccessBlock)(XMIAdRespAdData * _Nullable respData);

typedef void(^XMIAdDataRequestFailBlock)(NSError *error);

@interface XMIAdDataRequest : NSObject <XMIAdRequestProtocol>

@property (nonatomic, strong) XMIAdReqAdData *reqData;

@property (nonatomic, copy) XMIAdDataRequestSuccessBlock successBlock;

@property (nonatomic, copy) XMIAdDataRequestFailBlock failBlock;

- (BOOL)isCanceled;

@end

NS_ASSUME_NONNULL_END
