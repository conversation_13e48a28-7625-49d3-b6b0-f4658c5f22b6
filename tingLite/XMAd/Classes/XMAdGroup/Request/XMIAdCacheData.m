//
//  XMIAdCacheData.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/3.
//

#import "XMIAdCacheData.h"
#import "NSObject+XMICache.h"

@implementation XMIAdCacheData

+ (NSDictionary *)xmi_cachePropertyMapper {
    return @{
        @"responseId" : XMIC_INT,
        @"adid" : XMIC_INT,
        @"createTime" : XMIC_INT,
        @"updateTime" : XMIC_INT,
        @"showstyle" : XMIC_INT,
        @"adtype" : XMIC_INT,
        @"slotId" : XMIC_INT,
        @"adno" : XMIC_INT,
        @"used" : XMIC_INT,
        @"adData" : XMIC_DATA
    };
}

@end
