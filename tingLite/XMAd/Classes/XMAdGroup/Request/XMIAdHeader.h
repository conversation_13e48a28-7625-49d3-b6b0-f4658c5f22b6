//
//  XMIAdHeader.h
//  XMAd
//  广告请求头管理
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/4.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class XMIHardwareInfo;

@interface XMIAdHeader : NSObject

/**
 经纬度
 */
@property (nonatomic, copy) NSString *NSUP;
/**
 mac地址信息
 */
@property (nonatomic, copy) NSString *XUM;

/**
 运营商
 */
@property (nonatomic, copy) NSString *cOperator;
/**
 渠道
 */
@property (nonatomic, copy) NSString *channel;

/**
 渠道
 */
@property (nonatomic, copy) NSString *netType;

@property (nonatomic, assign) NSInteger findNativeVersion;

/**
 设备型号 iPhoneX
 */
@property (nonatomic, copy) NSString *deviceModel;
/**
 IDFA
 */
@property (nonatomic, copy) NSString *idfa;
/**
 IDFA-md5
 */
@property (nonatomic, copy) NSString *md5IDFA;
/**
 设备唯一ID
 */
@property (nonatomic, copy) NSString *deviceId;
/**
 设备类型
 */
@property (nonatomic, copy) NSString *machinetype;
/**
 包名
 */
@property (nonatomic, copy) NSString *impl;
/**
 网络类型：2G/3G/4G/5G/WIFI
 */
@property (nonatomic, copy) NSString *netMode;
/**
 系统版本
 */
@property (nonatomic, copy) NSString *osversion;
/**
 分辨率
 */
@property (nonatomic, copy) NSString *resolution;
/**
 自定义UA
 */
@property (nonatomic, copy) NSString *userAgent;
/**
 系统UA
 */
@property (nonatomic, copy) NSString *systemUserAgent;
/**
 是否越狱
 */
@property (nonatomic, assign) int jailbreak;
/**
 hw.model
 */
@property (nonatomic, copy) NSString *hwmodel;
/**
 设备名md5
 */
@property (nonatomic, copy) NSString *hwname;
@property (nonatomic, copy) NSString *hwmachine;
/**
 区码 CN
 */
@property (nonatomic, copy) NSString *phoneCountryCode;
/**
 阿里因子
 */
@property (nonatomic, copy) NSString *T2;
@property (nonatomic, copy) NSString *T3;
@property (nonatomic, copy) NSString *T8;
@property (nonatomic, copy) NSString *boot_mark;
/**
 硬件信息
 */
@property (nonatomic, strong) XMIHardwareInfo *hardwareInfo;

//caid用

/**
 启动秒数
 */

@property (nonatomic, copy) NSString *bootTimeInSec;
/**
 运营商信息
 */

@property (nonatomic, copy) NSString *carrierInfo;

/**
 语言 如 "zh-Hans-CN" 和language取法不同
 */
@property (nonatomic, copy) NSString *language2;

/**
 物理内存容量，单位为bytes
 */
@property (nonatomic, copy) NSString *memory;

/**
 硬盘容量，单位为bytes
 */
@property (nonatomic, copy) NSString *disk;

/**
 当前时区到格林尼治时间的偏移秒数
 */
@property (nonatomic, copy) NSString *timeZoneInSec;

@property (nonatomic, copy) NSString *mnt_id;

+ (instancetype)sharedInstance;

/**
 广告请求需要的cookie字符串
 */
- (NSString *)cookieValue;

/**
 广告请求头
 */
- (NSDictionary *)headers;

@property (nonatomic, copy) NSString *ipv6String;

+ (NSString *)getBootTimeInSec;
+ (NSString *)getCarrierInfo;
+ (NSString *)getLanguage2;
+ (NSString *)getMemory;
+ (NSString *)getDisk;
+ (NSString *)getTimeZoneInSec;
+ (NSString *)getCountryCode;
+ (NSString *)getHWMachine;
+ (NSString *)getHWModel;
+ (NSString *)getOsUpdateTime;
+ (NSString *)getMntId;

@end


@interface XMIHardwareInfo : NSObject

/**
 时区 如8
 */
@property (nonatomic, assign) NSInteger timeZone;
/**
 设备名 如"iPhoneX"
 */
@property (nonatomic, copy) NSString *equipmentName;
/**
 总容量 如"64"
 */
@property (nonatomic, copy) NSString *hddGb;
/**
 手机区号 如86
 */
@property (nonatomic, copy) NSString *phoneCountryCode;
/**
 最近开机时间 如 "1628013495.066016"
 */
@property (nonatomic, copy) NSString *latestStartTime;
/**
 语言 如 "zh-Hans-CN"
 */
@property (nonatomic, copy) NSString *language;
/**
 cpu核心数 如"6"
 */
@property (nonatomic, copy) NSString *cpuCores;
/**
 内存 如"2"
 */
@property (nonatomic, copy) NSString *ramGb;
/**
 电量 如"1", "0.8"
 */
@property (nonatomic, copy) NSString *batteryPower;
/**
 充电状态 如"1"-充电 "0"-未充电
 */
@property (nonatomic, copy) NSString *batteryStatus;
/**
 系统更新时间 如"1628013495.066016"
 */
@property (nonatomic, copy) NSString *osUpdateTime;

/**
 更新
 将一些变化数据更新，获取实时值 如batteryStatus, batteryPower
 */
- (void)update;

+ (NSString *)fileCreateTimeWithEncodePath:(NSString *)encodeStr;



@end

NS_ASSUME_NONNULL_END
