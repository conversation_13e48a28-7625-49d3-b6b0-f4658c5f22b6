//
//  XMIAdDataCenter.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/22.
//

#import "XMIAdDataCenter.h"
#import "XMIAdDefines.h"
#import "XMIAdRequest.h"
#import "XMIAdError.h"
#import "NSObject+XMIModel.h"
#import "NSObject+XMICache.h"
#import "XMICommonUtils.h"
#import "XMIAdSlotData.h"
#import "XMIAdCacheData.h"
#import "XMIAdConfigData.h"
#import "XMIAdPreviewManager.h"
#import "XMIAdServerBiddingTool.h"
#import "XMIAdDataRequest.h"
#import "XMIAdManager.h"
#import <YYModel/YYModel.h>
#import "XMIAdHeader.h"
#import "XMIExpressAdTrackInfoMapModel.h"

@interface XMIAdDataCenter ()

/**
 第三方加载超时时间
 */
@property (nonatomic, assign) NSInteger thirdSDKTimeoutMs;
/**
 首页信息流第三方加载超时时间
 */
@property (nonatomic, assign) NSInteger homeFeedThirdSDKRequestTime;

@property (nonatomic, copy) NSDictionary *closeAdPadding;

@property (nonatomic, assign) CGFloat adWHRatio;

@property (nonatomic, assign) BOOL adAlwaysPlayVideo;

@property (nonatomic, copy) NSDictionary *closeAdNeedDialogDic;

@property (nonatomic, copy) NSDictionary *trackVentConfig;

@property (nonatomic, copy) NSDictionary *aigcCacheDealConfig;
@end

@implementation XMIAdDataCenter

const NSInteger kDefaultThirdTimeout = 2000;
const NSInteger kDefaultHomeFeedThirdTimeout = 1000;
const CGFloat kDefaultAdWHRatio = 1.5f;

+ (instancetype)sharedInstance {
    static id _sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _sharedInstance = [[self alloc] init];
        [_sharedInstance commonInit];
    });
    
    return _sharedInstance;
}

- (void)commonInit {
    self.thirdSDKTimeoutMs = 0;
    self.homeFeedThirdSDKRequestTime = 0;
    self.closeAdPadding = nil;
}

+ (void)initAdx:(void (^)(NSError * _Nullable))dataHandler {
    XMIAdReqInitData *reqData = [[XMIAdReqInitData alloc] init];
    XMIAdHeader *header = [XMIAdHeader sharedInstance];
    //caid
    reqData.bootTimeInSec = header.bootTimeInSec;
    reqData.language = header.language2;
    reqData.carrierInfo = header.carrierInfo;
    reqData.memory = header.memory;
    reqData.disk = header.disk;
    reqData.timeZone = header.timeZoneInSec;
    reqData.mnt_id = header.mnt_id;
    if ([[XMIAdManager sharedInstance].delegate respondsToSelector:@selector(managerGetChannelId)]) {
        reqData.channelid = [[XMIAdManager sharedInstance].delegate managerGetChannelId] ?:@"0";
    }

    [XMIAdRequest initRequestWithParam:reqData completionHandler:^(XMIAdRespInitData * _Nullable respData, NSError * _Nullable error) {
        if (error != nil) {
            dataHandler([XMIAdError requestErrorWithError:error]);
            return;
        }
        
        NSArray<XMIAdSlotBean *> *slotBeans = [self adSlotBeanFromResponse:respData];
        if (slotBeans != nil) {
            [self cacheAdSlotData:slotBeans];
        }
        XMIAdGlobalConfig *config = [self adGlobalConfigFromResponse:respData];
        if (config != nil) {
            [self cacheGlobalConfig:config];
        }
        
        dataHandler(nil);
    }];
}

+ (void)getFeedAdDataWithPositionName:(NSString *)positionName
                          dataHandler:(void (^)(NSArray<XMIAdRelatedData *> * _Nullable, NSError * _Nullable))dataHandler {
    long long slotId = [self getSlotIdByPositionName:positionName];
    
    NSString *adSdkToken = [self getBidSlotIdTokenByPositionName:positionName];
    
    XMIAdReqAdData *reqData = [[XMIAdReqAdData alloc] init];
    reqData.slotIds = [NSString stringWithFormat:@"%lld", slotId];
    reqData.adSdkToken = adSdkToken;
    
    [XMIAdRequest adRequestWithParam:reqData completionHandler:^(XMIAdRespAdData * _Nullable respData, NSError * _Nullable error) {
        if (error != nil) {
            dataHandler(nil, [XMIAdError requestErrorWithError:error]);
            return;
        }
        
        // 缓存ip
        [self cacheClientIp:respData.clientIp];
        
        NSArray<XMIAdRelatedData *> *adDatas = [self adDataFromResponse:respData];
        if (adDatas == nil) {
            NSError *error = [XMIAdError emptyDataError];
            dataHandler(nil, error);
            return;
        }
        // 缓存数据
        [self clearFeedCache:slotId];
        [self cacheAdData:adDatas];
        dataHandler(adDatas, nil);
    }];
}

+ (id<XMIAdRequestProtocol>)adDataRequestWithPositionName:(NSString *)positionName props:(NSDictionary *)props dataHandler:(void (^)(NSArray<XMIAdRelatedData *> * _Nullable, NSError * _Nullable))dataHandler {
    long long slotId = [self getSlotIdByPositionName:positionName];
    NSString *adSdkToken = [self getBidSlotIdTokenByPositionName:positionName];
    
    NSString *sdkInfo = [self getBidSlotIdInfoByPositionName:positionName];
    
    XMIAdReqAdData *reqData = [[XMIAdReqAdData alloc] init];
    reqData.slotIds = [NSString stringWithFormat:@"%lld", slotId];
    reqData.adSdkToken = adSdkToken;
    reqData.sdkInfo = sdkInfo;
    reqData.props = props;
    XMIAdDataRequest *request = [[XMIAdDataRequest alloc] init];
    request.reqData = reqData;
    request.successBlock = ^(XMIAdRespAdData * _Nullable respData) {
        // 缓存ip
        [self cacheClientIp:respData.clientIp];
        NSArray<XMIAdRelatedData *> *adDatas = [self adDataFromResponse:respData];
        if (adDatas == nil) {
            NSError *error = [XMIAdError emptyDataError];
            dataHandler(nil, error);
            return;
        }
        // 缓存数据
        [self clearFeedCache:slotId];
        [self cacheAdData:adDatas];
        dataHandler(adDatas, nil);
    };
    request.failBlock = ^(NSError * _Nonnull error) {
        dataHandler(nil, error);
    };
    return request;
}


+ (NSArray<XMIAdRelatedData *> *)getShopWindowAdData:(long long)responseId withSlotId:(long long)slotId {
    NSString *condition = [NSString stringWithFormat:@"responseId = %lld AND slotId = %lld AND showstyle = %lld AND adtype = %lld", responseId, slotId, (long long)XMIAdStyleHomeShopWindow, (long long)XMIAdTypeXM];
    NSArray<XMIAdCacheData *> *cacheDataArray = [XMIAdCacheData xmi_selectByCondition:condition];
    NSMutableArray *resultArray = [NSMutableArray array];
    for (XMIAdCacheData *cData in cacheDataArray) {
        XMIAdRelatedData *rData = [self adRelatedDataFromCacheData:cData];
        if (rData != nil) {
            [resultArray addObject:rData];
        }
    }
    return resultArray;
}

+ (XMIAdRelatedData *)getEmptyFeedAdData {
    XMIAdRelatedData *adData = [[XMIAdRelatedData alloc] init];
    adData.positionName = XMI_ADP_FEED;
    adData.positionId = [self getSlotIdByPositionName:XMI_ADP_FEED];
    adData.adid = XMI_VIRTUAL_ADID;
    adData.slotId = [self getSlotIdByPositionName:XMI_ADP_FEED];
    
    return adData;
}

+ (NSInteger)getThirdTimeoutMS {
    NSInteger timeout = [XMIAdDataCenter sharedInstance].thirdSDKTimeoutMs;
    if (timeout <= 0) {
        timeout = [XMIAdConfigData intConfigForKey:XMI_CONFIG_THIRD_TIMEOUT];
        if (timeout <= 0) {
            timeout = kDefaultThirdTimeout;
        } else {
            [XMIAdDataCenter sharedInstance].thirdSDKTimeoutMs = timeout;
        }
    }
    
    return timeout;
}

+ (NSInteger)getHomeFeedThirdTimeoutMS {
    NSInteger timeout = [XMIAdDataCenter sharedInstance].homeFeedThirdSDKRequestTime;
    if (timeout <= 0) {
        timeout = [XMIAdConfigData intConfigForKey:XMI_CONFIG_FEED_THIRD_TIMEOUT];
        if (timeout <= 0) {
            timeout = kDefaultHomeFeedThirdTimeout;
        } else {
            [XMIAdDataCenter sharedInstance].homeFeedThirdSDKRequestTime = timeout;
        }
    }
    
    return timeout;
}

+ (NSValue *)closeAreaPadding:(NSString *)positionId
{
    NSValue *padding = nil;
    NSDictionary *dic = [XMIAdDataCenter sharedInstance].closeAdPadding;
    if (!dic) {
        dic =[XMIAdConfigData dictionaryConfigForKey:XMI_CONFIG_CLOSE_AD_PADDING];
        [XMIAdDataCenter sharedInstance].closeAdPadding = dic;
    }
   
    if (dic) {
        NSDictionary *paddingDic = [dic objectForKey:positionId];
        if (paddingDic && [paddingDic isKindOfClass:[NSDictionary class]]) {
            CGFloat top = [[paddingDic objectForKey:@"top"] doubleValue];
            CGFloat left = [[paddingDic objectForKey:@"left"] doubleValue];
            CGFloat bottom = [[paddingDic objectForKey:@"bottom"] doubleValue];
            CGFloat right = [[paddingDic objectForKey:@"right"] doubleValue];
            padding = [NSValue valueWithUIEdgeInsets: UIEdgeInsetsMake(top, left, bottom, right)];
        }
    }
    return padding;
}

+ (CGFloat)getAdWidthHeightRatio
{
    CGFloat ratio = [XMIAdDataCenter sharedInstance].adWHRatio;
    if (ratio <= 0) {
        ratio = [XMIAdConfigData doubleConfigForKey:XMI_CONFIG_AH_WH_RATIO];
        if (ratio <= 0) {
            ratio = kDefaultAdWHRatio;
        } else {
            [XMIAdDataCenter sharedInstance].adWHRatio = ratio;
        }
    }
    
    return ratio;
}

/**
 广告视频在任何情况下都自动播放（不考虑缓存及4g）
 */
+ (BOOL)getAdAlwaysPlayVideoValue {
    BOOL adAlwaysPlayVideo = [XMIAdDataCenter sharedInstance].adAlwaysPlayVideo;
    if (!adAlwaysPlayVideo) {
        adAlwaysPlayVideo = [XMIAdConfigData boolConfigForKey:XMI_CONFIG_ALWAYS_PLAY_VIDEO];
        [XMIAdDataCenter sharedInstance].adAlwaysPlayVideo = adAlwaysPlayVideo;
    }
    return adAlwaysPlayVideo;
}

+ (BOOL)getCloseAdNeedDialogByPositionId:(NSString *)positionId {
    NSDictionary *closeAdNeedDialogDic = [XMIAdDataCenter sharedInstance].closeAdNeedDialogDic;
    if (!closeAdNeedDialogDic) {
        closeAdNeedDialogDic =[XMIAdConfigData dictionaryConfigForKey:XMI_CLOSE_AD_NEED_DIALOG_BY_POSITIONID];
        [XMIAdDataCenter sharedInstance].closeAdNeedDialogDic = closeAdNeedDialogDic;
    }
    
    if (closeAdNeedDialogDic) {
        id needDialog = [closeAdNeedDialogDic valueForKey:positionId];
        if (needDialog) {
            return [needDialog boolValue];
        }
    }
    
    return YES;
}


// ---------------------------- internal methods --------------------------------------
/**
 初始化数据返回全局配置
 */
+ (XMIAdGlobalConfig *)adGlobalConfigFromResponse:(XMIAdRespInitData *)respData {
    if (respData == nil) {
        return nil;
    }
    if (respData.globalConfig == nil) {
        return nil;
    }
    return respData.globalConfig;
}

/**
 初始化数据返回转slotBean
 */
+ (NSArray<XMIAdSlotBean *> *)adSlotBeanFromResponse:(XMIAdRespInitData *)respData {
    if (respData == nil) {
        return nil;
    }
    if (respData.slots.count < 1) {
        return nil;
    }
    return respData.slots;
}

/**
 广告数据返回转relatedData
 */
+ (NSArray<XMIAdRelatedData *> *)adDataFromResponse:(XMIAdRespAdData *)respData {
    if (respData == nil) {
        return nil;
    }
    if (respData.slotAds.count < 1) {
        return nil;
    }
    XMIAdSlotAdBean *slotAdBean = respData.slotAds.firstObject;
    if (slotAdBean.ads.count < 1) {
        return nil;
    }
    
    long long responseId = respData.responseId;
    long long slotId = respData.slotAds.firstObject.slotId;
    long long positionId = respData.slotAds.firstObject.positionId;
    BOOL clickReportFlag = respData.clickReportFlag;
    NSString *positionName = respData.slotAds.firstObject.positionName;
    NSMutableArray *relatedDataArray = [[NSMutableArray alloc] init];
    for (int i = 0; i < slotAdBean.ads.count; i++) {
        XMIAdBean *bean = slotAdBean.ads[i];
        XMIAdRelatedData *rData = [self relatedDataFromAdBean:bean];
        rData.responseId = responseId;
        rData.slotId = slotId;
        rData.positionId = positionId;
        rData.positionName = positionName;
        rData.adno = i;
        NSMutableArray *compositeAds = [[NSMutableArray alloc] init];
        for (int j = 0; j < bean.compositeAds.count; j++) {
            XMIAdRelatedData *subData = [self relatedDataFromAdBean:bean.compositeAds[j]];
            subData.responseId = responseId;
            subData.slotId = slotId;
            subData.positionId = positionId;
            subData.positionName = positionName;
            subData.frames_num = j;
            [compositeAds addObject:subData];
        }
        rData.compositeAds = compositeAds;
        rData.currentCompositeAds = [compositeAds mutableCopy];
        NSArray *positionIds = [[[XMIAdConfigData stringConfigForKey:XMI_CONFIG_AD_PREVIEW_CLICK_REPORT_IDS] stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]] componentsSeparatedByString:@","];
        if (!positionIds) {
            positionIds = @[@"28", @"293"];
        }
        if (clickReportFlag && [positionIds containsObject:[NSString stringWithFormat:@"%lld", positionId]]) {// 首页大图支持上报
            rData.isAdPreview = NO;
        }
        [relatedDataArray addObject:rData];
    }
    
    return relatedDataArray;
}
/**
 模型转换 请求原始数据转XMIAdRelatedData
 */
+ (XMIAdRelatedData *)relatedDataFromAdBean:(XMIAdBean *)adBean {
    XMIAdRelatedData *rData = [[XMIAdRelatedData alloc] init];
    rData.name = adBean.name;
    rData.adDescription = adBean.adDescription;
    rData.adMark = adBean.adMark;
    rData.darkAdMark = adBean.darkAdMark;
    rData.adMarkStyle = adBean.adMarkStyle;
    rData.buttonText = adBean.buttonText;
    rData.cover = adBean.cover;
    // 优先取videoCover
    if (adBean.videoCover.length) {
        rData.videoUrl = adBean.videoCover;
    } else if (adBean.videoUrl.length) {
        rData.videoUrl = adBean.videoUrl;
    }
    rData.videoCover = adBean.videoCover;
    rData.showstyle = adBean.showstyle;
    rData.clickType = adBean.clickType;
    rData.realLink = adBean.realLink;
    rData.linkType = adBean.linkType;
    rData.openlinkType = adBean.openlinkType;
    rData.dpRealLink = adBean.dpRealLink;
    rData.bidMinPriceEncrypt = adBean.bidMinPrice;
    rData.priceEncrypt = adBean.priceEncrypt;
    rData.isMobileRtb = adBean.isMobileRtb;
    rData.price = adBean.price;
    rData.rankLevel = adBean.rankLevel;
    rData.enableContinuePlay = adBean.enableContinuePlay;
    rData.enableVideoJoinAppStore = adBean.enableVideoJoinAppStore;
    rData.adid = adBean.adid;
    rData.adUniqId = adBean.adUniqId;
    rData.adtype = adBean.adtype;
    rData.dsp = adBean.dsp;
    rData.dspPositionId = adBean.dspPositionId;
    rData.slotRealBid = adBean.slotRealBid;
    rData.slotAdm = adBean.slotAdm;
    rData.positionId = adBean.positionId;
    rData.thirdClickStatUrls = [adBean.thirdClickStatUrls copy];
    rData.abPassThroughParams = adBean.abPassThroughParams;
    rData.thirdShowStatUrls = [adBean.thirdShowStatUrls copy];
    rData.composite = adBean.composite;
    rData.thirdStatUrl = adBean.thirdStatUrl;
    rData.isTrueExposure = adBean.isTrueExposure;
    rData.showTokenEnable = adBean.showTokenEnable;
    rData.showTokens = [adBean.showTokens copy];
    rData.clickTokenEnable = adBean.clickTokenEnable;
    rData.clickTokens = [adBean.clickTokens copy];
    rData.link = adBean.link;
    rData.commonReportMap = adBean.commonReportMap;
    rData.bucketIds = adBean.bucketIds;
    rData.recSrc = adBean.recSrc;
    rData.recTrack = adBean.recTrack;
    rData.adpr = adBean.adpr;
    rData.downloadAppName = adBean.downloadAppName;
    rData.wxMiniProgramId = adBean.wxMiniProgramId;
    rData.businessExtraInfo = adBean.businessExtraInfo;
    rData.clickTitle = adBean.clickTitle;
    rData.adUserType = adBean.adUserType;
    rData.clickReportFlag = adBean.clickReportFlag;
    rData.isAdPreview = adBean.isAdPreview;
    rData.inScreenSource = adBean.inScreenSource;
    rData.materialProvideSource = adBean.materialProvideSource;
    rData.planId = adBean.planId;
    rData.adShowTime = adBean.adShowTime;
    rData.adIntervalTime = adBean.adIntervalTime;
    rData.clickableAreaType = adBean.clickableAreaType;
    rData.enableAnchorRec = adBean.enableAnchorRec;
    rData.trackInfoMap = adBean.trackInfoMap;
    if (adBean.trackInfoMap.allKeys.count) {
        XMIExpressAdTrackInfoMapModel *trackInfoModel = [XMIExpressAdTrackInfoMapModel xmi_modelWithJSON:adBean.trackInfoMap];
        rData.trackInfoMapModel = trackInfoModel;
    }
    rData.promoteTrackId = adBean.promoteTrackId;
    rData.clickJumpType = adBean.clickJumpType;
    rData.jumpTrackId = adBean.jumpTrackId;
    rData.autoJumpTime = adBean.autoJumpTime;
    rData.contentAdTitle = adBean.contentAdTitle;
    rData.popReminderStyle = adBean.popReminderStyle;
    rData.popReminderText = adBean.popReminderText;
    rData.soundAggType = adBean.soundAggType;
    rData.tags = adBean.tagArray;
    rData.adCacheExpireTime = adBean.adCacheExpireTime;
    rData.videoDuration = adBean.videoDuration;
    rData.unlockTime = adBean.unlockTime;
    rData.iconUrl = adBean.iconUrl;
    rData.providerName = adBean.providerName;
    rData.providerAvatar = adBean.providerAvatar;
    rData.dynamicCover = adBean.dynamicCover;
    rData.dynamicImage = adBean.dynamicImage;
    rData.hightingDynamicPicUrl = adBean.hightingDynamicPicUrl;
    rData.backupCover = adBean.backupCover;
    rData.videoDurationTime = adBean.videoDurationTime;
    rData.enableShake = adBean.enableShake;
    rData.videoAutoJump = adBean.videoAutoJump;
    rData.adTextMark = adBean.adTextMark;
    rData.ubtReportMap = adBean.ubtReportMap;
    rData.clickRequestMethod = adBean.clickRequestMethod;
    rData.clickPostMap = adBean.clickPostMap;
    rData.oneClickLeaveCash = adBean.oneClickLeaveCash;
    rData.internalCirculationFlag = adBean.internalCirculationFlag;
    rData.internalHomeGuessIndex = adBean.internalHomeGuessIndex;
    rData.actualStayTime = adBean.actualStayTime;
    rData.tipStayTime = adBean.tipStayTime;
    rData.dpRetrySecond = adBean.dpRetrySecond;
    rData.trackPreviewUrl = adBean.trackPreviewUrl;
    rData.preLoadH5LandingPage = adBean.preLoadH5LandingPage;
    rData.landVideoUrl = adBean.landVideoUrl;
    rData.downloadAppLogo = adBean.downloadAppLogo;
    rData.autoShowWebTime = adBean.autoShowWebTime;
    rData.volume = adBean.volume;
    rData.againPopupType = adBean.againPopupType;
    rData.againDuration = adBean.againDuration;
    rData.homePicTitleDown = adBean.homePicTitleDown;
    rData.needDedupClick = adBean.needDedupClick;
    rData.businessReportClickTime = adBean.reportClickTime;
    return rData;
}

/**
 对比init获取到的对应广告位的bidSlotList与db中存储的是否一致，不一致需要更新db
 */
+ (BOOL)compareOldSlotData:(XMIAdSlotData *)oldSlotData slotBean:(XMIAdSlotBean *)bean {
    BOOL oldBidSlotIsEmpty = NO;
    NSString *oldBidSlotString = oldSlotData.bidSlotList;
    NSString *beanBidSlotString = [bean.bidSlotList xmi_modelToJSONString];
    if ([oldBidSlotString isEqual:[NSNull null]]
        || oldBidSlotString == nil) {
        oldBidSlotIsEmpty = YES;
    }
    
    BOOL beanBidSlotIsEmpty = NO;
    if ([beanBidSlotString isEqual:[NSNull null]]
        || beanBidSlotString == nil) {
        beanBidSlotIsEmpty = YES;
    }
    
    if (oldBidSlotIsEmpty && beanBidSlotIsEmpty) {
        return NO;
    }
    
    if ([oldBidSlotString isEqual:beanBidSlotString]) {
        return NO;
    }
    
    return YES;
}

/**
 缓存广告位数据
 */
+ (void)cacheAdSlotData:(NSArray<XMIAdSlotBean *> *)slotBeans {
    for (XMIAdSlotBean *bean in slotBeans) {
        NSString *condition = [NSString stringWithFormat:@"positionName = '%@'", bean.positionName];
        XMIAdSlotData *oldSlotData = [XMIAdSlotData xmi_selectOneByCondition:condition];
        if (oldSlotData != nil) {
            // 实时竞价bidSlot字段是否需更新
            BOOL bidSlotNeedUpdate = [self compareOldSlotData:oldSlotData slotBean:bean];
            // 值有变化才需要更新
            if (oldSlotData.slotId != bean.slotId
                || oldSlotData.positionId != bean.positionId
                || bidSlotNeedUpdate) {
                oldSlotData.slotId = bean.slotId;
                oldSlotData.positionId = bean.positionId;
                oldSlotData.updateTime = [XMICommonUtils currentTimestamp];
                oldSlotData.bidSlotList = [bean.bidSlotList xmi_modelToJSONString] ? : @"";
                [oldSlotData xmi_updateByCondition:condition];
            }
        }
        // save
        else {
            XMIAdSlotData *slotData = [self slotDataFromSlotBean:bean];
            [slotData xmi_save];
        }
    }
}
+ (XMIAdSlotData *)slotDataFromSlotBean:(XMIAdSlotBean *)slotBean {
    XMIAdSlotData *slotData = [[XMIAdSlotData alloc] init];
    slotData.slotId = slotBean.slotId;
    slotData.positionId = slotBean.positionId;
    slotData.positionName = slotBean.positionName;
    long long time = [XMICommonUtils currentTimestamp];
    slotData.createTime = time;
    slotData.updateTime = time;
    slotData.bidSlotList = [slotBean.bidSlotList xmi_modelToJSONString] ? : @"";
    
    return slotData;
}

+ (long long)getSlotIdByPositionName:(NSString *)positionName {
    XMIAdSlotData *slotData = [XMIAdSlotData xmi_selectOneByCondition:[NSString stringWithFormat:@"positionName = '%@'", positionName]];
    if (slotData == nil) {
        return [XMIAdSlotData defaultSlotIdForPosition:positionName];
    }
    
    return slotData.slotId;
}

/**
 基于positionName获取实时竞价token
 */
+ (NSString *)getBidSlotIdTokenByPositionName:(NSString *)positionName {
    XMIAdSlotData *slotData = [XMIAdSlotData xmi_selectOneByCondition:[NSString stringWithFormat:@"positionName = '%@'", positionName]];
    NSString *adSdkToken = nil;
    if (slotData) {
        adSdkToken = [XMIAdServerBiddingTool getBiddingTokenWithPositionName:positionName slotData:slotData];
    }
    return adSdkToken;
}

+ (NSString *)getBidSlotIdInfoByPositionName:(NSString *)positionName {
    XMIAdSlotData *slotData = [XMIAdSlotData xmi_selectOneByCondition:[NSString stringWithFormat:@"positionName = '%@'", positionName]];
    NSString *adSdkToken = nil;
    if (slotData) {
        adSdkToken = [XMIAdServerBiddingTool getBiddingInfoWithPositionName:positionName slotData:slotData];
    }
    return adSdkToken;
}

/**
 缓存广告数据
 */
+ (void)cacheAdData:(NSArray<XMIAdRelatedData *> *)adDatas {
    for (XMIAdRelatedData *rData in adDatas) {
        XMIAdCacheData *cData = [self adCacheDataFromRelatedData:rData];
        cData.used = 0;
        [cData xmi_save];
    }
}
+ (XMIAdCacheData *)adCacheDataFromRelatedData:(XMIAdRelatedData *)rData {
    XMIAdCacheData *cData = [[XMIAdCacheData alloc] init];
    cData.adno = rData.adno;
    cData.responseId = rData.responseId;
    cData.adid = rData.adid;
    cData.showstyle = rData.showstyle;
    cData.adtype = rData.adtype;
    cData.slotId = rData.slotId;
    long long time = [XMICommonUtils currentTimestamp];
    cData.createTime = time;
    cData.updateTime = time;
    cData.adData = [rData xmi_modelToJSONData];
    
    return cData;
}
+ (XMIAdRelatedData *)adRelatedDataFromCacheData:(XMIAdCacheData *)cData {
    return [XMIAdRelatedData xmi_modelWithJSON:cData.adData];
}

+ (void)cacheGlobalConfig:(XMIAdGlobalConfig *)config {
    [XMIAdConfigData updateConfig:@(config.thirdSDKTimeoutMs) forKey:XMI_CONFIG_THIRD_TIMEOUT];
    [XMIAdConfigData updateConfig:@(config.homeFeedThirdSDKRequestTime) forKey:XMI_CONFIG_FEED_THIRD_TIMEOUT];
    [XMIAdConfigData updateConfig:config.closeAdPadding forKey:XMI_CONFIG_CLOSE_AD_PADDING];
    [XMIAdConfigData updateConfig:@(config.AdWidthHeightRatio) forKey:XMI_CONFIG_AH_WH_RATIO];
    [XMIAdConfigData updateConfig:@(config.adAlwaysPlayVideo) forKey:XMI_CONFIG_ALWAYS_PLAY_VIDEO];
    [XMIAdConfigData updateConfig:config.closeAdNeedDialogByPositionId forKey:XMI_CLOSE_AD_NEED_DIALOG_BY_POSITIONID];
    [XMIAdConfigData updateConfig:@(config.adRequestZipRate) forKey:XMI_CONFIG_AD_REQUEST_ZIP_RATE];
    [XMIAdConfigData updateConfig:@(config.enable_report_dsp) forKey:XMI_CONFIG_AD_ENABLE_REPORT_DSP];
    [XMIAdConfigData updateConfig:config.enable_report_dsp_rate forKey:XMI_CONFIG_AD_ENABLE_REPORT_DSP_RATE];
    [XMIAdConfigData updateConfig:@(config.enable_report_dsp_download) forKey:XMI_CONFIG_AD_ENABLE_REPORT_DSP_DOWNLOAD];
    [XMIAdConfigData updateConfig:config.trackVentConfig forKey:XMI_CONFIG_AD_TRACK_VENT_CONFIG];
    [XMIAdConfigData updateConfig:@(config.rewardSkipTime) forKey:XMI_CONFIG_AD_REWARD_SKIP_TIME];
    [XMIAdConfigData updateConfig:@(config.rewardAdAutoJump) forKey:XMI_CONFIG_AD_REWARD_AUTO_JUMP];
    [XMIAdConfigData updateConfig:@(config.requestUseHttpsiOS) forKey:XMI_CONFIG_AD_USE_HTTPS];
    [XMIAdConfigData updateConfig:@(config.fullVideoPageAutoOpenTime) forKey:XMI_CONFIG_AD_FULL_VIDEO_PAGE_AUTO_OPEN_TIME];
    [XMIAdConfigData updateConfig:@(config.webTopToastShowTime) forKey:XMI_CONFIG_AD_WEB_TOP_TOAST_SHOWTIME];
    [XMIAdConfigData updateConfig:config.abLabConfig forKey:XMI_CONFIG_AD_AB_LAB_CONFIG];
    [XMIAdConfigData updateConfig:config.clientFrequency forKey:XMI_CONFIG_AD_CLIENT_FREQUENCY];
    [XMIAdConfigData updateConfig:@(config.useNewRtbSplashV1_iOS) forKey:XMI_CONFIG_AD_USE_NEW_RTB_SPLASH_IOS];
    [XMIAdConfigData updateConfig:config.adCommonShakeTips forKey:XMI_CONFIG_AD_COMMON_SHAKE_TIPS];
    [XMIAdConfigData updateConfig:@(config.useUserInfoAllow) forKey:XMI_CONFIG_AD_USER_INFO_ALLOW];
    [XMIAdConfigData updateConfig:@(config.loadingFrequency) forKey:XMI_CONFIG_AD_HOT_LAUNCH_INTERVAL];
    [XMIAdConfigData updateConfig:@(config.aigc_cache_time) forKey:XMI_CONFIG_AD_AIGC_CACHE_VALID_INTERVAL];
    [XMIAdConfigData updateConfig:config.aigcCacheDealConfig forKey:XMI_CONFIG_AD_AIGC_CACHE_DEAL_CONFIG];
    [XMIAdConfigData updateConfig:@(config.dedupClickInterval) forKey:XMI_CONFIG_AD_DEDUP_CLICK_INTERVAL];
    [XMIAdConfigData updateConfig:config.previewClickReportIds forKey:XMI_CONFIG_AD_PREVIEW_CLICK_REPORT_IDS];
}

+ (void)cacheClientIp:(NSString *)clientIp {
    if (clientIp == nil || clientIp.length < 1) {
        return;
    }
    
    [XMIAdConfigData updateConfig:clientIp forKey:XMI_CONFIG_CLIENT_IP];
}

/**
 清理feed流缓存
 */
+ (void)clearFeedCache:(long long)slotId {
    NSString *condition = [NSString stringWithFormat:@"slotId = %lld", slotId];
    [XMIAdCacheData xmi_deleteByCondition:condition];
}

// -----------------------------------------------------------------------------------------------------------------

+ (BOOL)shouldCompressHeader
{
    if ([XMIAdManager sharedInstance].environment != XMIAdEnvironmentDefault) {
        return NO;
    }
    NSInteger rate = [XMIAdConfigData intConfigForKey:XMI_CONFIG_AD_REQUEST_ZIP_RATE];
    if (rate <= 0) {
        return NO;
    }
    static id randomObj = nil;
    if (!randomObj) {
        randomObj = [[NSUserDefaults standardUserDefaults] objectForKey:@"ad_request_zip_random"];
        if (!randomObj) {
            randomObj = @(arc4random() % 1000);
            [[NSUserDefaults standardUserDefaults] setObject:randomObj forKey:@"ad_request_zip_random"];
        }
       
    }
    return [randomObj integerValue] < rate;
}

+ (BOOL)shouldReportDSPSDK
{
    BOOL reportDSP =  [XMIAdConfigData boolConfigForKey:XMI_CONFIG_AD_ENABLE_REPORT_DSP] && [XMIAdConfigData boolConfigForKey:XMI_CONFIG_AD_ENABLE_REPORT_DSP_DOWNLOAD];
    if (!reportDSP) {
        return NO;
    }
    NSDictionary *rates = [XMIAdConfigData dictionaryConfigForKey:XMI_CONFIG_AD_ENABLE_REPORT_DSP_RATE];
    NSInteger rate =  [[rates objectForKey:[XMICommonUtils bundleId]] integerValue];
    if (rate <= 0) {
        return NO;
    }
    static id randomObj = nil;
    if (!randomObj) {
        randomObj = [[NSUserDefaults standardUserDefaults] objectForKey:@"enable_report_dsp_rate"];
        if (!randomObj) {
            randomObj = @(arc4random() % 1000);
            [[NSUserDefaults standardUserDefaults] setObject:randomObj forKey:@"enable_report_dsp_rate"];
        }
       
    }
    if ([randomObj integerValue] >= rate) {
        return NO;
    }
    return YES;
}

+ (NSDictionary *)trackVentConfig {
    NSDictionary *dic = [XMIAdDataCenter sharedInstance].trackVentConfig;
    if (!dic) {
        NSData *jsonData = [XMIAdConfigData dataConfigForKey:XMI_CONFIG_AD_TRACK_VENT_CONFIG];
        NSError *error = nil;
        if (jsonData) {
            dic = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&error];
        }
        if (error || ![dic isKindOfClass:[NSDictionary class]]) {
            return dic;
        }
        [XMIAdDataCenter sharedInstance].trackVentConfig = dic;
    }
   
    return dic;
}

+ (NSDictionary *)aigcCacheDealConfig {
    NSDictionary *dic = [XMIAdDataCenter sharedInstance].aigcCacheDealConfig;
    if (!dic) {
        NSData *jsonData = [XMIAdConfigData dataConfigForKey:XMI_CONFIG_AD_AIGC_CACHE_DEAL_CONFIG];
        NSError *error = nil;
        if (jsonData) {
            dic = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&error];
        }
        if (error || ![dic isKindOfClass:[NSDictionary class]]) {
            return dic;
        }
        [XMIAdDataCenter sharedInstance].aigcCacheDealConfig = dic;
    }
   
    return dic;
}

+ (NSTimeInterval)rewardVideoSKipTime
{
    return [XMIAdConfigData doubleConfigForKey:XMI_CONFIG_AD_REWARD_SKIP_TIME];
}

+ (BOOL)rewardAutoJump
{
    return [XMIAdConfigData boolConfigForKey:XMI_CONFIG_AD_REWARD_AUTO_JUMP];
}

+ (BOOL)requestUseHttps
{
    static BOOL useHttps = NO;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        useHttps = [XMIAdConfigData boolConfigForKey:XMI_CONFIG_AD_USE_HTTPS];
    });
    return useHttps;
}

+ (NSTimeInterval)fullVideoWebPageAutoOpenTime
{
    return [XMIAdConfigData doubleConfigForKey:XMI_CONFIG_AD_FULL_VIDEO_PAGE_AUTO_OPEN_TIME] / 1000;
}

+ (NSTimeInterval)webTopToastShowTime
{
    NSTimeInterval time = [XMIAdConfigData doubleConfigForKey:XMI_CONFIG_AD_WEB_TOP_TOAST_SHOWTIME];
    return time > 0 ? time : 3.0f;
}

+ (NSString *)adCommonShakeTips
{
    NSString *tip = [XMIAdConfigData stringConfigForKey:XMI_CONFIG_AD_COMMON_SHAKE_TIPS];
    return tip.length > 0 ? tip : @"摇一摇解锁精彩内容";
}

+ (NSInteger)dedupClickInterval
{
    return [XMIAdConfigData longLongConfigForKey:XMI_CONFIG_AD_DEDUP_CLICK_INTERVAL];
}


@end
