//
//  XMIAdRequest.m
//  XMAd
//  封装adx相关请求
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/5.
//

#import "XMIAdRequest.h"
#import "XMINetManager.h"
#import "XMINetDataTask.h"
#import "NSObject+XMIModel.h"
#import "XMIAdMacro.h"
#import "XMIAdManager.h"
#import "XMIAdHeader.h"
#import "XMIAdReqData.h"
#import "XMICommonUtils.h"
#import "XMIAdDataCenter.h"
#import <XMAd/XMIAdABTest.h>

#define kAdServerBaseUrl @"http://adse.ximalaya.com"
#define kAdServerBaseUrlTest @"http://adse.test.ximalaya.com"
#define kAdServerBaseUrlUAT @"http://adse.uat.ximalaya.com"
#define kAdURIAdxInit @"/adx/init"
#define kAdURIAdxAd @"/adx/ad"
#define kAdURINonce @"/nonce"

@implementation XMIAdRequest

+ (void)initRequestWithParam:(XMIAdReqInitData *)reqData completionHandler:(void (^)(XMIAdRespInitData * _Nullable, NSError * _Nullable))completionHandler {
    NSMutableURLRequest *request = [self requestWithUri:kAdURIAdxInit parameters:reqData];
    XMINetDataTask *task = [XMINetManager dataTaskWithRequest:request completionHandler:^(NSURLResponse * _Nonnull response, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error != nil) {
            completionHandler(nil, error);
            return;
        }
        
        XMIAdRespInitData *respData = [XMIAdRespInitData xmi_modelWithJSON:responseObject];
        completionHandler(respData, error);
    }];
    [task start];
}

+ (void)adRequestWithParam:(XMIAdReqAdData *)reqData completionHandler:(void (^)(XMIAdRespAdData * _Nullable, NSError * _Nullable))completionHandler {
    NSMutableURLRequest *request = [self requestWithUri:kAdURIAdxAd parameters:reqData];
    XMINetDataTask *task = [XMINetManager dataTaskWithRequest:request completionHandler:^(NSURLResponse * _Nonnull response, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error != nil) {
            completionHandler(nil, error);
            return;
        }
        
        XMIAdRespAdData *respData = [XMIAdRespAdData xmi_modelWithJSON:responseObject];
        completionHandler(respData, nil);
    }];
    [task start];
}

+ (void)nonceRequest:(void (^)(XMIAdRespNonceData * _Nullable, NSError * _Nullable))completionHandler {
    // headers
    NSDictionary *headers = [[XMIAdHeader sharedInstance] headers];
    NSString *url = [self getUrlWithUri:kAdURINonce];
    long long timestamp = NSDate.date.timeIntervalSince1970 * 1000;
    url = [url stringByAppendingPathComponent:[NSString stringWithFormat:@"ts-%lld", timestamp]];
    url = [url stringByAppendingFormat:@"?device=%@", [XMICommonUtils deviceType]];
    NSError *error = nil;
    NSMutableURLRequest *request = [XMINetManager createGetRequestWithUrl:url parameters:@{} headers:headers error:&error];
    XMINetDataTask *task = [XMINetManager dataTaskWithRequest:request completionHandler:^(NSURLResponse * _Nonnull response, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error != nil) {
            completionHandler(nil, error);
            return;
        }
        
        XMIAdRespNonceData *respData = [XMIAdRespNonceData xmi_modelWithJSON:responseObject];
        completionHandler(respData, nil);
    }];
    [task start];
}

+ (void)getWithUrl:(NSString *)urlString isThirdUrl:(BOOL)isThirdUrl relatedData:(XMIAdRelatedData *)relatedData {
    [self getWithUrl:urlString andParam:nil isThirdUrl:isThirdUrl relatedData:relatedData];
}
+ (void)getWithUrl:(NSString *)urlString andParam:(nullable NSDictionary *)param  isThirdUrl:(BOOL)isThirdUrl relatedData:(XMIAdRelatedData *)relatedData{
    NSError *error = nil;
    NSMutableDictionary *headers = [NSMutableDictionary dictionary];
    if (isThirdUrl) {
        NSString *adid = [NSString stringWithFormat:@"%lld",  relatedData.adid];
        NSString *fixThirdReportIdsStr = [[XMIAdManager sharedInstance].delegate getConfigCenterWithKey:@"fixThirdReportIdsiOS"];
        NSArray *fixThirdReportIds = [fixThirdReportIdsStr componentsSeparatedByString:@","];
        if ([fixThirdReportIds containsObject:adid]) {
            headers[@"User-Agent"] = [XMIAdHeader sharedInstance].systemUserAgent;
            if ([XMIAdABTest getBoolValueWithKey:@"fixThirdReportClearCookieiOS" defaultValue:NO]) {
                headers[@"Cookie"] = @"";
                
            } else {
                headers[@"Cookie"] = [[XMIAdHeader sharedInstance] cookieValue];
            }
        } else {
            [headers addEntriesFromDictionary:[[XMIAdHeader sharedInstance] headers]];
        }
    } else {
        [headers addEntriesFromDictionary:[[XMIAdHeader sharedInstance] headers]];
    }
    NSMutableURLRequest *request = [XMINetManager createGetRequestWithUrl:urlString parameters:param headers:headers error:&error];
    XMINetDataTask *task = [XMINetManager dataTaskWithRequest:request completionHandler:^(NSURLResponse * _Nonnull response, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error != nil) {
            XMILog(@"get fail, %@", error);
        }
    }];
    task.innerRequest.responseSerializerType = XMISerializerTypeHTTP;
    task.innerRequest.maxRetryCount = 0;
    [task start];
}

+ (void)postWithUrl:(NSString *)urlString andParam:(nullable NSDictionary *)param {
    NSError *error = nil;
    NSMutableURLRequest *request = [XMINetManager createRequestWithUrl:urlString parameters:param headers:[[XMIAdHeader sharedInstance] headers] error:&error];
    XMINetDataTask *task = [XMINetManager dataTaskWithRequest:request completionHandler:^(NSURLResponse * _Nonnull response, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error != nil) {
            XMILog(@"get fail, %@", error);
        }
    }];
    task.innerRequest.responseSerializerType = XMISerializerTypeHTTP;
    task.innerRequest.maxRetryCount = 0;
    [task start];
}

+ (void)getWithUrl:(NSString *)urlString andParam:(nullable NSDictionary *)param completionHandler:(void (^)(XMIAdRespAdData * _Nullable, NSError * _Nullable))completionHandler {
    NSError *error = nil;
    NSMutableURLRequest *request = [XMINetManager createGetRequestWithUrl:urlString parameters:param headers:[[XMIAdHeader sharedInstance] headers] error:&error];
    XMINetDataTask *task = [XMINetManager dataTaskWithRequest:request completionHandler:^(NSURLResponse * _Nonnull response, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error != nil) {
            completionHandler(nil, error);
            return;
        }
        
        XMIAdRespAdData *respData = [XMIAdRespAdData xmi_modelWithJSON:responseObject];
        completionHandler(respData, nil);
    }];
    task.innerRequest.responseSerializerType = XMISerializerTypeJSON;
    [task start];
}

+ (NSMutableURLRequest *)requestWithUri:(NSString *)uri parameters:(XMIAdReqBaseData *)reqData {
    // headers
    NSDictionary *headers = [[XMIAdHeader sharedInstance] headers];
    // param
    NSMutableDictionary *paramters = [NSMutableDictionary dictionary];
    
    NSDictionary *reqDataDictionary = [reqData xmi_modelToJSONObject];
    if (reqDataDictionary) {
        [paramters addEntriesFromDictionary:reqDataDictionary];
    }
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerGetCustomParamters)]) {
        NSDictionary *dict = [manager.delegate managerGetCustomParamters];
        if (dict != nil) {
            [paramters addEntriesFromDictionary:dict];
        }
    }
    
    BOOL needAddSoltId = NO;
    if ([uri isEqualToString:kAdURIAdxAd]) {
        needAddSoltId = YES;
    }
    //
    NSString *url = [self getUrlWithUri:uri];
    if (needAddSoltId) {
        NSMutableString *slotUrl = [NSMutableString stringWithString:url];
        id isCenterBigFeedAd = nil;
        if ([reqData isKindOfClass:[XMIAdReqAdData class]]) {
            XMIAdReqAdData *adRequest = (XMIAdReqAdData *)reqData;
            if (adRequest.slotIds.length) {
                [slotUrl appendString:[NSString stringWithFormat:@"?slotId=%@", adRequest.slotIds]];
            }
            isCenterBigFeedAd = [adRequest.props objectForKey:@"isCenterBigFeedAd"];
        }
        if ([XMIAdManager sharedInstance].isDebug && isCenterBigFeedAd != nil) {
            [slotUrl appendFormat:@"&isCenterBigFeedAd=%@", isCenterBigFeedAd];
        }
        url = [slotUrl copy];
    }
    NSError *error = nil;
    NSMutableURLRequest *request = [XMINetManager createRequestWithUrl:url parameters:paramters headers:headers error:&error];
    
    return request;
}

/**
 自动选择环境拼接url
 */
+ (NSString *)getUrlWithUri:(NSString *)uri {
    NSString *baseUrl = nil;
    XMIAdEnvironment env = [XMIAdManager sharedInstance].environment;
    switch (env) {
        case XMIAdEnvironmentDefault:
            baseUrl = kAdServerBaseUrl;
            break;
        case XMIAdEnvironmentTest:
            baseUrl = kAdServerBaseUrlTest;
            break;
        case XMIAdEnvironmentUAT:
            baseUrl = kAdServerBaseUrlUAT;
            break;
            
        default:
            baseUrl = kAdServerBaseUrl;
            break;
    }
    if ([XMIAdDataCenter requestUseHttps]) {
        baseUrl = [baseUrl stringByReplacingOccurrencesOfString:@"http://" withString:@"https://"];
    }
    return [NSString stringWithFormat:@"%@%@", baseUrl, uri];
}

@end
