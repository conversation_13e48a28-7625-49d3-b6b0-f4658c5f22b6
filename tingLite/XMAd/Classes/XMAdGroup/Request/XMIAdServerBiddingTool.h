//
//  XMIAdServerBiddingTool.h
//  XMAd
//
//  Created by xmly on 2022/2/18.
//

#import <Foundation/Foundation.h>
@class XMIAdSlotData;
NS_ASSUME_NONNULL_BEGIN

@interface XMIAdServerBiddingTool : NSObject
+ (NSString *)getBiddingTokenWithPositionName:(NSString *)positionName slotData:(XMIAdSlotData *)slotData;
+ (NSString *)getBiddingInfoWithPositionName:(NSString *)positionName slotData:(XMIAdSlotData *)slotData;
@end

NS_ASSUME_NONNULL_END
