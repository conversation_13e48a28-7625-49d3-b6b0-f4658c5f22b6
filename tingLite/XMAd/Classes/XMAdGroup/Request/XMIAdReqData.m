//
//  XMIAdReqData.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/15.
//

#import "XMIAdReqData.h"
#import "XMIAdDefines.h"
#import "XMICommonUtils.h"
#import "XMIAdManager.h"
#import "XMIAdHeader.h"
#import <YYModel/YYModel.h>

@implementation XMIAdReqBaseData

- (instancetype)init {
    self = [super init];
    if (self) {
        [self commonInit];
    }
    return self;
}

/**
 填充请求公共字段
 */
- (void)commonInit {
    self.appid = [XMIAdManager appID];
    self.appVersion = [XMICommonUtils appVersion];
    self.sdkVersion = [XMIAdManager sdkVersion];
    self.xt = [NSString stringWithFormat:@"%lld", [XMICommonUtils currentTimestamp]];
    self.osUpdateTime = [XMICommonUtils getOsUpdateTime];
    self.deviceBirthTime = [XMICommonUtils deviceInitializationTime];
    self.uid = [[XMIAdManager sharedInstance] getUid];
    
    NSString *systemIDFA = @"";
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerGetStystemIDFA)]) {
        systemIDFA = [manager.delegate managerGetStystemIDFA];
    }
    self.systemIDFA = systemIDFA ? : @"";
    
    NSInteger idfaLimit = 2;
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerGetStystemIDFALimit)]) {
        idfaLimit = [manager.delegate managerGetStystemIDFALimit];
    }
    self.idfaLimit = idfaLimit;
    
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerGetWxOpenSDKVersion)]) {
        self.opensdk_ver = [manager.delegate managerGetWxOpenSDKVersion];
    }
    
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerGetPddInstallState)]) {
        self.pddInstallState = [manager.delegate managerGetPddInstallState];
    }
    

}

@end

@implementation XMIAdReqInitData

@end

@interface XMIAdReqAdData ()<YYModel>

@end

@implementation XMIAdReqAdData

- (void)commonInit {
    [super commonInit];
    
    self.userAgent = [XMIAdHeader sharedInstance].systemUserAgent;
    self.sdks = [NSString stringWithFormat:@"%ld,%ld,%ld", (long)XMIAdSourceGDT, (long)XMIAdSourceBU, (long)XMIAdSourceXM];
}

//warning:YYModel替换成自有序列化方法的话这里要改
+ (NSArray<NSString *> *)modelPropertyBlacklist
{
    return @[@"props"];
}

- (BOOL)modelCustomTransformToDictionary:(NSMutableDictionary *)dic
{
    if (self.props) {
        [dic addEntriesFromDictionary:self.props];
    }
    return YES;
}

@end
