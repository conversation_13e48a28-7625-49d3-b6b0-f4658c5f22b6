//
//  XMIAdDataRequest.m
//  XMAd
//
//  Created by cuiyuanz<PERSON> on 2022/3/23.
//

#import "XMIAdDataRequest.h"
#import "XMIAdError.h"
#import "XMICommonUtils.h"
#import "XMINetManager.h"
#import "XMINetDataTask.h"
#import "XMIAdRespData.h"
#import "NSObject+XMIModel.h"
#import "XMIAdRequest.h"

#define kAdURIAdxAd @"/adx/ad"
#define kAdURINonce @"/nonce"

@interface XMIAdDataRequest ()

@property (nonatomic, assign) BOOL canceled;

@property (nonatomic, strong) XMINetDataTask *task;

@end

@implementation XMIAdDataRequest

- (void)start
{
    if (self.task || self.canceled) {
        return;
    }
    self.canceled = NO;
    NSMutableURLRequest *request = [XMIAdRequest requestWithUri:kAdURIAdxAd parameters:self.reqData];
    XMINetDataTask *task = [XMINetManager dataTaskWithRequest:request completionHandler:^(NSURLResponse * _Nonnull response, id  _Nullable responseObject, NSError * _Nullable error) {
        if ([self isCanceled]) {
            return;
        }
        if (error != nil) {
            if (self.failBlock) {
                self.failBlock([XMIAdError requestErrorWithError:error]);
            }
            return;
        }
        
        XMIAdRespAdData *respData = [XMIAdRespAdData xmi_modelWithJSON:responseObject];
        if (self.successBlock) {
            self.successBlock(respData);
        }
    }];
    self.task = task;
    [task start];
}

- (void)cancel
{
    self.canceled = YES;
    if (self.task) {
        if (self.failBlock) {
            self.failBlock([XMIAdError canceledError]);
        }
    }
    [self.task stop];
    self.task = nil;
}

- (BOOL)isCanceled
{
    return self.canceled;
}
     
@end
