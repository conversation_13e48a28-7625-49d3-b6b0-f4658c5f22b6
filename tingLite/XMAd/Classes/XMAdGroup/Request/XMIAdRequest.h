//
//  XMIAdRequest.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/5.
//

#import <Foundation/Foundation.h>
#import "XMIAdReqData.h"
#import "XMIAdRespData.h"
#import "XMIAdNonceData.h"

NS_ASSUME_NONNULL_BEGIN

@class XMIAdRelatedData;

@interface XMIAdRequest : NSObject

+ (void)initRequestWithParam:(XMIAdReqInitData *)reqData completionHandler:(nullable void (^)(XMIAdRespInitData * _Nullable respData, NSError * _Nullable error))completionHandler;

+ (void)adRequestWithParam:(XMIAdReqAdData *)reqData completionHandler:(nullable void (^)(XMIAdRespAdData * _Nullable respData, NSError * _Nullable error))completionHandler;

+ (void)nonceRequest:(nullable void (^)(XMIAdRespNonceData * _Nullable respData, NSError * _Nullable error))completionHandler;

/**
 像url发一个GET请求，用于点击上报、第三方上报等
 */
+ (void)getWithUrl:(NSString *)urlString isThirdUrl:(BOOL)isThirdUrl relatedData:(XMIAdRelatedData *)relatedData;
+ (void)getWithUrl:(NSString *)urlString andParam:(nullable NSDictionary *)param isThirdUrl:(BOOL)isThirdUrl relatedData:(XMIAdRelatedData *)relatedData;
/// 发送预览等
+ (void)getWithUrl:(NSString *)urlString andParam:(nullable NSDictionary *)param completionHandler:(void (^)(XMIAdRespAdData * _Nullable, NSError * _Nullable))completionHandler;

+ (NSMutableURLRequest *)requestWithUri:(NSString *)uri parameters:(XMIAdReqBaseData *)reqData;

// 点击上报由老的get改为post
+ (void)postWithUrl:(NSString *)urlString andParam:(nullable NSDictionary *)param;

@end

NS_ASSUME_NONNULL_END
