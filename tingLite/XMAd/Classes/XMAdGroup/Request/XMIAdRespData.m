//
//  XMIAdRespData.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/15.
//

#import "XMIAdRespData.h"
#import <XMCategories/NSObject+XMCommon.h>
#import <XMCategories/NSString+XMCommon.h>
#import <YYModel/YYModel.h>
#import "XMIAdDefines.h"
@implementation XMIAdRespBaseData

- (BOOL)isSuccess {
    return self.ret == 0;
}

@end

@implementation XMIAdRespInitData

+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{
        @"slots" : [XMIAdSlotBean class]
    };
}

@end

@implementation XMIAdGlobalConfig

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"adRequestZipRate" : @"ad_request_zip_rate"
    };
}

- (BOOL)modelCustomTransformFromDictionary:(NSDictionary *)dic {
    // 如果没配置这个key，那么默认触发的时间为10s
    id fullVideoPageAutoOpenTime = [dic valueForKey:@"fullVideoPageAutoOpenTime"];
    if (fullVideoPageAutoOpenTime) {
        _fullVideoPageAutoOpenTime = [dic doubleMaybeForKey:@"fullVideoPageAutoOpenTime"];
    } else {
        _fullVideoPageAutoOpenTime = 10000;
    }
    return YES;
}

@end

@implementation XMIAdSlotBean

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"slotId" : @"id"
    };
}

/*
 bidSlotList目前返回的数据格式为  "bidSlotList": {"4": ["2032295639976544"]}
 因此需要自定义转换
 */
- (BOOL)modelCustomTransformFromDictionary:(NSDictionary *)dic {
    NSDictionary *bidSlotDic = dic[@"bidSlotList"];
    if ([bidSlotDic isKindOfClass:[NSDictionary class]]) {
        NSMutableArray *bidSlotIds = [NSMutableArray array];
        for (NSString *key in bidSlotDic.allKeys) {
            XMIAdBidSlotInfoBean *slotInfo = [XMIAdBidSlotInfoBean new];
            slotInfo.adType = [key integerValue];
            slotInfo.bidSlotIds = [bidSlotDic valueForKey:key];
            [bidSlotIds addObject:slotInfo];
        }
        self.bidSlotList = [bidSlotIds copy];
    }
    return YES;
}

@end

@implementation XMIAdBidSlotInfoBean

@end

// ----------------------------------------------------------------

@implementation XMIAdRespAdData

+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{
        @"slotAds" : [XMIAdSlotAdBean class]
    };
}

@end

@implementation XMIAdSlotAdBean

+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{
        @"ads" : [XMIAdBean class]
    };
}

@end

@implementation XMIAdBean

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"isAuto"        : @"auto",
        @"adDescription" : @"description",
        @"adid"      : @[@"adid", @"adId"],
        @"tagArray"     : @"tags"
    };
}

+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{
        @"composite"          : [XMIAdBeanComposite class],
        @"compositeAds"       : [XMIAdBean class],
        @"clickTokens"        : [NSString class],
        @"showTokens"         : [NSString class],
        @"thirdShowStatUrls"  : [NSString class],
        @"thirdClickStatUrls" : [NSString class]
    };
}

- (BOOL)modelCustomTransformFromDictionary:(NSDictionary *)dic {
    if ([dic dictionaryMaybeForKey:@"businessExtraInfo"]) {
        if (!self.jumpTrackId) {
            self.jumpTrackId = [[dic dictionaryMaybeForKey:@"businessExtraInfo"] longLongMaybeForKey:@"jumpTrackId"];
        }
        if (!self.autoJumpTime) {
            self.autoJumpTime = [[dic dictionaryMaybeForKey:@"businessExtraInfo"] floatMaybeForKey:@"autoJumpTime"];
        }
        // 播音合规弹窗
        self.popReminderStyle = [[dic dictionaryMaybeForKey:@"businessExtraInfo"] stringMaybeForKey:@"popReminderStyle"];
        self.popReminderText = [[dic dictionaryMaybeForKey:@"businessExtraInfo"] stringMaybeForKey:@"popReminderText"];
        // 声音流彩蛋弹窗样式
        self.soundAggType = [[dic dictionaryMaybeForKey:@"businessExtraInfo"] stringMaybeForKey:@"soundAggType"];
        
        // 如果外层没有ubtReportMap，clickRequestMethod，clickPostMap，那么尝试从businessExtraInfo中获取
        if (!self.ubtReportMap.length) {
            self.ubtReportMap = [[dic dictionaryMaybeForKey:@"businessExtraInfo"] stringMaybeForKey:@"ubtReportMap"];
        }
        if (!self.clickRequestMethod.length) {
            self.clickRequestMethod = [[dic dictionaryMaybeForKey:@"businessExtraInfo"] stringMaybeForKey:@"clickRequestMethod"];
        }
        if (!self.clickPostMap.length) {
            self.clickPostMap = [[dic dictionaryMaybeForKey:@"businessExtraInfo"] stringMaybeForKey:@"clickPostMap"];
        }
        
        self.reportClickTime = [[dic dictionaryMaybeForKey:@"businessExtraInfo"] integerMaybeForKey:@"reportClickTime"];
    }
    NSString *commonReportMapStr = [dic stringMaybeForKey:@"commonReportMap"];
    if (commonReportMapStr.length) {
        NSDictionary *commonReportMap = [commonReportMapStr xm_dictionaryValue];
        self.preLoadH5LandingPage = [commonReportMap integerMaybeForKey:@"preLoadH5LandingPage"];
    }
    return YES;
}

- (BOOL)dsp {
    if (self.adtype == XMIAdTypeBU
        || self.adtype == XMIAdTypeGDT
        || self.adtype == XMIAdTypeBAIDU
        || self.adtype == XMIAdTypeJAD) {
        return YES;
    }
    return NO;
}

@end

@implementation XMIAdBootUpBean

@end

@implementation XMIAdShareDataBean

@end

@implementation XMIAdBeanComposite

@end
