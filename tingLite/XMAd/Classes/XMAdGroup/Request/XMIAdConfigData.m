//
//  XMIAdConfigData.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/4.
//

#import "XMIAdConfigData.h"
#import "NSObject+XMICache.h"
#import "XMICommonUtils.h"

@implementation XMIAdConfigData

+ (NSDictionary *)xmi_cachePropertyMapper {
    return @{
        @"cKey" : XMIC_STRING,
        @"cValue" : XMIC_DATA,
        @"createTime" : XMIC_INT,
        @"updateTime" : XMIC_INT
    };
}

+ (int)intConfigForKey:(NSString *)key {
    NSString *str = [self stringConfigForKey:key];
    if (str == nil) {
        return 0;
    }
    return [str intValue];
}

+ (long long)longLongConfigForKey:(NSString *)key {
    NSString *str = [self stringConfigForKey:key];
    if (str == nil) {
        return 0;
    }
    return [str longLongValue];
}

+ (double)doubleConfigForKey:(NSString *)key {
    NSString *str = [self stringConfigForKey:key];
    if (str == nil) {
        return 0;
    }
    return [str doubleValue];
}

+ (BOOL)boolConfigForKey:(NSString *)key {
    NSString *str = [self stringConfigForKey:key];
    if (str == nil) {
        return YES;
    }
    return [str boolValue];
}

+ (NSString *)stringConfigForKey:(NSString *)key {
    NSData *value = [self dataConfigForKey:key];
    if (value == nil) {
        return nil;
    }
    NSString *valueStr = [[NSString alloc] initWithData:value encoding:NSUTF8StringEncoding];
    return valueStr;
}


+ (NSDictionary *)dictionaryConfigForKey:(NSString *)key {
    NSData *value = [self dataConfigForKey:key];
    if (value == nil) {
        return nil;
    }
    NSError *error;
    NSDictionary *dictionary = [NSJSONSerialization JSONObjectWithData:value options:0 error:&error];
    if ([dictionary isKindOfClass:[NSDictionary class]]) {
        return dictionary;
    }
    return nil;
}

+ (NSData *)dataConfigForKey:(NSString *)key {
    XMIAdConfigData *configData = [XMIAdConfigData xmi_selectOneByCondition:[NSString stringWithFormat:@"cKey = '%@'", key]];
    if (configData == nil || [configData.cValue isKindOfClass:[NSNull class]]) {
        return nil;
    }
    
    return configData.cValue;
}

+ (BOOL)updateConfig:(id)config forKey:(NSString *)key {
    NSData *cData = nil;
    if ([config isKindOfClass:[NSNumber class]]) {
        NSString *str = [(NSNumber *)config stringValue];
        cData = [str dataUsingEncoding:NSUTF8StringEncoding];
    } else if ([config isKindOfClass:[NSString class]]) {
        cData = [(NSString *)config dataUsingEncoding:NSUTF8StringEncoding];
    } else if ([config isKindOfClass:[NSDictionary class]] || [config isKindOfClass:[NSArray class]]) {
        NSError *error;
        cData = [NSJSONSerialization dataWithJSONObject:config options:0 error:&error];
    } else if ([config isKindOfClass:[NSData class]]) {
        cData = config;
    }
    BOOL ret = NO;
    NSString *condition = [NSString stringWithFormat:@"cKey = '%@'", key];
    XMIAdConfigData *configData = [XMIAdConfigData xmi_selectOneByCondition:condition];
    if (configData == nil) {
        configData = [[XMIAdConfigData alloc] init];
        configData.cKey = key;
        configData.cValue = cData;
        long long time = [XMICommonUtils currentTimestamp];
        configData.createTime = time;
        configData.updateTime = time;
        ret = [configData xmi_save];
    } else {
        configData.cValue = cData;
        configData.updateTime = [XMICommonUtils currentTimestamp];
        ret = [configData xmi_updateByCondition:condition];
    }
    
    return ret;
}

@end
