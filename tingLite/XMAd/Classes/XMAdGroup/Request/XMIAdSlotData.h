//
//  XMIAdSlotData.h
//  XMAd
//  广告位数据缓存
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/4.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

#define XMI_ADP_FEED @"find_native"
#define XMI_ADP_SPLASH @"loading"
#define XMI_ADP_HOME_BASERIAL_FEED @"home_biserial_feed"
#define XMI_ADP_HOME_GUESS_YOU_LIKE @"home_guess_you_like"
#define XMI_ADP_HOME_BANNER_AD @"home_banner_ad"

@interface XMIAdSlotData : NSObject

@property (nonatomic, assign) long long slotId;
@property (nonatomic, assign) long long positionId;
@property (nonatomic, strong) NSString *positionName;
@property (nonatomic, assign) long long createTime;
@property (nonatomic, assign) long long updateTime;
@property (nonatomic, strong) NSString *bidSlotList;
/**
 默认的广告位id，广告位id初始化请求失败时容错处理
 */
+ (long long)defaultSlotIdForPosition:(NSString *)positionName;

@end

NS_ASSUME_NONNULL_END
