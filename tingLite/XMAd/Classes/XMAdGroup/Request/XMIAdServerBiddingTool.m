//
//  XMIAdServerBiddingTool.m
//  XMAd
//
//  Created by xmly on 2022/2/18.
//

#import "XMIAdServerBiddingTool.h"
#import "XMIAdRespData.h"
#import "XMIAdSlotData.h"
#import "XMIAdDefines.h"
#import "NSObject+XMIModel.h"
#import "XMIAdHelper.h"
#import <BUAdSDK/BUNativeAdsManager.h>
#import <GDTMobSDK/GDTSDKConfig.h>

@implementation XMIAdServerBiddingTool
+ (NSString *)getBiddingTokenWithPositionName:(NSString *)positionName slotData:(XMIAdSlotData *)slotData {
    if (![XMIAdHelper iOSServerBiddingEnable]) {
        return @"";
    }
    NSArray *bidSlotList = [NSArray xmi_modelArrayWithClass:[XMIAdBidSlotInfoBean class] json:slotData.bidSlotList];
    
    NSMutableString *adSdkToken = [NSMutableString string];
    NSMutableArray *tokens = [NSMutableArray array];
    
    for (XMIAdBidSlotInfoBean *bidSlotInfo in bidSlotList) {
        NSInteger platformType = bidSlotInfo.adType;
        NSArray *soltArray = bidSlotInfo.bidSlotIds;
        for (NSString *slotId in soltArray) {
            if (![slotId isKindOfClass:[NSString class]]) {
                break;
            }
            if (!slotId.length) {
                break;
            }
            NSString *token = [self getBiddingTokenWithPositionName:positionName adPlatformType:platformType slotId:slotId];
            if (!token.length) {
                break;
            }
            token = [token URLEncodedString];
            NSMutableString *tempStr = [NSMutableString string];
            [tempStr appendString:slotId];
            [tempStr appendString:@":"];
            [tempStr appendString:token];
            [tokens addObject:tempStr];
        }
    }
    
    if (tokens.count) {
        for (NSMutableString *token in tokens) {
            [adSdkToken appendString:token];
            if (token != tokens.lastObject) {
                [adSdkToken appendString:@","];
            }
        }
    }
    
    return adSdkToken;
}

+ (NSString *)getBiddingTokenWithPositionName:(NSString *)positionName adPlatformType:(XMIAdType)platformType slotId:(NSString *)slotId {
    
    [XMIAdHelper initSdkWithAdType:platformType];
    
    NSString *token = nil;
    if ([positionName isEqualToString:XMI_ADP_HOME_BASERIAL_FEED] || [positionName isEqualToString:XMI_ADP_FEED] ||
        [positionName isEqualToString:XMI_ADP_HOME_BANNER_AD]) {
        if (platformType == XMIAdTypeBU) {
            BUAdSlot *slot = [[BUAdSlot alloc] init];
            slot.ID = slotId;
            slot.AdType = BUAdSlotAdTypeFeed;
            slot.imgSize = [BUSize sizeBy:BUProposalSize_Feed228_150];
            BUNativeAdsManager *manager = [[BUNativeAdsManager alloc] initWithSlot:slot];
            token = [manager biddingToken];
        }
        else if (platformType == XMIAdTypeGDT) {
            token = [GDTSDKConfig getBuyerIdWithContext:nil];
        }
    }
    return token;
}

+ (NSString *)getBiddingInfoWithPositionName:(NSString *)positionName slotData:(XMIAdSlotData *)slotData {
    if (![XMIAdHelper iOSServerBiddingEnable]) {
        return @"";
    }
    NSArray *bidSlotList = [NSArray xmi_modelArrayWithClass:[XMIAdBidSlotInfoBean class] json:slotData.bidSlotList];
    
    NSMutableString *adSdkToken = [NSMutableString string];
    NSMutableArray *tokens = [NSMutableArray array];
    
    for (XMIAdBidSlotInfoBean *bidSlotInfo in bidSlotList) {
        NSInteger platformType = bidSlotInfo.adType;
        NSArray *soltArray = bidSlotInfo.bidSlotIds;
        for (NSString *slotId in soltArray) {
            if (![slotId isKindOfClass:[NSString class]]) {
                break;
            }
            if (!slotId.length) {
                break;
            }
            NSString *token = [self getBiddingInfoWithPositionName:positionName adPlatformType:platformType slotId:slotId];
            if (!token.length) {
                break;
            }
            token = [token URLEncodedString];
            NSMutableString *tempStr = [NSMutableString string];
            [tempStr appendString:slotId];
            [tempStr appendString:@":"];
            [tempStr appendString:token];
            [tokens addObject:tempStr];
        }
    }
    
    if (tokens.count) {
        for (NSMutableString *token in tokens) {
            [adSdkToken appendString:token];
            if (token != tokens.lastObject) {
                [adSdkToken appendString:@","];
            }
        }
    }
    
    return adSdkToken;
}

+ (NSString *)getBiddingInfoWithPositionName:(NSString *)positionName adPlatformType:(XMIAdType)platformType slotId:(NSString *)slotId {
    
    [XMIAdHelper initSdkWithAdType:platformType];
    
    NSString *token = nil;
    if ([positionName isEqualToString:XMI_ADP_HOME_BASERIAL_FEED] || [positionName isEqualToString:XMI_ADP_FEED] ||
        [positionName isEqualToString:XMI_ADP_HOME_BANNER_AD] ) {
        if (platformType == XMIAdTypeGDT) {
            token = [GDTSDKConfig getSDKInfoWithPlacementId:slotId];
        }
    }
    return token;
}
@end
