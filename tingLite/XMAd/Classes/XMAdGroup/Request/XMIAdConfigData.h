//
//  XMIAdConfigData.h
//  XMAd
//  广告配置缓存
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/4.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

#define XMI_CONFIG_SYSTEMUA @"sysua"
#define XMI_CONFIG_THIRD_TIMEOUT @"thirdSDKTimeoutMs"
#define XMI_CONFIG_FEED_THIRD_TIMEOUT @"homeFeedThirdSDKRequestTime"
#define XMI_CONFIG_AH_WH_RATIO @"AdWidthHeightRatio"
#define XMI_CONFIG_SEQID @"seqId"
#define XMI_CONFIG_CLIENT_IP @"clientIp"
#define XMI_CONFIG_CLOSE_AD_PADDING @"closeAdPadding"
#define XMI_CONFIG_ALWAYS_PLAY_VIDEO @"adAlwaysPlayVideo"
#define XMI_CLOSE_AD_NEED_DIALOG_BY_POSITIONID @"closeAdNeedDialogByPositionId"
#define XMI_CONFIG_AD_REQUEST_ZIP_RATE @"ad_request_zip_rate"
#define XMI_CONFIG_AD_ENABLE_REPORT_DSP @"enable_report_dsp"
#define XMI_CONFIG_AD_ENABLE_REPORT_DSP_RATE @"enable_report_dsp_rate"
#define XMI_CONFIG_AD_ENABLE_REPORT_DSP_DOWNLOAD @"enable_report_dsp_download"
#define XMI_CONFIG_AD_TRACK_VENT_CONFIG @"trackVentConfig"
#define XMI_CONFIG_AD_REWARD_SKIP_TIME @"rewardSkipTime"
#define XMI_CONFIG_AD_REWARD_AUTO_JUMP @"rewardAdAutoJump"
#define XMI_CONFIG_AD_USE_HTTPS @"requestUseHttps"
#define XMI_CONFIG_AD_FULL_VIDEO_PAGE_AUTO_OPEN_TIME @"fullVideoPageAutoOpenTime"
#define XMI_CONFIG_AD_WEB_TOP_TOAST_SHOWTIME @"webTopToastShowTime"
#define XMI_CONFIG_AD_AB_LAB_CONFIG @"abLabConfig"
#define XMI_CONFIG_AD_CLIENT_FREQUENCY @"clientFrequency"
#define XMI_CONFIG_AD_USE_NEW_RTB_SPLASH_IOS @"useNewRtbSplashV1_iOS"
#define XMI_CONFIG_AD_COMMON_SHAKE_TIPS @"adCommonShakeTips"
#define XMI_CONFIG_AD_USER_INFO_ALLOW @"useUserInfoAllow"
#define XMI_CONFIG_AD_HOT_LAUNCH_INTERVAL @"loadingFrequency"
#define XMI_CONFIG_AD_AIGC_CACHE_VALID_INTERVAL @"aigc_cache_time"
#define XMI_CONFIG_AD_AIGC_CACHE_DEAL_CONFIG @"aigc_cache_deal_config"
#define XMI_CONFIG_AD_DEDUP_CLICK_INTERVAL @"dedupClickInterval"
#define XMI_CONFIG_AD_PREVIEW_CLICK_REPORT_IDS @"previewClickReportIds"

@interface XMIAdConfigData : NSObject

@property (nonatomic, copy) NSString *cKey;
@property (nonatomic, strong) NSData *cValue;
@property (nonatomic, assign) long long createTime;
@property (nonatomic, assign) long long updateTime;

/**
 获取config，并转换为指定类型
 */
+ (int)intConfigForKey:(NSString *)key;
+ (long long)longLongConfigForKey:(NSString *)key;
+ (double)doubleConfigForKey:(NSString *)key;
+ (BOOL)boolConfigForKey:(NSString *)key;
+ (NSString *)stringConfigForKey:(NSString *)key;
+ (NSDictionary *)dictionaryConfigForKey:(NSString *)key;
+ (NSData *)dataConfigForKey:(NSString *)key;

/**
 更新某项config
 
 @param config :配置对象 NSNumber, NSString, NSData
 @param key :配置名称
 */
+ (BOOL)updateConfig:(id)config forKey:(NSString *)key;

@end

NS_ASSUME_NONNULL_END
