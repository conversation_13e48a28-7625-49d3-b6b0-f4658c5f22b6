//
//  XMIAdSlotData.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/4.
//

#import "XMIAdSlotData.h"
#import "NSObject+XMICache.h"

@implementation XMIAdSlotData

+ (NSDictionary *)xmi_cachePropertyMapper {
    return @{
        @"slotId" : XMIC_INT,
        @"positionId" : XMIC_INT,
        @"positionName" : XMIC_STRING,
        @"createTime" : XMIC_INT,
        @"updateTime" : XMIC_INT,
        @"bidSlotList" : XMIC_STRING
    };
}

+ (long long)defaultSlotIdForPosition:(NSString *)positionName {
    NSDictionary *dic = @{
        XMI_ADP_FEED : @(28),
        XMI_ADP_SPLASH : @(1),
        XMI_ADP_HOME_BASERIAL_FEED : @(238),
        XMI_ADP_HOME_BANNER_AD : @(293),
        XMI_ADP_HOME_GUESS_YOU_LIKE : @(44)
    };
    
    return [[dic objectForKey:positionName] longLongValue];
}

@end
