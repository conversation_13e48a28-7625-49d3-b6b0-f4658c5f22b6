//
//  XMIAdRespData.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/15.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XMIAdRespBaseData : NSObject

/**
 返回码 0-正常, 其它-错误
 */
@property (nonatomic, assign)         NSInteger ret;
/**
 错误信息
 */
@property (nullable, nonatomic, copy) NSString *msg;

- (BOOL)isSuccess;

@end

@class XMIAdGlobalConfig;
@class XMIAdSlotBean;
/**
 adx初始化返回模型
 */
@interface XMIAdRespInitData : XMIAdRespBaseData

/**
 配置版本
 */
@property (nullable, nonatomic, copy) NSString *version;
/**
 配置
 */
@property (nonatomic, strong) XMIAdGlobalConfig *globalConfig;
/**
 广告代码位
 */
@property (nonatomic, strong) NSArray<XMIAdSlotBean *> *slots;

@end

/**
 adx初始化返回全局配置
 */
@interface XMIAdGlobalConfig : NSObject

@property (nonatomic, assign) NSInteger thirdSDKTimeoutMs;
@property (nonatomic, assign) NSInteger homeFeedThirdSDKRequestTime;
@property (nonatomic, assign) NSInteger downloadApkInstallWaitTime;
@property (nonatomic, copy) NSDictionary *closeAdPadding;
@property (nonatomic, assign) CGFloat AdWidthHeightRatio;
@property (nonatomic, assign) BOOL adAlwaysPlayVideo;
@property (nonatomic, copy) NSDictionary *closeAdNeedDialogByPositionId;
@property (nonatomic, assign) NSInteger adRequestZipRate;

@property (nonatomic, assign) BOOL enable_report_dsp;
@property (nonatomic, copy) NSDictionary *enable_report_dsp_rate;
@property (nonatomic, assign) BOOL enable_report_dsp_download ;

@property (nonatomic, copy) NSString *trackVentConfig;

@property (nonatomic, assign) NSTimeInterval rewardSkipTime;
@property (nonatomic, assign) BOOL rewardAdAutoJump;
@property (nonatomic, assign) BOOL requestUseHttpsiOS;
@property (nonatomic, assign) NSTimeInterval webTopToastShowTime;

@property (nonatomic, assign) NSTimeInterval fullVideoPageAutoOpenTime;

@property (nonatomic, copy) NSDictionary *abLabConfig;

@property (nonatomic, copy) NSArray *clientFrequency;

@property (nonatomic, assign) BOOL useNewRtbSplashV1_iOS;
@property (nonatomic, copy) NSString *adCommonShakeTips;
// 一键留资，用户是否已经授权
@property (nonatomic, assign) BOOL useUserInfoAllow;

@property (nonatomic, assign) NSTimeInterval loadingFrequency;

// aigc缓存有效时间
@property (nonatomic, assign) NSTimeInterval aigc_cache_time;

// aigc缓存配置（播放冷却时间 & 每天曝光次数）
@property (nonatomic, copy) NSString *aigcCacheDealConfig;

@property (nonatomic, assign) NSInteger dedupClickInterval;

@property (nonatomic, copy) NSString *previewClickReportIds;//预览支持点击上报的positionId

@end

@class XMIAdBidSlotInfoBean;
/**
 adx初始化返回slot对象
 */
@interface XMIAdSlotBean : NSObject

/**
 广告代码位id
 */
@property (nonatomic, assign) long long slotId;
/**
 广告位名称
 */
@property (nullable, nonatomic, copy)   NSString *positionName;
/**
 广告位id
 */
@property (nonatomic, assign)           long long positionId;
/**
 实时竞价使用的slotId
 */
@property (nullable, nonatomic, copy)             NSArray<XMIAdBidSlotInfoBean *> *bidSlotList;
@end

/**
 adx广告数据返回实时竞价相关信息
 */
@interface XMIAdBidSlotInfoBean : NSObject
/**
 广告数据源，和dspId一致。例如 0：喜马拉雅后台物料
 */
@property (nonatomic, assign)           NSInteger adType;
/**
 实时竞价返回的slotId数组
 */
@property (nonatomic, copy)             NSArray<NSString *> *bidSlotIds;
@end


/**
 多图样式
 */
@interface XMIAdBeanComposite : NSObject

@property (nonatomic, copy)           NSString *picUrl;

@property (nonatomic, copy)           NSString *title;

@property (nonatomic, copy)           NSString *videoUrl;

@property (nonatomic, copy)           NSString *realLink;

@property (nonatomic, copy)           NSString *dpLink;

@end

// -------------------------------------------------------------------


@class XMIAdSlotAdBean;
@class XMIAdBean;
@class XMIAdShareDataBean;
@class XMIAdBootUpBean;
/**
 adx广告数据返回模型
 */
@interface XMIAdRespAdData : XMIAdRespBaseData

/**
 返回id
 */
@property (nonatomic, assign)         long long responseId;
/**
 返回时间戳
 */
@property (nonatomic, assign)         long long timestamp;
/**
 客户端ip
 */
@property (nonatomic, copy)           NSString *clientIp;
@property (nullable, nonatomic, copy) NSString *userShowReportExt;
/**
 代码位数据
 */
@property (nonatomic, strong)         NSArray<XMIAdSlotAdBean *> *slotAds;

// 用于预览时标记需不需要上报点击，服务端返回
@property (nonatomic, assign)         BOOL clickReportFlag;
@end

/**
 adx广告数据返回slotAd对象
 */
@interface XMIAdSlotAdBean : NSObject

/**
 代码位id
 */
@property (nonatomic, assign)         long long positionId;
/**
 代码位id
 */
@property (nonatomic, assign)         long long slotId;
@property (nullable, nonatomic, copy) NSString *slotShowReportExt;
/**
 广告位名称
 */
@property (nonatomic, copy)           NSString *positionName;
/**
 广告数据
 */
@property (nonatomic, strong)         NSArray<XMIAdBean *> *ads;

@end

/**
 adx广告数据对象
 */
@interface XMIAdBean : NSObject

/**
 焦点图使用字段
 */
@property (nullable, nonatomic, copy)   NSString *adBucketIds;
/**
 广告显示时的样式图片(旧版广告logo)
 */
@property (nullable, nonatomic, copy)   NSString *adMark;
@property (nullable, nonatomic, copy)   NSString *darkAdMark;

/**
 广告logo式样
 */
@property (nonatomic, assign) NSInteger adMarkStyle;
/**
 广告类型 "SALE":销售广告 "PAY":付费内容广告 "OTHER":其他广告
 */
@property (nullable, nonatomic, copy)   NSString *adUserType;
/**
 物料ID
 */
@property (nonatomic, assign)           long long adid;
/**
 广告唯一ID
 */
@property (nonatomic, assign) long long adUniqId;
/**
 cpm竞价时的出价”经过加密处理后的密文
 */
@property (nullable, nonatomic, copy)   NSString *adpr;
/**
 广告数据源，和dspId一致。例如 0：喜马拉雅后台物料
 */
@property (nonatomic, assign)           NSInteger adtype;
/**
 下载APP链接，apk包地址
 */
@property (nullable, nonatomic, copy)   NSString *apkUrl;
/**
 是否静默下载APP(auto)
 */
@property (nonatomic, assign)           BOOL isAuto;
/**
 背景图地址
 */
@property (nullable, nonatomic, copy)   NSString *bgCover;
/**
 多屏长图
 */
@property (nullable, nonatomic, strong) XMIAdBootUpBean *bootUps;
/**
 分桶信息
 */
@property (nullable, nonatomic, copy)   NSString *bucketIds;
/**
 按钮文案
 */
@property (nullable, nonatomic, copy)   NSString *buttonText;
/**
 广告上报透传信息
 */
@property (nullable, nonatomic, copy)   NSString *commonReportMap;
/**
 广告投放方式
 */
@property (nullable, nonatomic, copy)   NSString *chargeMethod;
/**
 广告点击时携带Token
 */
@property (nullable, nonatomic, strong) NSArray<NSString *> *clickTokens;
/**
 多图样式
 */
@property (nullable, nonatomic, strong) NSArray<XMIAdBeanComposite *> *composite;

/**
 混投
 */
@property (nullable, nonatomic, strong) NSArray<XMIAdBean *> *compositeAds;

/**
 点击动作 1:可以点击 2:不可点击
 */
@property (nonatomic, assign)           NSInteger clickType;
/**
 是否是套餐
 */
@property (nonatomic, assign)           BOOL comboAd;
/**
 [重要]图片类素材的图片地址，或是视频类素材的备选图地址（在视频下载失败时展示该图片）
 */
@property (nullable, nonatomic, copy)   NSString *cover;
/**
 广告副标题(接口返回description)
 */
@property (nullable, nonatomic, copy)   NSString *adDescription;
/**
 [忽略]展示类型：此处默认是1
 */
@property (nonatomic, assign)           NSInteger displayType;
/**
 deeplink或UniversalLink
 */
@property (nullable, nonatomic, copy)   NSString *dpRealLink;
/**
 客户端竞价： 加密底价过滤
 */
@property (nullable, nonatomic, copy)   NSString *bidMinPrice;
/**
 客户端竞价：加密价格
 */
@property (nullable, nonatomic, copy)   NSString *priceEncrypt;
/**
 客户端竞价：客户端sdk竞价物料
 */
@property (nonatomic, assign)           BOOL isMobileRtb;
/**
 客户端竞价：价格
 */
@property (nonatomic, assign)           float price;
/**
 客户端竞价：优先级
 */
@property (nonatomic, assign)           NSInteger rankLevel;
/**
 是否是DSP
 */
@property (nonatomic, assign)           BOOL dsp;
/**
 SDK接入DSP的广告位ID
 */
@property (nullable, nonatomic, copy)   NSString *dspPositionId;
/**
 该广告是否为实时竞价
 */
@property (nonatomic, assign) BOOL slotRealBid;
/**
 该广告实时竞价相关数据
 */
@property (nonatomic, copy) NSString *slotAdm;
/**
 动态图地址
 */
@property (nullable, nonatomic, copy)   NSString *dynamicCover;
/**
 非全屏开机静态启动图 动态浮层引导文案
 */
@property (nullable, nonatomic, copy)   NSString *floatingLayerGuideCopy;
/**
 开机巨幕联合霸屏新增字段，巨幕定帧图
 */
@property (nullable, nonatomic, copy)   NSString *frameCover;
/**
 开机巨幕联合霸屏新增字段，巨幕备胎图
 */
@property (nullable, nonatomic, copy)   NSString *giantCover;
/**
 开机巨幕联合霸屏新增字段，巨幕视频
 */
@property (nullable, nonatomic, copy)   NSString *giantVideoCover;
/**
 开机巨幕联合霸屏新增字段，音量百分比
 */
@property (nonatomic, assign)           double giantVolume;
/**
 引导文案
 */
@property (nullable, nonatomic, copy)   NSString *guidanceText;
/**
 是否展示来源（仅dsp可能为非0，1：文字，2：logo）
 */
@property (nonatomic, assign)           NSInteger inScreenSource;
/**
 是否内链； 0：否；1：是
 */
@property (nonatomic, assign)           NSInteger isInternal;
/**
 是否支持横屏
 */
@property (nonatomic, assign)           BOOL isLandScape;
/**
 是否分享
 */
@property (nonatomic, assign)           BOOL isShareFlag;
/**
 是否商圈定向
 */
@property (nonatomic, assign)           BOOL lbs;
/**
 点击统计地址
 */
@property (nullable, nonatomic, copy)   NSString *link;
/**
 跳转类型：1、跳转落地页；2、下载APP
 */
@property (nonatomic, assign)           NSInteger linkType;
/**
 [忽略]开机大图跳过广告倒计时，单位ms
 */
@property (nonatomic, assign)           NSInteger loadingShowTime;
/**
 水印文字内容或logo地址
 */
@property (nullable, nonatomic, copy)   NSString *materialProvideSource;
/**
 是否独占
 */
@property (nonatomic, assign)           BOOL monopolize;
/**
 广告标题
 */
@property (nullable, nonatomic, copy)   NSString *name;
/**
 广告跳转打开类型 0:应用内打开 1:第三方浏览器打开 2:喜马拉雅活动 3:拨打电话  4;游戏中心
 */
@property (nonatomic, assign)           NSInteger openlinkType;
/**
 计划ID
 */
@property (nonatomic, assign)           long long planId;
/**
 开机巨幕联合霸屏新增字段，0：无声、1：有声
 */
@property (nonatomic, assign)           NSInteger playMode;
/**
 落地页地址
 */
@property (nullable, nonatomic, copy)   NSString *realLink;
/**
 CTR算法追踪字段
 */
@property (nullable, nonatomic, copy)   NSString *recSrc;
/**
 CTR算法追踪字段
 */
@property (nullable, nonatomic, copy)   NSString *recTrack;
/**
 轮播的计划ID，非轮播为负数
 */
@property (nonatomic, assign)           long long round;
/**
 开机大图不适用
 */
@property (nullable, nonatomic, copy)   NSString *scheme;
/**
 分享信息，可能为null
 */
@property (nullable, nonatomic, strong) XMIAdShareDataBean *shareData;
/**
 广告展示时携带Token
 */
@property (nullable, nonatomic, strong) NSArray<NSString *> *showTokens;
/**
 广告位样式ID 46:图文样式 47:纯音贴
 */
@property (nonatomic, assign)           NSInteger showstyle;
/**
 跳过广告提示倒计时出现时间，单位：秒
 */
@property (nonatomic, assign)           NSInteger skipAdTipAppearTime;
/**
 跳过广告文案提示类型
 */
@property (nonatomic, assign)           NSInteger skipTipStyle;
/**
 广告点击时需要数据上报
 */
@property (nullable, nonatomic, strong) NSArray<NSString *> *thirdClickStatUrls;
/**
 ab透传字段
 */
@property (nullable, nonatomic, copy) NSString *abPassThroughParams;
/**
 广告展示时需要数据上报(与thirdStatUrl)全部触发
 */
@property (nullable, nonatomic, strong) NSArray<NSString *> *thirdShowStatUrls;
/**
 广告展示时需要数据上报(与thirdShowStatUrls)全部触发
 */
@property (nullable, nonatomic, copy)   NSString *thirdStatUrl;
/**
 [重要]视频类素材的视频地址
 */
@property (nullable, nonatomic, copy)   NSString *videoUrl;
/**
 [重要]视频类素材的视频地址
 */
@property (nullable, nonatomic, copy)   NSString *videoCover;
/**
 广告音量百分比(范围0~100)
 */
@property (nonatomic, assign)           double volume;
/**
 H5资源包url
 */
@property (nullable, nonatomic, copy)   NSString *zipUrl;
/**
 信息流类广告的展示位置
 */
@property (nonatomic, assign)           long long position;
/**
 广告位置的ID
 */
@property (nonatomic, assign)           long long positionId;
/**
 是否需要上报真实曝光
 */
@property (nonatomic, assign)           BOOL isTrueExposure;
/**
 [忽略]子样式的素材图片地址
 */
@property (nullable, nonatomic, copy)   NSString *subCover;
/**
 [忽略]字样式的素材文字标题内容
 */
@property (nullable, nonatomic, copy)   NSString *subName;
/**
 下载弹窗样式类型，0：不弹窗；1，样式1；2，样式2
 */
@property (nonatomic, assign)           NSInteger downloadPopupStyle;
/**
 广告展示时是否需要携带Token
 */
@property (nonatomic, assign)           BOOL showTokenEnable;
/**
 广告点击时是否需要携带Token
 */
@property (nonatomic, assign)           BOOL clickTokenEnable;
/**
 纯音贴声音返回
 */
@property (nullable, nonatomic, copy)   NSString *soundUrl;
/**
 是否需要预加载
 */
@property (nonatomic, assign)           BOOL isNeedPreRequest;
/**
 预加载有效时间
 */
@property (nonatomic, assign)           NSInteger preRequestEffectTime;
/**
 是否开启视频半拼接webview
 */
@property (nonatomic, assign) BOOL enableContinuePlay;
/**
 是否开启视频半拼接appstore
 */
@property (nonatomic, assign) BOOL enableVideoJoinAppStore;
/**
 待下载app名
 */
@property (nonatomic, copy) NSString *downloadAppName;

@property (nonatomic, strong) NSString *wxMiniProgramId;

/// 由各业务线补充
@property (nullable, nonatomic, copy) NSDictionary *businessExtraInfo;

// 首页混排-猜你喜欢
@property (nullable, nonatomic, copy) NSDictionary *trackInfoMap;

/// 行动按钮文案
@property (nullable, nonatomic, copy) NSString *clickTitle;

/// 预览广告点击是否上报
@property (nonatomic, assign) BOOL clickReportFlag;

/// 自定义属性
///是否是预览广告
@property (nonatomic, assign) BOOL isAdPreview;

/**
 自动关闭时间 x秒后自动关闭 单位ms
 */
@property (nonatomic, assign) NSInteger adShowTime;

/**
 自动重新请求间隔  单位ms
 */
@property (nonatomic, assign) NSInteger adIntervalTime;

/**
 点击区域 1:支持点击任意区域响应；2:仅点击按钮区域响应
 */
@property (nonatomic, assign) NSInteger clickableAreaType;

/**
 是否显示主播标签
 */
@property (nonatomic, assign) BOOL enableAnchorRec;

@property (nonatomic, copy) NSArray *tagArray;

// 内容推广一期 跳转声音字段
@property (nonatomic, assign) long long promoteTrackId;
@property (nonatomic, assign) NSInteger clickJumpType;

// 内容推广二期，广告跳转声音Id
@property (nonatomic, assign) long long jumpTrackId;
// 多长时间自动弹起广告弹窗
@property (nonatomic, assign) float autoJumpTime;
// 内容推广，播放页底部视图标题
@property (nonatomic, copy) NSString *contentAdTitle;

// 播音类广告弹窗字段
//弹窗提示样式,0-不出弹窗提示，1-弹窗提示样式1，2-弹窗提示样式2
@property (nonatomic, copy) NSString *popReminderStyle;
@property (nonatomic, copy) NSString *popReminderText;
// 声音流彩蛋弹窗样式
@property (nonatomic, copy) NSString *soundAggType;

//过期时间 毫秒
@property (nonatomic, assign) long long adCacheExpireTime;

//视频可关闭时间
@property (assign, nonatomic) NSInteger videoDuration;

// 视频总时长
@property (assign, nonatomic) NSInteger unlockTime;

// 视频总时长
@property (nonatomic, copy) NSString *iconUrl;

//广告主

@property (nullable, nonatomic, copy) NSString *providerAvatar;

@property (nullable, nonatomic, copy) NSString *providerName;


// 下挂浮层广告gif
@property (nullable, nonatomic, copy) NSString *dynamicImage;

@property (nullable, nonatomic, copy) NSString *hightingDynamicPicUrl;

@property (nullable, nonatomic, copy) NSString *backupCover;

@property (nonatomic, assign) double videoDurationTime;
//是否支持dsp摇一摇
@property (nonatomic, assign) BOOL enableShake;
//激励视频完播自动跳转落地页
@property (nonatomic, assign) BOOL videoAutoJump;

@property (nonatomic, copy) NSString *adTextMark;

@property (nonatomic, copy) NSString *ubtReportMap;

@property (nonatomic, copy) NSString *clickRequestMethod;

@property (nonatomic, copy) NSString *clickPostMap;

// 是否需要一键留资
@property (nonatomic, assign) BOOL oneClickLeaveCash;

// webview缓存
@property (nonatomic, assign) NSInteger preLoadH5LandingPage;

// 落地页视频地址新增物料字段
@property (nonatomic, copy) NSString *landVideoUrl;
// 落地页视频logo
@property (nonatomic, copy) NSString *downloadAppLogo;
// 落地页自动拉起时间新增字段 ms, 默认5000
@property (nonatomic, assign) double autoShowWebTime;

//坑位
@property (nonatomic, assign) NSInteger internalHomeGuessIndex;

//是否是内循环物料
@property (nonatomic, assign) BOOL internalCirculationFlag;

@property (nonatomic, assign) NSInteger actualStayTime;
@property (nonatomic, assign) NSInteger tipStayTime;
@property (nonatomic, assign) float dpRetrySecond;

// 用于shownotes预览
@property (nonatomic, copy) NSString *trackPreviewUrl;

@property (nonatomic, assign) NSInteger againPopupType;

@property (nonatomic, assign) NSInteger againDuration;

@property (nonatomic, assign) BOOL homePicTitleDown;

@property (nonatomic, assign) BOOL needDedupClick;

// https://alidocs.dingtalk.com/i/nodes/YMyQA2dXW793xlpjh9GQozPRJzlwrZgb?corpId=ding51f195092fd77474
@property (nonatomic, assign) NSInteger reportClickTime;

@end

@interface XMIAdBootUpBean : NSObject
/**
 轮播图片地址
 */
@property (nullable, nonatomic, copy) NSArray<NSString *> *carouseCovers;
/**
 图片地址
 */
@property (nullable, nonatomic, copy) NSString *cover;
/**
 展示顺序，值越小越先展示
 */
@property (nonatomic, assign)         NSInteger order;
/**
 0: 半屏视频，1: 全屏视频，2: 全屏图片，3: 半屏轮播
 */
@property (nonatomic, assign)         NSInteger type;
/**
 视频地址
 */
@property (nullable, nonatomic, copy) NSString *videoUrl;

@end

@interface XMIAdShareDataBean : NSObject
/**
 是否是第三方链接
 */
@property (nonatomic, assign)         BOOL isExternalUrl;
/**
 分享内容
 */
@property (nullable, nonatomic, copy) NSString *linkContent;
/**
 分享图片地址
 */
@property (nullable, nonatomic, copy) NSString *linkCoverPath;
/**
 分享标题
 */
@property (nullable, nonatomic, copy) NSString *linkTitle;
/**
 分享链接地址
 */
@property (nullable, nonatomic, copy) NSString *linkUrl;

@end

NS_ASSUME_NONNULL_END
