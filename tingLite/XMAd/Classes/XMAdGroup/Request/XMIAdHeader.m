//
//  XMIAdCookie.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/4.
//

#import "XMIAdHeader.h"
#import <sys/sysctl.h>
#import <sys/stat.h>
#import <sys/mount.h>
#import <WebKit/WKWebView.h>
#import "XMIAdMacro.h"
#import "XMICommonUtils.h"
#import "NSString+XMIUtils.h"
#import "NSObject+XMIModel.h"
#import "XMIAdConfigData.h"
#import "XMINetworkReachability.h"
#import "XMIAdManager.h"
#import <CoreTelephony/CTCarrier.h>
#import <CoreTelephony/CTTelephonyNetworkInfo.h>
#import <XMCommonUtil/XMDeviceInfo.h>
#define MIB_SIZE 2
@interface XMIAdHeader ()

@property (nonatomic, strong) WKWebView *webView;
@property (nonatomic, strong) XMINetworkReachability *hostReachability;

@end

@implementation XMIAdHeader

typedef NS_ENUM(NSInteger, XMIAdEnvironmentID) {
    XMIAdEnvironmentIDOnline = 1,  // 生产环境
    XMIAdEnvironmentIDTest = 4,    // 测试环境
    XMIAdEnvironmentIDUAT = 6      // UAT环境
};

+ (instancetype)sharedInstance {
    static XMIAdHeader *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
        [instance commonInit];
    });
    
    return instance;
}

- (void)commonInit {
    _NSUP = @"";
    _impl = [XMICommonUtils bundleId];
    _netMode = @"WIFI";
    _osversion = [XMICommonUtils systemVersion];
    //caid
    self.bootTimeInSec = [XMIAdHeader getBootTimeInSec];
    self.carrierInfo = [XMIAdHeader getCarrierInfo];
    self.language2 = [XMIAdHeader getLanguage2];
    self.memory = [XMIAdHeader getMemory];
    self.disk = [XMIAdHeader getDisk];
    self.timeZoneInSec = [XMIAdHeader getTimeZoneInSec];
    self.mnt_id =  [XMIAdHeader getMntId];
    [self observeNetwork];
}

- (NSString *)XUM {
    if (_XUM == nil) {
        _XUM = [self getDeviceId];
    }
    
    return _XUM;
}

- (NSString *)deviceModel {
    if (_deviceModel == nil) {
        _deviceModel = [XMICommonUtils deviceReadableModel];
    }
    
    return _deviceModel;
}

- (NSString *)idfa {
    if (_idfa == nil) {
        _idfa = [self getDeviceId];
    }
    return _idfa;
}

- (NSString *)md5IDFA {
    if (_md5IDFA == nil) {
        _md5IDFA = [self getMd5IDFA];
    }
    return _md5IDFA;
}

- (NSString *)deviceId {
    if (_deviceId == nil) {
        _deviceId = [self getDeviceId];
    }
    return _deviceId;
}

- (NSString *)machinetype {
    if (_machinetype == nil) {
        _machinetype = [XMICommonUtils deviceType];
    }
    return _machinetype;
}

- (NSString *)cOperator {
    NSString *oper = [XMICommonUtils deviceOperator];
    return [oper xmi_URLEncodedString];
}

- (NSString *)channel {
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerGetChannel)]) {
        return [manager.delegate managerGetChannel];
    }
    
    return [XMICommonUtils channel];
}

- (NSString *)netType {
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerGetNetType)]) {
        return [manager.delegate managerGetNetType];
    }
    return @"";
}

/// 1-单列 2-双列
- (NSInteger)findNativeVersion
{
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerFindNativeVersion)]) {
        return [manager.delegate managerFindNativeVersion];
    }
    return 0;
}


- (NSString *)resolution {
    if (_resolution != nil) {
        return _resolution;
    }
    
    CGSize r = [XMICommonUtils deviceResolution];
    NSString *rStr = [NSString stringWithFormat:@"%d,%d", (int)r.width, (int)r.height];
    _resolution = [rStr xmi_URLEncodedString];
    return _resolution;
}

- (NSString *)userAgent {
    if (_userAgent != nil) {
        return _userAgent;
    }
    
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerGetCustomUserAgent)]) {
        _userAgent = [manager.delegate managerGetCustomUserAgent];
    }
    if (_userAgent == nil) {
        _userAgent = [NSString stringWithFormat:@"ting_v%@_%@(CFNetwork, %@ %@, %@)",
                      [XMICommonUtils appVersion],
                      @"c5",
                      [XMICommonUtils systemName],
                      [XMICommonUtils systemVersion],
                      [XMICommonUtils deviceModel]];
    }
    return _userAgent;
}

- (NSString *)systemUserAgent {
    if (_systemUserAgent != nil) {
        return _systemUserAgent;
    }
    
    _systemUserAgent = [XMIAdConfigData stringConfigForKey:XMI_CONFIG_SYSTEMUA];
    // 版本变化了，需要更新UA缓存
    NSString *version = [[[UIDevice currentDevice] systemVersion] stringByReplacingOccurrencesOfString:@"." withString:@"_"];
    if (!_systemUserAgent || ![_systemUserAgent containsString:version]) {
        _systemUserAgent = [NSString stringWithFormat:@"Mozilla/5.0 (iPhone; CPU iPhone OS %@ like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148", version];
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self requestSystemUserAgent];
        });
    }
    
    return _systemUserAgent;
}

- (int)jailbreak {
    return [XMICommonUtils isJailBreak] ? 1 : 0;
}

- (NSString *)hwmodel {
    if (_hwmodel == nil) {
        _hwmodel = [XMIAdHeader getHWModel];
    }
    
    return _hwmodel;
}

- (NSString *)hwname {
    if (_hwname == nil) {
        _hwname = [self getHWName];
    }
    
    return _hwname;
}

- (NSString *)hwmachine {
    if (_hwmachine == nil) {
        _hwmachine = [[XMIAdHeader getHWMachine] xmi_URLEncodedString];
    }
    
    return _hwmachine;
}

- (NSString *)phoneCountryCode {
    if (_phoneCountryCode == nil) {
        _phoneCountryCode = [XMIAdHeader getCountryCode];
    }
    
    return _phoneCountryCode;
}

- (NSString *)T2 {
    if (_T2 == nil) {
        _T2 = [self getT2];
    }
    
    return _T2;
}

- (NSString *)T3 {
    if (_T3 == nil) {
        _T3 = [self getT3];
    }
    
    return _T3;
}

- (NSString *)T8 {
    if (_T8 == nil) {
        _T8 = [self getT8];
    }
    
    return _T8;
}

- (XMIHardwareInfo *)hardwareInfo {
    if (_hardwareInfo == nil) {
        _hardwareInfo = [[XMIHardwareInfo alloc] init];
    }
    
    return _hardwareInfo;
}

- (NSString *)boot_mark {
    if (_boot_mark == nil) {
        _boot_mark = [self getBootMark];
    }
    return _boot_mark;
}

- (NSString *)ipv6String
{
    if (!_ipv6String) {
        _ipv6String = [XMICommonUtils getIPV6String:NO];
    }
    return _ipv6String;
}


- (NSString *)cookieValue {
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    NSMutableDictionary *cookieDic = [[NSMutableDictionary alloc] init];
    // 取不到，暂时不报
//    cookieDic[@"NSUP"] = self.NSUP;
    cookieDic[[NSString stringWithFormat:@"%ld&_device", (long)[self getEnvironmentID]]] = [NSString stringWithFormat:@"%@&%@&%@", self.machinetype, self.deviceId, [XMICommonUtils appVersion]];
    cookieDic[@"XUM"] = self.XUM;
    cookieDic[@"c-oper"] = self.cOperator;
    cookieDic[@"channel"] = self.channel;
    cookieDic[@"device_model"] = self.deviceModel;
    cookieDic[@"idfa"] = self.idfa;
    cookieDic[@"impl"] = self.impl;
    cookieDic[@"net-mode"] = self.netMode;
    cookieDic[@"osversion"] = self.osversion;
    cookieDic[@"system_ua"] = [self.systemUserAgent xmi_URLEncodedString];
    cookieDic[@"res"] = self.resolution;
    cookieDic[@"jailbreak"] = [NSString stringWithFormat:@"%d", self.jailbreak];
    cookieDic[@"machinetype"] = self.machinetype;
    cookieDic[@"hwmodel"] = self.hwmodel;
    cookieDic[@"hwname"] = self.hwname;
    cookieDic[@"hwmachine"] = self.hwmachine;
    cookieDic[@"T2"] = self.T2;
    cookieDic[@"T3"] = self.T3;
    cookieDic[@"T8"] = self.T8;
    cookieDic[@"boot_mark"] = self.boot_mark;
    cookieDic[@"update_mark"] = self.T3;
    cookieDic[@"hardwareInfo"] = [[self.hardwareInfo xmi_modelToJSONString] xmi_URLEncodedString];
    cookieDic[@"phoneCountryCode"] = self.phoneCountryCode;
    cookieDic[@"IPV6"] = self.ipv6String;
    if (manager.delegate
        && [manager.delegate respondsToSelector:@selector(managerGetUid)]
        && [manager.delegate respondsToSelector:@selector(managerGetToken)]) {
        NSString *uid = [manager.delegate managerGetUid];
        NSString *token = [manager.delegate managerGetToken];
        if (uid != nil && uid.length > 0 && token != nil && token.length > 0) {
            cookieDic[[NSString stringWithFormat:@"%ld&_token", (long)[self getEnvironmentID]]] = [NSString stringWithFormat:@"%@&%@", uid, token];
        }
    }
    // 对应配置的App的安装状态
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerGetAppInstallList)]) {
        NSString *applist = [manager.delegate managerGetAppInstallList];
        if (applist.length) {
            cookieDic[@"adPI"] = applist;
        }
    }
    
    
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerGetCustomCookie)]) {
        NSDictionary *customCookieDic = [manager.delegate managerGetCustomCookie];
        if (customCookieDic != nil) {
            [cookieDic addEntriesFromDictionary:customCookieDic];
        }
    }
    
    NSMutableString *cookieString = [NSMutableString string];
    [cookieDic enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, id  _Nonnull obj, BOOL * _Nonnull stop) {
        [cookieString appendFormat:@"%@=%@; ", (NSString *)key, (NSString *)obj];
    }];
    // 去掉最后多余的"; "
    [cookieString deleteCharactersInRange:NSMakeRange(cookieString.length - 2, 2)];
    
    return cookieString;
}

- (NSDictionary *)headers {
    // headers
    NSMutableDictionary *headers = [[NSMutableDictionary alloc] init];
    headers[@"Cookie"] = [[XMIAdHeader sharedInstance] cookieValue];
    headers[@"User-Agent"] = [XMIAdHeader sharedInstance].userAgent;
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerGetCustomHeaders)]) {
        NSDictionary *customHeaders = [manager.delegate managerGetCustomHeaders];
        if (customHeaders != nil) {
            [headers addEntriesFromDictionary:customHeaders];
        }
    }
    
    return headers;
}

/**
 获取环境ID
 */
- (XMIAdEnvironmentID)getEnvironmentID {
    XMIAdEnvironment env = [XMIAdManager sharedInstance].environment;
    XMIAdEnvironmentID envID = XMIAdEnvironmentIDOnline;
    switch (env) {
        case XMIAdEnvironmentDefault:
            envID = XMIAdEnvironmentIDOnline;
            break;
        case XMIAdEnvironmentTest:
            envID = XMIAdEnvironmentIDTest;
            break;
        case XMIAdEnvironmentUAT:
            envID = XMIAdEnvironmentIDUAT;
            break;
    }
    return envID;
}

/**
 获取设备id
 */
- (NSString *)getDeviceId {
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerGetCustomDeviceId)]) {
        NSString *anID = [manager.delegate managerGetCustomDeviceId];
        if (anID != nil && anID.length > 0) {
            return anID;
        }
    }
    
    return [XMICommonUtils deviceId];
}

- (NSString *)getMd5IDFA {
    XMQueryIDFAType idfaType = XMQueryIDFATypeEmpty;
    NSString *idfa = [XMDeviceInfo cachedIDFAType:&idfaType];
    if (idfa.length) {
        return [idfa xmi_md5String];
    }
    return @"";
}


/**
 获取web UA
 */
- (void)requestSystemUserAgent {
    if (self.webView != nil) {
        return;
    }
    
    self.webView = [[WKWebView alloc] initWithFrame:CGRectZero];
    @weakify(self)
    [self.webView evaluateJavaScript:@"navigator.userAgent" completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        @strongify(self)
        if (!self) {
            return;
        }
        
        if ([result isKindOfClass:[NSString class]]) {
            self.systemUserAgent = result;
            [XMIAdConfigData updateConfig:result forKey:XMI_CONFIG_SYSTEMUA];
        }
    }];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(10 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        self.webView = nil;
    });
}

+ (NSString *)getHWModel {
    size_t size;
    sysctlbyname("hw.model", NULL, &size, NULL, 0);
    char *model = malloc(size);
    sysctlbyname("hw.model", model, &size, NULL, 0);
    NSString *result = [NSString stringWithUTF8String:model];
    free(model);
    return result ? : @"";
}

- (NSString *)getHWName {
    NSString *name = [XMICommonUtils deviceName];
    return [name xmi_md5String];
}

+ (NSString *)getHWMachine {
    size_t size;
    sysctlbyname("hw.machine", NULL, &size, NULL, 0);
    char *machine = malloc(size);
    sysctlbyname("hw.machine", machine, &size, NULL, 0);
    NSString *result = [NSString stringWithUTF8String:machine];
    free(machine);
    return result ? : @"";
}

+ (NSString *)getCountryCode {
    NSLocale *currentLocale = [NSLocale currentLocale];
    NSString *countryCode = [currentLocale objectForKey:NSLocaleCountryCode];
    return countryCode ? : @"";
}

- (NSString *)getT2 {
    NSString *timeString = nil;
    struct stat sb;
    NSString *enCodePath = @"L3Zhcg==";
    NSData *data = [[NSData alloc] initWithBase64EncodedString:enCodePath options:0];
    NSString *dataString = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    const char *dePath = [dataString cStringUsingEncoding:NSUTF8StringEncoding];
    if (stat(dePath, &sb) != -1) {
        timeString = [NSString stringWithFormat:@"%d.%d", (int)sb .st_ctimespec.tv_sec, (int)sb.st_ctimespec.tv_nsec];
    }
    else {
        timeString = @"0.0";
    }
    return timeString ? : @"";
}

- (NSString *)getT3 {
    NSString *timeString = nil;
    struct stat sb;
    NSString *enCodePath = @"L3Zhci9tb2JpbGU=";
    NSData *data = [[NSData alloc] initWithBase64EncodedString:enCodePath options:0];
    NSString *dataString = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    const char* dePath = [dataString cStringUsingEncoding:NSUTF8StringEncoding];
    if (stat(dePath, &sb) != -1) {
        timeString = [NSString stringWithFormat:@"%d.%d", (int)sb.st_ctimespec.tv_sec, (int)sb.st_ctimespec.tv_nsec];
    }
    else {
        timeString = @"0.0";
    }
    return timeString ? : @"";
}

- (NSString *)getT8 {
    NSString *enCodePath = @"L3ByaXZhdGUvdmFy";
    NSString *timeString = [XMIHardwareInfo fileCreateTimeWithEncodePath:enCodePath];
    if (timeString == nil) {
        timeString = @"0.0";
    }
    return timeString ? : @"";
}

- (NSString *)getBootMark {
    NSString *timeString = nil;
    int mib[MIB_SIZE];
    size_t size;
    struct timeval boottime;
    
    mib[0] = CTL_KERN;
    mib[1] = KERN_BOOTTIME;
    size = sizeof(boottime);
    if (sysctl(mib, MIB_SIZE, &boottime, &size, NULL, 0) != -1) {
        timeString = [NSString stringWithFormat:@"%d.%d", (int)boottime.tv_sec, (int)boottime.tv_usec];
    }
    return timeString ?: @"";
}

/**
 监听网络
 */
- (void)observeNetwork {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(reachabilityChanged:) name:kXMINetworkReachabilityChangedNotification object:nil];
    
    XMINetworkReachability *reachability = [XMINetworkReachability reachabilityWithHostName:@"www.baidu.com"];
    self.hostReachability = reachability;
    [reachability startNotifier];
}

/**
 网络状态变化
 */
- (void)reachabilityChanged:(NSNotification *)notification {
    XMINetworkReachability *curReach = notification.object;
    XMINetworkStatus netStatus = [curReach currentReachabilityStatus];
    switch (netStatus) {
        case XMINetworkStatusNotReachable:
            break;
        case XMINetworkStatusUnknown:
            break;
        case XMINetworkStatusWWAN2G:
            _netMode = @"2G";
            break;
        case XMINetworkStatusWWAN3G:
            _netMode = @"3G";
            break;
        case XMINetworkStatusWWAN4G:
            _netMode = @"4G";
            break;
        case XMINetworkStatusWWAN5G:
            _netMode = @"5G";
            break;
        case XMINetworkStatusWiFi:
            _netMode = @"WIFI";
            break;
            
        default:
            break;
    }
}

- (void)cleanOberver {
    if (self.hostReachability != nil) {
        [self.hostReachability stopNotifier];
    }
    [[NSNotificationCenter defaultCenter] removeObserver:self name:kXMINetworkReachabilityChangedNotification object:nil];
}

- (void)dealloc {
    [self cleanOberver];
}

+ (NSString *)getBootTimeInSec
{
    struct timeval boottime;
    size_t len = sizeof(boottime);
    int mib[2] = { CTL_KERN, KERN_BOOTTIME };
    if( sysctl(mib, 2, &boottime, &len, NULL, 0) < 0 ) {
        return @"0";
        
    }
    return [NSString stringWithFormat:@"%ld",boottime.tv_sec];
}
+ (NSString* )getCarrierInfo {
#if TARGET_IPHONE_SIMULATOR
    return @"SIMULATOR";
#else
    static dispatch_queue_t _queue;
    static dispatch_once_t once;
    dispatch_once(&once, ^{
        _queue = dispatch_queue_create([[NSString stringWithFormat:@"com.carr.%@"
                                         , self] UTF8String], NULL);
    });
    __block NSString * carr = nil;
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
    dispatch_async(_queue, ^(){
        CTTelephonyNetworkInfo *info = [[CTTelephonyNetworkInfo alloc] init];
        CTCarrier *carrier = nil;
        if ([[[UIDevice currentDevice] systemVersion] floatValue] >= 12.1) {
            if ([info respondsToSelector:@selector
                 (serviceSubscriberCellularProviders)]) {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wunguarded-availability-new"
                NSArray *carrierKeysArray =
                [info.serviceSubscriberCellularProviders
                    .allKeys sortedArrayUsingSelector:@selector(compare:)];
                carrier = info.serviceSubscriberCellularProviders
                [carrierKeysArray.firstObject];
                if (!carrier.mobileNetworkCode) {
                    carrier = info.serviceSubscriberCellularProviders
                    [carrierKeysArray.lastObject];
                }
#pragma clang diagnostic pop
            }
        }
        if(!carrier) {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
            carrier = info.subscriberCellularProvider;
#pragma clang diagnostic pop
        }
        if (carrier != nil) {
            NSString *networkCode = [carrier mobileNetworkCode];
            NSString *countryCode = [carrier mobileCountryCode];
            if (countryCode && [countryCode isEqualToString:@"460"] &&
                networkCode
                ) {
                if ([networkCode isEqualToString:@"00"] ||
                    [networkCode isEqualToString:@"02"] ||
                    [networkCode isEqualToString:@"07"] ||
                    [networkCode isEqualToString:@"08"]) {
                    carr= @"中国移动";
                }
                if ([networkCode isEqualToString:@"01"]
                    || [networkCode isEqualToString:@"06"]
                    || [networkCode isEqualToString:@"09"]) {
                    carr= @"中国联通";
                }
                if ([networkCode isEqualToString:@"03"]
                    || [networkCode isEqualToString:@"05"]
                    || [networkCode isEqualToString:@"11"]) {
                    carr= @"中国电信";
                }
                if ([networkCode isEqualToString:@"04"]) {
                    carr= @"中国卫通";
                }
                if ([networkCode isEqualToString:@"20"]) {
                    carr= @"中国铁通";
                }
            }else {
                carr = [carrier.carrierName copy];
            }
        }
        if (carr.length <= 0) {
            carr = @"unknown";
        }
        dispatch_semaphore_signal(semaphore);
    });
    dispatch_time_t t = dispatch_time(DISPATCH_TIME_NOW, 0.5* NSEC_PER_SEC);
    dispatch_semaphore_wait(semaphore, t);
    return [carr copy];
#endif
}


+ (NSString *)getLanguage2 {
    NSString *language;
    NSLocale *locale = [NSLocale currentLocale];
    if ([NSLocale preferredLanguages].count == 0) {
        language = [locale objectForKey:NSLocaleLanguageCode];
    } else {
        language = [[NSLocale preferredLanguages] firstObject];
        
    }
    return language;
}

+ (NSString *)getMemory
{
    return [NSString stringWithFormat:@"%lld", [NSProcessInfo processInfo].physicalMemory];
}

+ (NSString *)getDisk
{
    int64_t space = -1;
    NSError *error = nil;
    NSDictionary *attrs = [[NSFileManager defaultManager] attributesOfFileSystemForPath:NSHomeDirectory() error:&error];
    if (!error) {
    space = [[attrs objectForKey:NSFileSystemSize] longLongValue];
    }
    if(space < 0) {
    space = -1;
    }
    return [NSString stringWithFormat:@"%lld",space];
}


+ (NSString *)getTimeZoneInSec {
    NSInteger offset = [NSTimeZone systemTimeZone].secondsFromGMT;
    return [NSString stringWithFormat:@"%ld",(long)offset];
}

+ (NSString *)getOsUpdateTime {
    NSString *enCodePath = @"L3Zhci9tb2JpbGUvTGlicmFyeS9Vc2VyQ29uZmlndXJhdGlvblByb2ZpbGVzL1B1YmxpY0luZm8vTUNNZXRhLnBsaXN0";
    NSString *timeString = [XMIHardwareInfo fileCreateTimeWithEncodePath:enCodePath];
    if (timeString == nil) {
        timeString = @"0.0";
    }
    return timeString ? timeString : @"";
}

+ (NSString *)getMntId
{
    struct statfs buf;
    statfs("/", &buf);
    char* prefix = "com.apple.os.update-";
    if(strstr(buf.f_mntfromname, prefix)) {
        return [NSString stringWithFormat:@"%s", buf.f_mntfromname + strlen(prefix)];
    }
    return @"";
}


@end


@implementation XMIHardwareInfo

- (instancetype)init {
    self = [super init];
    if (self) {
        [self commonInit];
    }
    return self;
}

- (void)commonInit {
    self.timeZone = [self getTimeZone];
    self.equipmentName = [self getEquipmentName];
    self.hddGb = [self getDiskSize];
    self.latestStartTime = [self getSystemUptime];
    self.language = [self getLanguage];
    self.cpuCores = [self getCpuCores];
    self.ramGb = [self getRamSize];
    self.batteryStatus = [self getBatteryStatus];
    self.batteryPower = [self getBatteryLevel];
    self.osUpdateTime = [XMIAdHeader getOsUpdateTime];
    self.phoneCountryCode = [self getPhoneCountryCode];
}

- (void)update {
    self.batteryPower = [self getBatteryLevel];
    self.batteryStatus = [self getBatteryStatus];
}

- (NSInteger)getTimeZone {
    NSTimeZone *localTimeZone = [NSTimeZone localTimeZone];
    NSInteger interval = [localTimeZone secondsFromGMT];
    NSInteger hour = interval / 3600;
    return hour;
}

- (NSString *)getEquipmentName {
    return [XMICommonUtils deviceName];
}

- (NSString *)getLanguage {
    return [[NSLocale preferredLanguages] firstObject];
}

/**
 开机时间
 */
- (NSString *)getSystemUptime {
    NSDate *systemUptime = [[NSDate date] dateByAddingTimeInterval:(-[NSProcessInfo processInfo].systemUptime)];
    NSTimeInterval uptimestamp = [systemUptime timeIntervalSince1970];
    return [NSString stringWithFormat:@"%lf", uptimestamp];
}

- (NSString *)getCpuCores {
    unsigned int ncpu;
    size_t len = sizeof(ncpu);
    sysctlbyname("hw.ncpu", &ncpu, &len, NULL, 0);
    return [NSString stringWithFormat:@"%u", ncpu];
}

- (NSString *)getRamSize {
    long long size = [NSProcessInfo processInfo].physicalMemory/(1024 * 1024 * 1024);
    return [NSString stringWithFormat:@"%lld", size];
}

- (NSString *)getDiskSize {
    struct statfs buf;
    unsigned long long freeSpace = -1;
    if (statfs("/var", &buf) >= 0) {
        freeSpace = (unsigned long long)(buf.f_bsize * buf.f_blocks);
    }
    NSString *space = [NSString stringWithFormat:@"%.0f", freeSpace/(1000*1000*1000.0)];
    return space;
}

- (NSString *)getBatteryStatus {
    [UIDevice currentDevice].batteryMonitoringEnabled = YES;
    UIDeviceBatteryState batteryState = [UIDevice currentDevice].batteryState;
    return batteryState == UIDeviceBatteryStateCharging ? @"1" : @"0";
}

- (NSString *)getBatteryLevel {
    [UIDevice currentDevice].batteryMonitoringEnabled = YES;
    float batteryLevel = [UIDevice currentDevice].batteryLevel;
    return [NSString stringWithFormat:@"%.2f", batteryLevel];
}

- (NSString *)getPhoneCountryCode {
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerGetPhoneCountryCode)]) {
        return [manager.delegate managerGetPhoneCountryCode];
    }
    return @"86";
}

+ (NSString *)fileCreateTimeWithEncodePath:(NSString *)encodeStr {
    NSString *result = nil;
    NSData *data = [[NSData alloc] initWithBase64EncodedString:encodeStr options:0];
    NSString *dataString = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    NSError *error = nil;
    NSDictionary *fileAttributes = [[NSFileManager defaultManager] attributesOfItemAtPath:dataString error:&error];
    if (fileAttributes) {
        id singleAttibute = [fileAttributes objectForKey:NSFileCreationDate];
        if ([singleAttibute isKindOfClass:[NSDate class]]) {
            NSDate *dataDate = singleAttibute;
            result = [NSString stringWithFormat:@"%f",[dataDate timeIntervalSince1970]];
        }
    }
    return result;
}

@end
