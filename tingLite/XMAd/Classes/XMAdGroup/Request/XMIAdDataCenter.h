//
//  XMIAdDataCenter.h
//  XMAd
//  广告数据中心 请求数据及缓存处理
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/22.
//

#import <Foundation/Foundation.h>
#import "XMIAdRelatedData.h"
#import "XMIAdRequestProtocol.h"

NS_ASSUME_NONNULL_BEGIN
@class XMIAdRespAdData,XMIAdBean;
@interface XMIAdDataCenter : NSObject

/**
 初始化adx，会发请求
 */
+ (void)initAdx:(void (^)(NSError * _Nullable error))dataHandler;

/**
 信息流数据
 */
+ (void)getFeedAdDataWithPositionName:(NSString *)positionName
                          dataHandler:(void (^)(NSArray<XMIAdRelatedData *> * _Nullable, NSError * _Nullable))dataHandler;

/**
 通用广告请求数据
 */
+ (id<XMIAdRequestProtocol>)adDataRequestWithPositionName:(NSString *)positionName props:(nullable NSDictionary *)props dataHandler:(void (^)(NSArray<XMIAdRelatedData *> * _Nullable, NSError * _Nullable))dataHandler;
/**
 橱窗样式数据
 */
+ (NSArray<XMIAdRelatedData *> *)getShopWindowAdData:(long long)responseId withSlotId:(long long)slotId;

/**
 基本填充的addata，用于请求失败时上报
 */
+ (XMIAdRelatedData *)getEmptyFeedAdData;

/**
 获取第三方超时时间
 */
+ (NSInteger)getThirdTimeoutMS;
/**
 获取首页信息流第三方超时时间
 */
+ (NSInteger)getHomeFeedThirdTimeoutMS;

/**
 获取自渲染比例
 */
+ (CGFloat)getAdWidthHeightRatio;

/**
 广告视频在任何情况下都自动播放（不考虑缓存及4g）
 */
+ (BOOL)getAdAlwaysPlayVideoValue;

+ (long long)getSlotIdByPositionName:(NSString *)positionName;


/// 获取广告关闭区域的配置
/// @param positionId 广告positionId
+ (NSValue *)closeAreaPadding:(NSString *)positionId;

/**
 对应广告位是否需要打开负反馈开关
 */
+ (BOOL)getCloseAdNeedDialogByPositionId:(NSString *)positionId;

/**
 广告数据返回转relatedData
 */
+ (NSArray<XMIAdRelatedData *> *)adDataFromResponse:(XMIAdRespAdData *)respData;

+ (XMIAdRelatedData *)relatedDataFromAdBean:(XMIAdBean *)adBean;

+ (BOOL)shouldCompressHeader;

+ (BOOL)shouldReportDSPSDK;

+ (NSDictionary *)trackVentConfig;

+ (NSTimeInterval)rewardVideoSKipTime;

+ (BOOL)rewardAutoJump;

+ (BOOL)requestUseHttps;

+ (NSTimeInterval)fullVideoWebPageAutoOpenTime;

+ (NSTimeInterval)webTopToastShowTime;

+ (NSString *)adCommonShakeTips;

+ (NSDictionary *)aigcCacheDealConfig;

+ (NSInteger)dedupClickInterval;

@end

NS_ASSUME_NONNULL_END
