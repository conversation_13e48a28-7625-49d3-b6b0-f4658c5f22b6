//
//  XMIAdCacheData.h
//  XMAd
//  广告缓存对象
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/3.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XMIAdCacheData : NSObject

@property (nonatomic, assign) long long responseId;
@property (nonatomic, assign) long long adid;
@property (nonatomic, assign) long long createTime;
@property (nonatomic, assign) long long updateTime;
@property (nonatomic, assign) NSInteger showstyle;
@property (nonatomic, assign) NSInteger adtype;
@property (nonatomic, assign) long long slotId;
/**
 序号 0开始
 */
@property (nonatomic, assign) NSUInteger adno;
/**
 是否已被使用过 0-否 1-是
 */
@property (nonatomic, assign) int used;
@property (nonatomic, strong) NSData *adData;

@end

NS_ASSUME_NONNULL_END
