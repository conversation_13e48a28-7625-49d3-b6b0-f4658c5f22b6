//
//  XMIAdStatusData.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/30.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class XMIAdStatusInnerData;

@interface XMIAdStatusData : NSObject

@property (nonatomic, assign) long long slotId;
@property (nonatomic, assign) long long responseId;
@property (nonatomic, assign) long long positionId;
@property (nonatomic, copy) NSString *sdkVersion;
@property (nonatomic, copy) NSString *adAppId;
@property (nonatomic, copy) NSString *positionName;
@property (nonatomic, copy) NSString *data;

@end

@interface XMIAdStatusInnerData : NSObject

@property (nonatomic, assign) long long adid;
@property (nonatomic, assign) NSInteger sdkType;
@property (nonatomic, assign) long long useTime;
@property (nonatomic, assign) NSInteger status;
@property (nonatomic, assign) NSInteger backStatus;
@property (nonatomic, copy) NSString *dspPositionId;

@end

typedef NS_ENUM(NSInteger, XMIAdLoadStatus) {
    XMIAdLoadStatusNone = 0,            // 空状态，用于占位
    XMIAdLoadStatusSuccess = 1,         // 请求并展示成功
    XMIAdLoadStatusPreloadSuccess = 2,  // 预加载物料展示成功（开机广告）
    XMIAdLoadStatusUseCache = 3,        // 展示了缓存的广告
    XMIAdLoadStatusResourceFail = 1001, // 加载物料物料失败，加载视频或图片超时或失败
    XMIAdLoadStatusNoShow = 1002,       // 物料有返回未展示，有返回，并且每超时，最终没有展示
    XMIAdLoadStatusReachLimit = 1003,   // 因频次控制不展示
    XMIAdLoadStatusNoShowChance = 1004, // 没有展示机会
    XMIAdLoadStatusCacheLoadFail = 1005,// 缓存广告，加载失败
    XMIAdLoadStatusOtherTimeout = 3001, // 请求第三方SDK超时
    XMIAdLoadStatusOtherNoData = 4001,  // 请求第三方SDK未返回物料
    XMIAdLoadStatusRequestFail = 5001,  // 请求广告接口超时或出错 adid用90
    XMIAdLoadStatusNoOtherLoad = 6001   // 未请求第三方SDK，缓存中有广告数据，直接获取了缓存没请求SDK，或者对于激励视频不是并发请求的
};

NS_ASSUME_NONNULL_END
