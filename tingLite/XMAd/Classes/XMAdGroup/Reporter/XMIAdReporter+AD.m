//
//  XMIAdReporter+AD.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/9/17.
//

#import "XMIAdReporter+AD.h"
#import "XMIAdError.h"
#import "XMICommonUtils.h"
#import "XMIAdRelatedData.h"
#import "XMIAdConverter.h"
#import "XMIAdViewProtocol.h"
#import "XMIAdSlotData.h"
#import "NSObject+XMIModel.h"
#import "XMIAdDataCenter.h"
#import "XMIAdMacro.h"
#import <XMConfigCenter/XMConfigCenter.h>
#import "XMIAdManager.h"

@implementation XMIAdReporter (AD)

// ---------------------------- 上报 ----------------------------------------------
/**
 上报请求状态
 @param dataEmpty :数据为空
 */
+ (void)reportRequestStatus:(BOOL)dataEmpty withReporter:(long long)reporterId andAd:(XMIAdRelatedData *)adData {
    if (adData.isAdPreview) {
        return;
    }
    XMIAdStatusReporter *reporter = [[XMIAdReporter sharedInstance] statusReportForId:reporterId];
    if (reporter == nil) {
        return;
    }
    [reporter fillWithAd:adData];
    reporter.endMS = [XMICommonUtils currentMS];
    reporter.status = dataEmpty ? XMIAdLoadStatusOtherNoData : XMIAdLoadStatusSuccess;
    reporter.backStatus = 0;
    reporter.ready = YES;
    [[XMIAdReporter sharedInstance] addStatusReport:reporter];
}
+ (XMIAdStatusReporter *)reporterWithError:(NSError *)error andReporter:(long long)reporterId andAd:(XMIAdRelatedData *)adData {
    if (adData.isAdPreview) {
        return nil;
    }
    XMIAdStatusReporter *reporter = [[XMIAdReporter sharedInstance] statusReportForId:reporterId];
    if (reporter == nil) {
        return nil;
    }
    [reporter fillWithAd:adData];
    reporter.endMS = [XMICommonUtils currentMS];
    XMIAdLoadStatus status = XMIAdLoadStatusRequestFail;
    NSInteger otherStatus = 0;
    if (error != nil) {
        // adx的error
        if ([XMIAdError isXMIError:error]) {
            status = error.code == XMIAdErrorOtherTimeout ? XMIAdLoadStatusOtherTimeout : XMIAdLoadStatusRequestFail;
        } else {
            status = XMIAdLoadStatusOtherNoData;
            otherStatus = error.code;
        }
    }
    reporter.status = status;
    reporter.backStatus = otherStatus;
    return reporter;
}
+ (void)reportRequestStatusWithReporter:(XMIAdStatusReporter *)reporter {
    if (reporter == nil) {
        return;
    }
    reporter.ready = YES;
    [[XMIAdReporter sharedInstance] addStatusReport:reporter];
}
// -------------------------------------------曝光上报-------------------------------------------------------
/**
 isTrueExposure=false请求到就可以上报曝光，要排除虚拟广告
 */
+ (void)exposeReportValidAds:(NSArray<XMIAdRelatedData *> *)adDatas {
    NSMutableArray *rArray = [[NSMutableArray alloc] init];
    for (XMIAdRelatedData *adData in adDatas) {
        if (adData.isAdPreview) {
            continue;
        }
        if (!adData.isTrueExposure && ![XMIAdConverter isVirtualAD:adData.adid]) {
            XMIAdShowType showType = [XMIAdConverter showTypeFromRelatedData:adData];
            XMIAdExposeReporter *reporter = [[XMIAdExposeReporter alloc] init];
            [reporter fillWithAd:adData];
            reporter.showType = showType;
            [rArray addObject:reporter];
            
            [self exposeReportOtherWithAd:adData];
        }
    }
    [[XMIAdReporter sharedInstance] addExposeReports:rArray];
    
    if ([[XMIAdManager sharedInstance].delegate respondsToSelector:@selector(managerPreLoadAdItemRealLinkWithDatas:)]) {
        [[XMIAdManager sharedInstance].delegate managerPreLoadAdItemRealLinkWithDatas:adDatas];
    }
}

+ (void)exposeNewReportValidAds:(NSArray<XMIAdRelatedData *> *)adDatas radio:(NSInteger)radio {
    for (XMIAdRelatedData *adData in adDatas) {
        if (adData.isAdPreview) {
            continue;
        }
        if (![XMIAdConverter isVirtualAD:adData.adid]) {
            XMIAdNewExposeReporter *reporter = [[XMIAdNewExposeReporter alloc] init];
            [reporter fillWithAd:adData];
            reporter.radio = radio;
            [[XMIAdReporter sharedInstance] adAdNewExposeReport:reporter];
        }
    }
}

+ (void)exposeDspShowAds:(NSArray<XMIAdRelatedData *> *)adDatas sdkShow:(NSInteger)sdkShow {
    for (XMIAdRelatedData *adData in adDatas) {
        if (adData.isAdPreview) {
            continue;
        }
        if (![XMIAdConverter isVirtualAD:adData.adid]) {
            XMIAdDspShowReporter *reporter = [[XMIAdDspShowReporter alloc] init];
            [reporter fillWithAd:adData];
            [[XMIAdReporter sharedInstance] adXlogReport:reporter];
        }
    }
}

+ (void)exposeReportWithAd:(XMIAdRelatedData *)adData andView:(UIView *)aView {
    if (adData.isAdPreview) {
        return;
    }
    if ([XMIAdConverter isVirtualAD:adData.adid]) {
        [self virtualReportWithAd:adData];
        return;
    }
    
    XMIAdShowType showType = [XMIAdConverter showTypeFromRelatedData:adData];
    [self exposeReportWithAd:adData andShowType:showType andRealExpose:adData.isTrueExposure];
}
+ (void)exposeReportWithAd:(XMIAdRelatedData *)adData andShowType:(XMIAdShowType)showType andRealExpose:(BOOL)realExpose {
    if (adData.isAdPreview) {
        return;
    }
    
    BOOL needReportShowOb = YES;
    // 猜你喜欢不上报showOb
    if ([adData.positionName isEqualToString:XMI_ADP_HOME_GUESS_YOU_LIKE]) {
        needReportShowOb = NO;
    }
    
    if (needReportShowOb) {
        // ShowOb上报
        XMIAdExposeReporter *realReporter = [[XMIAdExposeReporter alloc] init];
        [realReporter fillWithAdShowOb:adData];
        realReporter.showType = showType;
        // ShowOb仅做统计用，需要对数据进行处理
        [realReporter toRealExposeReporter];
        [[XMIAdReporter sharedInstance] addExposeReport:realReporter];
    }
    
    // 真实曝光
    if (realExpose) {
        XMIAdExposeReporter *reporter = [[XMIAdExposeReporter alloc] init];
        [reporter fillWithAd:adData];
        reporter.showType = showType;
        [[XMIAdReporter sharedInstance] addExposeReport:reporter];
        // 第三方
        [self exposeReportOtherWithAd:adData];
    }
    
}

+ (void)exposeReportOtherWithAd:(XMIAdRelatedData *)adData {
    if (adData.isAdPreview) {
        return;
    }
    NSMutableArray *urlArray = [[NSMutableArray alloc] init];
    if (adData.thirdStatUrl != nil && adData.thirdStatUrl.length > 0) {
        [urlArray addObject:adData.thirdStatUrl];
    }
    if (adData.thirdShowStatUrls != nil && adData.thirdShowStatUrls.count > 0) {
        [urlArray addObjectsFromArray:adData.thirdShowStatUrls];
    }
    [[XMIAdReporter sharedInstance] addOtherReport:urlArray relatedData:adData];
}

+ (void)exposeReportTingShowWithAd:(XMIAdRelatedData *)adData andShowType:(XMIAdShowType)showType
{
    XMIAdExposeReporter *reporter = [[XMIAdExposeReporter alloc] init];
    [reporter fillWithAd:adData];
    reporter.showType = showType;
    [[XMIAdReporter sharedInstance] addExposeReport:reporter];
    // 第三方
    [self exposeReportOtherWithAd:adData];
}

// -------------------------------------------点击上报-------------------------------------------------------
+ (void)clickReportWithAd:(XMIAdRelatedData *)adData andView:(UIView *)aView andUserInfo:(NSDictionary *)userInfo {
    if (adData.isAdPreview) {
        return;
    }
    NSDictionary *extraParams = [userInfo objectForKey:@"extraParams"];
    BOOL isManualClick = (![extraParams objectForKey:@"autoPull"] || ([[extraParams objectForKey:@"autoPull"] integerValue] == 1));
    if (adData.needDedupClick && adData.inClickFrequencyControl && isManualClick) {
        return;
    }
    XMIAdClickReporter *reporter = [[XMIAdClickReporter alloc] init];
    [reporter fillWithAd:adData];
    [reporter fillWithClickInfo:userInfo];
    XMIAdShowType showType = [XMIAdConverter showTypeFromRelatedData:adData];
    reporter.showType = showType;
    [[XMIAdReporter sharedInstance] addClickReport:reporter];
    if (adData.needDedupClick && isManualClick) {
        adData.inClickFrequencyControl = YES;
        NSInteger interval = [XMIAdDataCenter dedupClickInterval];
        if (interval == 0) {
            interval = 1000;
        }
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(interval * NSEC_PER_SEC / 1000)), dispatch_get_main_queue(), ^{
            adData.inClickFrequencyControl = NO;
        });
    }
    // 第三方
    if (adData.thirdClickStatUrls == nil || adData.thirdClickStatUrls.count < 1) {
        return;
    }
    NSMutableArray *urlArray = [[NSMutableArray alloc] init];
    for (NSString *url in adData.thirdClickStatUrls) {
        if ([XMIAdSpecailClickReporter isSpecail:url]) {
            XMIAdSpecailClickReporter *sReporter = [[XMIAdSpecailClickReporter alloc] init];
            [sReporter fillWithAd:adData];
            sReporter.showType = showType;
            sReporter.url = url;
            [[XMIAdReporter sharedInstance] addClickReport:sReporter];
        } else {
            if (url.length > 0) {
                [urlArray addObject:url];
            }
        }
    }
    [[XMIAdReporter sharedInstance] addOtherReport:urlArray relatedData:adData];
}
// -----------------------------------------------虚拟广告上报----------------------------------------------------------
+ (void)virtualReportWithAd:(XMIAdRelatedData *)adData {
    if (adData.isAdPreview) {
        return;
    }
    XMIAdVirtualReporter *reporter = [[XMIAdVirtualReporter alloc] init];
    [reporter fillWithAd:adData];
    reporter.duringPlay = 0;
    reporter.paused = 0;
    reporter.isDisplayedInScreen = 1;
    [[XMIAdReporter sharedInstance] addVirtualReport:reporter];
}
// -----------------------------------------------播放进度上报-------------------------------------------------
+ (void)videoPlayReportWithAd:(XMIAdRelatedData *)adData andAdView:(UIView<XMIAdViewProtocol> *)adView andPlayerState:(XMIPlayerPlayState)state {
    if (adData.isAdPreview) {
        return;
    }
    // 只有喜马广告上报这个
    if (adData.adtype != XMIAdTypeXM) {
        return;
    }
    if (state != XMIPlayerStatePlaying && state != XMIPlayerStateFailed) {
        return;
    }
    XMIAdPlayStatus status = 0;
    if (state == XMIPlayerStatePlaying) {
        status = XMIAdPlayStatusPlayStart;
    } else if (state == XMIPlayerStateFailed) {
        status = XMIAdPlayStatusPlayFail;
    }
    [self videoPlayReportWithAd:adData andAdView:adView andPlayStatus:status];
}
+ (void)videoPlayFinishReportWithAd:(XMIAdRelatedData *)adData andAdView:(UIView<XMIAdViewProtocol> *)adView {
    if (adData.isAdPreview) {
        return;
    }
    // 只有喜马广告上报这个
    if (adData.adtype != XMIAdTypeXM) {
        return;
    }
    [self videoPlayReportWithAd:adData andAdView:adView andPlayStatus:XMIAdPlayStatusPlayFinish];
}
+ (void)videoPlayInvisibleReportWithAd:(XMIAdRelatedData *)adData andAdView:(UIView<XMIAdViewProtocol> *)adView {
    if (adData.isAdPreview) {
        return;
    }
    // 只有喜马广告上报这个
    if (adData.adtype != XMIAdTypeXM) {
        return;
    }
    [self videoPlayReportWithAd:adData andAdView:adView andPlayStatus:XMIAdPlayStatusInvisible];
}
+ (void)videoPlayReportWithAd:(XMIAdRelatedData *)adData andAdView:(UIView<XMIAdViewProtocol> *)adView andPlayStatus:(XMIAdPlayStatus)status {
    if (adData.isAdPreview) {
        return;
    }
    XMIAdVideoReporter *reporter = [[XMIAdVideoReporter alloc] init];
    [reporter fillWithAd:adData];
    reporter.playStatus = status;
    if ([adView respondsToSelector:@selector(getVideoPlayTime)]) {
        reporter.playMs = [adView getVideoPlayTime];
    }
    if (status == XMIAdPlayStatusPlayStart) {
        reporter.playMs = 0;
    }
    
    if ([adView respondsToSelector:@selector(getVideoDuration)]) {
        reporter.videoMs = [adView getVideoDuration];
    }
    [[XMIAdReporter sharedInstance] addVideoReport:reporter];
}

+ (void)loadFailReportWithAd:(XMIAdRelatedData *)adData error:(NSError *)error useTime:(long long)useTime
{
    XMIAdStatusReporter *reporter = [[XMIAdStatusReporter alloc] init];
    reporter.paddingMs = useTime;
    [[XMIAdReporter sharedInstance] addStatusReport:reporter];
    
    XMIAdStatusReporter *statusReporter = [XMIAdReporter reporterWithError:error andReporter:reporter.reporterId andAd:adData];
    [XMIAdReporter reportRequestStatusWithReporter:statusReporter];
}

+ (void)dspSDKReportLoad:(XMIAdRelatedData *)adData
{
    [self dspSDKReport:adData params:@{@"op1" : @"request"}];
    [self dspSDKMaterialReport:adData];
}

+ (void)dspSDKReportClick:(XMIAdRelatedData *)adData
{
    [self dspSDKReport:adData params:@{@"op1" : @"click"}];
}

+ (void)dspSDKReport:(XMIAdRelatedData *)adData params:(NSDictionary *)params
{
    if (![XMIAdDataCenter shouldReportDSPSDK]) {
        return;
    }
    @try {
        if (adData.originData && [adData.originData respondsToSelector:@selector(reportDSPSDK)] && [adData.originData reportDSPSDK]) {
            NSMutableDictionary *dic = [NSMutableDictionary dictionary];
            if ([adData.originData respondsToSelector:@selector(dspSDKReportParams)]) {
                [dic addEntriesFromDictionary:[adData.originData dspSDKReportParams]];
            }
            dic[@"dspSlotid"] = adData.dspPositionId;
            XMIAdCommonReporter *reporter = [[XMIAdCommonReporter alloc] init];
            [reporter fillWithAd:adData];
            [dic addEntriesFromDictionary:[reporter xmi_modelToJSONObject]];
            [dic addEntriesFromDictionary:params];
            NSString *message = [[NSString alloc] initWithData:[NSJSONSerialization dataWithJSONObject:dic options:NSJSONWritingPrettyPrinted error:NULL] encoding:NSUTF8StringEncoding];
            [[XMIAdReporter sharedInstance] report:message withType:@"XmAd" andSubtype:@"dspsdk"];
        }
    } @catch (NSException *exception) {
        
    } @finally {
        
    }
   
}

+ (void)dspSDKMaterialReport:(XMIAdRelatedData *)adData {
    BOOL enable = [[XMConfigCenter sharedConfigCenter] getBoolValueWithGroup:@"ad" andItem:@"dspMaterialTrackEnable" defaultValue:YES];
    if (!enable) {
        return;
    }
    if (adData.originData && [adData.originData respondsToSelector:@selector(materialExtraInfo)]) {
        NSMutableDictionary *dic = [NSMutableDictionary dictionary];
        NSDictionary *materialExtraInfo = [adData.originData materialExtraInfo];
        if (materialExtraInfo.count) {
            [dic addEntriesFromDictionary:materialExtraInfo];
        }
        dic[@"dspPositionId"] = adData.dspPositionId;
        [[XMIAdReporter sharedInstance] reportParams:dic withSubtype:@"dspMaterial"];
    }
}

// ---------------------------------------------------------------------------------------------


/**
 web加载完成处理
 web需要上报首屏加载时间
 */
+ (void)webFinishLoading:(BOOL)success withAdData:(XMIAdRelatedData *)adData startTimestamp:(long long)startTime {
    if (adData.isAdPreview) {
        return;
    }
    long long endTime = [XMICommonUtils currentTimestamp];
    XMILog(@"web加载耗时 %lld", endTime - startTime);
    XMIAdLoadReporter *reporter = [[XMIAdLoadReporter alloc] init];
    [reporter fillWithAd:adData];
    reporter.loadSuccess = success;
    reporter.loadStartTimeMs = startTime;
    reporter.loadEndTimeMs = endTime;
    reporter.firstPaintTimeMs = endTime - startTime;
    [[XMIAdReporter sharedInstance] addLoadReport:reporter];
}

@end
