//
//  XMIAdEventTracker.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/9/6.
//

#import "XMIAdEventTracker.h"
#import "XMIAdMacro.h"
#import "XMICommonUtils.h"
#import "XMINetManager.h"
#import "XMINetDataTask.h"
#import "NSObject+XMIModel.h"
#import "XMIAdConfigData.h"
#import "XMIAdManager.h"
#import "XMIAdHeader.h"
#import "XMIAdDataCenter.h"

@interface XMIAdEventTracker ()

@property (nonatomic, assign) long long currentSeqId;
@property (nonatomic, strong) dispatch_queue_t trackQueue;

@end

@implementation XMIAdEventTracker

#define kAdTrackerBaseUrl @"http://adbehavior.ximalaya.com"
#define kAdTrackerBaseUrlTest @"http://xdcs-collector.test.ximalaya.com"
#define kAdURIAdRealTime @"/api/v1/adRealTime"
#define kAdMaxSeqId 1147483648
#define kAdEventTypeAD @"AD"

+ (instancetype)sharedInstance {
    static id _sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _sharedInstance = [[self alloc] init];
        [_sharedInstance commonInit];
    });
    
    return _sharedInstance;
}
- (void)commonInit {
    self.currentSeqId = [XMIAdConfigData longLongConfigForKey:XMI_CONFIG_SEQID];
    if (self.currentSeqId > kAdMaxSeqId) {
        self.currentSeqId = 0;
    }
    self.trackQueue = dispatch_queue_create("com.ximalaya.adtrack", DISPATCH_QUEUE_SERIAL);
    
    [self addObserver];
}

- (void)trackData:(NSDictionary *)data {
    if (data == nil) {
        return;
    }
    
    [self trackDatas:@[data]];
}

- (void)trackDatas:(NSArray<NSDictionary *> *)datas {
    if (datas == nil || datas.count < 1) {
        return;
    }
    
    dispatch_async(self.trackQueue, ^{
        NSMutableArray *array = [[NSMutableArray alloc] init];
        for (NSDictionary *item in datas) {
            NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithDictionary:item];
            dic[@"euid"] = [NSUUID UUID].UUIDString;
            XMIAdEvent *event = [self eventWithData:dic];
            [array addObject:event];
        }
        [self trackEvents:array];
    });
}

- (XMIAdEvent *)eventWithData:(NSDictionary *)dic {
    XMIAdEvent *event = [[XMIAdEvent alloc] init];
    event.seqId = ++self.currentSeqId;
    event.ts = [XMICommonUtils currentTimestamp];
    event.type = kAdEventTypeAD;
    event.props = dic;
    
    if (self.currentSeqId % 10 == 0) {
        [self saveSeqId];
    }

    return event;
}

- (void)trackEvents:(NSArray<XMIAdEvent *> *)events {
    XMIAdEventsBody *bodyData = [[XMIAdEventsBody alloc] init];
    bodyData.events = events;
    bodyData.sendTime = [XMICommonUtils currentTimestamp];
    [self trackWithBody:bodyData];
}

- (void)trackWithBody:(XMIAdEventsBody *)bodyData {
    NSMutableURLRequest *request = [self requestWithUri:kAdURIAdRealTime parameters:bodyData];
    XMINetDataTask *task = [XMINetManager dataTaskWithRequest:request completionHandler:^(NSURLResponse * _Nonnull response, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error != nil) {
            XMILog(@"%@", error);
        }
    }];
    [task start];
}

- (NSMutableURLRequest *)requestWithUri:(NSString *)uri parameters:(XMIAdEventsBody *)bodyData {
    // headers
    NSMutableDictionary *headers = [[NSMutableDictionary alloc] init];
    NSDictionary *headerDic = [[XMIAdHeader sharedInstance] headers];
    if (headerDic != nil) {
        [headers addEntriesFromDictionary:headerDic];
    }
    headers[@"Content-Type"] = @"application/octet-stream";
    // param
    NSDictionary *paramters = [bodyData xmi_modelToJSONObject];
    //
    NSString *url = [self getUrlWithUri:uri];
    NSError *error = nil;
    NSMutableURLRequest *request = [XMINetManager createRequestWithUrl:url parameters:paramters headers:headers error:&error];
    
    return request;
}

/**
 自动选择环境拼接url
 */
- (NSString *)getUrlWithUri:(NSString *)uri {
    NSString *baseUrl = nil;
    XMIAdEnvironment env = [XMIAdManager sharedInstance].environment;
    switch (env) {
        case XMIAdEnvironmentDefault:
            baseUrl = kAdTrackerBaseUrl;
            break;
        case XMIAdEnvironmentTest:
        case XMIAdEnvironmentUAT:
            baseUrl = kAdTrackerBaseUrlTest;
            break;
            
        default:
            baseUrl = kAdTrackerBaseUrl;
            break;
    }
    if ([XMIAdDataCenter requestUseHttps]) {
        baseUrl = [baseUrl stringByReplacingOccurrencesOfString:@"http://" withString:@"https://"];
    }
    return [NSString stringWithFormat:@"%@%@", baseUrl, uri];
}

- (void)saveSeqId {
    [XMIAdConfigData updateConfig:@(self.currentSeqId) forKey:XMI_CONFIG_SEQID];
}

- (void)handleApplicationDidEnterBackgroundNotification:(NSNotification *)notification {
    [self saveSeqId];
}

- (void)handleApplicationWillTerminateNotification:(NSNotification *)notification {
    [self saveSeqId];
}

- (void)addObserver {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleApplicationDidEnterBackgroundNotification:) name:UIApplicationDidEnterBackgroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleApplicationWillTerminateNotification:) name:UIApplicationWillTerminateNotification object:nil];
}

- (void)cleanObserver {
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIApplicationDidEnterBackgroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIApplicationWillTerminateNotification object:nil];
}

- (void)dealloc {
    [self cleanObserver];
}

@end


@implementation XMIAdEvent

@end


@implementation XMIAdEventsBody

+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{
        @"events" : [XMIAdEvent class]
    };
}

@end
