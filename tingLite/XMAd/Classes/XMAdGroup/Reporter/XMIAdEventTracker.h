//
//  XMIAdEventTracker.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/9/6.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class XMIAdEvent;

@interface XMIAdEventTracker : NSObject

+ (instancetype)sharedInstance;

- (void)trackData:(NSDictionary *)data;
- (void)trackDatas:(NSArray<NSDictionary *> *)datas;

@end

@interface XMIAdEvent : NSObject

@property (nonatomic, assign) long long seqId;
@property (nonatomic, assign) long long ts;
@property (nonatomic, copy) NSString *type;
@property (nonatomic, strong) NSDictionary *props;

@end

@interface XMIAdEventsBody : NSObject

@property (nonatomic, strong) NSArray<XMIAdEvent *> *events;
@property (nonatomic, assign) long long sendTime;

@end

NS_ASSUME_NONNULL_END
