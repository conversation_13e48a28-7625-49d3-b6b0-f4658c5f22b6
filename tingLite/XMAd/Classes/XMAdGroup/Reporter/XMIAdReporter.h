//
//  XMIAdReporter.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/27.
//

#import <Foundation/Foundation.h>
#import "XMIAdReportHelper.h"

NS_ASSUME_NONNULL_BEGIN

/**
 通用上报
 */
@interface XMIAdReporter : NSObject

+ (instancetype)sharedInstance;
@property (nonatomic, strong) dispatch_queue_t reportQueue;


/**
 新增请求状态上报
 */
- (void)addStatusReport:(XMIAdStatusReporter *)reporter;
/**
 用id查请求状态
 */
- (XMIAdStatusReporter *)statusReportForId:(long long)reporterId;

/**
 feed刷新了，homeRank N值计数
 */
- (void)feedDidRefresh;

// 猜你喜欢 homeRank N值计数
- (void)guessYouLikeDidRefresh;

/**
 首页二四屏中插大图，homeRank N值计数
 */
- (void)centerBigFeedDidRefresh;
/**
 首页首屏中插大图，，homeRank N值计数
 */
- (void)homeBannerDidRefresh;

- (void)report:(NSString *)msg withType:(NSString *)type andSubtype:(NSString *)subtype;
- (void)report:(NSString *)msg withSubtype:(NSString *)subtype;
- (void)reportParams:(NSDictionary *)params withSubtype:(NSString *)subtype;

@end

/**
 曝光上报
 */
@interface XMIAdReporter (Exposure)

/**
 新增曝光上报
 */
- (void)addExposeReport:(XMIAdExposeReporter *)reporter;
- (void)addExposeReports:(NSArray<XMIAdExposeReporter *> *)reporters;

@end

/**
 点击上报
 */
@interface XMIAdReporter (Click)

/**
 新增点击上报
 */
- (void)addClickReport:(XMIAdClickReporter *)reporter;

@end


/**
 第三方上报
 */
@interface XMIAdReporter (Other)

- (void)addOtherReport:(NSArray<NSString *> *)urlArray relatedData:(XMIAdRelatedData *)relatedData;

@end


/**
 上报token管理
 */
@interface XMIAdReporter (Token)

- (void)queueECReport:(XMIAdECReporter *)reporter;

@end


/**
 实时上报的
 */
@interface XMIAdReporter (Track)

- (void)addLoadReport:(XMIAdLoadReporter *)reporter;

@end


/**
 xlog埋点的
 */
@interface XMIAdReporter (XLog)

- (void)addDpReport:(XMIAdDpReporter *)reporter;
- (void)addVideoReport:(XMIAdVideoReporter *)reporter;
- (void)adAdNewExposeReport:(XMIAdNewExposeReporter *)reporter;
- (void)addVirtualReport:(XMIAdVirtualReporter *)reporter;
- (void)addAnchorClickReport:(XMIAdAnchorReporter *)reporter;
- (void)addLandingPageReport:(XMIAdLandingPageReporter *)reporter;

/// 通用上报
- (void)adXlogReport:(XMIAdCommonReporter *)reporter;

@end

NS_ASSUME_NONNULL_END
