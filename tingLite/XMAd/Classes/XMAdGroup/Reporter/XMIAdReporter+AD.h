//
//  XMIAdReporter+AD.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/9/17.
//

#import "XMIAdReporter.h"

NS_ASSUME_NONNULL_BEGIN

@class XMIAdRelatedData;
@protocol XMIAdViewProtocol;

@interface XMIAdReporter (AD)

/**
 上报请求状态
 @param dataEmpty :数据为空
 */
+ (void)reportRequestStatus:(BOOL)dataEmpty withReporter:(long long)reporterId andAd:(XMIAdRelatedData *)adData;
+ (XMIAdStatusReporter *)reporterWithError:(NSError *)error andReporter:(long long)reporterId andAd:(XMIAdRelatedData *)adData;
+ (void)reportRequestStatusWithReporter:(XMIAdStatusReporter *)reporter;

/**
 曝光上报
 */
+ (void)exposeReportValidAds:(NSArray<XMIAdRelatedData *> *)adDatas;
+ (void)exposeNewReportValidAds:(NSArray<XMIAdRelatedData *> *)adDatas radio:(NSInteger)radio;
+ (void)exposeReportWithAd:(XMIAdRelatedData *)adData andView:(UIView *)aView;
+ (void)exposeReportTingShowWithAd:(XMIAdRelatedData *)adData andShowType:(XMIAdShowType)showType;
+ (void)exposeDspShowAds:(NSArray<XMIAdRelatedData *> *)adDatas sdkShow:(NSInteger)sdkShow;
/**
 点击上报
 */
+ (void)clickReportWithAd:(XMIAdRelatedData *)adData andView:(UIView *)aView andUserInfo:(NSDictionary *)userInfo;
/**
 虚拟上报
 */
+ (void)virtualReportWithAd:(XMIAdRelatedData *)adData;

/**
 视频播放进度上报
 */
+ (void)videoPlayReportWithAd:(XMIAdRelatedData *)adData andAdView:(UIView<XMIAdViewProtocol> *)adView andPlayerState:(XMIPlayerPlayState)state;
+ (void)videoPlayReportWithAd:(XMIAdRelatedData *)adData andAdView:(UIView<XMIAdViewProtocol> *)adView andPlayStatus:(XMIAdPlayStatus)status;
+ (void)videoPlayFinishReportWithAd:(XMIAdRelatedData *)adData andAdView:(UIView<XMIAdViewProtocol> *)adView;
+ (void)videoPlayInvisibleReportWithAd:(XMIAdRelatedData *)adData andAdView:(UIView<XMIAdViewProtocol> *)adView;

/**
 加载失败曝光
 */

+ (void)loadFailReportWithAd:(XMIAdRelatedData *)adData error:(NSError *)error useTime:(long long)useTime;

+ (void)dspSDKReportLoad:(XMIAdRelatedData *)adData;
+ (void)dspSDKReportClick:(XMIAdRelatedData *)adData;

/**
 web加载完成处理
 web需要上报首屏加载时间
 */
+ (void)webFinishLoading:(BOOL)success withAdData:(XMIAdRelatedData *)adData startTimestamp:(long long)startTime;
@end

NS_ASSUME_NONNULL_END
