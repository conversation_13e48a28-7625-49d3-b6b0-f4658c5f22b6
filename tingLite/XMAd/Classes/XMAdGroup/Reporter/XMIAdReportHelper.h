//
//  XMIAdReportHelper.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/9/7.
//

#import <Foundation/Foundation.h>
#import "XMIAdDefines.h"
#import "XMIAdStatusData.h"

NS_ASSUME_NONNULL_BEGIN

@class XMIAdRelatedData;

@interface XMIAdReportHelper : NSObject

@end

/**
 请求状态上报辅助
 */
@interface XMIAdStatusReporter : NSObject

/**
 上报唯一ID
 */
@property (nonatomic, assign) long long reporterId;
@property (nonatomic, assign) long long adid;
@property (nonatomic, assign) NSInteger sdkType;
@property (nonatomic, assign) long long startMS;
@property (nonatomic, assign) long long endMS;
@property (nonatomic, assign) NSInteger status;
@property (nonatomic, assign) NSInteger backStatus;
@property (nonatomic, copy) NSString *dspPositionId;
@property (nonatomic, assign) long long slotId;
@property (nonatomic, assign) long long responseId;
@property (nonatomic, assign) long long positionId;
@property (nonatomic, copy) NSString *positionName;
/**
 附加时间，目前只包含adx请求时间
 */
@property (nonatomic, assign) long long paddingMs;
/**
 上报关联唯一ID，用于联合上报
 */
@property (nonatomic, assign) long long refReporterId;
/**
 是否可以上报了
 */
@property (nonatomic, assign) BOOL ready;

- (void)fillWithAd:(XMIAdRelatedData *)adData;

@end


@interface XMIAdECReporter : NSObject

@property (nonatomic, assign) long long responseId;
@property (nonatomic, copy) NSString *appId;
@property (nonatomic, copy) NSString *sdkVersion;
@property (nonatomic, assign) long long slotId;
@property (nonatomic, assign) long long positionId;
@property (nonatomic, copy) NSString *positionName;
@property (nonatomic, assign) long long adItemId;
@property (nonatomic, assign) long long adSource;
@property (nonatomic, assign) XMIAdShowType showType;
@property (nonatomic, copy) NSString *albumId;
@property (nonatomic, copy) NSString *trackId;
@property (nonatomic, copy) NSString *logType;
@property (nonatomic, copy) NSString *bucketIds;
@property (nonatomic, copy) NSString *rec_track;
@property (nonatomic, copy) NSString *rec_src;
@property (nonatomic, copy) NSString *adpr;
@property (nonatomic, copy) NSString *homeRank;
@property (nonatomic, assign) long long time;
@property (nonatomic, copy) NSString *realLink;
@property (nonatomic, copy) NSString *bannerAdStyle;
@property (nonatomic, assign) NSUInteger frames_num;
@property (nonatomic, copy) NSString *dspPositionId;

@property (nonatomic, copy) NSString *ipv6;
/**
 以下为辅助字段
 */
@property (nonatomic, assign) BOOL tokenEnable;
@property (nonatomic, assign) NSUInteger adno;
@property (nonatomic, assign) BOOL prepared;
@property (nonatomic, assign) BOOL shouldAddHomeRank;
@property (nonatomic, assign) BOOL shouldAddHomeGuessYouLikeRank;
@property (nonatomic, assign) BOOL shouldAddHomeBannerHomeRank;
@property (nonatomic, assign) BOOL shouldAddCenterBigFeedHomeRank;
@property (nonatomic, strong) NSNumber *isCenterBigFeedAd;

//特殊上报
@property (nonatomic, copy) NSDictionary *extraParams;
// ubt上报
@property (nonatomic, copy) NSString *ubtReportMap;

@property (nonatomic, copy) NSString *clickPostMap;

//新播放页

@property (nullable, nonatomic, strong) NSNumber *playPageRevision;

//激励
@property (nonatomic, copy) NSString *sourceName;
@property (nonatomic, copy) NSString *startSource;

- (void)fillWithAd:(XMIAdRelatedData *)adData;
- (NSMutableDictionary *)toDictionary;

@end


/**
 曝光上报辅助
 */
@interface XMIAdExposeReporter : XMIAdECReporter

@property (nonatomic, copy) NSString *showToken;

@property (nonatomic, copy) NSString *commonReportMap;

- (void)fillWithAdShowOb:(XMIAdRelatedData *)adData;
/**
 转换为真实曝光
 */
- (void)toRealExposeReporter;

@end


/**
 点击上报辅助
 */
@interface XMIAdClickReporter : XMIAdECReporter

@property (nonatomic, copy) NSString *clickToken;
/**
 点击x坐标比例 x/width
 */
@property (nonatomic, copy) NSString *x;
/**
 点击y坐标比例 y/height
 */
@property (nonatomic, copy) NSString *y;
/**
 点击x坐标
 */
@property (nonatomic, copy) NSString *absX;
/**
 点击y坐标
 */
@property (nonatomic, copy) NSString *absY;
/**
 点击上报url
 */
@property (nonatomic, copy) NSString *url;

/**
 最终已播放时长
 */
@property (nonatomic, assign) NSInteger finishSeconds;

/**
 填充点击信息
 */
- (void)fillWithClickInfo:(NSDictionary *)clickInfo;
/**
 获取点击信息
 */
- (NSDictionary *)getClickInfo;

// 辅助字段，点击上报是否使用post
@property (nonatomic, assign) BOOL clickRequestMethodPost;

// webview加载状态
@property (nonatomic, assign) NSInteger preLoadH5Status;

@end


/**
 针对ximalaya.com/adrecord/thirdAdRecord特殊上报
 */
@interface XMIAdSpecailClickReporter : XMIAdClickReporter

@property (nonatomic, copy) NSString *app;
@property (nonatomic, copy) NSString *version;
@property (nonatomic, copy) NSString *impl;
@property (nonatomic, copy) NSString *isAppleReview;
@property (nonatomic, copy) NSString *appid;
@property (nonatomic, copy) NSString *mac;
@property (nonatomic, copy) NSString *adId;

+ (BOOL)isSpecail:(NSString *)url;

@end


@interface XMIAdCommonReporter : NSObject

@property (nonatomic, assign) long long responseId;
@property (nonatomic, copy) NSString *sdkVersion;
@property (nonatomic, copy) NSString *adAppId;
@property (nonatomic, assign) long long slotId;
@property (nonatomic, assign) long long positionId;
@property (nonatomic, copy) NSString *positionName;
@property (nonatomic, assign) long long adItemId;
@property (nonatomic, assign) long long adSource;
@property (nonatomic, copy) NSString *commonReportMap;
@property (nonatomic, copy) NSString* albumId;
@property (nonatomic, copy) NSString* trackId;
@property (nonatomic, copy) NSString *logType;
@property (nonatomic, copy) NSString *dspPositionId;

@property (nonatomic, strong) NSString *sourceName;
@property (nonatomic, strong) NSString *startSource;

- (void)fillWithAd:(XMIAdRelatedData *)adData;


@end


/**
 加载上报 web
 */
@interface XMIAdLoadReporter : XMIAdCommonReporter

@property (nonatomic, assign) BOOL loadSuccess;
@property (nonatomic, assign) long long loadStartTimeMs;
@property (nonatomic, assign) long long loadEndTimeMs;
@property (nonatomic, assign) long long firstPaintTimeMs;

@end


typedef NS_ENUM(int, XMIAdPlayStatus) {
    XMIAdPlayStatusPlayStart = 1,   // 开始播放
    XMIAdPlayStatusPlayFinish = 2,  // 播放完成
    XMIAdPlayStatusManualClose = 3, // 广告关闭、手动关闭
    XMIAdPlayStatusInvisible = 4,   // 广告不可见
    XMIAdPlayStatusPlayFail = 5     // 播放失败
};
/**
 视频展示进度上报
 */
@interface XMIAdVideoReporter : XMIAdCommonReporter

@property (nonatomic, assign) int playStatus;
/**
 当前播放时间，单位ms
 */
@property (nonatomic, assign) int playMs;
/**
 视频时长，单位ms
 */
@property (nonatomic, assign) int videoMs;

@end


typedef NS_ENUM(int, XMIAdDpCallStatus) {
    XMIAdDpCallStatusNotInstall = 100,  // 未安装
    XMIAdDpCallStatusSuccess = 201,     // 已安装吊起成功
    XMIAdDpCallStatusFail = 202         // 已安装吊起失败
};
/**
 DP/UL 吊起上报
 */
@interface XMIAdDpReporter : XMIAdCommonReporter

@property (nonatomic, copy) NSString *appName;
@property (nonatomic, assign) int dpCallStatus;

@end


@interface XMIAdNewExposeReporter : XMIAdCommonReporter

/**
 曝光比例(单位百分比)
 */
@property (nonatomic, assign) NSInteger radio;

@end


@interface XMIAdDspShowReporter : XMIAdCommonReporter

@property (nonatomic, assign) NSInteger sdkShow;

@end


@interface XMIAdVirtualReporter : XMIAdCommonReporter

/**
 是否暂停贴
 */
@property (nonatomic, assign) int paused;
/**
 是否二次曝光
 */
@property (nonatomic, assign) int duringPlay;
/**
 是否亮屏
 对于声音贴片广告来说是指播放页是否在前台
 */
@property (nonatomic, assign) int isDisplayedInScreen;

@end


@interface XMIAdAnchorReporter : XMIAdCommonReporter
@property (nonatomic, copy) NSString *broadcasterId;
@end


@interface XMIAdLandingPageReporter : XMIAdCommonReporter

@property (nonatomic, copy) NSString *clickTime;
@property (nonatomic, copy) NSString *firstCompleteTime;
@property (nonatomic, copy) NSString *completeTime;
@property (nonatomic, copy) NSString *closeTime;
@property (nonatomic, copy) NSString *realLinkStatus;
@property (nonatomic, copy) NSString *landingPageUrl;

@end

NS_ASSUME_NONNULL_END
