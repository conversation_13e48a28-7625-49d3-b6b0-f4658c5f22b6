//
//  XMIAdReporter.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/27.
//

#import "XMIAdReporter.h"
#import <XMXlog/XMXlogUtil.h>
#import "XMIAdMacro.h"
#import "XMIAdDefines.h"
#import "XMICommonUtils.h"
#import "XMIAdRelatedData.h"
#import "XMIAdManager.h"
#import "NSObject+XMIModel.h"
#import "XMIAdConverter.h"
#import "XMIAdEventTracker.h"
#import "XMIAdRequest.h"
#import <XMNetworkRequest/XMNRequest.h>
/**
 展示,真实曝光展示,点击,这三个还是用老逻辑，其它用xlog
 */

@interface XMIAdReporter ()

/**
 请求状态上报dic，key-唯一id，value-XMIAdStatusHelper
 为了多次广告加载的联合上报，需要暂存一些中间数据
 */
@property (nonatomic, strong) NSMutableDictionary *statusMapper;
@property (nonatomic, strong) dispatch_semaphore_t semaphore;
@property (nonatomic, assign) long long feedRefreshCount;
@property (nonatomic, assign) long long guessYouLikeRefreshCount;
@property (nonatomic, assign) long long centerBigFeedRefreshCount;
@property (nonatomic, assign) long long homeBannerRefreshCount;
@property (nonatomic, assign) BOOL isTokenRequesting;
@property (nonatomic, strong) NSMutableArray *tokenArray;
/**
 曝光、点击待上报数据
 */
@property (nonatomic, strong) NSMutableArray *ecReportArray;

@end

@implementation XMIAdReporter

#define kReportTypeAd @"XmAd"
#define kReportSubTypeStatus @"adMaterialStatus"

+ (instancetype)sharedInstance {
    static id _sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _sharedInstance = [[self alloc] init];
        [_sharedInstance commonInit];
    });
    
    return _sharedInstance;
}
- (void)commonInit {
    self.semaphore = dispatch_semaphore_create(1);
    self.reportQueue = dispatch_queue_create("com.ximalaya.adreport", DISPATCH_QUEUE_SERIAL);
    self.feedRefreshCount = 0;
    self.guessYouLikeRefreshCount = 0;
    self.centerBigFeedRefreshCount = 0;
    self.homeBannerRefreshCount = 0;
}

- (void)addStatusReport:(XMIAdStatusReporter *)reporter
{
//    dispatch_async(self.reportQueue, ^{
//    });
    [self addStatusReportInner:reporter];
}


- (void)addStatusReportInner:(XMIAdStatusReporter *)reporter {
    if (reporter == nil) {
        return;
    }
    
    XMILock();
    self.statusMapper[@(reporter.reporterId)] = reporter;
    XMIUnlock();
    
    if (reporter.ready) {
        [self reportRequestStatus:reporter];
        
        NSMutableArray *reporterIds = [[NSMutableArray alloc] init];
        [reporterIds addObject:@(reporter.reporterId)];
        if (reporter.refReporterId != 0) {
            [reporterIds addObject:@(reporter.refReporterId)];
        }
        [self cleanReporters:reporterIds];
    }
}

- (XMIAdStatusReporter *)statusReportForId:(long long)reporterId {
    
    XMILock();
    XMIAdStatusReporter *reporter = self.statusMapper[@(reporterId)];
    XMIUnlock();
    
    return reporter;
}

- (void)reportRequestStatus:(XMIAdStatusReporter *)reporter {
    NSMutableArray *dataArray = [[NSMutableArray alloc] init];
    XMIAdStatusInnerData *innerData = [self statusInnerDataWithStatusReporter:reporter];
    [dataArray addObject:innerData];
    if (reporter.refReporterId != 0) {
        XMIAdStatusReporter *refReporter = [self statusReportForId:reporter.refReporterId];
        if (refReporter != nil) {
            XMIAdStatusInnerData *refInnerData = [self statusInnerDataWithStatusReporter:refReporter];
            [dataArray addObject:refInnerData];
        }
    }
    
    XMIAdStatusData *statusData = [[XMIAdStatusData alloc] init];
    statusData.adAppId = [XMIAdManager appID];
    statusData.responseId = reporter.responseId;
    statusData.slotId = reporter.slotId;
    statusData.positionId = reporter.positionId;
    statusData.positionName = reporter.positionName;
    statusData.sdkVersion = [XMIAdManager sdkVersion];
    statusData.data = [dataArray xmi_modelToJSONString];
    
    [self report:[statusData xmi_modelToJSONString] withType:kReportTypeAd andSubtype:kReportSubTypeStatus];
}
- (XMIAdStatusInnerData *)statusInnerDataWithStatusReporter:(XMIAdStatusReporter *)reporter {
    XMIAdStatusInnerData *innerData = [[XMIAdStatusInnerData alloc] init];
    innerData.sdkType = reporter.sdkType;
    innerData.adid = reporter.adid;
    innerData.useTime = reporter.endMS - reporter.startMS + reporter.paddingMs;
    innerData.status = reporter.status;
    innerData.backStatus = reporter.backStatus;
    innerData.dspPositionId = reporter.dspPositionId;
    return innerData;
}

- (void)report:(NSString *)msg withType:(NSString *)type andSubtype:(NSString *)subtype {
    XMILog(@"%@", msg);
    [self debugMsg:msg withType:type andSubtype:subtype];
    LOG_INFO(type, subtype, msg);
}

- (void)report:(NSString *)msg withSubtype:(NSString *)subtype {
    [self report:msg withType:kReportTypeAd andSubtype:subtype];
}

- (void)reportParams:(NSDictionary *)params withType:(NSString *)type andSubtype:(NSString *)subtype {
    NSString *msg = [params jsonString];
    XMILog(@"%@", msg);
    [self debugMsg:msg withType:type andSubtype:subtype];
    LOG_INFO(type, subtype, params);
}

- (void)reportParams:(NSDictionary *)params withSubtype:(NSString *)subtype {
    [self reportParams:params withType:kReportTypeAd andSubtype:subtype];
}

- (void)debugMsg:(NSString *)msg withType:(NSString *)type andSubtype:(NSString *)subtype {
    BOOL isDebug = [XMIAdManager sharedInstance].isDebug;
    
    if (isDebug) {
        BOOL xlogRequestEnabled = [[NSUserDefaults standardUserDefaults] boolForKey:@"kXMAdSendXlogFakeRequest"];
        if (!xlogRequestEnabled) {
            return;
        }
        NSDictionary *parms = @{@"type":type, @"subtype":subtype, @"msg":msg};
        XMNRequest *request = [XMNRequest requestWithServerUrl:@"http://adse.test.ximalaya.com"
                                                          path:[NSString stringWithFormat:@"/ad/xlog?subtype=%@",subtype]
                                                    parameters:parms
                                                        method:XMRequestMethodPost
                                             completionHandler:nil];
        request.errorHint = NO;
        [request start];
    }
}

- (void)cleanReporters:(NSArray *)reporterIds {
    XMILock();
    [self.statusMapper removeObjectsForKeys:reporterIds];
    XMIUnlock();
}

- (NSMutableDictionary *)statusMapper {
    if (_statusMapper == nil) {
        _statusMapper = [[NSMutableDictionary alloc] init];
    }
    return _statusMapper;
}

- (void)feedDidRefresh {
    XMILock();
    self.feedRefreshCount++;
    XMIUnlock();
}

- (void)guessYouLikeDidRefresh {
    XMILock();
    self.guessYouLikeRefreshCount++;
    XMIUnlock();
}

- (void)centerBigFeedDidRefresh
{
    XMILock();
    self.centerBigFeedRefreshCount++;
    XMIUnlock();
}

- (void)homeBannerDidRefresh
{
    XMILock();
    self.homeBannerRefreshCount++;
    XMIUnlock();
}
/**
 填充必要字段
 */
- (void)prepareReporter:(XMIAdECReporter *)reporter {
    if (reporter.prepared) {
        return;
    }
    
    reporter.prepared = YES;
    if (reporter.shouldAddHomeRank) {
        reporter.homeRank = [NSString stringWithFormat:@"%lld-%lu", self.feedRefreshCount, reporter.adno+1];
    } else if (reporter.shouldAddHomeGuessYouLikeRank) {
        reporter.homeRank = [NSString stringWithFormat:@"%lld-%lu", self.guessYouLikeRefreshCount, reporter.adno+1];
    }
    if (reporter.shouldAddCenterBigFeedHomeRank) {
        //adno第一位是首屏
        reporter.homeRank = [NSString stringWithFormat:@"%lld-%lu", self.centerBigFeedRefreshCount, reporter.adno+1];
    } else if (reporter.shouldAddHomeBannerHomeRank) {
        reporter.homeRank = [NSString stringWithFormat:@"%lld-%lu", self.homeBannerRefreshCount, reporter.adno+1];
    }
}

@end


@implementation XMIAdReporter (Exposure)

- (void)addExposeReport:(XMIAdExposeReporter *)reporter {
    dispatch_async(self.reportQueue, ^{
        [self addExposeReportInner:reporter];
    });
}
- (void)addExposeReportInner:(XMIAdExposeReporter *)reporter {
    [self prepareReporter:reporter];
    if (reporter.tokenEnable && reporter.showToken == nil) {
        [self queueECReport:reporter];
        return;
    }
    [[XMIAdEventTracker sharedInstance] trackData:[reporter toDictionary]];
}
- (void)addExposeReports:(NSArray<XMIAdExposeReporter *> *)reporters {
    dispatch_async(self.reportQueue, ^{
        [self addExposeReportsInner:reporters];
    });
}
- (void)addExposeReportsInner:(NSArray<XMIAdExposeReporter *> *)reporters {
    if (reporters == nil || reporters.count < 1) {
        return;
    }
    NSMutableArray *rArray = [[NSMutableArray alloc] init];
    for (XMIAdExposeReporter *reporter in reporters) {
        [self prepareReporter:reporter];
        if (reporter.tokenEnable && reporter.showToken == nil) {
            [self queueECReport:reporter];
            continue;
        }
        NSDictionary *dic = [reporter toDictionary];
        if (dic != nil) {
            [rArray addObject:dic];
        }
    }
    [[XMIAdEventTracker sharedInstance] trackDatas:rArray];
}

@end


@implementation XMIAdReporter (Click)

- (void)addClickReport:(XMIAdClickReporter *)reporter {
    dispatch_async(self.reportQueue, ^{
        [self addClickReportInner:reporter];
    });
}
- (void)addClickReportInner:(XMIAdClickReporter *)reporter {
    [self prepareReporter:reporter];
    if (reporter.tokenEnable && reporter.clickToken == nil) {
        [self queueECReport:reporter];
        return;
    }
    if (reporter.url == nil || reporter.url.length < 1) {
        return;
    }
    NSString *urlString = [XMIAdConverter urlStringByReplaceADParams:reporter.url withClickInfo:[reporter getClickInfo]];
    urlString = [XMIAdConverter urlStringByReplaceJtParam:urlString withOtherParma:[reporter toDictionary]];
    if (reporter.clickRequestMethodPost) { // post
        NSMutableDictionary *postParams = [NSMutableDictionary dictionary];
        NSDictionary *ubtReportMap = [NSDictionary objectFormJsonString:reporter.ubtReportMap];
        NSDictionary *clickPostMap = [NSDictionary objectFormJsonString:reporter.clickPostMap];
        [postParams addEntriesFromDictionary:ubtReportMap];
        [postParams addEntriesFromDictionary:clickPostMap];
        
        [XMIAdRequest postWithUrl:urlString andParam:postParams];
    } else { // get
        NSString *ubtString = reporter.ubtReportMap;
        // 单独拼在adrecord的最后，防止网关截断的时候影响有效参数
        if(ubtString.length) {
            ubtString = [NSString stringWithFormat:@"&ubtReportMap=%@", [ubtString base64EncodedString]];
            urlString = [urlString stringByAppendingFormat:@"%@", ubtString];
        }
        [XMIAdRequest getWithUrl:urlString isThirdUrl:NO relatedData:nil];
    }
}

@end


@implementation XMIAdReporter (Other)

/**
 第三方上报，就是替换url中要替换的参数，然后发一个GET请求
 */
- (void)addOtherReport:(NSArray<NSString *> *)urlArray relatedData:(XMIAdRelatedData *)relatedData {
    dispatch_async(self.reportQueue, ^{
        [self addOtherReportInner:urlArray relatedData:relatedData ];
    });
}
- (void)addOtherReportInner:(NSArray<NSString *> *)urlArray relatedData:(XMIAdRelatedData *)relatedData {
    if (urlArray == nil || urlArray.count < 1) {
        return;
    }
    
    for (NSString *url in urlArray) {
        NSString *urlString = [XMIAdConverter urlStringByReplaceADParams:url];
        if (urlString == nil) {
            continue;
        }
        
        [XMIAdRequest getWithUrl:urlString isThirdUrl:YES relatedData:relatedData];
    }
}

@end


@implementation XMIAdReporter (Token)
#define kReportQueueMaxCount 5000

- (NSString *)getToken {
    long long ts = [XMICommonUtils currentTimestamp];
    while (self.tokenArray.count > 0) {
        XMIAdNonceData *nonceData = self.tokenArray.firstObject;
        if (nonceData != nil) {
            [self.tokenArray removeObjectAtIndex:0];
        }
        if (nonceData != nil && nonceData.expireTime > ts) {
            return nonceData.nonce;
        }
    }
    
    // 积压太多，使用默认token消费
    if (self.ecReportArray.count > kReportQueueMaxCount) {
        return XMI_REPORT_DEFAULT_TOKEN;
    }
    
    return nil;
}

- (void)queueECReport:(XMIAdECReporter *)reporter {
    dispatch_async(self.reportQueue, ^{
        [self queueECReportInner:reporter];
    });
}
- (void)queueECReportInner:(XMIAdECReporter *)reporter {
    [self.ecReportArray addObject:reporter];
    [self consumeECQueue];
}

- (void)consumeECQueue {
    XMIAdECReporter *reporter = self.ecReportArray.firstObject;
    while (reporter != nil) {
        NSString *token = [self getToken];
        if (token == nil) {
            [self requestToken];
            break;
        }
        
        if ([reporter isKindOfClass:[XMIAdExposeReporter class]]) {
            XMIAdExposeReporter *eReporter = (XMIAdExposeReporter *)reporter;
            eReporter.showToken = token;
            [self addExposeReport:eReporter];
        } else if ([reporter isKindOfClass:[XMIAdClickReporter class]]) {
            XMIAdClickReporter *cReporter = (XMIAdClickReporter *)reporter;
            cReporter.clickToken = token;
            [self addClickReport:cReporter];
        }
        [self.ecReportArray removeObjectAtIndex:0];
        reporter = self.ecReportArray.firstObject;
    }
}

- (void)requestToken {
    if (self.isTokenRequesting) {
        return;
    }
    self.isTokenRequesting = YES;
    
    [XMIAdRequest nonceRequest:^(XMIAdRespNonceData * _Nullable respData, NSError * _Nullable error) {
        self.isTokenRequesting = NO;
        
        if (error != nil) {
            XMILog(@"request token fail, %@", error);
            return;
        }
        if (respData == nil) {
            return;
        }
        if (respData.data == nil) {
            return;
        }
        if (respData.data.nonces == nil || respData.data.nonces.count < 1) {
            return;
        }
        
        [self.tokenArray addObjectsFromArray:respData.data.nonces];
        [self consumeECQueue];
    }];
}

- (NSMutableArray *)ecReportArray {
    if (_ecReportArray == nil) {
        _ecReportArray = [[NSMutableArray alloc] init];
    }
    return _ecReportArray;
}

- (NSMutableArray *)tokenArray {
    if (_tokenArray == nil) {
        _tokenArray = [[NSMutableArray alloc] init];
    }
    return _tokenArray;
}

@end


@implementation XMIAdReporter (Track)

- (void)addLoadReport:(XMIAdLoadReporter *)reporter {
    NSDictionary *dic = [reporter xmi_modelToJSONObject];
    [[XMIAdEventTracker sharedInstance] trackData:dic];
}

@end


@implementation XMIAdReporter (XLog)

- (void)addDpReport:(XMIAdDpReporter *)reporter {
    NSString *msg = [reporter xmi_modelToJSONString];
    [self report:msg withSubtype:reporter.logType];
}

- (void)addVideoReport:(XMIAdVideoReporter *)reporter {
    NSString *msg = [reporter xmi_modelToJSONString];
    [self report:msg withSubtype:reporter.logType];
}

- (void)adAdNewExposeReport:(XMIAdNewExposeReporter *)reporter {
    NSDictionary *params = [reporter xmi_modelToJSONObject];
    [self reportParams:params withSubtype:reporter.logType];
}

- (void)adXlogReport:(XMIAdCommonReporter *)reporter {
    NSDictionary *params = [reporter xmi_modelToJSONObject];
    [self reportParams:params withSubtype:reporter.logType];
}

- (void)addVirtualReport:(XMIAdVirtualReporter *)reporter {
    NSString *msg = [reporter xmi_modelToJSONString];
    [self report:msg withSubtype:reporter.logType];
}

- (void)addAnchorClickReport:(XMIAdAnchorReporter *)reporter {
    NSString *msg = [reporter xmi_modelToJSONString];
    [self report:msg withSubtype:reporter.logType];
}

- (void)addLandingPageReport:(XMIAdLandingPageReporter *)reporter {
    NSDictionary *params = [reporter xmi_modelToJSONObject];
    [self reportParams:params withSubtype:reporter.logType];
}

@end
