//
//  XMIAdReportHelper.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/9/7.
//

#import "XMIAdReportHelper.h"
#import "NSObject+XMIModel.h"
#import "XMICommonUtils.h"
#import "XMIAdRelatedData.h"
#import "XMIAdConverter.h"
#import "XMIAdManager.h"
#import "XMIAdHeader.h"
#import "XMIAdDefines.h"
#import "XMIAdHelper.h"
#import <XMBase/NSObject+YYModel.h>

#define kAdLogTypeShow @"tingShow"
#define kAdLogTypeClick @"tingClick"
#define kAdLogTypeLoadRecord @"landingPageRecord"
#define KAdLogTypeVideoRecord @"videoRecord"
#define kAdLogTypeDpRecord @"dpRecord"
#define kAdLogTypeVirtualShow @"adVirtualShow"
#define kAdLogTypeAdNewExpose @"adNewExpose"
#define kAdLogTypeDspShow @"dspShow"
#define kAdLogTypeShowOb @"showOb"
#define kAdLogTypeAnchorClick @"anchorInfoClick"

@implementation XMIAdReportHelper
+ (NSString *)generateUbtReportMapWithReporter:(XMIAdECReporter *)reporter adData:(XMIAdRelatedData *)adData {
    // 曝光上报增加 ubtReportMap
    NSString *ubtReportMap = adData.ubtReportMap.length ? adData.ubtReportMap : @"";
    if (ubtReportMap.length) {
        NSData *jsonData = [ubtReportMap dataUsingEncoding:NSUTF8StringEncoding];
        NSError *err;
        NSMutableDictionary *ubtMapDic;
        if (jsonData) {
            ubtMapDic = [NSJSONSerialization JSONObjectWithData:jsonData
                                                   options:NSJSONReadingMutableContainers
                                                     error:&err];
        }
        NSMutableDictionary *adExt = [NSMutableDictionary dictionary];
        
        [adExt setValue:reporter.albumId forKey:@"albumId"];
        [adExt setValue:reporter.trackId forKey:@"trackId"];
        
        [ubtMapDic setValue:adExt forKey:@"adExt"];
        
        ubtReportMap = [ubtMapDic xmi_modelToJSONString];
    }
    return ubtReportMap;
}
@end

@implementation XMIAdStatusReporter

- (instancetype)init {
    self = [super init];
    if (self) {
        [self commonInit];
    }
    return self;
}
- (void)commonInit {
    self.reporterId = [XMICommonUtils currentTimestamp];
    self.refReporterId = 0;
    self.ready = NO;
}

- (void)fillWithAd:(XMIAdRelatedData *)adData {
    self.slotId = adData.slotId;
    self.responseId = adData.responseId;
    self.positionId = adData.positionId;
    self.positionName = adData.positionName;
    self.adid = adData.adid;
    self.sdkType = [XMIAdConverter sdkTypeFromAdType:adData.adtype];
}

@end


@implementation XMIAdECReporter

- (void)fillWithAd:(XMIAdRelatedData *)adData {
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    
    self.responseId = adData.responseId;
    self.slotId = adData.slotId;
    self.positionId = adData.positionId;
    self.positionName = adData.positionName;
    self.adItemId = adData.adid;
    self.adSource = adData.adtype;
    self.bucketIds = adData.bucketIds;
    self.rec_track = adData.recTrack;
    self.rec_src = adData.recSrc;
    self.adpr = adData.adpr;
    self.adno = adData.adno;
    self.time = [XMICommonUtils currentTimestamp];
    self.appId = [XMIAdManager appID];
    self.sdkVersion = [XMIAdManager sdkVersion];
    self.realLink = adData.realLink;
    self.dspPositionId = adData.dspPositionId;
    self.frames_num = adData.frames_num;
    if (adData.isHeaderModule) {
        self.bannerAdStyle = @"1";
    }
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerGetIPv6Address)]) {
        self.ipv6 = [manager.delegate managerGetIPv6Address];
    }
    
    switch (adData.showstyle) {
        case XMIAdStyleHomeVideo:
        case XMIAdStyleHomeLargeImage:
        case XMIAdStyleHomeBackgroundImage:
        case XMIAdStyleHomeDoubleRowVideo:
        case XMIAdStyleHomeDoubleRowAudio:
        case XMIAdStyleHomeDoubleRowSquareImage:
        case XMIAdStyleHomeDoubleRowVerticalImage:
        case XMIAdStyleHomeDoubleRowVerticalVideo:
            self.shouldAddHomeRank = YES;
            break;
            
        default:
            self.shouldAddHomeRank = NO;
            break;
    }
    // 特殊处理混排广告
    if (adData.bizType > 0) {
        self.shouldAddHomeGuessYouLikeRank = YES;
    } else {
        self.shouldAddHomeGuessYouLikeRank = NO;
    }
    
    if (adData.isGuessYouLikeAd) {
        self.shouldAddHomeGuessYouLikeRank = YES;
    }
    if ((adData.positionId == 293 || [adData.positionName isEqualToString:@"home_banner_ad"])) {
       
        self.isCenterBigFeedAd = [NSNumber numberWithInteger:adData.isCenterBigFeedAd ? 1 : 0];
        self.shouldAddHomeRank = NO;
        if (self.isCenterBigFeedAd) {
            self.shouldAddCenterBigFeedHomeRank = YES;
            self.shouldAddHomeBannerHomeRank = NO;
        } else {
            self.shouldAddCenterBigFeedHomeRank = NO;
            self.shouldAddHomeBannerHomeRank = YES;
        }
    }
    
    NSString *albumId = @"";
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerGetAlbumId)]) {
        albumId = [manager.delegate managerGetAlbumId];
    }
    self.albumId = albumId;
    
    NSString *trackId = @"";
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerGetTrackId)]) {
        trackId = [manager.delegate managerGetTrackId];
    }
    self.trackId = trackId;
    
    self.playPageRevision = adData.playPageRevision;
    if (adData.extraReportParams) {
        self.extraParams = adData.extraReportParams;
    }
    self.sourceName = adData.sourceName;
    self.startSource = adData.startSource;
}

- (NSMutableDictionary *)toDictionary {
    NSDictionary *json = [self xmi_modelToJSONObject];
    if (json == nil) {
        return nil;
    }
    NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithDictionary:json];
    [dic removeObjectForKey:@"tokenEnable"];
    [dic removeObjectForKey:@"adno"];
    [dic removeObjectForKey:@"prepared"];
    [dic removeObjectForKey:@"shouldAddHomeRank"];
    [dic removeObjectForKey:@"shouldAddHomeGuessYouLikeRank"];
    [dic removeObjectForKey:@"shouldAddCenterBigFeedHomeRank"];
    [dic removeObjectForKey:@"shouldAddHomeBannerHomeRank"];
    if (self.extraParams) {
        [dic addEntriesFromDictionary:self.extraParams];
    }
    if (self.positionName) {
        NSDictionary *extra = [XMIAdHelper extraReportParamsForAdPosition:self.positionName];
        if (extra) {
            [dic addEntriesFromDictionary:extra];
        }
    }
    return dic;
}

@end


@implementation XMIAdExposeReporter

- (void)fillWithAd:(XMIAdRelatedData *)adData {
    [super fillWithAd:adData];
    
    self.ubtReportMap = [XMIAdReportHelper generateUbtReportMapWithReporter:self adData:adData];
    
    if (adData.isMobileRtb) {
        NSData *jsonData = [adData.commonReportMap dataUsingEncoding:NSUTF8StringEncoding];
        NSError *err;
        NSDictionary *dict;
        if (jsonData) {
            dict = [NSJSONSerialization JSONObjectWithData:jsonData
                                                   options:NSJSONReadingMutableContainers
                                                     error:&err];
        }
        if (err == nil && dict) {// 新版本价格单位统一用 元
            NSString *priceEncode = adData.priceEncrypt;// 价格加密 dsp广告
            if (priceEncode) {
                [dict setValue:priceEncode forKey:@"adxRtbSettlementPrice"];
                [dict setValue:priceEncode forKey:@"adxRtbRealTimePrice"];
                NSString *newMap = [dict yy_modelToJSONString];
                adData.commonReportMap = newMap;
            }
        }
    }
    
    self.commonReportMap = adData.commonReportMap;
    self.logType = kAdLogTypeShow;
    self.tokenEnable = adData.showTokenEnable;
    if (adData.showTokenEnable) {
        if (adData.showTokens && adData.showTokens.count > 0) {
            self.showToken = [adData.showTokens lastObject];
            adData.showTokens = adData.showTokens.count > 1 ? [adData.showTokens subarrayWithRange:NSMakeRange(0, adData.showTokens.count - 1)] : @[];
        }
    }
}

- (void)fillWithAdShowOb:(XMIAdRelatedData *)adData {
    [self fillWithAd:adData];
    self.logType = kAdLogTypeShowOb;
}

- (NSDictionary *)toDictionary {
    NSMutableDictionary *dic = [super toDictionary];
    if (dic == nil) {
        return nil;
    }
    // token需要解码
    dic[@"showToken"] = [XMIAdConverter decodeAdToken:self.showToken];
    
    return dic;
}

- (void)toRealExposeReporter {
    // 真实曝光，为方便统计，需要将adid变成负数，广告位加上_new后缀
    self.adItemId = -self.adItemId;
    self.positionName = [NSString stringWithFormat:@"%@_new", self.positionName];
}

@end


@implementation XMIAdClickReporter

- (void)fillWithAd:(XMIAdRelatedData *)adData {
    [super fillWithAd:adData];
    
    self.ubtReportMap = [XMIAdReportHelper generateUbtReportMapWithReporter:self adData:adData];
    self.clickPostMap = adData.clickPostMap;
    self.url = adData.link;
    if (adData.finishSeconds > 0) {
        self.finishSeconds = (NSInteger)adData.finishSeconds;
    }
    self.logType = kAdLogTypeClick;
    self.tokenEnable = adData.clickTokenEnable;
    
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerGetPreLoadAdItemResult:)]) {
        self.preLoadH5Status = [manager.delegate managerGetPreLoadAdItemResult:adData];
    }
    
    self.clickRequestMethodPost = [adData.clickRequestMethod.lowercaseString isEqualToString:@"post"];
    
    if (adData.clickTokenEnable) {
        if (adData.clickTokens && adData.clickTokens.count > 0) {
            self.clickToken = [adData.clickTokens lastObject];
            adData.clickTokens = adData.clickTokens.count > 1 ? [adData.clickTokens subarrayWithRange:NSMakeRange(0, adData.clickTokens.count - 1)] : @[];
        }
    }
}

- (NSDictionary *)toDictionary {
    NSMutableDictionary *dic = [super toDictionary];
    if (dic == nil) {
        return nil;
    }
    [dic removeObjectForKey:@"url"];
    [dic removeObjectForKey:@"realLink"];
    // 这里的ubtReportMap不放在URL里，而是单独拼在adrecord的最后，防止网关截断的时候影响有效数据(仅GET，POST放到body里)
    [dic removeObjectForKey:@"ubtReportMap"];
    // 这里的clickPostMap放在body里（仅POST，GET不传该字段）
    [dic removeObjectForKey:@"clickPostMap"];

    // token需要解码
    dic[@"clickToken"] = [XMIAdConverter decodeAdToken:self.clickToken];
    
    return dic;
}

- (void)fillWithClickInfo:(NSDictionary *)clickInfo {
    if (clickInfo == nil) {
        return;
    }
    // x, y, absX, absY
    NSString *x = clickInfo[@"x"];
    NSString *y = clickInfo[@"y"];
    NSString *absX = clickInfo[@"absX"];
    NSString *absY = clickInfo[@"absY"];
    // 要么全在，要么丢弃
    if (x && y && absX && absY) {
        self.x = x;
        self.y = y;
        self.absX = absX;
        self.absY = absY;
    }
    NSDictionary *extraParams = [clickInfo objectForKey:@"extraParams"];
    if (extraParams) {
        if (self.extraParams) {
            NSMutableDictionary *combineParams = [self.extraParams mutableCopy];
            [combineParams addEntriesFromDictionary:extraParams];
            self.extraParams = combineParams;
        } else {
            self.extraParams = extraParams;
        }
    }
}

- (NSDictionary *)getClickInfo {
    NSMutableDictionary *dic = [[NSMutableDictionary alloc] init];
    dic[@"absX"] = self.absX;
    dic[@"absY"] = self.absY;
    dic[@"x"] = self.x;
    dic[@"y"] = self.y;
    return dic;
}

@end


@implementation XMIAdSpecailClickReporter
#define kADSpecailClickUrl @"ximalaya.com/adrecord/thirdAdRecord"

- (void)fillWithAd:(XMIAdRelatedData *)adData {
    [super fillWithAd:adData];
    
    self.app = @"iting";
    self.version = [XMICommonUtils appVersion];
    self.impl = [XMICommonUtils bundleId];
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerGetIsAppleReview)]) {
        if ([manager.delegate managerGetIsAppleReview]) {
            self.isAppleReview = @"true";
        }
    }
    self.appid = [XMIAdManager appID];
    self.mac = [XMICommonUtils macaddress];
    self.adId = [XMIAdHeader sharedInstance].deviceId;
}

+ (BOOL)isSpecail:(NSString *)url {
    if (url == nil) {
        return NO;
    }
    NSRange range = [url rangeOfString:kADSpecailClickUrl];
    return range.location != NSNotFound;
}

@end


@implementation XMIAdCommonReporter

- (void)fillWithAd:(XMIAdRelatedData *)adData {
    self.responseId = adData.responseId;
    self.slotId = adData.slotId;
    self.positionId = adData.positionId;
    self.positionName = adData.positionName;
    self.adItemId = adData.adid;
    self.adSource = adData.adtype;
    self.commonReportMap = adData.commonReportMap;
    self.adAppId = [XMIAdManager appID];
    self.sdkVersion = [XMIAdManager sdkVersion];
    self.sourceName = adData.sourceName;
    self.startSource = adData.startSource;
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerGetAlbumId)]) {
        self.albumId = [manager.delegate managerGetAlbumId];
    }
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerGetTrackId)]) {
        self.trackId = [manager.delegate managerGetTrackId];
    }
}

@end


@implementation XMIAdLoadReporter

- (void)fillWithAd:(XMIAdRelatedData *)adData {
    [super fillWithAd:adData];
    
    self.logType = kAdLogTypeLoadRecord;
}

@end


@implementation XMIAdVideoReporter

- (void)fillWithAd:(XMIAdRelatedData *)adData {
    [super fillWithAd:adData];
    
    self.logType = KAdLogTypeVideoRecord;
}

@end


@implementation XMIAdDpReporter

- (void)fillWithAd:(XMIAdRelatedData *)adData {
    [super fillWithAd:adData];
    
    self.logType = kAdLogTypeDpRecord;
}

@end


@implementation XMIAdNewExposeReporter

- (void)fillWithAd:(XMIAdRelatedData *)adData {
    [super fillWithAd:adData];
    
    self.logType = kAdLogTypeAdNewExpose;
}

@end


@implementation XMIAdDspShowReporter

- (void)fillWithAd:(XMIAdRelatedData *)adData {
    [super fillWithAd:adData];
    self.commonReportMap = @"";
    self.sdkShow = 1;
    self.dspPositionId = adData.dspPositionId;
    self.logType = kAdLogTypeDspShow;
}

@end

@implementation XMIAdVirtualReporter

- (void)fillWithAd:(XMIAdRelatedData *)adData {
    [super fillWithAd:adData];
    
    self.logType = kAdLogTypeVirtualShow;
}

@end

@implementation XMIAdAnchorReporter

- (void)fillWithAd:(XMIAdRelatedData *)adData {
    [super fillWithAd:adData];
    self.logType = kAdLogTypeAnchorClick;
}

@end

@implementation XMIAdLandingPageReporter

- (void)fillWithAd:(XMIAdRelatedData *)adData {
    [super fillWithAd:adData];
    self.logType = kAdLogTypeLoadRecord;
}

@end
