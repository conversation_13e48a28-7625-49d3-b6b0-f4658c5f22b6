//
//  XMIRewardAd.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/6/30.
//

#import "XMIRewardAd.h"
#import "XMIAdHelper.h"
#import "XMIAdReporter+AD.h"
#import "XMIRewardAdFactory.h"


@implementation XMIRewardAd

+ (instancetype)rewardAdWithRelateData:(XMIAdRelatedData *)relatedData
{
    [XMIAdHelper initSdkWithAdType:relatedData.adtype];
    return [XMIRewardAdFactory rewardAdWithRelatedData:relatedData];
    return [[self alloc] init];
}

- (void)startLoadAd
{
    NSAssert(false, @"自类需实现相关方法");
}

- (void)showFromRootViewController:(UIViewController *)rootViewController
{
    NSAssert(false, @"自类需实现相关方法");
}
@end


@implementation XMIRewardAd (inner)

- (void)loadSuccess
{
    self.loaded = YES;
    if ([self.delegate respondsToSelector:@selector(rewardAdDidLoadSucess:)]) {
        [self.delegate rewardAdDidLoadSucess:self];
    }
}

- (void)loadFail:(NSError *)error
{
    if ([self.delegate respondsToSelector:@selector(rewardAd:didFailWithError:)]) {
        [self.delegate rewardAd:self didFailWithError:error];
    }
}

- (void)loadVideoSuccess
{
    self.readyToShow = YES;
    if ([self.delegate respondsToSelector:@selector(rewardAdDidReadyToShow:)]) {
        [self.delegate rewardAdDidReadyToShow:self];
    }
}

- (void)loadVideoFail:(NSError *)error
{
    if ([self.delegate respondsToSelector:@selector(rewardAd:didFailWithError:)]) {
        [self.delegate rewardAd:self didFailWithError:error];
    }
}

- (void)controllerWillShow
{
    if ([self.delegate respondsToSelector:@selector(rewardAdWillShow:)]) {
        [self.delegate rewardAdWillShow:self];
    }
}

- (void)controllerWillClose
{
    if ([self.delegate respondsToSelector:@selector(rewardAdWillClose:)]) {
        [self.delegate rewardAdWillClose:self];
    }
}

- (void)controllerDidShow
{
    if ([self.delegate respondsToSelector:@selector(rewardAdDidShow:)]) {
        [self.delegate rewardAdDidShow:self];
    }
}


- (void)controllerDidHide
{
    if ([self.delegate respondsToSelector:@selector(rewardAdDidClose:)]) {
        [self.delegate rewardAdDidClose:self];
    }
}

- (void)didClickAd
{
    if ([self.delegate respondsToSelector:@selector(rewardAdDidClick:)]) {
        [self.delegate rewardAdDidClick:self];
    }
}

- (void)didClickSkip
{
    if ([self.delegate respondsToSelector:@selector(rewardAdDidSkip:)]) {
        [self.delegate rewardAdDidSkip:self];
    }
}

- (void)rewardSuccess:(NSDictionary *)userInfo
{
    if ([self.delegate respondsToSelector:@selector(rewardAdDidRewardSuccess:userInfo:)]) {
        [self.delegate rewardAdDidRewardSuccess:self userInfo:userInfo];
    }
}

- (void)rewardFail:(NSError *)error
{
    if ([self.delegate respondsToSelector:@selector(rewardAd:didRewardFailWithError:)]) {
        [self.delegate rewardAd:self didRewardFailWithError:error];
    }
}

- (void)playFinished
{
    if ([self.delegate respondsToSelector:@selector(rewardAdDidFinishPlay:)]) {
        [self.delegate rewardAdDidFinishPlay:self];
    }
}


@end
