//
//  XMIRewardAd.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/6/30.
//

#import "XMIBaseAd.h"
#import <XMAd/XMIAdRelatedData.h>

NS_ASSUME_NONNULL_BEGIN

@class XMIRewardAd;

@protocol XMIRewardAdDelegate <NSObject>

@optional

- (void)rewardAdDidLoadSucess:(XMIRewardAd *)rewardAd;

- (void)rewardAdDidReadyToShow:(XMIRewardAd *)rewardAd;

- (void)rewardAdWillShow:(XMIRewardAd *)rewardAd;

- (void)rewardAdWillClose:(XMIRewardAd *)rewardAd;

- (void)rewardAdDidShow:(XMIRewardAd *)rewardAd;

- (void)rewardAdDidClose:(XMIRewardAd *)rewardAd;

- (void)rewardAdDidClick:(XMIRewardAd *)rewardAd;

- (void)rewardAdDidSkip:(XMIRewardAd *)rewardAd;

- (void)rewardAdDidRewardSuccess:(XMIRewardAd *)rewardAd userInfo:(NSDictionary *)userInfo;

- (void)rewardAd:(XMIRewardAd *)rewardAd didFailWithError:(NSError *)error;

- (void)rewardAd:(XMIRewardAd *)rewardAd didRewardFailWithError:(NSError *)error;

- (void)rewardAdDidFinishPlay:(XMIRewardAd *)rewardAd;

@end

@protocol XMIRewardAdVideoControlProtocol <NSObject>

@optional

@property (nonatomic, assign) BOOL mute;

@end

@interface XMIRewardAd : XMIBaseAd <XMIRewardAdVideoControlProtocol>

+ (instancetype)rewardAdWithRelateData:(XMIAdRelatedData *)relatedData;

@property (nonatomic, strong) XMIAdRelatedData* relatedData;

@property (nonatomic, weak) id<XMIRewardAdDelegate> delegate;

@property (nonatomic, assign) NSTimeInterval timeoutInterval;


@property (nonatomic, assign) BOOL loaded;

@property (nonatomic, assign) BOOL readyToShow;

- (void)startLoadAd;

- (void)showFromRootViewController:(UIViewController *)rootViewController;

@end

@interface XMIRewardAd (inner)

- (void)loadSuccess;

- (void)loadFail:(NSError *)error;

- (void)loadVideoSuccess;

- (void)loadVideoFail:(NSError *)error;

- (void)controllerWillShow;

- (void)controllerWillClose;

- (void)controllerDidShow;

- (void)controllerDidHide;

- (void)didClickAd;

- (void)didClickSkip;

- (void)rewardSuccess:(NSDictionary *)userInfo;

- (void)rewardFail:(NSError *)error;

- (void)playFinished;

@end

NS_ASSUME_NONNULL_END
