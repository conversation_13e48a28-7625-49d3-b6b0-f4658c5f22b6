//
//  XMIRewardAdManager.h
//  XMAd
//
//  Created by cui<PERSON>z<PERSON> on 2022/9/13.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class XMIRewardAdManager;

@protocol XMIRewardAdManagerDelegate <NSObject>

@optional

- (void)rewardAdManagerDidRewardSuccess:(XMIRewardAdManager *)manager extInfo:(NSDictionary *)extInfo;

- (void)rewardAdManager:(XMIRewardAdManager *)manager didFailWithError:(NSError *)error;

- (void)rewardAdManager:(XMIRewardAdManager *)manager didRewardFailWithError:(NSError *)error;

- (void)rewardAdManagerDidReadyToShow:(XMIRewardAdManager *)manager;

- (void)rewardAdManagerWillShowReward:(XMIRewardAdManager *)manager;

- (void)rewardAdManagerDidCancel:(XMIRewardAdManager *)manager;

- (void)rewardAdManagerDidClose:(XMIRewardAdManager *)manager;

@end

@interface XMIRewardAdManager : NSObject

@property (nonatomic, weak) id<XMIRewardAdManagerDelegate> delegate;

@property (nonatomic, weak) UIViewController *rootViewController;

@property (nonatomic, assign) NSTimeInterval timeoutInterval;

- (void)loadRewardAdWithPositionName:(NSString *)positionName;

- (void)loadRewardAdWithPositionName:(NSString *)positionName extra:(NSDictionary *_Nullable)extra;

- (BOOL)canMute;

- (void)mute:(BOOL)isMuted;

@end

NS_ASSUME_NONNULL_END
