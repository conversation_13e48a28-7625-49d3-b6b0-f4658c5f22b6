//
//  XMIRewardAdManager.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/9/13.
//

#import "XMIRewardAdManager.h"
#import "XMIAdDataCenter.h"
#import "XMIAdPreviewManager.h"
#import "XMICommonUtils.h"
#import "XMIAdMacro.h"
#import "XMIAdError.h"
#import "XMIAdReporter+AD.h"
#import "XMIRewardAd.h"
#import "XMIAdReportHelper.h"
#import "XMIAdManager.h"

typedef enum : NSUInteger {
    enumXMIRewardAdManagerStateInit,
    enumXMIRewardAdManagerStateRequesting,
    enumXMIRewardAdManagerStateVideoLoading,
    enumXMIRewardAdManagerStateVideoShowing,
} enumXMIRewardAdManagerState;

@interface XMIRewardAdManager ()<XMIRewardAdDelegate>

@property (nonatomic, assign) enumXMIRewardAdManagerState state;

@property (nonatomic, strong) id<XMIAdRequestProtocol> request;

@property (nonatomic, strong, nullable) NSTimer *timeoutTimer;

@property (nonatomic, strong) XMIRewardAd *rewardAd;

@property (nonatomic, strong) NSMutableArray *rewardAds;

@property (nonatomic ,strong) NSDate *showStartDate;

@property (nonatomic, assign) BOOL rewardSuccess;

@property (nonatomic, strong) NSDictionary *extraInfo;

@end

@implementation XMIRewardAdManager

- (instancetype)init
{
    self = [super init];
    if (self) {
        if ([[XMIAdManager sharedInstance].delegate respondsToSelector:@selector(managerGetRewardAdTimeout)]) {
            self.timeoutInterval = [[XMIAdManager sharedInstance].delegate managerGetRewardAdTimeout];
        }
        
    }
    return self;
}

- (void)loadRewardAdWithPositionName:(NSString *)positionName {
    [self loadRewardAdWithPositionName:positionName extra:nil];
}

- (void)loadRewardAdWithPositionName:(NSString *)positionName extra:(NSDictionary *_Nullable)extra {
    if (self.state != enumXMIRewardAdManagerStateInit) {
        return;
    }
    /// 加入预览数据
    long long slotId = [XMIAdDataCenter getSlotIdByPositionName:positionName];
    XMIAdRespAdData *prevData = [XMIAdPreviewManager getPreviewResponseWith:slotId];
    if (prevData) {
        NSArray<XMIAdRelatedData *> *preDatas = [XMIAdDataCenter adDataFromResponse:prevData];
        if (preDatas.count > 0) {
            [self didLoadData:preDatas andUseTime:0];
        }
//            [tempArray addObjectsFromArray:adArray];
        return;
    }
    self.extraInfo = extra;
    NSMutableDictionary *props = [NSMutableDictionary dictionary];
    [props addEntriesFromDictionary:extra];
    [self.request cancel];
    [self.timeoutTimer invalidate];
    self.state = enumXMIRewardAdManagerStateRequesting;
    long long startMs = [XMICommonUtils currentTimestamp];
    @weakify(self)
    id<XMIAdRequestProtocol> request = [XMIAdDataCenter adDataRequestWithPositionName:positionName props:props dataHandler:^(NSArray<XMIAdRelatedData *> * _Nullable adArray, NSError * _Nullable error) {
        XMILog(@"%@", error);
        long long endMs = [XMICommonUtils currentTimestamp];
        long long useTime = endMs - startMs;
        
        @strongify(self)
        if (!self) {
            return;
        }
        if (!self.request) {
            return;
        }
        
        if (error != nil) {
            // TODO: 打印日志
            [self didLoadFailWithError:error andUseTime:useTime];
            return;
        }
        
        if (adArray.count < 1) {
            [self didLoadFailWithError:[XMIAdError emptyDataError] andUseTime:useTime];
            return;
        }
        
        [self didLoadData:adArray andUseTime:useTime];
    }];
    self.request = request;
    self.timeoutTimer = [NSTimer scheduledTimerWithTimeInterval:self.timeoutInterval > 0 ? self.timeoutInterval : 10 target:self selector:@selector(onTimeout:) userInfo:nil repeats:NO];
    [request start];
}

- (void)onTimeout:(NSTimer *)timer
{
    [self.timeoutTimer invalidate];
    self.timeoutTimer = nil;
    if (self.rewardAd) {
        return;
    }
    if (self.rewardAds.count > 0) {
        for (XMIRewardAd *rewardAd in [self.rewardAds copy]) {
            if (rewardAd.readyToShow) {
                self.rewardAd = rewardAd;
                self.rewardAds = nil;
                if ([self.delegate respondsToSelector:@selector(rewardAdManagerDidReadyToShow:)]) {
                    [self.delegate rewardAdManagerDidReadyToShow:self];
                }
                dispatch_async(dispatch_get_main_queue(), ^{
                    [rewardAd showFromRootViewController:self.rootViewController];
                });
                return;
            }
        }
        for (XMIRewardAd *rewardAd in [self.rewardAds copy]) {
            if (rewardAd.loaded) {
                self.rewardAd = rewardAd;
                self.rewardAds = nil;
                if ([self.delegate respondsToSelector:@selector(rewardAdManagerDidReadyToShow:)]) {
                    [self.delegate rewardAdManagerDidReadyToShow:self];
                }
                dispatch_async(dispatch_get_main_queue(), ^{
                    [rewardAd showFromRootViewController:self.rootViewController];
                });
                return;
            }
            rewardAd.delegate = nil;
        }
    } else if (!self.request) {
        return;
    }
    
    [self.request cancel];
    self.request = nil;
    self.rewardAds = nil;
    self.rewardAd = nil;
    [self didLoadFailWithError:[XMIAdError timeoutError] andUseTime:self.timeoutInterval * 1000];
}

- (void)didLoadData:(NSArray *)dataArray andUseTime:(long long)useTime
{
    self.state = enumXMIRewardAdManagerStateVideoLoading;
    NSMutableArray *ads = [NSMutableArray arrayWithCapacity:dataArray.count];
    [dataArray enumerateObjectsUsingBlock:^(XMIAdRelatedData *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        XMIRewardAd *rewardAd = [XMIRewardAd rewardAdWithRelateData:obj];
        rewardAd.delegate = self;
        [ads addObject:rewardAd];
    }];
    self.rewardAds = ads;
    XMIRewardAd *firstRewardAd = ads.firstObject;
    [firstRewardAd startLoadAd];
}

- (void)didLoadFailWithError:(NSError *)error andUseTime:(long long)useTime {
    [self.timeoutTimer invalidate];
    self.timeoutTimer = nil;
    self.request = nil;
    XMIAdRelatedData *rData = [XMIAdDataCenter getEmptyFeedAdData];
    XMIAdStatusReporter *reporter = [[XMIAdStatusReporter alloc] init];
    reporter.paddingMs = useTime;
    [[XMIAdReporter sharedInstance] addStatusReport:reporter];
    
    XMIAdStatusReporter *statusReporter = [XMIAdReporter reporterWithError:error andReporter:reporter.reporterId andAd:rData];
    [XMIAdReporter reportRequestStatusWithReporter:statusReporter];
    if (self.delegate && [self.delegate respondsToSelector:@selector(rewardAdManager:didFailWithError:)]) {
        [self.delegate rewardAdManager:self didFailWithError:error];
    }
    self.state = enumXMIRewardAdManagerStateInit;
}

#pragma mark - reward ad delegate

- (void)rewardAdDidRewardSuccess:(XMIRewardAd *)rewardAd userInfo:(NSDictionary *)userInfo
{
    NSMutableDictionary *extraParams = [NSMutableDictionary dictionary];
    extraParams[@"adUserType"] = rewardAd.relatedData.adUserType ? : @"";
    NSTimeInterval showTimeMs = [[NSDate date] timeIntervalSinceDate:self.showStartDate];
    extraParams[@"showTimeMs"] = @((long long)(showTimeMs * 1000));
    extraParams[@"uid"] = [[XMIAdManager sharedInstance] getUid];
    [extraParams addEntriesFromDictionary:self.extraInfo];
    XMIAdExposeReporter *reporter = [[XMIAdExposeReporter alloc] init];
    [reporter fillWithAd:rewardAd.relatedData];
    reporter.logType = @"showTime";
    reporter.extraParams = extraParams;
    reporter.showType = XMIAdShowTypeVideo;
    [[XMIAdReporter sharedInstance] addExposeReport:reporter];
    self.rewardSuccess = YES;
    
    NSMutableDictionary *extInfo = [NSMutableDictionary dictionary];
    extInfo[@"adId"] = [NSString stringWithFormat:@"%lld", rewardAd.relatedData.adid];
    extInfo[@"responseId"] = @(rewardAd.relatedData.responseId);
    extInfo[@"adViewDuration"] = @((NSInteger)showTimeMs);
    
    
    if ([self.delegate respondsToSelector:@selector(rewardAdManagerDidRewardSuccess:extInfo:)]) {
        [self.delegate rewardAdManagerDidRewardSuccess:self extInfo:extInfo];
    }
}

- (void)rewardAdDidSkip:(XMIRewardAd *)rewardAd
{
   
}

- (void)rewardAd:(XMIRewardAd *)rewardAd didFailWithError:(NSError *)error
{
    [self.rewardAds removeObject:rewardAd];
    XMIRewardAd *nextRewardAd = [self.rewardAds firstObject];
    if (nextRewardAd) {
        [nextRewardAd startLoadAd];
    } else {
        if ([self.delegate respondsToSelector:@selector(rewardAdManager: didFailWithError:)]) {
            [self.delegate rewardAdManager:self didFailWithError:error];
        }
        self.rewardAd = nil;
        self.state = enumXMIRewardAdManagerStateInit;
    }
  
}

- (void)rewardAd:(XMIRewardAd *)rewardAd didRewardFailWithError:(NSError *)error
{
    if (rewardAd == self.rewardAd) {
        if ([self.delegate respondsToSelector:@selector(rewardAdManager: didRewardFailWithError:)]) {
            [self.delegate rewardAdManager:self didRewardFailWithError:error];
        }
        self.rewardAd = nil;
        self.state = enumXMIRewardAdManagerStateInit;
    }
}

- (void)rewardAdDidLoadSucess:(XMIRewardAd *)rewardAd
{
    
}

- (void)rewardAdDidReadyToShow:(XMIRewardAd *)rewardAd
{
    dispatch_async(dispatch_get_main_queue(), ^{
        if (!self.timeoutTimer) {
            self.rewardAds = nil;
            return;
        }
        if (!self.rewardAd) {
            [self.timeoutTimer invalidate];
            self.timeoutTimer = nil;
            if ([self.delegate respondsToSelector:@selector(rewardAdManagerDidReadyToShow:)]) {
                [self.delegate rewardAdManagerDidReadyToShow:self];
            }
            self.rewardAd = rewardAd;
            self.rewardAds = nil;
            self.request = nil;
                [rewardAd showFromRootViewController:self.rootViewController];
        }
    });
}

- (void)rewardAdWillShow:(XMIRewardAd *)rewardAd
{
    if ([self.delegate respondsToSelector:@selector(rewardAdManagerWillShowReward:)]) {
        [self.delegate rewardAdManagerWillShowReward:self];
    }
}

- (void)rewardAdDidShow:(XMIRewardAd *)rewardAd
{
    self.state = enumXMIRewardAdManagerStateVideoShowing;
    self.rewardSuccess = NO;
    self.showStartDate = [NSDate date];
    
    // 增加字段，上个广告的数据
    NSMutableDictionary *extraReportParams = [NSMutableDictionary dictionaryWithDictionary:rewardAd.relatedData.extraReportParams];
    [extraReportParams addEntriesFromDictionary:self.extraInfo];
    rewardAd.relatedData.extraReportParams = extraReportParams;
    
    //tingshow
    [XMIAdReporter exposeReportTingShowWithAd:rewardAd.relatedData andShowType:XMIAdShowTypeVideo];
    //showob
    NSMutableDictionary *extraParams = [NSMutableDictionary dictionary];
    if (rewardAd.relatedData.adtype == XMIAdTypeXM) {
        extraParams[@"sdkType"] = @([rewardAd.relatedData adItemSDKType]);
        extraParams[@"dspPositionId"] = rewardAd.relatedData.dspPositionId ? : @"";
    }
    extraParams[@"uid"] = [[XMIAdManager sharedInstance] getUid];
    [extraParams addEntriesFromDictionary:self.extraInfo];
    XMIAdExposeReporter *reporter = [[XMIAdExposeReporter alloc] init];
    [reporter fillWithAdShowOb:rewardAd.relatedData];
    reporter.extraParams = extraParams;
    reporter.showType = XMIAdShowTypeVideo;
    [[XMIAdReporter sharedInstance] addExposeReport:reporter];
}


- (void)rewardAdDidFinishPlay:(XMIRewardAd *)rewardAd
{
    NSMutableDictionary *extraParams = [NSMutableDictionary dictionary];
    if (rewardAd.relatedData.adtype == XMIAdTypeXM) {
        extraParams[@"sdkType"] = @([rewardAd.relatedData adItemSDKType]);
        extraParams[@"dspPositionId"] = rewardAd.relatedData.dspPositionId ? : @"";
    }
    extraParams[@"uid"] = [[XMIAdManager sharedInstance] getUid];
    extraParams[@"playFinish"] = @(1);
    [extraParams addEntriesFromDictionary:self.extraInfo];
    XMIAdExposeReporter *reporter = [[XMIAdExposeReporter alloc] init];
    [reporter fillWithAdShowOb:rewardAd.relatedData];
    reporter.extraParams = extraParams;
    reporter.showType = XMIAdShowTypeVideo;
    [[XMIAdReporter sharedInstance] addExposeReport:reporter];
}

- (void)rewardAdDidClick:(XMIRewardAd *)rewardAd
{
    if (rewardAd.relatedData.adtype != XMIAdTypeXM) {
        [XMIAdReporter clickReportWithAd:rewardAd.relatedData andView:nil andUserInfo:nil];
    }
}

- (void)rewardAdDidClose:(XMIRewardAd *)rewardAd
{
    if (self.rewardSuccess) {
        if ([self.delegate respondsToSelector:@selector(rewardAdManagerDidClose:)]) {
            [self.delegate rewardAdManagerDidClose:self];
        }
    } else {
        if ([self.delegate respondsToSelector:@selector(rewardAdManagerDidCancel:)]) {
            [self.delegate rewardAdManagerDidCancel:self];
        }
    }
    self.rewardAd.delegate = nil;
    self.rewardAd = nil;
    self.state = enumXMIRewardAdManagerStateInit;
}

- (BOOL)canMute
{
    return [self.rewardAd respondsToSelector:@selector(setMute:)];
}

- (void)mute:(BOOL)isMuted
{
    if (![self canMute]) {
        return;
    }
    self.rewardAd.mute = isMuted;
}


@end
