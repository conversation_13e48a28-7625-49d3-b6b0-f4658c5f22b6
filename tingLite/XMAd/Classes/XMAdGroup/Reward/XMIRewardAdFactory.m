//
//  XMIRewardAdFactory.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/8.
//

#import "XMIRewardAdFactory.h"
#import "XMIAdConverter.h"
#import "XMIRewardAd.h"
#import "XMIAdRelatedData.h"
#import "XMIAdHelper.h"

@implementation XMIRewardAdFactory

+ (XMIRewardAd *)rewardAdWithRelatedData:(XMIAdRelatedData *)relatedData
{
    [XMIAdHelper initSdkWithAdType:relatedData.adtype];
    NSString *provider = [XMIAdConverter providerFromAdType:relatedData.adtype];
    
    if (provider.length > 0) {
        NSString *clsName = [NSString stringWithFormat:@"XMI%@RewardAdFactory", provider];
        __typeof(self) cls = NSClassFromString(clsName);
        if (cls != nil) {
            return [cls rewardAdWithRelatedData:relatedData];
        }
    }
    return nil;
}


@end
