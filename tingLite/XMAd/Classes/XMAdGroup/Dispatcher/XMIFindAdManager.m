//
//  XMIFindAdManager.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON>z<PERSON> on 2022/3/2.
//

#import "XMIFindAdManager.h"
#include <Foundation/Foundation.h>
#import "XMIAdMacro.h"
#import "XMIAdError.h"
#import <XMWebImage/SDWebImageManager.h>
#import <XMConfigCenter/XMConfigCenter.h>
#import <XMIAdABTest.h>
#import "XMIFeedAdModelGroup.h"
#import "XMIFeedAdViewFactory.h"
#import "XMIAdDataCenter.h"
#import "XMIAdPreviewManager.h"
#import "XMICommonUtils.h"
#import "XMIAdReportHelper.h"
#import "XMIAdReporter+AD.h"
#import "XMIAdDefines.h"
#import "XMIFeedAdModel.h"
#import "XMIAdHelper.h"
#import "XMIAdLoader.h"
#import "XMIFeedAdCustomViewFactory.h"
#import "XMIAdManager.h"
#import "XMIAdConverter.h"
#import "XMIAdNativeLogger.h"

typedef enum : NSUInteger {
    XMIFindAdManagerRequestStateNone,
    XMIFindAdManagerRequestStateReload,
    XMIFindAdManagerRequestStateLoadMore,
} XMIFindAdManagerRequestState;

static NSString * const kHeaderAdShowTsKey = @"kHeaderAdShowTsKey";

@interface XMIFindAdManager ()<XMIFeedAdRendererDelegate, XMIFeedAdModelDelegate>

@property (nonatomic, strong) NSMutableDictionary *reusePool;

@property (nonatomic, strong) NSMutableArray *renderers;

@property (nonatomic, strong) XMIFeedAdRenderer *rendererForHeader;

@property (nonatomic, copy) NSString *positionName;

@property (nonatomic, copy) NSString *lastShowAdType;

@property (nonatomic, copy) NSDictionary *reloadIntervalParams;

@property (nonatomic, assign) XMIFindAdManagerRequestState requestState;

@property (nonatomic, weak) id<XMIAdRequestProtocol> request;

@property (nonatomic, strong) NSMutableArray *adModelCacheQueue;

@property (nonatomic, strong) NSMutableArray *shownAdIds;

@property (nonatomic, assign) BOOL feedbacked;

@property (nonatomic, assign) BOOL isAdPreviewMode;

@property (nonatomic, assign) NSInteger lastShownAdNo;

@property (nonatomic, assign) BOOL systemTimeInvalid;

@property (nonatomic, assign) XMIAdShowSubStyle subShowStyle;

@property (nonatomic, strong) XMIFeedAdRenderer *presetingRenderer;

@property (nonatomic, weak) XMIFeedAdModel *firstLoadingAdModel;

- (void)loadMoreAd;

@end

@implementation XMIFindAdManager
@synthesize headerAdState = _headerAdState;

- (instancetype)initWithScrollView:(UIScrollView *)scrollView positionName:(nonnull NSString *)positionName
{
    self = [self init];
    if (self) {
        self.positionName = positionName;
        self.scrollView = scrollView;
        self.edge = UIEdgeInsetsMake(16, 16, 16, 16);
        self.lastShownAdNo = -1;
        self.canLoadMore = YES;
        self.headerAdState = XMIFindAdHeaderAdWaiting;
        _abShowStyle = [XMIAdManager findNativeShowStyle];
        self.subShowStyle = (self.abShowStyle > 0) ? XMIAdSubShowSubStyleFeedAB : XMIAdSubShowSubStyleNone;
        self.findNativeRealVersion = 1;
    }
    return self;
}

- (void)updateAbShowStyle
{
    _abShowStyle = [XMIAdManager findNativeShowStyle];
    self.subShowStyle = (self.abShowStyle > 0) ? XMIAdSubShowSubStyleFeedAB : XMIAdSubShowSubStyleNone;
}

#pragma mark - setter

- (void)setScrollView:(UIScrollView *)scrollView {
    _scrollView = scrollView;
    if (scrollView) {
        [scrollView addObserver:self forKeyPath:@"contentOffset" options:NSKeyValueObservingOptionOld context:nil];
    }
}

- (void)setHeaderAdState:(XMIFindAdHeaderAdState)headerAdState {
    if (_headerAdState != XMIFindAdHeaderAdDisable) {
        _headerAdState = headerAdState;
    }
    if (self.headerAdState == XMIFindAdHeaderAdEnable &&
        self.requestState == XMIFindAdManagerRequestStateNone) {
        if ([self.delegate respondsToSelector:@selector(findManagerDidReloadAdData:)]) {
            [self.delegate findManagerDidReloadAdData:self];
        }
    }
}

- (XMIFindAdHeaderAdState)headerAdState {
    if (!self.bannerAd) {
        return XMIFindAdHeaderAdEnable;
    }
    return _headerAdState;
}

- (BOOL)isInAdShowInterval {
    if (!self.bannerAd || self.isAdPreviewMode) {
        return NO;
    }
    NSDate *date = [[NSUserDefaults standardUserDefaults] objectForKey:kHeaderAdShowTsKey];
    if (date) {
        NSInteger showInterval = [XMIAdABTest getIntegerValueWithKey:@"centerBigPicAdShowInterval" defaultValue:-1];
        if (showInterval >= 0) {
            if (NSDate.date.timeIntervalSince1970 - date.timeIntervalSince1970 < showInterval) {
                return YES;
            }
        } else {
            if ([[NSCalendar currentCalendar] isDateInToday:date]) {
                return YES;
            }
        }
    }
    return NO;
}

- (NSInteger)bigFeedAdReloadInterval {
    if (self.lastShowAdType.length) {
        if (!self.reloadIntervalParams) {
            NSDictionary *dict = [XMIAdABTest getJsonObjectWithKey:@"centerBigPicAdReloadIntervalString" defaultObject:@{}];
            if ([dict isKindOfClass:[NSDictionary class]]) {
                self.reloadIntervalParams = dict;
            }
        }
        if (self.reloadIntervalParams) {
            NSNumber *interval = self.reloadIntervalParams[self.lastShowAdType];
            if (interval) {
                return interval.integerValue;
            }
        }
    }
    return [XMIAdABTest getIntegerValueWithKey:@"centerBigPicAdReloadInterval" defaultValue:-1];
}

- (BOOL)isAdPreviewMode {
    long long slotId = [XMIAdDataCenter getSlotIdByPositionName:self.positionName];
    if ([XMIAdPreviewManager previewItemWithPosition:slotId]) {
        return YES;
    }
    return _isAdPreviewMode;
}

#pragma mark - lazy load

- (NSMutableDictionary *)reusePool
{
    if (!_reusePool) {
        _reusePool = [NSMutableDictionary dictionary];
    }
    return _reusePool;
}

- (NSMutableArray *)renderers
{
    if (!_renderers) {
        _renderers = [NSMutableArray array];
    }
    return _renderers;
}

- (NSMutableArray *)adModelCacheQueue
{
    if (!_adModelCacheQueue) {
        _adModelCacheQueue = [NSMutableArray array];
    }
    return _adModelCacheQueue;
}

- (NSMutableArray *)shownAdIds
{
    if (!_shownAdIds) {
        _shownAdIds = [NSMutableArray array];
    }
    return _shownAdIds;
}

#pragma mark - load

- (void)reloadAds
{
    [self reloadAdsIsRefresh:NO];
}

- (void)reloadAdsIsRefresh:(BOOL)isRefresh
{
    if ([self isInAdShowInterval]) {
        self.hasValidAdModelCache = NO;
        if (self.bannerAd) {
            [self.adModelCacheQueue removeAllObjects];
        }
        return;
    }
    switch (self.requestState) {
        case XMIFindAdManagerRequestStateReload:
            return;
            break;
        case XMIFindAdManagerRequestStateLoadMore:
            [self.request cancel];
            self.request = nil;
            break;
        case XMIFindAdManagerRequestStateNone:
        default:
            break;
    }
    if (isRefresh && _headerAdState == XMIFindAdHeaderAdDisable) {
        _headerAdState = XMIFindAdHeaderAdEnable;
    }
    self.requestState = XMIFindAdManagerRequestStateReload;
    self.feedbacked = NO;
    self.systemTimeInvalid = NO;
    [self.shownAdIds removeAllObjects];
    self.lastShowAdType = nil;
    if (self.bannerAd) {
        [self.adModelCacheQueue removeAllObjects];
    }
    [self loadAdData];
}

- (void)loadMoreAd
{
    if (self.findNativeRealVersion == 0) {
        //非实时化不请求更多
        return;
    }

    if (self.feedbacked || self.systemTimeInvalid || !self.canLoadMore) {
        return;
    }
    switch (self.requestState) {
        case XMIFindAdManagerRequestStateReload:
        case XMIFindAdManagerRequestStateLoadMore:
            return;
            break;
        case XMIFindAdManagerRequestStateNone:
        default:
            break;
    }
    self.requestState = XMIFindAdManagerRequestStateLoadMore;
    [self loadAdData];
}

- (void)loadAdData
{
    self.firstLoadingAdModel = nil;
    /// 加入预览数据
    long long slotId = [XMIAdDataCenter getSlotIdByPositionName:self.positionName];
    XMIAdRespAdData *prevData = nil;
    if (slotId != 293 || self.bannerAd) {
        prevData = [XMIAdPreviewManager getPreviewResponseWith:slotId];
    }
    if (prevData) {
        self.isAdPreviewMode = YES;
        NSArray<XMIAdRelatedData *> *preDatas = [XMIAdDataCenter adDataFromResponse:prevData];
        NSMutableArray *tempArray = [NSMutableArray arrayWithCapacity:0];
        [tempArray addObjectsFromArray:preDatas];
//            [tempArray addObjectsFromArray:adArray];
        [self downloadResourcesForAdDatas:tempArray useTime:0];
        return;
    }
    NSMutableDictionary *props = [[self adRequestProps] mutableCopy];
    if (!props) {
        props = [NSMutableDictionary dictionary];
    }
    NSArray *styles = nil;
    if (![XMIAdHelper shouldRequestAdWithSlotId:slotId params:props forbidStyles:&styles]) {
        [self didLoadFailWithError:[XMIAdError emptyDataError] andUseTime:0 sendReport:NO];
        return;
    }
    if (styles.count > 0) {
        props[@"shieldAdPositionNames"] = [styles componentsJoinedByString:@","];
    }
    
    long long startMs = [XMICommonUtils currentTimestamp];
    
    @weakify(self)
    id<XMIAdRequestProtocol> request = [XMIAdDataCenter adDataRequestWithPositionName:self.positionName props:props dataHandler:^(NSArray<XMIAdRelatedData *> * _Nullable adArray, NSError * _Nullable error) {
        if (error) {
            XMILogNativeInfo(@"AD_LOG_FIND",@"adx：%@ 请求失败 error:%@", self.positionName, error);
        }

        long long endMs = [XMICommonUtils currentTimestamp];
        long long useTime = endMs - startMs;
        
        @strongify(self)
        if (self.requestState == XMIFindAdManagerRequestStateReload) {
            switch (slotId) {
                case 28:
                    [[XMIAdReporter sharedInstance] feedDidRefresh];
                    break;
                case 44:
                    [[XMIAdReporter sharedInstance] guessYouLikeDidRefresh];
                    break;
                case 293:
                    if (!self.bannerAd) {
                        [[XMIAdReporter sharedInstance] centerBigFeedDidRefresh];
                    } else {
                        [[XMIAdReporter sharedInstance] homeBannerDidRefresh];
                    }
                    break;
                default:
                    break;
            }
        }
        if (!self) {
            return;
        }
        
        if (error != nil) {
            // TODO: 打印日志
            if (self.requestState == XMIFindAdManagerRequestStateReload) {
                [self.adModelCacheQueue removeAllObjects];
                [self.shownAdIds removeAllObjects];
            }
            [self didLoadFailWithError:error andUseTime:useTime sendReport:YES];
            return;
        }
        
        if (adArray.count < 1) {
            if (self.requestState == XMIFindAdManagerRequestStateReload) {
                [self.adModelCacheQueue removeAllObjects];
                [self.shownAdIds removeAllObjects];
            }
            [self didLoadFailWithError:[XMIAdError emptyDataError] andUseTime:useTime sendReport:YES];
            return;
        }
        
        [self downloadResourcesForAdDatas:adArray useTime:useTime];
    }];
    self.request = request;
    [request start];
}

- (NSDictionary *)adRequestProps
{
    NSMutableDictionary *props = [NSMutableDictionary dictionary];
    if ([self.delegate respondsToSelector:@selector(findAdManagerGetRequestParams:)]) {
        NSDictionary *params = [self.delegate findAdManagerGetRequestParams:self];
        if (params) {
            [props addEntriesFromDictionary:params];
        }
    }
    if (self.requestState == XMIFindAdManagerRequestStateReload) {
        [props addEntriesFromDictionary:@{@"shownAdIds" : [self.shownAdIds copy], @"findNativeRealVersion" : @(self.findNativeRealVersion), @"refreshAd" : @(YES), @"homeRank" : @(self.lastShownAdNo + 1)}];
    } else {
        [props addEntriesFromDictionary:@{@"shownAdIds" : [self.shownAdIds copy], @"findNativeRealVersion" : @(self.findNativeRealVersion), @"refreshAd" : @(NO), @"homeRank" : @(self.lastShownAdNo + 1)}];
    }
    if ([self.positionName isEqualToString:@"find_native"]) {
        props[@"findNativeAdShowStyle"] = @(self.abShowStyle);
    }
    if ([self.positionName isEqualToString:@"home_banner_ad"]) {
        props[@"isCenterBigFeedAd"] = @(self.bannerAd ? 0 : 1);
    }
    return [props copy];
}

- (void)downloadResourcesForAdDatas:(NSArray *)adDatas useTime:(long long)useTime
{
    BOOL reportWhenRequest = YES;
    BOOL adxRtbEnable = NO;
    XMIAdRelatedData *firstData = [adDatas objectMaybeAtIndex:0];
    if (firstData.isGuessYouLikeAd) {
        reportWhenRequest = NO;
    }
    for (XMIAdRelatedData *relatedData in adDatas) {
        if (!adxRtbEnable && relatedData.isMobileRtb) {
            adxRtbEnable = YES;
        }
        relatedData.isCenterBigFeedAd = !self.bannerAd;
        [relatedData.compositeAds enumerateObjectsUsingBlock:^(XMIAdRelatedData * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            obj.isCenterBigFeedAd = relatedData.isCenterBigFeedAd;
        }];
        [relatedData.currentCompositeAds enumerateObjectsUsingBlock:^(XMIAdRelatedData * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            obj.isCenterBigFeedAd = relatedData.isCenterBigFeedAd;
        }];
        switch (self.abShowStyle) {
            case XMIAdFindNativeShowStyleNone:
                break;
            case XMIAdFindNativeShowStyleAvatar:
            {
                if (relatedData.iconUrl.length == 0 || relatedData.adDescription.length == 0) {
                    relatedData.extraReportParams = @{
                        @"findNativeAdShowStyle" : @(XMIAdFindNativeShowStyleNone)
                    };
                } else {
                    relatedData.extraReportParams = @{
                        @"findNativeAdShowStyle" : @(XMIAdFindNativeShowStyleAvatar)
                    };
                }
            }
                break;
            case XMIAdFindNativeShowStyleBigCard:
            {
                if (relatedData.adDescription.length == 0) {
                    relatedData.extraReportParams = @{
                        @"findNativeAdShowStyle" : @(XMIAdFindNativeShowStyleNone)
                    };
                } else {
                    relatedData.extraReportParams = @{
                        @"findNativeAdShowStyle" : @(XMIAdFindNativeShowStyleBigCard)
                    };
                }
            }
                break;
            default:
                break;
        }
    }
    if (reportWhenRequest) {
        [XMIAdReporter exposeReportValidAds:adDatas];
    }
    
    if (self.mobileRtbEnable && adxRtbEnable) {
        if (self.feedbacked && self.requestState == XMIFindAdManagerRequestStateLoadMore) {
            return;
        }
        if (self.requestState == XMIFindAdManagerRequestStateReload) {
          [self.adModelCacheQueue removeAllObjects];
          [self.shownAdIds removeAllObjects];
        }
        [XMIAdLoader fillAdItems:adDatas timeout:3 rootViewController:self.rootViewController completion:^(NSArray<XMIFeedAdModel *> * _Nonnull results) {
            if (results.count > 0) {
                [self.adModelCacheQueue addObject:results.firstObject];
            }
            if (self.requestState == XMIFindAdManagerRequestStateReload) {
                self.requestState = XMIFindAdManagerRequestStateNone;
                self.request = nil;
                [[self.reusePool allValues] enumerateObjectsUsingBlock:^(NSMutableSet *obj, NSUInteger idx, BOOL * _Nonnull stop) {
                    [obj enumerateObjectsUsingBlock:^(XMIFeedAdRenderer *renderer, BOOL * _Nonnull stop) {
                        if (renderer != self.presetingRenderer) {
                            renderer.delegate = nil;
                        }
                    }];
                }];
                [self.renderers enumerateObjectsUsingBlock:^(XMIFeedAdRenderer *renderer, NSUInteger idx, BOOL * _Nonnull stop) {
                    if (renderer != self.presetingRenderer) {
                        renderer.delegate = nil;
                    }
                }];
                [self.reusePool removeAllObjects];
                [self.renderers removeAllObjects];
                if ([self.delegate respondsToSelector:@selector(findManagerDidReloadAdData:)]) {
                    [self.delegate findManagerDidReloadAdData:self];
                }
            } else {
                self.requestState = XMIFindAdManagerRequestStateNone;
                self.request = nil;
                if ([self.delegate respondsToSelector:@selector(findManagerDidLoadMoreAdData:)]) {
                    [self.delegate findManagerDidLoadMoreAdData:self];
                }
            }
        }];
        return;
    }
    // 如果第一个广告是虚拟广告，直接上报，后续的流程都不走了
    if ([XMIAdConverter isVirtualAD:firstData.adid]) {
        [XMIAdReporter virtualReportWithAd:firstData];
        self.requestState = XMIFindAdManagerRequestStateNone;
        return;
    }
    
    NSMutableArray *dataDownloaded = [NSMutableArray arrayWithArray:adDatas];
    dispatch_group_t group = dispatch_group_create();
    for (XMIAdRelatedData *relatedData in adDatas) {
        relatedData.paddingTimeMs = useTime;
        switch (relatedData.showstyle) {
            case XMIAdStyleHomeBackgroundImage:
            {
                dispatch_group_enter(group);
                [[SDWebImageManager sharedManager] loadImageWithURL:[NSURL URLWithString:relatedData.cover] options:0 progress:nil completed:^(UIImage * _Nullable image, NSData * _Nullable data, NSError * _Nullable error, SDImageCacheType cacheType, BOOL finished, NSURL * _Nullable imageURL) {
                    if (error) {
                        [dataDownloaded removeObject:relatedData];
                    }
                    dispatch_group_leave(group);
                }];
            }
                break;
                
            default:
                break;
        }
    }
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        if (self.feedbacked && self.requestState == XMIFindAdManagerRequestStateLoadMore) {
            return;
        }
        if (self.requestState == XMIFindAdManagerRequestStateReload) {
            [self.adModelCacheQueue removeAllObjects];
            [self.shownAdIds removeAllObjects];
        }
        [dataDownloaded enumerateObjectsUsingBlock:^(XMIAdRelatedData *relatedData, NSUInteger idx, BOOL * _Nonnull stop) {
            XMIFeedAdModel *model = [XMIFeedAdModel feedAdModelWithRelatedData:relatedData];
            relatedData.loadingStatus = XMIAdRelatedLoadingStatusLoadCompleted;
            model.rootViewController = self.rootViewController;
            model.delegate = self;
            [self.adModelCacheQueue addObject:model];
        }];
        self.hasValidAdModelCache = NO;
        [self.adModelCacheQueue enumerateObjectsUsingBlock:^(XMIFeedAdModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (!obj.isVirtual && obj.loadingStatus == XMIFeedAdModelLoadingStatusLoadSuccess) {
                self.hasValidAdModelCache = YES;
                *stop = YES;
            }
        }];
        XMIFeedAdModel *firstModel = nil;
        if (self.adModelCacheQueue.count > 0) {
            firstModel = [self.adModelCacheQueue firstObject];
            if ([firstModel.relatedData isExpired]) {
                self.systemTimeInvalid = YES;
            }
            [firstModel loadAdData];
            
        }
        if (self.requestState == XMIFindAdManagerRequestStateReload) {
            self.requestState = XMIFindAdManagerRequestStateNone;
            self.request = nil;
            [[self.reusePool allValues] enumerateObjectsUsingBlock:^(NSMutableSet *obj, NSUInteger idx, BOOL * _Nonnull stop) {
                [obj enumerateObjectsUsingBlock:^(XMIFeedAdRenderer *renderer, BOOL * _Nonnull stop) {
                    if (renderer != self.presetingRenderer) {
                        renderer.delegate = nil;
                    }
                }];
            }];
            [self.renderers enumerateObjectsUsingBlock:^(XMIFeedAdRenderer *renderer, NSUInteger idx, BOOL * _Nonnull stop) {
                if (renderer != self.presetingRenderer) {
                    renderer.delegate = nil;
                }
            }];
            [self.reusePool removeAllObjects];
            [self.renderers removeAllObjects];
            if (firstModel.loadingStatus == XMIFeedAdModelLoadingStatusLoading) {
                self.firstLoadingAdModel = firstModel;
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    if (self.firstLoadingAdModel == firstModel) {
                        self.firstLoadingAdModel = nil;
                        if ([self.delegate respondsToSelector:@selector(findManagerDidReloadAdData:)]) {
                            [self.delegate findManagerDidReloadAdData:self];
                        }
                    }
                });
            } else {
                if ([self.delegate respondsToSelector:@selector(findManagerDidReloadAdData:)]) {
                    [self.delegate findManagerDidReloadAdData:self];
                }
            }
            
        } else {
            self.requestState = XMIFindAdManagerRequestStateNone;
            self.request = nil;
            if ([self.delegate respondsToSelector:@selector(findManagerDidLoadMoreAdData:)]) {
                [self.delegate findManagerDidLoadMoreAdData:self];
            }
        }
        
    });
}

- (void)didLoadFailWithError:(NSError *)error andUseTime:(long long)useTime sendReport:(BOOL)sendReport {
    self.requestState = XMIFindAdManagerRequestStateNone;
    self.request = nil;
    if (sendReport) {
        XMIAdRelatedData *rData = [XMIAdDataCenter getEmptyFeedAdData];
        XMIAdStatusReporter *reporter = [[XMIAdStatusReporter alloc] init];
        reporter.paddingMs = useTime;
        [[XMIAdReporter sharedInstance] addStatusReport:reporter];
        
        XMIAdStatusReporter *statusReporter = [XMIAdReporter reporterWithError:error andReporter:reporter.reporterId andAd:rData];
        [XMIAdReporter reportRequestStatusWithReporter:statusReporter];
    }
    self.hasValidAdModelCache = NO;
    [self.adModelCacheQueue enumerateObjectsUsingBlock:^(XMIFeedAdModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (!obj.isVirtual && obj.loadingStatus == XMIFeedAdModelLoadingStatusLoadSuccess) {
            self.hasValidAdModelCache = YES;
            *stop = YES;
        }
    }];
    if (self.delegate && [self.delegate respondsToSelector:@selector(findManager:didFailLoadAdWithError:)]) {
        [self.delegate findManager:self didFailLoadAdWithError:error];
    }
}

#pragma mark - 取缓存
- (XMIFeedAdModel *)queueOutValidAdModelCache
{
    if (self.systemTimeInvalid) {
        self.hasValidAdModelCache = NO;
        return nil;
    }
    if (self.firstLoadingAdModel) {
        return nil;
    }
    ///检查过期
    if (self.adModelCacheQueue.count > 0) {
        XMIFeedAdModel *firstModel = [self.adModelCacheQueue firstObject];
        if ([firstModel.relatedData isExpired]) {
            [self.adModelCacheQueue removeAllObjects];
            self.hasValidAdModelCache = NO;
            dispatch_async(dispatch_get_main_queue(), ^{
                [self reloadAds];
            });
            return nil;
        }
    }
    XMIFeedAdModel *outModel = nil;
    for (XMIFeedAdModel *model in self.adModelCacheQueue) {
        if (model.loadingStatus == XMIFeedAdModelLoadingStatusLoadSuccess) {
            outModel = model;
            break;
        }
    }
    NSInteger index = NSNotFound;
    if (outModel) {
        index = [self.adModelCacheQueue indexOfObject:outModel];
        if (!self.bannerAd) {
            [self.adModelCacheQueue removeObject:outModel];
        }
        if (outModel.relatedData.compositeAds.count) {
            [outModel.relatedData.compositeAds enumerateObjectsUsingBlock:^(XMIAdRelatedData * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                [self.shownAdIds addObject:@(obj.adid)];
            }];
        } else {
            [self.shownAdIds addObject:@(outModel.relatedData.adid)];
        }
    }
    if (index != NSNotFound) {
        if (self.adModelCacheQueue.count - index <= 0) {
            if (self.hasValidAdModelCache) {
                self.hasValidAdModelCache = NO;
            }
        } else if (self.adModelCacheQueue.count - index > 0){
            XMIFeedAdModel *model = [self.adModelCacheQueue objectAtIndex:index];
            [model loadAdData];
        }
    }
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        self.lastShownAdNo = outModel.relatedData.adno;
        if (self.adModelCacheQueue.count == 0) {
            [self loadMoreAd];
        }
    });
    return outModel;
}

#pragma mark - renderer delegate

- (XMIFeedAdRenderer *)rendererWithAdModel:(XMIFeedAdModel *)adModel
{
   Class viewClass = [self viewClassForAdModel:adModel];
    if (!viewClass) {
        if ([self.delegate respondsToSelector:@selector(findManager:didFailLoadRenderWithError:)]) {
            [self.delegate findManager:self didFailLoadRenderWithError:[XMIAdError emptyDataError]];
        }
        return nil;
    }
    
//    //中插大图首位快速返回
//    if ([[XMConfigCenter sharedConfigCenter] getBoolValueWithGroup:@"ad" andItem:@"rendererForHeaderQuickEnable" defaultValue:YES]) {
//        if (adModel.relatedData.isHeaderModule && self.bannerAd && self.rendererForHeader && self.rendererForHeader.adModel == adModel) {
//            return self.rendererForHeader;
//        }
//    }
    XMIFeedAdRenderer *renderer = nil;
    NSMutableSet *classPool = [_reusePool objectForKey:NSStringFromClass(viewClass)];
    if (classPool) {
        renderer = [classPool anyObject];
        if (renderer) {
            renderer.adModel = adModel;
            [classPool removeObject:renderer];
            if (![self.renderers containsObject:renderer]) {
                [self.renderers addObject:renderer];
            }
            return renderer;
        }
    }
    renderer = [[XMIFeedAdRenderer alloc] initWithRenderingViewClass:viewClass];
    renderer.edge = self.edge;
    renderer.onlyReportExposeOnce = YES;
    renderer.delegate = self;
    renderer.rootViewController = self.rootViewController;
    renderer.adModel = adModel;
    if (adModel.relatedData.isHeaderModule && self.bannerAd) {
        self.rendererForHeader = renderer;
    } else {
        [self.renderers addObject:renderer];
    }
    return renderer;
}

- (UIView<XMIFeedAdViewProtocol> *)adViewWithAdModel:(XMIFeedAdModel *)adModel
{
    XMIFeedAdRenderer *renderer = [self rendererWithAdModel:adModel];
    return [renderer renderedView];
}

- (Class)viewClassForAdModel:(XMIFeedAdModel *)adModel
{
    NSString *className = nil;
    if ([self.delegate respondsToSelector:@selector(findManager:customRenderingClassNameForAdModel:)]) {
        className = [self.delegate findManager:self customRenderingClassNameForAdModel:adModel];
    }
    Class class = nil;
    if (className.length > 0) {
        class = NSClassFromString(className);
    }
    if (!class) {
        class = [XMIFeedAdCustomViewFactory feedAdViewClasslWithRelatedData:adModel.relatedData];
    }
    if (!class) {
        XMIAdShowSubStyle subShowStyle = self.subShowStyle;
        if (adModel.relatedData.isHeaderModule) {
            if (!adModel.relatedData.homePicTitleDown) {
                subShowStyle = XMIAdSubShowSubStyleSocial;
            } else {
                subShowStyle = XMIAdSubShowSubStyleNone;
            }
        } else if (adModel.relatedData.adtype == XMIAdTypeXM) {
            if ([XMIAdManager findNativeShowStyle] == 1 && (adModel.relatedData.iconUrl.length == 0 || adModel.relatedData.adDescription.length == 0)) {
                subShowStyle = XMIAdSubShowSubStyleNone;
            } else if ([XMIAdManager findNativeShowStyle] == 2 && adModel.relatedData.adDescription.length == 0) {
                subShowStyle = XMIAdSubShowSubStyleNone;
            }
        }
        class = [XMIFeedAdViewFactory feedAdViewClasslWithShowStyle:adModel.relatedData.showstyle adType:adModel.relatedData.adtype subShowStyle:subShowStyle];
    }
    return class;
}

- (CGSize)feedAdRendererWillRender:(XMIFeedAdRenderer *)renderer
{
    if ([self.delegate respondsToSelector:@selector(findManager:willRender:)]) {
        return [self.delegate findManager:self willRender:renderer];
    }
    return CGSizeMake(XMI_SCREEN_WIDTH, (XMI_SCREEN_WIDTH - 32) * 9.0f / 16.0f);
}

- (void)feedAdRendererdidRender:(XMIFeedAdRenderer *)renderer
{
    if ([self.delegate respondsToSelector:@selector(findManager:didRender:)]) {
        [self.delegate findManager:self didRender:renderer];
    }
}

- (void)feedAdRenderer:(XMIFeedAdRenderer *)renderer didRenderFailWithError:(NSError *)error
{
    if ([self.delegate respondsToSelector:@selector(findManager:renderer:didRenderFailWithError:)]) {
        [self.delegate findManager:self renderer:renderer didRenderFailWithError:error];
    }
}

- (void)feedAdRendererDidExpose:(XMIFeedAdRenderer *)renderer
{
    if (renderer.adModel.relatedData.isHeaderModule && self.bannerAd) {
        [[NSUserDefaults standardUserDefaults] setObject:[NSDate date] forKey:kHeaderAdShowTsKey];
        self.lastShowAdType = renderer.adModel.relatedData.adUserType;
    }
    if ([self.delegate respondsToSelector:@selector(findManager:rendererDidExpose:)]) {
        [self.delegate findManager:self rendererDidExpose:renderer];
    }
}

- (void)feedAdRenderer:(XMIFeedAdRenderer *)renderer adViewDidClick:(UIView<XMIFeedAdViewProtocol> *)aView withUserInfo:(nullable NSDictionary *)userInfo
{
    if ([self.delegate respondsToSelector:@selector(findManager:renderer:adViewDidClick:withUserInfo:   )]) {
        [self.delegate findManager:self renderer:renderer adViewDidClick:aView withUserInfo:userInfo];
    }
}

- (void)feedAdRendererDidRemoved:(XMIFeedAdRenderer *)renderer
{
    NSString *key = NSStringFromClass(renderer.renderingViewClass);
    NSMutableSet *set = [self.reusePool objectForKey:key];
    if (!set) {
        set = [NSMutableSet set];
        self.reusePool[key] = set;
    }
    [set addObject:renderer];
    [self.renderers removeObject:renderer];
    if ([self.delegate respondsToSelector:@selector(findManager:rendererDidRemoved:)]) {
        [self.delegate findManager:self rendererDidRemoved:renderer];
    }
}

- (void)feedAdRendererWillPresentScreen:(XMIFeedAdRenderer *)renderer
{
    if ([self.delegate respondsToSelector:@selector(findManager:rendererWillPresentScreen:)]) {
        [self.delegate findManager:self rendererWillPresentScreen:renderer];
    }
    self.presetingRenderer = renderer;
}

- (void)feedAdRendererDidPresentScreen:(XMIFeedAdRenderer *)renderer
{
    if ([self.delegate respondsToSelector:@selector(findManager:rendererDidPresentScreen:)]) {
        [self.delegate findManager:self rendererDidPresentScreen:renderer];
    }
}

- (void)feedAdRendererDetailControllerDidClosed:(XMIFeedAdRenderer *)renderer
{
    if ([self.delegate respondsToSelector:@selector(findManager:rendererDetailControllerDidClosed:)]) {
        [self.delegate findManager:self rendererDetailControllerDidClosed:renderer];
    }
    self.presetingRenderer = nil;
}

- (void)feedAdRenderer:(XMIFeedAdRenderer *)renderer playerStateChanged:(XMIPlayerPlayState)state
{
    if ([self.delegate respondsToSelector:@selector(findManager:renderer:playerStateChanged:)]) {
        [self.delegate findManager:self renderer:renderer playerStateChanged:state];
    }
}

- (void)feedAdRenderer:(XMIFeedAdRenderer *)renderer playerDidPlayFinish:(NSError *_Nullable)error
{
    if ([self.delegate respondsToSelector:@selector(findManager:renderer:playerDidPlayFinish:)]) {
        [self.delegate findManager:self renderer:renderer playerDidPlayFinish:error];
    }

}

- (void)feedAdRenderer:(XMIFeedAdRenderer *)renderer dislikeWithReason:(NSArray<__kindof NSString *> *)reasons
{
    if ([self.delegate respondsToSelector:@selector(findManager:renderer:dislikeWithReason:)]) {
        [self.delegate findManager:self renderer:renderer dislikeWithReason:reasons];
    }

}

- (void)feedAdRenderDidClickedClose:(nonnull XMIFeedAdRenderer *)renderer
                           userInfo:(nullable NSDictionary *)userInfo {
    if ([self.delegate respondsToSelector:@selector(findManager:rendererDidClickedClose:userInfo:)]) {
        [self.delegate findManager:self rendererDidClickedClose:renderer userInfo:userInfo];
    }
}

- (void)feedAdRenderDidClose:(XMIFeedAdRenderer *)renderer
{
    if ([self.delegate respondsToSelector:@selector(findManager:rendererDidClose:)]) {
        [self.delegate findManager:self rendererDidClose:renderer];
    }
}

- (void)feedAdRenderer:(XMIFeedAdRenderer *)renderer clickAnchorId:(NSString *)anchorId {
    if ([self.delegate respondsToSelector:@selector(findManager:clickAnchorId:)]) {
        [self.delegate findManager:self clickAnchorId:anchorId];
    }
}

- (void)dealloc
{
    if (self.scrollView) {
        [self.scrollView removeObserver:self forKeyPath:@"contentOffset" context:nil];
    }
    [_reusePool enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, id  _Nonnull obj, BOOL * _Nonnull stop) {
        if ([obj isKindOfClass:[NSMutableSet class]]) {
            [(NSMutableSet *)obj removeAllObjects];
        }
    }];
    [_reusePool removeAllObjects];
}

#pragma mark - KVO&曝光&预加载

- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSKeyValueChangeKey,id> *)change context:(void *)context
{
    [[NSNotificationCenter defaultCenter] postNotificationName:XMIFeedAdRendererScrollViewDidScrollNotification object:nil];
}

/// 控制器已经出现
- (void)controllerViewDidAppear
{
    [[NSNotificationCenter defaultCenter] postNotificationName:XMIFeedAdRendererControllerDidAppearNotification object:nil];
    
}
/// 控制器已经消失
- (void)controllerViewDidDisappear
{
    [[NSNotificationCenter defaultCenter] postNotificationName:XMIFeedAdRendererControllerDidDisappearNotification object:nil];
}

#pragma mark - 高度计算

- (CGFloat)heightForAdViewByModel:(XMIFeedAdModel *)adModel withWidth:(CGFloat)adWidth
{
    adWidth = adWidth - self.edge.left - self.edge.right;
    Class cls = [self viewClassForAdModel:adModel];
    if ([cls respondsToSelector:@selector(calAdHeight:withAdWidth:)]) {
        return [cls calAdHeight:adModel.relatedData withAdWidth:adWidth] + self.edge.top + self.edge.bottom;
    }
    return self.defaultAdViewHeight;
}


#pragma mark - admodel delegate

- (void)feedAdModelDidLoadDataSuccess:(XMIFeedAdModel *)adModel timeout:(BOOL)isTimeout
{
    if (![self.adModelCacheQueue containsObject:adModel]) {
        return;
    }
    if (adModel == self.firstLoadingAdModel) {
        self.firstLoadingAdModel = nil;
        if ([self.delegate respondsToSelector:@selector(findManagerDidReloadAdData:)]) {
            [self.delegate findManagerDidReloadAdData:self];
        }
        return;
    }
    if (!adModel.isVirtual && !self.hasValidAdModelCache) {
        self.hasValidAdModelCache = YES;
        if ([self.delegate respondsToSelector:@selector(findManagerDidLoadMoreAdData:)]) {
            [self.delegate findManagerDidLoadMoreAdData:self];
        }
    }
}

- (void)feedAdModel:(XMIFeedAdModel *)adModel didLoadDataFailWithError:(NSError *)error
{
    if (![self.adModelCacheQueue containsObject:adModel]) {
        return;
    }
    [self.adModelCacheQueue removeObject:adModel];
    if (adModel.relatedData.compositeAds.count) {
        [adModel.relatedData.compositeAds enumerateObjectsUsingBlock:^(XMIAdRelatedData * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            [self.shownAdIds addObject:@(obj.adid)];
        }];
    } else {
        [self.shownAdIds addObject:@(adModel.relatedData.adid)];
    }
    if (self.adModelCacheQueue.count == 0) {
        if (self.hasValidAdModelCache) {
            self.hasValidAdModelCache = NO;
        }
        [self loadMoreAd];
    } else {
        if (adModel == self.firstLoadingAdModel) {
            self.firstLoadingAdModel = nil;
            if ([self.delegate respondsToSelector:@selector(findManagerDidReloadAdData:)]) {
                [self.delegate findManagerDidReloadAdData:self];
            }
        }
        XMIFeedAdModel *model = [self.adModelCacheQueue firstObject];
        [model loadAdData];
    }
}


- (void)interruptWithFeedback
{
    self.feedbacked = YES;
    [self.adModelCacheQueue removeAllObjects];
    self.hasValidAdModelCache = NO;
}

@end
