//
//  XMISingleFeedAdManager.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON>z<PERSON> on 2022/3/8.
//

#import <XMAd/XMIBaseAdManager.h>
#import <XMAd/XMIAdRelatedData.h>
#import <XMAd/XMIAdDefines.h>

NS_ASSUME_NONNULL_BEGIN

extern NSString * const kUserInfoJumpNotSupport;

@class XMISingleFeedAdManager;

@protocol XMISingleFeedAdManagerDelegate <NSObject>

@optional

- (void)singleFeedAdManager:(XMISingleFeedAdManager *)manager  didLoadAdView:(UIView *)adView;

- (void)singleFeedAdManager:(XMISingleFeedAdManager *)manager didLoadFailedLoadWithError:(NSError *)error;

- (void)singleFeedAdManagerDidExpose:(XMISingleFeedAdManager *)manager;

- (void)singleFeedAdManagerDidClick:(XMISingleFeedAdManager *)manager userInfo:(NSDictionary *)userInfo;

- (void)singleFeedAdManagerDidClickClose:(XMISingleFeedAdManager *)manager;

- (void)singleFeedAdManagerDidClose:(XMISingleFeedAdManager *)manager;

- (void)singleFeedAdManagerDidPresentScreen:(XMISingleFeedAdManager *)manager;

- (void)singleFeedAdManagerDidCloseDetailPage:(XMISingleFeedAdManager *)manager;

- (NSDictionary *)singleFeedAdManagerGetDefaultRequestParams:(XMISingleFeedAdManager *)manager;

@end

@interface XMISingleFeedAdManager : NSObject

@property (nonatomic, weak) UIViewController *rootViewController;

@property (nonatomic, assign) BOOL needTrackData;

- (instancetype)initWithPositionName:(NSString *)positionName;

- (instancetype)initWithPositionName:(NSString *)positionName subShowStyle:(XMIAdShowSubStyle)subShowsStyle;

@property (nonatomic, strong, readonly) UIView *adView;

@property (nonatomic, strong, readonly) XMIAdRelatedData *relatedData;

@property (nonatomic, weak) id<XMISingleFeedAdManagerDelegate> delegate;

@property (nonatomic, assign) BOOL xmActive;

@property (nonatomic, assign) BOOL autoReload;

@property (nonatomic, strong) NSNumber *playPageRevision;


- (void)adDidScroll:(UIScrollView *)scrollView;

- (void)closeAd;

- (void)reloadDataWithExtraParams:(nullable NSDictionary *)extra;

- (void)cancelReloadTimer;

@end

NS_ASSUME_NONNULL_END
