//
//  XMIExpressAdManager.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/6.
//

#import "XMIBaseAdManager.h"
#import "XMIAdDefines.h"

NS_ASSUME_NONNULL_BEGIN

@class XMIExpressAdContainer,XMIAdRelatedData,XMIAdModel;
@protocol XMIExpressAdManagerDelegate;

@interface XMIExpressAdManager : XMIBaseAdManager

@property (nonatomic, weak) id<XMIExpressAdManagerDelegate> delegate;


@property (nonatomic, assign) BOOL needIncreaseHomeRank;
/**
 通知广告滚动了
 曝光检测依赖该方法，必须调用
 */
- (void)adDidScroll:(UIScrollView *)scrollView;


-(void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate;

-(void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView;

- (void)scrollViewPreloadDataWithModel:(XMIAdModel *)adModel;

/// 控制器已经出现
- (void)controllerViewDidAppear;

/// 控制器已经消失
- (void)controllerViewDidDisappear;

// 猜你喜欢对应的专辑声音播放状态-用于混排信息流
- (void)updatePlayStateWithTrackId:(long long)trackId playing:(BOOL)playing;

@end

@protocol XMIExpressAdManagerDelegate <NSObject>

@optional
- (void)expressAd:(XMIExpressAdManager *)expressAd didLoadData:(NSArray<__kindof XMIAdModel *> *)adDataArray;
- (void)expressAd:(XMIExpressAdManager *)expressAd didLoadFailWithError:(NSError *)error;
- (void)expressAd:(XMIExpressAdManager *)expressAd didLoadFailWithError:(NSError *)error replaceXMAdView:(XMIExpressAdContainer *)adContainer;
- (CGSize)expressAd:(XMIExpressAdManager *)expressAd adViewWillRender:(XMIExpressAdContainer *)adContainer;
- (void)expressAd:(XMIExpressAdManager *)expressAd adViewDidRender:(XMIExpressAdContainer *)adContainer hasNeedRefresh:(BOOL)isNeedRefresh;
- (void)expressAd:(XMIExpressAdManager *)expressAd adView:(XMIExpressAdContainer *)adContainer didRenderFailWithError:(NSError *)error;
- (void)expressAd:(XMIExpressAdManager *)expressAd adViewDidExpose:(XMIExpressAdContainer *)adContainer;
- (void)expressAd:(XMIExpressAdManager *)expressAd adContainer:(XMIExpressAdContainer *)container adViewDidClick:(UIView *)aView withUserInfo:(nullable NSDictionary *)userInfo;
- (void)expressAd:(XMIExpressAdManager *)expressAd adViewDidRemoved:(XMIExpressAdContainer *)adContainer;
- (void)expressAd:(XMIExpressAdManager *)expressAd adViewWillPresentScreen:(XMIExpressAdContainer *)adContainer;
- (void)expressAd:(XMIExpressAdManager *)expressAd adViewDidPresentScreen:(XMIExpressAdContainer *)adContainer;
- (void)expressAd:(XMIExpressAdManager *)expressAd adViewDetailControllerDidClosed:(XMIExpressAdContainer *)adContainer;
- (void)expressAd:(XMIExpressAdManager *)expressAd adView:(XMIExpressAdContainer *)adContainer playerStateChanged:(XMIPlayerPlayState)state;
- (void)expressAd:(XMIExpressAdManager *)expressAd adView:(XMIExpressAdContainer *)adContainer playerDidPlayFinish:(NSError *_Nullable)error;

- (void)expressAd:(XMIExpressAdManager *)expressAd adView:(XMIExpressAdContainer *)adContainer dislikeWithReason:(NSArray<__kindof NSString *> *)reasons;

/// 点击关闭广告
- (void)expressAd:(XMIExpressAdManager *)expressAd adViewDidClickedClose:(XMIExpressAdContainer *)adContainer;

- (void)expressAdDidExplaceXMAdData:(XMIExpressAdManager *)expressAd;

- (void)expressAd:(XMIExpressAdManager *)expressAd adContainer:(XMIExpressAdContainer *)adContainer clickedAnchorItem:(UIView *)aView;

- (NSDictionary *)expressAdManagerGetAdditionalRequestParams:(XMIExpressAdManager *)manager;

@end

NS_ASSUME_NONNULL_END
