//
//  XMIFindAdManager.h
//  XMAd
//
//  Created by cu<PERSON><PERSON>z<PERSON> on 2022/3/2.
//

#import <XMAd/XMIBaseAdManager.h>
#import <XMAd/XMIFeedAdRenderer.h>
#import <XMAd/XMIAdDefines.h>

NS_ASSUME_NONNULL_BEGIN

typedef enum : NSUInteger {
    XMIFindAdHeaderAdWaiting = 0,
    XMIFindAdHeaderAdDisable = 1,
    XMIFindAdHeaderAdEnable = 2,
} XMIFindAdHeaderAdState;

@class XMIFindAdManager, XMIFeedAdModel;

@protocol XMIFindAdManagerDelegate <NSObject>

@optional

//请求额外参数
- (NSDictionary *)findAdManagerGetRequestParams:(XMIFindAdManager *)manager;

//移除之前所有已曝光过的广告并刷新tableview
- (void)findManagerDidReloadAdData:(XMIFindAdManager *)manager;
//刷新tableview，填充未填充的广告位
- (void)findManagerDidLoadMoreAdData:(XMIFindAdManager *)manager;
//加载失败
- (void)findManager:(XMIFindAdManager *)manager didFailLoadAdWithError:(NSError *)error;
- (CGSize)findManager:(XMIFindAdManager *)manager willRender:(XMIFeedAdRenderer *)renderer;
- (void)findManager:(XMIFindAdManager *)manager didRender:(XMIFeedAdRenderer *)renderer;
- (void)findManager:(XMIFindAdManager *)manager renderer:(XMIFeedAdRenderer *)renderer didRenderFailWithError:(NSError *)error;
- (void)findManager:(XMIFindAdManager *)manager rendererDidExpose:(XMIFeedAdRenderer *)renderer;
//点击事件，主站做魔镜上报
- (void)findManager:(XMIFindAdManager *)manager renderer:(XMIFeedAdRenderer *)renderer adViewDidClick:(UIView<XMIFeedAdViewProtocol> *)aView withUserInfo:(nullable NSDictionary *)userInfo;
- (void)findManager:(XMIFindAdManager *)manager rendererDidRemoved:(XMIFeedAdRenderer *)renderer;
- (void)findManager:(XMIFindAdManager *)manager rendererWillPresentScreen:(XMIFeedAdRenderer *)renderer;;
- (void)findManager:(XMIFindAdManager *)manager rendererDidPresentScreen:(XMIFeedAdRenderer *)renderer;
- (void)findManager:(XMIFindAdManager *)manager rendererDetailControllerDidClosed:(XMIFeedAdRenderer *)renderer;;
- (void)findManager:(XMIFindAdManager *)manager renderer:(XMIFeedAdRenderer *)renderer playerStateChanged:(XMIPlayerPlayState)state;
- (void)findManager:(XMIFindAdManager *)manager renderer:(XMIFeedAdRenderer *)renderer playerDidPlayFinish:(NSError *_Nullable)error;
- (void)findManager:(XMIFindAdManager *)manager renderer:(XMIFeedAdRenderer *)renderer dislikeWithReason:(NSArray<__kindof NSString *> *)reasons;

/// 点击关闭广告,交给XMAdApi做负反馈处理
- (void)findManager:(XMIFindAdManager *)manager 
rendererDidClickedClose:(XMIFeedAdRenderer *)renderer
           userInfo:(nullable NSDictionary *)userInfo;

/// 关闭广告
- (void)findManager:(XMIFindAdManager *)manager rendererDidClose:(XMIFeedAdRenderer *)renderer;

- (void)findManager:(XMIFindAdManager *)manager didFailLoadRenderWithError:(NSError *)error;

- (NSString *)findManager:(XMIFindAdManager *)manager  customRenderingClassNameForAdModel:(XMIFeedAdModel *)adModel;
// 猜你喜欢广告点击了主播头像
- (void)findManager:(XMIFindAdManager *)manager clickAnchorId:(NSString *)anchorId;
@end

@interface XMIFindAdManager : NSObject

- (instancetype)initWithScrollView:(UIScrollView *)scrollView positionName:(NSString *)positionName;

@property (nonatomic, weak) UIViewController *rootViewController;

@property (nonatomic, weak) id<XMIFindAdManagerDelegate> delegate;

@property (nonatomic, assign) CGFloat defaultAdViewHeight;

//用于判断是否还有可使用的缓存
@property (nonatomic, assign) BOOL hasValidAdModelCache;

@property (nonatomic, assign) UIEdgeInsets edge;

@property (nonatomic, assign, readonly) BOOL systemTimeInvalid;

@property (nonatomic, assign) BOOL canLoadMore;

@property (nonatomic, assign) BOOL mobileRtbEnable;

@property (nonatomic, assign) BOOL bannerAd;

@property (nonatomic, assign) XMIFindAdHeaderAdState headerAdState;

@property (nonatomic, assign, readonly) XMIAdFindNativeShowStyle abShowStyle;

@property (nonatomic, weak) UIScrollView *scrollView;

@property (nonatomic, assign) NSInteger findNativeRealVersion;

- (void)updateAbShowStyle;

//重载方法，刷新所有已显示过和缓存中未使用过的广告位
- (void)reloadAds;
- (void)reloadAdsIsRefresh:(BOOL)isRefresh;

- (BOOL)isInAdShowInterval;

- (NSInteger)bigFeedAdReloadInterval;

//通过adModel获取对应adView，内部renderer有重用逻辑
- (UIView<XMIFeedAdViewProtocol> *)adViewWithAdModel:(XMIFeedAdModel *)adModel;

/// 控制器已经出现
- (void)controllerViewDidAppear;

/// 控制器已经消失
- (void)controllerViewDidDisappear;

//计算广告view高度
- (CGFloat)heightForAdViewByModel:(XMIFeedAdModel *)adModel withWidth:(CGFloat)adWidth;

//取一条缓存池中未使用的已完成加载的广告数据，如没有则返回nil
- (nullable XMIFeedAdModel *)queueOutValidAdModelCache;

//负反馈时调用，会清空缓存池中未使用的广告数据，并且在调用reloadAds刷新数据之前都不会再请求广告
- (void)interruptWithFeedback;

@end

NS_ASSUME_NONNULL_END
