//
//  XMISingleFeedAdManager.m
//  XMAd
//
//  Created by cuiyuanz<PERSON> on 2022/3/8.
//

#import "XMISingleFeedAdManager.h"
#import "XMIFeedAdRenderer.h"
#import "XMIFeedAdViewFactory.h"
#import "XMIAdDataCenter.h"
#import "XMIAdPreviewManager.h"
#import "XMIAdReporter+AD.h"
#import "XMIAdError.h"
#import "XMICommonUtils.h"
#import "XMIAdMacro.h"
#import "XMITimeoutHelper.h"
#import "XMIAdManager.h"
#import "XMIAdHelper.h"

#define kAdAutoRefresh @"autoRefresh"

@interface XMISingleFeedAdManager ()<XMIFeedAdRendererDelegate>

@property (nonatomic, copy) NSString *positionName;

@property (nonatomic, strong) XMIFeedAdRenderer *renderer;

@property (nonatomic, assign) XMIHandlerID closeTimerID;

@property (nonatomic, assign) XMIHandlerID reloadTimerID;

@property (nonatomic, assign) BOOL shouldReloadWhenActive;

@property (nonatomic, copy) NSDictionary *extraParams;

@property (nonatomic, weak) id<XMIAdRequestProtocol> request;

@property (nonatomic, assign) XMIAdShowSubStyle subShowStyle;

@end

@implementation XMISingleFeedAdManager
@dynamic adView;
@dynamic relatedData;

- (instancetype)initWithPositionName:(NSString *)positionName subShowStyle:(XMIAdShowSubStyle)subShowsStyle
{
    self = [self init];
    if (self) {
        self.positionName = positionName;
        self.subShowStyle = subShowsStyle;
    }
    return self;
}

- (instancetype)initWithPositionName:(NSString *)positionName
{
    return [self initWithPositionName:positionName subShowStyle:XMIAdSubShowSubStyleNone];
}
/**
 如果处于非ative状态 reloaddata时不会执行只会保存状态
 等到active的时候再进行reload
 因为有半屏状态下不展示广告的复杂逻辑所以才这么做
 */

- (void)setXmActive:(BOOL)xmActive
{
    if (xmActive == _xmActive) {
        return;
    }
    _xmActive = xmActive;
    if (xmActive && self.shouldReloadWhenActive) {
        self.shouldReloadWhenActive = NO;
        [self loadAdDataWithPositionName:self.positionName];
    }
}

- (void)reloadDataWithExtraParams:(NSDictionary *)extra
{
    self.extraParams = extra;
    if (self.xmActive) {
        [self loadAdDataWithPositionName:self.positionName];
    } else {
        self.shouldReloadWhenActive = YES;
    }
}

- (UIView *)adView
{
    return [self.renderer renderedView];
}

- (XMIAdRelatedData *)relatedData
{
    return self.renderer.adModel.relatedData;
}

- (void)loadAdDataWithPositionName:(NSString *)positionName {
    [[XMITimeoutHelper sharedInstance] cancelHandler:self.reloadTimerID];
    [self cancelReloadTimer];
    /// 加入预览数据
    long long slotId = [XMIAdDataCenter getSlotIdByPositionName:positionName];
    XMIAdRespAdData *prevData = [XMIAdPreviewManager getPreviewResponseWith:slotId];
    if (prevData) {
        NSArray<XMIAdRelatedData *> *preDatas = [XMIAdDataCenter adDataFromResponse:prevData];
        NSMutableArray *tempArray = [NSMutableArray arrayWithCapacity:0];
        [tempArray addObjectsFromArray:preDatas];
//            [tempArray addObjectsFromArray:adArray];
        [self didLoadData:tempArray andUseTime:0];
        return;
    }
    NSMutableDictionary *props = [NSMutableDictionary dictionary];
    if ([self.delegate respondsToSelector:@selector(singleFeedAdManagerGetDefaultRequestParams:)]) {
        [props addEntriesFromDictionary:[self.delegate singleFeedAdManagerGetDefaultRequestParams:self]];
    }
    if (self.needTrackData) {
        if ([[XMIAdManager sharedInstance].delegate respondsToSelector:@selector(managerGetTrackId)]) {
            props[@"trackId"] = @([[[XMIAdManager sharedInstance].delegate managerGetTrackId] integerValue]);
        }
        if ([[XMIAdManager sharedInstance].delegate respondsToSelector:@selector(managerGetAlbumId)]) {
            props[@"album"] = @([[[XMIAdManager sharedInstance].delegate managerGetAlbumId] integerValue]);
        }
        
    }
    if (self.playPageRevision != nil) {
        props[@"playPageRevision"] = ([self.playPageRevision integerValue] == 0) ? @"false" : @"true" ;
    }
    if (self.extraParams) {
        [props addEntriesFromDictionary:self.extraParams];
    }
    if (props.count == 0 && self.request) {
        return;
    }
    if (![[props objectForKey:kAdAutoRefresh] boolValue]) {
        props[kAdAutoRefresh] = @(NO);
    }
    [self.request cancel];
    self.extraParams = nil;
    NSArray *styles = nil;
    if (![XMIAdHelper shouldRequestAdWithSlotId:slotId params:props forbidStyles:&styles]) {
        [self didLoadFailWithError:[XMIAdError emptyDataError] andUseTime:0 sendReport:NO];
        return;
    }
    if (styles.count > 0) {
        props[@"shieldAdPositionNames"] = [styles componentsJoinedByString:@","];
    }
    long long startMs = [XMICommonUtils currentTimestamp];
    @weakify(self)
    id<XMIAdRequestProtocol> request = [XMIAdDataCenter adDataRequestWithPositionName:positionName props:props dataHandler:^(NSArray<XMIAdRelatedData *> * _Nullable adArray, NSError * _Nullable error) {
        XMILog(@"%@", error);
        long long endMs = [XMICommonUtils currentTimestamp];
        long long useTime = endMs - startMs;
        
        @strongify(self)
        if (!self) {
            return;
        }
        
        if (error != nil) {
            // TODO: 打印日志
            [self didLoadFailWithError:error andUseTime:useTime sendReport:YES];
            return;
        }
        
        if (adArray.count < 1) {
            [self didLoadFailWithError:[XMIAdError emptyDataError] andUseTime:useTime sendReport:YES];
            return;
        }
        
        [self didLoadData:adArray andUseTime:useTime];
    }];
    self.request = request;
    [request start];
}

- (void)didLoadData:(NSArray<XMIAdRelatedData *> *)adArray andUseTime:(long long)useTime {
    [self cancelCloseTimer];
    XMIAdRelatedData *rData = adArray.firstObject;
    rData.paddingTimeMs = useTime;
    rData.reuseIdentifier = [NSString stringWithFormat:@"%ld-%ld", (long)rData.adtype, (long)rData.showstyle];
    rData.playPageRevision = self.playPageRevision;
    XMIFeedAdModel *model = [XMIFeedAdModel feedAdModelWithRelatedData:rData];
    model.rootViewController = self.rootViewController;
    Class renderingViewClass = [XMIFeedAdViewFactory feedAdViewClasslWithShowStyle:rData.showstyle adType:rData.adtype subShowStyle:self.subShowStyle];
    if (self.renderer && self.renderer.renderingViewClass == renderingViewClass) {
        self.renderer.subShowStyle = self.subShowStyle;
        if (self.renderer.hasRendered && [self.renderer renderedView].superview) {
            [[self.renderer renderedView] removeFromSuperview];
        }
        self.renderer.adModel = model;
    } else {
        if (self.renderer.hasRendered) {
            [self.renderer.renderedView removeFromSuperview];
        }
        self.renderer = [[XMIFeedAdRenderer alloc] initWithRenderingViewClass:renderingViewClass];
        self.renderer.delegate = self;
        self.renderer.subShowStyle = self.subShowStyle;
        self.renderer.rootViewController = self.rootViewController;
    }
    self.renderer.adModel = model;
    [XMIAdReporter exposeReportValidAds:adArray];
    if ([self.delegate respondsToSelector:@selector(singleFeedAdManager:didLoadAdView:)]) {
        [self.delegate singleFeedAdManager:self didLoadAdView:[self.renderer renderedView]];
    }
}

- (void)didLoadFailWithError:(NSError *)error andUseTime:(long long)useTime sendReport:(BOOL)sendReport
{
    if (error.code == XMIAdDataErrorCanceled) {
        return;
    }
    if (sendReport) {
        [XMIAdReporter loadFailReportWithAd:[XMIAdDataCenter getEmptyFeedAdData] error:error useTime:useTime];
    }
    if (self.delegate && [self.delegate respondsToSelector:@selector(singleFeedAdManager:didLoadFailedLoadWithError:)]) {
        [self.delegate singleFeedAdManager:self didLoadFailedLoadWithError:error];
    }
    [self cancelCloseTimer];
    [self cancelReloadTimer];
    self.renderer = nil;
}

- (void)adDidScroll:(UIScrollView *)scrollView
{
    [self.renderer checkExpose];
}

- (void)closeAd
{
    if (!self.renderer || !self.renderer.hasRendered) {
        return;
    }
    [self.renderer closeAd];
}

#pragma mark - renderer delegate

- (void)feedAdRendererDidExpose:(XMIFeedAdRenderer *)renderer
{
    if ([self.delegate respondsToSelector:@selector(singleFeedAdManagerDidExpose:)]) {
        [self.delegate singleFeedAdManagerDidExpose:self];
    }
    [self setupCloseTimer];
}

- (void)feedAdRenderer:(XMIFeedAdRenderer *)renderer adViewDidClick:(UIView<XMIFeedAdViewProtocol> *)aView withUserInfo:(NSDictionary *)userInfo
{
    if ([self.delegate respondsToSelector:@selector(singleFeedAdManagerDidClick:userInfo:)]) {
        [self.delegate singleFeedAdManagerDidClick:self userInfo:userInfo];
    }
}

- (void)feedAdRenderDidClickedClose:(XMIFeedAdRenderer *)renderer
                           userInfo:(nullable NSDictionary *)userInfo
{
    if ([self.delegate respondsToSelector:@selector(singleFeedAdManagerDidClickClose:)]) {
        [self.delegate singleFeedAdManagerDidClickClose:self];
    }
}

- (void)feedAdRenderDidClose:(XMIFeedAdRenderer *)renderer
{
    if ([self.delegate respondsToSelector:@selector(singleFeedAdManagerDidClose:)]) {
        [self.delegate singleFeedAdManagerDidClose:self];
    }
    [self cancelCloseTimer];
    [self setupReloadTimer];
    self.renderer.adModel = nil;
}

#pragma mark - 自动关闭重加载
//广告曝光之后自动关闭的间隔
- (NSTimeInterval)autoCloseInterval
{
    return self.renderer.adModel.relatedData.adShowTime;
}
//广告关闭之后自动重加载间隔
- (NSTimeInterval)autoReloadInterval
{
    return self.renderer.adModel.relatedData.adIntervalTime;
}

//广告关闭后m秒后重新请求展示广告
- (void)setupReloadTimer
{
    if (!self.autoReload) {
        return;
    }
    if (!self.xmActive) {
        return;
    }
    if ([self autoReloadInterval] <= 0) {
        return;
    }
    @weakify(self)
    if (self.reloadTimerID > 0) {
        [[XMITimeoutHelper sharedInstance] cancelHandler:self.reloadTimerID];
    }
    self.reloadTimerID = [[XMITimeoutHelper sharedInstance] delay:[self autoReloadInterval] runHandler:^{
        @strongify(self)
        if (!self) {
            return;
        }
        self.reloadTimerID = 0;
        [self reloadDataWithExtraParams:@{kAdAutoRefresh : @(YES)}];
    } repeat:NO];
}

- (void)setupCloseTimer
{
    if ([self autoCloseInterval] <= 0) {
        return;
    }
    if (self.closeTimerID > 0) {
        return;
    }
    @weakify(self)
    self.closeTimerID = [[XMITimeoutHelper sharedInstance] delay:[self autoCloseInterval] runHandler:^{
        @strongify(self)
        if (!self) {
            return;
        }
        self.closeTimerID = 0;
        [self closeAd];
    } repeat:NO];
}

- (void)cancelReloadTimer
{
    [[XMITimeoutHelper sharedInstance] cancelHandler:self.reloadTimerID];
    self.reloadTimerID = 0;
}

- (void)cancelCloseTimer
{
    [[XMITimeoutHelper sharedInstance] cancelHandler:self.closeTimerID];
    self.closeTimerID = 0;
}

- (void)dealloc
{
    
}

@end
