//
//  XMIExpressAdManager.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/6.
//

#import "XMIExpressAdManager.h"
#import "XMIAdMacro.h"
#import "XMIAdError.h"
#import "XMICommonUtils.h"
#import "XMIAdDataCenter.h"
#import "XMIExpressAdContainer.h"
#import "XMIAdReporter+AD.h"
#import "XMIExpressAdloadManager.h"
#import "XMIAdModel.h"
#import "XMIExpressAdDoubleView.h"
#import <YYThreadSafeArray/YYThreadSafeArray.h>
#import "XMIAdPreviewManager.h"
#import "XMIAdSlotData.h"
#import "XMIAdHelper.h"
#import "XMIOwnMixFeedCardAdView.h"
#import "XMIAdConverter.h"

@interface XMIExpressAdManager ()<XMIExpressAdContainerDelegate, XMIExpressAdloadManagerDelegate>

@property (nonatomic, strong) NSMutableArray<XMIAdModel *> *dataArray;
@property (nonatomic, strong) XMIExpressAdloadManager *loadManager;
@property (nonatomic, strong) NSRecursiveLock *recursiveLock;
@property (nonatomic, strong) YYThreadSafeArray *replacingAdModels;
@end

@implementation XMIExpressAdManager

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.loadManager = [XMIExpressAdloadManager new];
        self.loadManager.delegate = self;
        self.recursiveLock = [[NSRecursiveLock alloc] init];
        self.replacingAdModels = [YYThreadSafeArray arrayWithCapacity:0];
    }
    return self;
}

/**
 穿山甲视频-900546910
 穿山甲大图-945019352
 广点通视频或大图-3050349752532954
 广点通三小图或大图-2000566593234845
 */
- (void)loadAdDataWithPositionName:(NSString *)positionName {
    /// 加入预览数据
    long long slotId = [XMIAdDataCenter getSlotIdByPositionName:positionName];
    XMIAdRespAdData *prevData = [XMIAdPreviewManager getPreviewResponseWith:slotId];
    if (prevData) {
        NSArray<XMIAdRelatedData *> *preDatas = [XMIAdDataCenter adDataFromResponse:prevData];
        NSMutableArray *tempArray = [NSMutableArray arrayWithCapacity:0];
        [tempArray addObjectsFromArray:preDatas];
//            [tempArray addObjectsFromArray:adArray];
        [self didLoadData:tempArray andUseTime:0];
        return;
    }
    NSMutableDictionary *props = [NSMutableDictionary dictionary];
    if ([self.delegate respondsToSelector:@selector(expressAdManagerGetAdditionalRequestParams:)]) {
        [props addEntriesFromDictionary:[self.delegate expressAdManagerGetAdditionalRequestParams:self]];
    }
    NSArray *styles = nil;
    if (![XMIAdHelper shouldRequestAdWithSlotId:slotId params:props forbidStyles:&styles]) {
        [self didLoadFailWithError:[XMIAdError emptyDataError] andUseTime:0 sendReport:NO];
        return;
    }
    if (styles.count > 0) {
        props[@"shieldAdPositionNames"] = [styles componentsJoinedByString:@","];
    }
   
    
    long long startMs = [XMICommonUtils currentTimestamp];
    @weakify(self)
    id<XMIAdRequestProtocol> request = [XMIAdDataCenter adDataRequestWithPositionName:positionName props:props dataHandler:^(NSArray<XMIAdRelatedData *> * _Nullable adArray, NSError * _Nullable error) {
        XMILog(@"%@", error);
        long long endMs = [XMICommonUtils currentTimestamp];
        long long useTime = endMs - startMs;
        
        [self increaseHomeRankWithPositionName:positionName];
        @strongify(self)
        if (!self) {
            return;
        }
        
        if (error != nil) {
            // TODO: 打印日志
            [self didLoadFailWithError:error andUseTime:useTime sendReport:YES];
            return;
        }
        
        if (adArray.count < 1) {
            [self didLoadFailWithError:[XMIAdError emptyDataError] andUseTime:useTime sendReport:YES];
            return;
        }
        
        [self didLoadData:adArray andUseTime:useTime];
    }];
    [request start];
}

- (void)increaseHomeRankWithPositionName:(NSString *)positionName {
    BOOL needIncreaseHomeRank = self.needIncreaseHomeRank;
    // 首页信息流请求计数
    if ([positionName isEqualToString:XMI_ADP_HOME_BASERIAL_FEED] || [positionName isEqualToString:XMI_ADP_FEED]) {
        if (needIncreaseHomeRank) {
            [[XMIAdReporter sharedInstance] feedDidRefresh];
        }
    }
    // 猜你喜欢请求计数
    if ([positionName isEqualToString:XMI_ADP_HOME_GUESS_YOU_LIKE]) {
        if (needIncreaseHomeRank) {
            [[XMIAdReporter sharedInstance] guessYouLikeDidRefresh];
        }
    }
    self.needIncreaseHomeRank = NO;
}

- (void)didLoadData:(NSArray<XMIAdRelatedData *> *)adArray andUseTime:(long long)useTime {
    // 第一个广告是否为虚拟广告
    XMIAdRelatedData *firstData = adArray.firstObject;
    if ([XMIAdConverter isVirtualAD:firstData.adid] ) {
        // 上报虚拟广告
        [XMIAdReporter virtualReportWithAd:firstData];
        if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:didLoadData:)]) {
            [self.delegate expressAd:self didLoadData:@[]];
        }
        return;
    }
    
    // 橱窗样式特殊处理，只上报一个
    NSMutableArray *rArray = [[NSMutableArray alloc] init];
    BOOL shopWindowHit = NO;
    NSInteger adno = 0;
    for (int i = 0; i < adArray.count; i++) {
        XMIAdRelatedData *rData = adArray[i];
        if (rData.showstyle == XMIAdStyleHomeShopWindow) {
            if (shopWindowHit) {
                continue;
            }
            shopWindowHit = YES;
        }
        
        //homerank处理
        rData.adno = adno;
        if (!rData.internalCirculationFlag) {
            adno++;
        } else {
            rData.extraReportParams = @{@"internalCirculationFlag" : @(rData.internalCirculationFlag), @"internalHomeGuessIndex" : @(rData.internalHomeGuessIndex)};
        }
        [rArray addObject:rData];
    }
    
    [self reportTingShowWithAdArray:rArray];

    
    self.dataArray = [[NSMutableArray alloc] init];
    for (XMIAdRelatedData *rData in rArray) {
        rData.rootViewController = self.rootViewController;
        rData.delegate = self;
        rData.paddingTimeMs = useTime;
        rData.reuseIdentifier = [NSString stringWithFormat:@"%ld-%ld", (long)rData.adtype, (long)rData.showstyle];
        rData.isNeedAnimated = YES;
        XMIAdModel *adModel = [[XMIAdModel alloc] init];
        adModel.relatedData = rData;
        [self.dataArray addObject:adModel];
    }
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:didLoadData:)]) {
        [self.delegate expressAd:self didLoadData:self.dataArray];
    }
}
- (void)didLoadFailWithError:(NSError *)error andUseTime:(long long)useTime sendReport:(BOOL)sendReport {
    if (sendReport) {
        XMIAdRelatedData *rData = [XMIAdDataCenter getEmptyFeedAdData];
        XMIAdStatusReporter *reporter = [[XMIAdStatusReporter alloc] init];
        reporter.paddingMs = useTime;
        [[XMIAdReporter sharedInstance] addStatusReport:reporter];
        
        XMIAdStatusReporter *statusReporter = [XMIAdReporter reporterWithError:error andReporter:reporter.reporterId andAd:rData];
        [XMIAdReporter reportRequestStatusWithReporter:statusReporter];
    }
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:didLoadFailWithError:)]) {
        [self.delegate expressAd:self didLoadFailWithError:error];
    }
}

- (void)adDidScroll:(UIScrollView *)scrollView {
    if (self.dataArray == nil || self.dataArray.count < 1) {
        return;
    }
    [[NSNotificationCenter defaultCenter] postNotificationName:XMIExpressAdContainerScrollViewDidScrollNotification object:scrollView];
}

-(void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate {
    if (self.dataArray == nil || self.dataArray.count < 1) {
        return;
    }
    if (!decelerate) {
        BOOL dragToDragStop = scrollView.tracking && !scrollView.dragging && !scrollView.decelerating;
        if (dragToDragStop) {
            [self scrollViewDidEndScroll];
        }
    }
}

-(void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    if (self.dataArray == nil || self.dataArray.count < 1) {
        return;
    }
    BOOL scrollToScrollStop = !scrollView.tracking && !scrollView.dragging && !scrollView.decelerating;
    if (scrollToScrollStop) {
        [self scrollViewDidEndScroll];
    }
}


- (void)scrollViewDidEndScroll {
    /// 滑动停止
    [[NSNotificationCenter defaultCenter] postNotificationName:XMIExpressAdContainerScrollViewDidEndScrollNotification object:nil];
}

- (void)scrollViewPreloadDataWithModel:(XMIAdModel *)adModel
{
    XMIAdRelatedData *relatedData = adModel.relatedData;
    if (relatedData.isLoaded) {
        return;
    }
    if (relatedData.adtype == XMIAdTypeXM) {
        return;
    }
    [self.loadManager loadAdData:relatedData];
}


/// 控制器已经出现
- (void)controllerViewDidAppear
{
    [[NSNotificationCenter defaultCenter] postNotificationName:XMIExpressAdContainerControllerDidAppearNotification object:nil];
    
}
/// 控制器已经消失
- (void)controllerViewDidDisappear
{
    [[NSNotificationCenter defaultCenter] postNotificationName:XMIExpressAdContainerControllerDidDisappearNotification object:nil];
}

// 猜你喜欢对应的专辑声音播放状态-用于混排信息流
- (void)updatePlayStateWithTrackId:(long long)trackId playing:(BOOL)playing {
    NSMutableDictionary *playerInfo = [NSMutableDictionary dictionary];
    [playerInfo setValue:@(trackId) forKey:@"trackId"];
    [playerInfo setValue:@(playing) forKey:@"playing"];
    [[NSNotificationCenter defaultCenter] postNotificationName:XMIExpressAdContainerControllerPlayerStatusDidChange object:nil userInfo:playerInfo];
}


- (void)expressAdLoad:(XMIExpressAdloadManager *)manager requireNextReplaceableXMAd:(XMIAdRelatedData *)relatedData withRefReporter:(long long)refReporterId
{
    [self requireNextReplaceableXMAd:relatedData withRefReporter:refReporterId];
}

//- (void)expressAdLoad:(XMIExpressAdloadManager *)manager findNoSpareTireData:(XMIAdRelatedData *)relatedData
//{
////    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdViewDidRender:hasNeedRefresh:)]) {
////        [self.delegate expressAdViewDidRender:self hasNeedRefresh:YES];
////    }
//    [self expressAdViewDidRender:nil hasNeedRefresh:YES];
//}


#pragma mark - XMIExpressAdContainerDelegate
- (void)expressAd:(XMIExpressAdContainer *)adView didLoadFailWithError:(NSError *)error {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:didLoadFailWithError:)]) {
        [self.delegate expressAd:self didLoadFailWithError:error];
    }
}

- (void)expressAdDidLoadFailureReplaceAdContainer:(XMIExpressAdContainer *)adContainer withError:(NSError *)error
{
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:didLoadFailWithError:replaceXMAdView:)]) {
        [self.delegate expressAd:self didLoadFailWithError:error replaceXMAdView:adContainer];
    }
}

- (CGSize)expressAdViewWillRender:(XMIExpressAdContainer *)adContainer
{
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:adViewWillRender:)]) {
        return [self.delegate expressAd:self adViewWillRender:adContainer];
    }
    return CGSizeZero;
}

- (void)expressAdViewDidRender:(XMIExpressAdContainer *)adContainer hasNeedRefresh:(BOOL)isNeedRefresh {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:adViewDidRender:hasNeedRefresh:)]) {
        [self.delegate expressAd:self adViewDidRender:adContainer hasNeedRefresh:isNeedRefresh];
    }
}
- (void)expressAd:(XMIExpressAdContainer *)adContainer didRenderFailWithError:(NSError *)error {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:adView:didRenderFailWithError:)]) {
        [self.delegate expressAd:self adView:adContainer didRenderFailWithError:error];
    }
}
- (void)expressAdViewDidExpose:(XMIExpressAdContainer *)adContainer {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:adViewDidExpose:)]) {
        [self.delegate expressAd:self adViewDidExpose:adContainer];
    }
}
- (void)expressAd:(XMIExpressAdContainer *)adContainer adViewDidClick:(UIView *)aView withUserInfo:(nullable NSDictionary *)userInfo {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:adContainer:adViewDidClick:withUserInfo:)]) {
        [self.delegate expressAd:self adContainer:adContainer adViewDidClick:aView withUserInfo:userInfo];
    }
}


- (void)expressAdViewDidRemoved:(XMIExpressAdContainer *)adContainer {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:adViewDidRemoved:)]) {
        [self.delegate expressAd:self adViewDidRemoved:adContainer];
    }
}
- (void)expressAdViewWillPresentScreen:(XMIExpressAdContainer *)adContainer {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:adViewWillPresentScreen:)]) {
        [self.delegate expressAd:self adViewWillPresentScreen:adContainer];
    }
}
- (void)expressAdViewDidPresentScreen:(XMIExpressAdContainer *)adContainer {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:adViewDidPresentScreen:)]) {
        [self.delegate expressAd:self adViewDidPresentScreen:adContainer];
    }
}
- (void)expressAdViewDetailControllerDidClosed:(XMIExpressAdContainer *)adContainer {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:adViewDetailControllerDidClosed:)]) {
        [self.delegate expressAd:self adViewDetailControllerDidClosed:adContainer];
    }
}
- (void)expressAd:(XMIExpressAdContainer *)adContainer playerStateChanged:(XMIPlayerPlayState)state {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:adView:playerStateChanged:)]) {
        [self.delegate expressAd:self adView:adContainer playerStateChanged:state];
    }
}
- (void)expressAd:(XMIExpressAdContainer *)adContainer playerDidPlayFinish:(NSError *_Nullable)error {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:adView:playerDidPlayFinish:)]) {
        [self.delegate expressAd:self adView:adContainer playerDidPlayFinish:error];
    }
}

- (void)expressAd:(XMIExpressAdContainer *)adContainer dislikeWithReason:(NSArray<__kindof NSString *> *)reasons {
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:adView:dislikeWithReason:)]) {
        [self.delegate expressAd:self adView:adContainer dislikeWithReason:reasons];
    }
}

- (void)expressAd:(XMIExpressAdContainer *)adContainer adViewDidClickedClose:(UIView *)aView
{
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:adViewDidClickedClose:)]) {
        [self.delegate expressAd:self adViewDidClickedClose:adContainer];
    }
}

- (void)expressAd:(XMIExpressAdContainer *)adContainer requireNextReplaceableXMAd:(XMIAdRelatedData *)oldAdData withRefReporter:(long long)refReporterId {
    [self requireNextReplaceableXMAd:oldAdData withRefReporter:refReporterId];
}

/// 主播点击
- (void)expressAd:(XMIExpressAdContainer *)adContainer didClickedAnchorItem:(XMIAdRelatedData *)relatedData clickedView:(UIView *)aView
{
    if (self.delegate && [self.delegate respondsToSelector:@selector(expressAd:adContainer:clickedAnchorItem:)]) {
        [self.delegate expressAd:self adContainer:adContainer clickedAnchorItem:aView];
    }
}


- (void)requireNextReplaceableXMAd:(XMIAdRelatedData *)oldAdData
                   withRefReporter:(long long)refReporterId
{
    dispatch_async(dispatch_get_global_queue(0, 0), ^{
        [self p_beginConfigDataXMAd:oldAdData withRefReporter:refReporterId];
    });
}




- (void)p_beginConfigDataXMAd:(XMIAdRelatedData *)oldAdData
              withRefReporter:(long long)refReporterId
{
    CGFloat adWidth = oldAdData.adWidth;
    [self.recursiveLock lock];
    XMIAdModel *oldAdModel = nil;
    if (oldAdData.adno < self.dataArray.count) {
        oldAdModel = [self.dataArray objectAtIndex:oldAdData.adno];
    }
    NSUInteger oldIndex = [self.dataArray indexOfObject:oldAdModel];
    XMIAdRelatedData *oldAdRelated = oldAdModel.relatedData;
    BOOL hasFindSpirate = NO;
    XMIAdModel *tempAdModel = nil;
    for (NSUInteger i = oldIndex; i < self.dataArray.count; i++) {
        XMIAdModel *adModel = self.dataArray[i];
        XMIAdRelatedData *relatedData = adModel.relatedData;
        if ([oldAdRelated isEqual:relatedData]) {
            continue;
        }
        BOOL isResult = [self p_compareRelatedWithOldData:oldAdRelated relatedData:relatedData];
        if (isResult && !hasFindSpirate) {
            relatedData.adWidth = adWidth;
            oldAdModel.relatedData = relatedData;
            relatedData.adno = i;
            relatedData.used = YES;
            adModel.relatedData = nil;
            hasFindSpirate = YES;
            tempAdModel = adModel;
            adModel.isFindSpareAd = YES;
            [self.replacingAdModels addObject:relatedData];
        }
        if (hasFindSpirate &&
            !tempAdModel.relatedData &&
            i+1 < self.dataArray.count) {
            XMIAdModel *nextAdModel = self.dataArray[i+1];
            if (nextAdModel.relatedData && !nextAdModel.relatedData.isExposed) {
                tempAdModel.relatedData = nextAdModel.relatedData;
                tempAdModel.relatedData.adno = i;
                nextAdModel.relatedData = nil;
                tempAdModel = nextAdModel;
            }
        }
    }
    if (!hasFindSpirate) {
        oldAdRelated.adHeight = 0.01;
        // 无备胎广告，上报
        XMIAdStatusReporter *refReporter = [[XMIAdReporter sharedInstance] statusReportForId:refReporterId];
        if (refReporter != nil) {
            refReporter.ready = YES;
            [[XMIAdReporter sharedInstance] addStatusReport:refReporter];
        }
    }
    [self.recursiveLock unlock];
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(expressAdDidExplaceXMAdData:)]) {
            NSMutableArray *tempArray = [NSMutableArray arrayWithCapacity:0];
            [self.replacingAdModels enumerateObjectsUsingBlock:^(XMIAdRelatedData * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
//                [XMIExpressAdDoubleView startRefreshSizeWithRelatedData:obj adViewWidth:obj.adWidth];
                [XMIOwnMixFeedCardAdView startRefreshSizeWithRelatedData:obj adViewWidth:obj.adWidth];
                [tempArray addObject:obj];
            }];
            [self.replacingAdModels removeObjectsInArray:tempArray];
            [self.delegate expressAdDidExplaceXMAdData:self];
        }
    });
}



- (BOOL)p_compareRelatedWithOldData:(XMIAdRelatedData *)oldAdData
                        relatedData:(XMIAdRelatedData *)rData
{
    if (rData.responseId == oldAdData.responseId
        && rData.slotId == oldAdData.slotId
        && rData.adtype == XMIAdTypeXM
        && rData.showstyle == oldAdData.showstyle
        && rData.isExposed == NO
        && rData.used == NO
        && rData.adid > XMI_VIRTUAL_ADID_MAX) {
        return YES;
    }
    return NO;
}

- (void)reportTingShowWithAdArray:(NSArray<XMIAdRelatedData *> *)adArray {
    BOOL reportWhenRequest = YES;
    if (adArray.count) {// 混排猜你喜欢，显示时再上报
        XMIAdRelatedData *relatedData = adArray.firstObject;
        if ([relatedData.positionName isEqualToString:@"home_guess_you_like"]) {
            reportWhenRequest = NO;
        }
    }
    
    if (reportWhenRequest) {
        [XMIAdReporter exposeReportValidAds:adArray];
    }
}
@end
