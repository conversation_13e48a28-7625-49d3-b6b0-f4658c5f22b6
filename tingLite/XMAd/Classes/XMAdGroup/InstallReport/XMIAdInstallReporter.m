//
//  XMIAdInstallReporter.m
//  Alamofire
//
//  Created by cuiyuanz<PERSON> on 2023/8/16.
//

#define kInstallReportHost @"http://ad.ximalaya.com"
#define kInstallReportDebugHost @"http://ops.test.ximalaya.com"
#define kInstallReportPath @"/ad-action/download"

#import "XMIAdInstallReporter.h"
#import <UIKit/UIKit.h>
#import <XMNetworkRequest/XMNRequest.h>
#import "XMIAdManager.h"
#import <XMCategories/XMCategory.h>
#import "XMIAdHeader.h"
#import "XMIAdABTest.h"

@interface XMIAdInstallReporter ()

@property (nonatomic, strong) NSOperationQueue *operationQueue;

@property (nonatomic, assign) BOOL started;

@property (nonatomic, copy) NSDictionary *installDic;

@property (nonatomic, strong) NSDate *backgroundDate;

@property (nonatomic, assign) BOOL didRequest;

@property (nonatomic, strong) NSTimer *timer;

@property (nonatomic, assign) NSInteger appIntervalSeconds;

@end

@implementation XMIAdInstallReporter

+ (instancetype)sharedInstance{
    static XMIAdInstallReporter *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] init];
    });
    return sharedInstance;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onDidEnterBackground) name:UIApplicationDidEnterBackgroundNotification object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onWillEnterForeground) name:UIApplicationDidBecomeActiveNotification object:nil];
        
        // 后台定时检测开关及时长间隔
        NSDictionary *strategy = [self installDetectionStrategyWithCurrentUser];
        BOOL appEnable = [strategy boolMaybeForKey:@"appEnable"];
        NSInteger appIntervalSeconds =  [strategy integerMaybeForKey:@"appIntervalSeconds"];
        _appIntervalSeconds = appIntervalSeconds;
        XMWeakObject(self);
        if (appEnable && appIntervalSeconds > 0) {
            _timer = [NSTimer scheduledTimerWithTimeInterval:appIntervalSeconds repeats:YES block:^(NSTimer * _Nonnull timer) {
                [weak_self addReportOperation];
            }];
        }
    }
    return self;
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (NSOperationQueue *)operationQueue
{
    if (!_operationQueue) {
        _operationQueue = [[NSOperationQueue alloc] init];
        _operationQueue.maxConcurrentOperationCount = 1;
    }
    return _operationQueue;
}

- (void)beginInstallReport
{
    self.started = YES;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if (!self.didRequest) {
            [self addReportOperation];
        }
    });
}

- (void)addReportOperation
{
    if (![[XMIAdManager sharedInstance].delegate respondsToSelector:@selector(managerGetInstalledConfigList)]) {
        return;
    }
    NSArray *schemeList = [[XMIAdManager sharedInstance].delegate managerGetInstalledConfigList];
    if (schemeList.count == 0) {
        return;
    }
    if (!self.started || [UIApplication sharedApplication].applicationState != UIApplicationStateActive) {
        return;
    }
    self.didRequest = YES;
    [self.operationQueue cancelAllOperations];
    NSBlockOperation *operation = [[NSBlockOperation alloc] init];
    __weak NSBlockOperation *weakOperation = operation;
    [operation addExecutionBlock:^{
        if (weakOperation.isCancelled) {
            return;
        }
        NSMutableDictionary *dic = [NSMutableDictionary dictionary];
        for (NSString *scheme in schemeList) {
            NSString *url = [scheme hasSuffix:@"://"] ? scheme : [scheme stringByAppendingString:@"://"];
            if ([[UIApplication sharedApplication] canOpenURL:[NSURL URLWithString:url]]) {
                dic[scheme] = @(YES);
            }
        }
        if (weakOperation.isCancelled) {
            return;
        }
        NSString *path = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) firstObject];
        NSString *uid = @"0";
        if ([[XMIAdManager sharedInstance].delegate respondsToSelector:@selector(managerGetUid)]) {
            uid = [[XMIAdManager sharedInstance].delegate managerGetUid] ?:@"0";
        }
        NSString *fileName = [NSString stringWithFormat:@"XMInstalled_%@", uid];
        path = [path stringByAppendingPathComponent:fileName];
        if (!self.installDic) {
            NSData *data = [NSData dataWithContentsOfFile:path];
            if (data) {
                self.installDic = [NSJSONSerialization JSONObjectWithData:data options:0 error:NULL];
            }
        }
        if (!isDictionaryAContainsDictionaryB(self.installDic, dic)) {
         
            NSMutableDictionary *params = [NSMutableDictionary dictionary];
            params[@"time"] = @((NSInteger)([[NSDate date] timeIntervalSince1970] * 1000));
            params[@"type"] = @(30);
            NSMutableString *installed = [NSMutableString string];
            for (NSString *key in dic.allKeys) {
                if ([[dic objectForKey:key] boolValue]) {
                    [installed appendString:key];
                    [installed appendString:@","];
                }
            }
            if (installed.length > 0) {
                [installed deleteCharactersInRange:NSMakeRange(installed.length - 1, 1)];
            }
            params[@"txInstallPackages"] = [installed stringWithURLEncoded];
            params[@"appIntervalSeconds"] = @(self.appIntervalSeconds);
            NSString *url = [XMIAdManager sharedInstance].environment == XMIAdEnvironmentDefault ? kInstallReportHost : kInstallReportDebugHost;
            XMNRequest *request = [XMNRequest requestPostWithServerUrl:url path:kInstallReportPath parameters:params completionHandler:^(XMBaseRequest * _Nonnull request) {
                if (!request.error && request.statusCode == 200) {
                    self.installDic = dic;
                    [[NSJSONSerialization dataWithJSONObject:dic options:0 error:NULL] writeToFile:path atomically:YES];
                }
            } failureHandler:^(XMBaseRequest * _Nonnull request) {

            }];
            request.requestSerializerType = XMRequestSerializerTypeJSON;
            request.requestCustomHeaderField = [[XMIAdHeader sharedInstance] headers];
                [request start];
        }
    }];
    [self.operationQueue addOperation:operation];
}

- (void)onDidEnterBackground
{
    self.backgroundDate = [NSDate date];
}

- (void)onWillEnterForeground
{
    NSTimeInterval interval = 20;
    if ([[XMIAdManager sharedInstance].delegate respondsToSelector:@selector(managerGetInstallReportInterval)]) {
        interval = [[XMIAdManager sharedInstance].delegate managerGetInstallReportInterval];
    }
    if (!self.didRequest || (self.backgroundDate && [[NSDate date] timeIntervalSinceDate:self.backgroundDate] > interval)) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self addReportOperation];
        });
    }
    self.backgroundDate = nil;
}

- (NSDictionary *)installDetectionStrategyWithCurrentUser {
    NSDictionary *dic = nil;
    if ([[XMIAdManager sharedInstance].delegate respondsToSelector:@selector(managerGetInstallDetectionStrategy)]) {
        dic = [[XMIAdManager sharedInstance].delegate managerGetInstallDetectionStrategy];
    }
    if (dic.count == 0) {
        dic = [XMIAdABTest getJsonObjectWithKey:@"IOS_CONTACT_UPDATE" defaultObject:@{}];
    }
    return dic;
}

BOOL isDictionaryAContainsDictionaryB(NSDictionary *dictA, NSDictionary *dictB) {
    // 遍历字典B的所有键
    for (NSString *key in dictB.allKeys) {
        // 检查字典A中是否存在该键
        id valueA = dictA[key];
        id valueB = dictB[key];
        
        // 如果字典A不存在该键或值不相等，返回NO
        if (!valueA || ![valueA isEqual:valueB]) {
            return NO;
        }
    }
    // 所有键值对匹配，返回YES
    return YES;
}
@end
