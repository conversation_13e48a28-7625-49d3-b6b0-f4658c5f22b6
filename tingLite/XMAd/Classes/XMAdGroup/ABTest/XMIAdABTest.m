//
//  XMIAdABTest.m
//  Alamofire
//
//  Created by cu<PERSON><PERSON><PERSON><PERSON> on 2023/3/8.
//

#import "XMIAdABTest.h"
#import "XMIAdConfigData.h"

@implementation XMIAdABTest

+ (NSDictionary *)abLabConfigDic {
    static NSDictionary *configDic = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        configDic =  [XMIAdConfigData dictionaryConfigForKey:XMI_CONFIG_AD_AB_LAB_CONFIG];
    });
    return configDic;
}

+ (BOOL)getBoolValueWithKey:(NSString *)key defaultValue:(BOOL)defaultBool
{
    id boolObject = [self getValueWithKey:key];
    if (boolObject) {
        return [boolObject boolValue];
    }
    return defaultBool;
}

+ (NSInteger)getIntegerValueWithKey:(NSString *)key defaultValue:(NSInteger)defaultInt
{
    id integerObject = [self getValueWithKey:key];
    if (integerObject) {
        return [integerObject integerValue];
    }
    return defaultInt;
}

+ (NSString *)getStrValueWithKey:(NSString *)key defaultValue:(NSString *)defaultStr
{
    id strObject = [self getValueWithKey:key];
    if (strObject) {
        if ([strObject isKindOfClass:[NSString class]]) {
            return strObject;
        } else {
            return [NSString stringWithFormat:@"%@", strObject];
        }
    }
    return defaultStr;
}

+ (double)getDoubleValueWithKey:(NSString *)key defaultValue:(double)defaultDouble
{
    id doubleObject = [self getValueWithKey:key];
    if (doubleObject) {
        return [doubleObject doubleValue];
    }
    return defaultDouble;
}

+ (id)getValueWithKey:(NSString *)key
{
    id result = [[self abLabConfigDic] objectForKey:key];
    if ([result isKindOfClass:[NSNull class]]) {
        return nil;
    }
    return result;
}

+ (id)getJsonObjectWithKey:(NSString *)key defaultObject:(id)defaultObject
{
    NSString *str = [self getStrValueWithKey:key defaultValue:@""];
    if (str.length == 0) {
        return defaultObject;
    }
    return [NSJSONSerialization JSONObjectWithData:[str dataUsingEncoding:NSUTF8StringEncoding] options:0 error:NULL];
}

@end
