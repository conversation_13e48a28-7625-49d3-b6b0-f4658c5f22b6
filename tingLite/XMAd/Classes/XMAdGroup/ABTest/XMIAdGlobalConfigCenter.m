//
//  XMIAdGlobalConfigCenter.m
//  XMAd
//
//  Created by xmly on 2023/4/17.
//

#import "XMIAdGlobalConfigCenter.h"
#import "XMIAdConfigData.h"
#import <XMCategories/NSObject+XMCommon.h>
#import "XMIAdDataCenter.h"
@implementation XMIAdGlobalConfigCenter
+ (BOOL)useNewRtbSplashV1_iOS {
    NSString *useNewRtb = [XMIAdConfigData stringConfigForKey:XMI_CONFIG_AD_USE_NEW_RTB_SPLASH_IOS];
    BOOL value = [useNewRtb boolValue];
    return value;
}

// 用户授权广告获取信息
+ (BOOL)useUserInfoAllowForAd {
    BOOL value = [XMIAdConfigData boolConfigForKey:XMI_CONFIG_AD_USER_INFO_ALLOW];
    return value;
}

+ (NSTimeInterval)hotLaunchAdInterval
{
    return [XMIAdConfigData doubleConfigForKey:XMI_CONFIG_AD_HOT_LAUNCH_INTERVAL];
}

+ (NSTimeInterval)aigcCacheValidInterval
{
    double interval = [XMIAdConfigData doubleConfigForKey:XMI_CONFIG_AD_AIGC_CACHE_VALID_INTERVAL];
    if (interval <= 0) {
        return 3600;//一个小时
    }
    return interval * 60;
}

+ (NSDictionary *)aigcCacheDealConfig {
    static NSDictionary *aigcCacheDealConfig = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        aigcCacheDealConfig =  [XMIAdDataCenter aigcCacheDealConfig];
    });
    return aigcCacheDealConfig;
}

+ (NSTimeInterval)aigcPlayCoolDownInterval {
    double interval = [[self aigcCacheDealConfig] doubleMaybeForKey:@"coolingTime"];
    if (interval < 0) {
        return 45 * 60;
    }
    return interval * 60;
}

+ (NSInteger)aigcShowCountDaily {
    NSInteger count = [[self aigcCacheDealConfig] integerMaybeForKey:@"displayMaxSize"];
    if (count <= 0) {
        return 0;
    }
    return count;
}
@end
