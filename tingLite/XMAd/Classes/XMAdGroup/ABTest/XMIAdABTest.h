//
//  XMIAdABTest.h
//  Alamofire
//
//  Created by cu<PERSON><PERSON><PERSON><PERSON> on 2023/3/8.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XMIAdABTest : NSObject

+ (BOOL)getBoolValueWithKey:(NSString *)key defaultValue:(BOOL)defaultBool;

+ (NSInteger)getIntegerValueWithKey:(NSString *)key defaultValue:(NSInteger)defaultInt;

+ (NSString *)getStrValueWithKey:(NSString *)key defaultValue:(NSString *)defaultStr;

+ (double)getDoubleValueWithKey:(NSString *)key defaultValue:(double)defaultDouble;

+ (id)getValueWithKey:(NSString *)key;

+ (id)getJsonObjectWithKey:(NSString *)key defaultObject:(id)defaultObject;

@end

NS_ASSUME_NONNULL_END
