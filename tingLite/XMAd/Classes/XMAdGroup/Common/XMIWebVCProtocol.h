//
//  XMIWebVCProtocol.h
//  XMAd
//  web容器，实现该协议的UIViewController
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/17.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@protocol XMIWebVCProtocol <NSObject>

@required
/**
 UIViewController viewDidLoad回调
 */
@property (nonatomic, copy) void (^viewDidLoadHandler)(void);
/**
 UIViewController dealloc回调
 */
@property (nonatomic, copy) void (^viewWillDealloc)(void);

/**
 页面加载完成回调
 */
@property (nonatomic, copy) void (^finishLoadingHandler)(BOOL success, NSError *_Nullable error);

/**
 首屏刷新回调
 */
@property (nonatomic, copy) void (^firstPaintHandler)(void);

/**
 初始化
 */
- (instancetype)initWithURL:(NSURL *)url;
/**
 返回
 */
- (void)goBack;
/**
 重新加载页面
 */
- (void)reload;
/**
 播音合规弹窗文案
 */
@property (nonatomic, copy) NSString *popReminderText;
/**
 播音合规弹窗文案样式3
 */
@property (nonatomic, copy) NSString *popReminderTextStyle3;
/**
 隐藏导航栏
 */
@property (nonatomic, assign) BOOL hiddenNavigationBar;

@property (nonatomic, copy) NSString *popReminderTextStyle4;

@end

NS_ASSUME_NONNULL_END
