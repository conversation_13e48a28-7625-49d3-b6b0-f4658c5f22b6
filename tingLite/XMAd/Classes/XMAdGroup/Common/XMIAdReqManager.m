//
//  XMIAdReqManager.m
//  XMAd
//
//  Created by xiaodong on 2023/6/19.
//

#import "XMIAdReqManager.h"
#import "XMIAdConfigData.h"
#import "NSObject+XMIModel.h"
#import <YYCache/YYCache.h>
#import <XMBase/XMPathTool.h>

@interface XMIAdFrequencyConfig : NSObject

@property (nonatomic, assign) NSInteger isDisplayedInScreen;
@property (nonatomic, assign) NSInteger intervalSecond;
@property (nonatomic, assign) NSInteger maxShow;
@property (nonatomic, assign) NSInteger dayMaxShow;

@end

@implementation XMIAdFrequencyConfig

- (BOOL)modelCustomTransformFromDictionary:(NSDictionary *)dic {
    NSNumber *isDisplayedInScreen = dic[@"isDisplayedInScreen"];
    if (!isDisplayedInScreen) {
        self.isDisplayedInScreen = XMIAdDisplayedInScreenNone;
    }
    return YES;
}

@end


@interface XMIAdClientFrequency : NSObject

@property (nonatomic, copy) NSArray<NSNumber *> *position;
@property (nonatomic, copy) NSArray<XMIAdFrequencyConfig *> *frequency;

@end

@implementation XMIAdClientFrequency

+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{
        @"frequency" : [XMIAdFrequencyConfig class]
    };
}

@end

@interface XMIAdReqManager ()

@property (nonatomic, strong) NSMutableDictionary<NSString *, XMIAdFrequencyConfig *> *configList;
@property (nonatomic, strong) YYCache *adShowCache;

@end


@implementation XMIAdReqManager

+ (instancetype)shared {
    static XMIAdReqManager *_shared = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _shared = [XMIAdReqManager new];
    });
    
    return _shared;
}

- (instancetype)init {
    if (self = [super init]) {
        [self initConfig];
    }
    return self;
}

- (void)initConfig {
    self.configList = [NSMutableDictionary dictionary];
    
    NSData *data = [XMIAdConfigData dataConfigForKey:XMI_CONFIG_AD_CLIENT_FREQUENCY];
    NSArray<XMIAdClientFrequency *> *list = [NSArray xmi_modelArrayWithClass:[XMIAdClientFrequency class] json:data];

    [list enumerateObjectsUsingBlock:^(XMIAdClientFrequency * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        [obj.position enumerateObjectsUsingBlock:^(NSNumber * _Nonnull pos, NSUInteger idx, BOOL * _Nonnull stop) {
            [obj.frequency enumerateObjectsUsingBlock:^(XMIAdFrequencyConfig * _Nonnull config, NSUInteger idx, BOOL * _Nonnull stop) {
                NSString *key = [self configKey:pos.integerValue isDisplayedInScreen:config.isDisplayedInScreen];
                [self.configList setObject:config forKey:key];
            }];
        }];
    }];
}

- (void)adDidShowWithPositionId:(NSInteger)positionId displayedInScreen:(XMIAdDisplayedInScreen)displayedInScreen {
    NSString *key = [self configKey:positionId isDisplayedInScreen:displayedInScreen];
    XMIAdFrequencyConfig *config = self.configList[key];
    if (config) {
        NSMutableArray *list = (NSMutableArray *)[self.adShowCache objectForKey:key];
        if(!list || ![list isKindOfClass:[NSMutableArray class]]) {
            list = [NSMutableArray array];
        }
        [list addObject:@((NSInteger)NSDate.date.timeIntervalSince1970)];
        if (list.count > config.dayMaxShow) {
            [list removeObjectAtIndex:0];
        }
        [self.adShowCache setObject:list forKey:key];
    }
}

- (BOOL)shouldRequestWithPositionId:(NSInteger)positionId displayedInScreen:(XMIAdDisplayedInScreen)displayedInScreen {
    NSString *key = [self configKey:positionId isDisplayedInScreen:displayedInScreen];
    XMIAdFrequencyConfig *config = self.configList[key];
    if (config) {
        NSMutableArray *list = (NSMutableArray *)[self.adShowCache objectForKey:key];
        if(list && [list isKindOfClass:[NSMutableArray class]]) {
            //短时过滤
            if (list.count >= config.maxShow && config.maxShow > 0) {
                NSInteger last = [list[list.count - config.maxShow] integerValue];
                if (NSDate.date.timeIntervalSince1970 - last < config.intervalSecond) {
                    return NO;
                }
            }
            
            //当日过滤
            if (list.count >= config.dayMaxShow && config.dayMaxShow > 0) {
                NSInteger last = [list[list.count - config.dayMaxShow] integerValue];
                NSInteger now = (NSInteger)([[NSDate date] timeIntervalSince1970]);
                //北京时区
                NSInteger zone = 8 * 60 * 60;
                //当天零点 = 现在时间 - （当天秒数）
                NSInteger zero = now - (now + zone) % 86400;
                if (last - zero >= 0) {
                    return NO;
                }
            }
        }
    }
    return YES;
}

#pragma mark - cache

- (YYCache *)adShowCache {
    if (!_adShowCache) {
        NSString *cacheDirectory = [XMPathTool cachesDirectory];
        NSString *directory = [cacheDirectory stringByAppendingPathComponent:@"XMAd/XMIAdReqManager"];
        [[NSFileManager defaultManager] createDirectoryAtPath:directory withIntermediateDirectories:YES attributes:nil error:nil];
        _adShowCache = [[YYCache alloc] initWithPath:directory];
        _adShowCache.memoryCache.countLimit = 100;      // 内存最大缓存数据个数
        _adShowCache.diskCache.countLimit = 100;      // 磁盘最大缓存数据个数
        _adShowCache.diskCache.costLimit = 10*1024;  // 磁盘最大缓存开销
    }
    return _adShowCache;
}

#pragma mark - utils

- (NSString *)configKey:(NSInteger)position isDisplayedInScreen:(NSInteger)isDisplayedInScreen {
    NSString *key = [NSString stringWithFormat:@"%@_%@", @(position), @(isDisplayedInScreen)];
    return key;
}

@end
