//
//  XMIAdNativeLogger.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/22.
//

#ifdef DEBUG

#import <Foundation/Foundation.h>
#import "XMIAdDefines.h"
#import "XMIAdMacro.h"

NS_ASSUME_NONNULL_BEGIN

@interface XMIAdNativeLogger : NSObject

+ (void)setNativeLoglevel:(XMIAdNativeLogLevel)level;

+ (BOOL)shouldLog:(XMIAdNativeLogLevel)level;

@end

static inline NSString* nameForLogLevel(XMIAdNativeLogLevel level) {
    switch (level) {
        case XMIAdNativeLogLevelNone:
            return @"NONE";
            break;
        case XMIAdNativeLogLevelError:
            return @"ERROR";
            break;
        case XMIAdNativeLogLevelWarning:
            return @"WARNING";
            break;
        case XMIAdNativeLogLevelInfo:
            return @"Info";
            break;
        case XMIAdNativeLogLevelDebug:
            return @"Debug";
            break;
        case XMIAdNativeLogLevelVerbose:
            return @"Verbose";
            break;
            
        default:
            break;
    }
    return @"";
}

NS_ASSUME_NONNULL_END

#define XMILogNative(level, type, fmt, ... )  \
do { \
        if ([XMIAdNativeLogger shouldLog:level]) { \
            NSLog((@"[XMIAdSDK][Type %@]" fmt), type, ##__VA_ARGS__);\
        } \
} while(0)
#else

#define XMILogNative(level, type, fmt, ... )

#endif

#define XMILogNativeError(type, fmt, ... ) XMILogNative(XMIAdNativeLogLevelError, type, fmt, ##__VA_ARGS__)
#define XMILogNativeWarning(type, fmt, ... ) XMILogNative(XMIAdNativeLogLevelWarning, type, fmt, ##__VA_ARGS__)
#define XMILogNativeInfo(type, fmt, ... ) XMILogNative(XMIAdNativeLogLevelInfo, type, fmt, ##__VA_ARGS__)
#define XMILogNativeDebug(type, fmt, ... ) XMILogNative(XMIAdNativeLogLevelDebug, type, fmt, ##__VA_ARGS__)
#define XMILogNativeVerbose(type, fmt, ... ) XMILogNative(XMIAdNativeLogLevelVerbose, type, fmt, ##__VA_ARGS__)

#define XMILogNativeNetworkError(fmt, ... ) XMILogNativeError(@"NETWORK", fmt, ##__VA_ARGS__)
#define XMILogNativeNetworkWarning(fmt, ... ) XMILogNativeWarning(@"NETWORK", fmt, ##__VA_ARGS__)
#define XMILogNativeNetworkInfo(fmt, ... ) XMILogNativeInfo(@"NETWORK", fmt, ##__VA_ARGS__)


#define XMILogNativeAdInfo(fmt, ... ) XMILogNativeInfo(@"AD", fmt, ##__VA_ARGS__)
#define XMILogNativeAdError(fmt, ... ) XMILogNativeError(@"AD", fmt, ##__VA_ARGS__)
