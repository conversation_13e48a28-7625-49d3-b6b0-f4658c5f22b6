//
//  XMIAdReqManager.h
//  XMAd
//
//  Created by xiaodong on 2023/6/19.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, XMIAdDisplayedInScreen) {
    XMIAdDisplayedInScreenNone = -1,
    XMIAdDisplayedInScreenNO,
    XMIAdDisplayedInScreenYES
};

@interface XMIAdReqManager : NSObject

+ (instancetype)shared;

- (void)adDidShowWithPositionId:(NSInteger)positionId displayedInScreen:(XMIAdDisplayedInScreen)displayedInScreen;

- (BOOL)shouldRequestWithPositionId:(NSInteger)positionId displayedInScreen:(XMIAdDisplayedInScreen)displayedInScreen;

@end

NS_ASSUME_NONNULL_END
