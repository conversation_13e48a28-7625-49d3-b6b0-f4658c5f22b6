//
//  XMITimeoutHelper.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/23.
//

#import "XMITimeoutHelper.h"
#import "XMIAdMacro.h"
#import "XMICommonUtils.h"

@interface XMITimer : NSObject

@property (nonatomic, strong) dispatch_source_t source;
@property (nonatomic, copy) dispatch_block_t handler;

@end

@implementation XMITimer

@end


@interface XMITimeoutHelper ()

@property (nonatomic, strong) dispatch_semaphore_t semaphore;
@property (nonatomic, strong) NSMutableDictionary *handlerMapper;

@end

@implementation XMITimeoutHelper

+ (instancetype)sharedInstance {
    static XMITimeoutHelper *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _semaphore = dispatch_semaphore_create(1);
    }
    return self;
}

- (XMIHandlerID)delay:(long long)ms runHandler:(void (^)(void))handler {
    return [self delay:ms runHandler:handler repeat:NO];
}
- (XMIHandlerID)delay:(long long)ms runHandler:(void (^)(void))handler repeat:(BOOL)repeat {
    XMILock();
    XMIHandlerID handlerID = [self delayInner:ms runHandler:handler repeat:repeat];
    XMIUnlock();
    return handlerID;
}
- (XMIHandlerID)delayInner:(long long)ms runHandler:(void (^)(void))handler repeat:(BOOL)repeat {
    XMIHandlerID handlerID = [XMICommonUtils currentTimestamp];
    dispatch_source_t ds = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, dispatch_get_main_queue());
    XMITimer *t = [[XMITimer alloc] init];
    t.source = ds;
    t.handler = handler;
    self.handlerMapper[@(handlerID)] = t;
    
    uint64_t nsec = ms * NSEC_PER_MSEC;
    dispatch_source_set_timer(ds, dispatch_walltime(NULL, nsec), nsec, 0);
    if (repeat) {
        dispatch_source_set_event_handler(ds, handler);
    } else {
        dispatch_source_set_event_handler(ds, ^{
            handler();
            [self cancelHandler:handlerID];
        });
    }
    dispatch_resume(ds);
    
    return handlerID;
}

- (BOOL)cancelHandler:(XMIHandlerID)handlerID {
    XMILock();
    BOOL ret = [self cancelHandlerInner:handlerID];
    XMIUnlock();
    return ret;
}
- (BOOL)cancelHandlerInner:(XMIHandlerID)handlerID {
    XMITimer *t = self.handlerMapper[@(handlerID)];
    if (t == nil) {
        return NO;
    }
    
    if (t.source) {
        dispatch_source_cancel(t.source);
    }
    t.handler = nil;
    t.source = nil;
    [self.handlerMapper removeObjectForKey:@(handlerID)];
    
    return YES;
}

- (BOOL)isCancel:(XMIHandlerID)handlerID {
    XMILock();
    BOOL ret = [self isCancelInner:handlerID];
    XMIUnlock();
    return ret;
}
- (BOOL)isCancelInner:(XMIHandlerID)handlerID {
    XMITimer *t = self.handlerMapper[@(handlerID)];
    return t == nil;
}

- (NSMutableDictionary *)handlerMapper {
    if (_handlerMapper == nil) {
        _handlerMapper = [[NSMutableDictionary alloc] init];
    }
    return _handlerMapper;
}

@end
