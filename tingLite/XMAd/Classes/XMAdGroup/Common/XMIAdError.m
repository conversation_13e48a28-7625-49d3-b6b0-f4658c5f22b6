//
//  XMIAdError.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/28.
//

#import "XMIAdError.h"

NSErrorDomain const kAdErrorDomain = @"com.ximalaya.adsdk";

@implementation XMIAdError

+ (NSError *)emptyDataError {
    NSDictionary *userInfo = @{ NSLocalizedDescriptionKey : @"数据为空" };
    return [NSError errorWithDomain:kAdErrorDomain code:XMIAdDataErrorEmpty userInfo:userInfo];
}

+ (NSError *)emptyProductIDError {
    NSDictionary *userInfo = @{ NSLocalizedDescriptionKey : @"ProductID为空" };
    return [NSError errorWithDomain:kAdErrorDomain code:XMIAdDataErrorProductID userInfo:userInfo];
}

+ (NSError *)otherTimeoutError {
    NSDictionary *userInfo = @{ NSLocalizedDescriptionKey : @"第三方加载超时" };
    return [NSError errorWithDomain:kAdErrorDomain code:XMIAdErrorOtherTimeout userInfo:userInfo];
}

+ (NSError *)timeoutError {
    NSDictionary *userInfo = @{ NSLocalizedDescriptionKey : @"加载超时" };
    return [NSError errorWithDomain:kAdErrorDomain code:XMIAdErrorTimeout userInfo:userInfo];
}

+ (NSError *)requestErrorWithError:(NSError *)error {
    NSMutableDictionary *userInfo = [[NSMutableDictionary alloc] init];
    if (error != nil) {
        userInfo[NSLocalizedDescriptionKey] = error.localizedDescription;
    } else {
        userInfo[NSLocalizedDescriptionKey] = @"请求出错";
    }
    return [NSError errorWithDomain:kAdErrorDomain code:XMIAdErrorRequest userInfo:userInfo];
}

+ (NSError *)noBackupError
{
    NSDictionary *userInfo = @{ NSLocalizedDescriptionKey : @"找不到备胎" };
    return [NSError errorWithDomain:kAdErrorDomain code:XMIAdDataErrorNoBackup userInfo:userInfo];
}

+ (NSError *)canceledError
{
    NSDictionary *userInfo = @{ NSLocalizedDescriptionKey : @"操作取消" };
    return [NSError errorWithDomain:kAdErrorDomain code:XMIAdDataErrorCanceled userInfo:userInfo];
}

+ (BOOL)isXMIError:(NSError *)error {
    return [error.domain isEqualToString:kAdErrorDomain];
}

@end
