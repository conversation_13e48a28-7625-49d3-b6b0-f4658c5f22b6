//
//  XMIAdNativeLogger.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/22.
//
#ifdef DEBUG

#import "XMIAdNativeLogger.h"

static XMIAdNativeLogLevel logLevel = XMIAdNativeLogLevelNone;

@implementation XMIAdNativeLogger

+ (void)setNativeLoglevel:(XMIAdNativeLogLevel)level
{
    logLevel = level;
}

+ (BOOL)shouldLog:(XMIAdNativeLogLevel)level
{
    return level <= logLevel;
}

@end

#endif
