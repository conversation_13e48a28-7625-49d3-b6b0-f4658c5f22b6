//
//  XMIAdHelper.h
//  XMAd
//
//  Created by xmly on 2022/3/10.
//

#import <Foundation/Foundation.h>
#import "XMIAdDefines.h"
#import <XMCategories/XMCategory.h>
NS_ASSUME_NONNULL_BEGIN
#define kSingleCoverSize ([XMIAdHelper mixFeed_singleCoverSize])
#define kHomeRatioSize(...) (__VA_ARGS__ * [XMIAdHelper mixFeed_configRatio])

@class XMIAdRelatedData, UIView;
@interface XMIAdHelper : NSObject
// 初始化三方广告SDK
+ (void)initSdkWithAdType:(XMIAdType)adType;
// 当前是否是wifi，或者视频链接已存在缓存
+ (BOOL)canPlayVideoOnWIFIOrCached:(XMIAdRelatedData *)relatedData;
// 弹窗
+ (void)showAlertWithMessage:(NSString *)message delayTime:(NSInteger)seconds;
// 调用视频播放逻辑时，弹窗提示信息，仅debug
+ (void)showDebugMessageWhenPlayVideo:(XMIAdRelatedData *)relatedData;
// 实时竞价开关是否打开
+ (BOOL)iOSServerBiddingEnable;
//上报时的额外参数，由主app传入
+ (NSDictionary *)extraReportParamsForAdPosition:(NSString *)postionName;
// 广告是否使用新的负反馈样式
+ (BOOL)negativeFeedbackValueForAd;


+ (CGFloat)mixFeed_singleCoverSize;
// 首页混排比例
+ (CGFloat)mixFeed_configRatio;

+ (BOOL)shouldRequestAdWithSlotId:(long long)slotId params:(nullable NSDictionary *)params forbidStyles:(NSArray *__autoreleasing *)styles;

+ (void)showTopRemindViewInView:(UIView *)view remindText:(NSString *)remindText;

// 是否是运营广告
+ (BOOL)adDataIsOperation:(XMIAdRelatedData *)adData;

//声音总时长格式化
+ (NSString *)stringForSeconds2:(NSUInteger)seconds;

+ (void)apmLogMessage:(NSString *)message;

+ (BOOL)isSocialHome;

+ (NSString *)getTicketWithBusiness:(NSString *)business scene:(NSString *)scene;

@end

NS_ASSUME_NONNULL_END
