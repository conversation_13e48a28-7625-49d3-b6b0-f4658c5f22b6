//
//  XMIJumpManagerFactory.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/8.
//

#import "XMIJumpManagerFactory.h"
#import "XMIAdDefines.h"
#import "XMIAdConverter.h"

@implementation XMIJumpManagerFactory

+ (id<XMIJumpManagerProtocol>)jumpManagerByAdType:(NSInteger)adType
{
    NSString *provider = [XMIAdConverter providerFromAdType:adType];
    if (provider.length > 0) {
        NSString *className = [NSString stringWithFormat:@"XMI%@JumpManager", provider];
        Class cls = NSClassFromString(className);
        if (cls) {
            id<XMIJumpManagerProtocol> jumpManager = [[cls alloc] init];
            if (jumpManager && ![jumpManager conformsToProtocol:@protocol(XMIJumpManagerProtocol)]) {
                NSAssert(nil, @"跳转器没有遵从协议！！");
                return nil;
            }
            return jumpManager;
        }
    }
    return nil;
}

@end
