//
//  XMIAdError.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/28.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, XMIAdErrorCode) {
    XMIAdDataErrorNone = 0,         // 无错误
    XMIAdDataErrorEmpty = 1,        // 广告数据是空
    XMIAdDataErrorProductID = 2,    // SKStoreProductParameterITunesItemIdentifier是空
    XMIAdErrorRequest = 3,          // 请求错误
    XMIAdDataErrorNoBackup = 4,        // 找不到备胎
    XMIAdDataErrorCanceled = 101,   //操作取消
    XMIAdErrorOtherTimeout = 998,   // 第三方超时
    XMIAdErrorTimeout = 999,        // 超时
};

@interface XMIAdError : NSObject

+ (NSError *)emptyDataError;
+ (NSError *)emptyProductIDError;
+ (NSError *)otherTimeoutError;
+ (NSError *)timeoutError;
+ (NSError *)requestErrorWithError:(NSError *)error;
+ (NSError *)noBackupError;
+ (NSError *)canceledError;
/**
 是否自定义Error
 */
+ (BOOL)isXMIError:(NSError *)error;

@end

NS_ASSUME_NONNULL_END
