//
//  XMIAdViewProtocol.h
//  XMAd
//  广告视图能力扩展，获取关联广告数据、关联容器视图等
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/18.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class XMIAdRelatedData;
@class XMIExpressAdView;

@protocol XMIAdViewProtocol <NSObject>

@optional
/**
 获取关联广告数据
 */
- (XMIAdRelatedData *)getRelatedAdData;
/**
 获取关联信息流容器视图
 */
- (XMIExpressAdView *)getExpressAdView;
/**
 获取显示类型 0-静态图 1-git 2-视频 3-备胎图
 */
- (int)getAdShowType;
/**
 * 视频广告时长，单位 ms
 */
- (int)getVideoDuration;

/**
 * 视频广告已播放时长，单位 ms
 */
- (int)getVideoPlayTime;

@end

NS_ASSUME_NONNULL_END
