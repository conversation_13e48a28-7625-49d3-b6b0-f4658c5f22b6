//
//  XMIAdHelper.m
//  XMAd
//
//  Created by xmly on 2022/3/10.
//

#import "XMIAdHelper.h"
#import "XMIAdManager.h"
#import "XMIAdConverter.h"
#import "XMIAdRelatedData.h"
#import "XMIAdDataCenter.h"
#import "XMINetworkReachability.h"
#import <XMCategories/NSObject+XMCommon.h>
#import <XMVideoHttpCache/XMVideoHttpCache.h>
#import "XMIAdMacro.h"
#import <UIKit/UIKit.h>
#import <XMAd/UIView+XMIUtils.h>
#import <XMConfigCenter/XMConfigCenter.h>
#import "XMICommonUtils.h"
#import <XMCategories/XMUIScaleMng.h>

@implementation XMIAdHelper
// 初始化三方广告SDK
+ (void)initSdkWithAdType:(XMIAdType)adType {
    XMIAdSource adPlatform = [XMIAdConverter sdkTypeFromAdType:adType];
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (adPlatform >= 1
        && manager.delegate
        && [manager.delegate respondsToSelector:@selector(managerSetupAdIfNeededWithAdPlatform:)]) {
        [manager.delegate managerSetupAdIfNeededWithAdPlatform:adPlatform];
    }
}

// 当前是否是wifi，或者视频链接已存在缓存
+ (BOOL)canPlayVideoOnWIFIOrCached:(XMIAdRelatedData *)relatedData {
    if ([XMIAdDataCenter getAdAlwaysPlayVideoValue]) {
        return YES;
    }
    
    if ([XMINetworkReachability reachabilityForInternetConnection].currentReachabilityStatus == XMINetworkStatusWiFi) {
        return YES;
    }
    NSURL *cachedUrl = [XMVideoHttpCacheIMP cacheCompleteFileURLIfExistedWithURL:relatedData.videoUrl.aktUrlValue];
    if (cachedUrl) {
        return YES;
    }
    return NO;
}

// 弹窗
+ (void)showAlertWithMessage:(NSString *)message delayTime:(NSInteger)seconds {
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(seconds * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        UIAlertController *alertController = [UIAlertController alertControllerWithTitle:message message:nil preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *action = [UIAlertAction actionWithTitle:@"确定" style:UIAlertActionStyleCancel handler:nil];
        [alertController addAction:action];
        [[UIApplication sharedApplication].keyWindow.rootViewController presentViewController:alertController animated:NO completion:nil];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [alertController dismissViewControllerAnimated:YES completion:nil];
        });
    });
}

+ (void)showDebugMessageWhenPlayVideo:(XMIAdRelatedData *)relatedData {
    // 需要调试时再开启弹窗
    return;
    if ([XMIAdManager sharedInstance].isDebug) {
        NSString *wifiStr = @"wifi";
        if ([XMINetworkReachability reachabilityForInternetConnection].currentReachabilityStatus != XMINetworkStatusWiFi) {
            wifiStr = @"流量";
        }
        BOOL isCache = [XMVideoHttpCacheIMP cacheCompleteFileURLIfExistedWithURL:relatedData.videoUrl.aktUrlValue];;
        NSString *cacheStr = @"无无无缓存";
        if (isCache) {
            cacheStr = @"有有有缓存";
        }
        NSString *str = [NSString stringWithFormat:@"广告 %@  %@ positionID %lld", wifiStr, cacheStr, relatedData.positionId];
        
        [self showAlertWithMessage:str delayTime:2];
    }
}

// 实时竞价开关是否打开
+ (BOOL)iOSServerBiddingEnable {
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(getConfigCenterWithKey:)]) {
        id object = [manager.delegate getConfigCenterWithKey:@"iOSServerBiddingEnable"];
        if ([object isKindOfClass:NSNumber.class]) {
            return [object boolValue];
        }
    }
    return YES;
}

// 广告是否使用新的负反馈样式
+ (BOOL)negativeFeedbackValueForAd {
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(getConfigCenterWithKey:)]) {
        id object = [manager.delegate getConfigCenterWithKey:@"Negative_feedback"];
        if ([object isKindOfClass:NSNumber.class]) {
            return [object boolValue];
        }
    }
    return NO;
}

+ (NSDictionary *)extraReportParamsForAdPosition:(NSString *)postionName
{
    if ([[XMIAdManager sharedInstance].delegate respondsToSelector:@selector(managerGetExtraReportParamsForAdPosition:)]) {
        return [[XMIAdManager sharedInstance].delegate managerGetExtraReportParamsForAdPosition:postionName];
    }
    return nil;
}

+ (CGFloat)mixFeed_singleCoverSize {
    return kHomeRatioSize(80);
}

+ (CGFloat)mixFeed_configRatio {
    //和主站适配规则保持一致，保留fix开关以防万一
    if ([[XMConfigCenter sharedConfigCenter] getBoolValueWithGroup:@"ad" andItem:@"new_mixFeed_configRatio" defaultValue:YES]) {
        return xmUIEle(1.0f);
    } else {
        CGFloat ratio = MIN(XMI_SCREEN_WIDTH, 414.f) / 375.f;
        return ratio;
    }
}

+ (BOOL)shouldRequestAdWithSlotId:(long long)slotId params:(nullable NSDictionary *)params forbidStyles:(NSArray *__autoreleasing *)styles
{
    XMIAdManager *manager = [XMIAdManager sharedInstance];
    if (manager.delegate && [manager.delegate respondsToSelector:@selector(managerShouldRequestAdWithSlotId:params:forbidStyles:)]) {
        return [manager.delegate managerShouldRequestAdWithSlotId:slotId params:params forbidStyles:styles];
    }
    return YES;
}

+ (void)showTopRemindViewInView:(UIView *)view remindText:(nonnull NSString *)remindText
{
    NSTimeInterval showTime = [XMIAdDataCenter webTopToastShowTime];
    if (showTime == 0) {
        return;
    }
    NSInteger tag = 23451;
    if ([view viewWithTag:tag]) {
        return;
    }
    UIView *remindView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, view.width, 40)];
    remindView.backgroundColor = XMI_COLOR_RGB(0xD6E7FF);
    remindView.tag = tag;
    [view addSubview:remindView];
    UILabel *titleLabel = [[UILabel alloc] init];
    [remindView addSubview:titleLabel];
    titleLabel.text = remindText;
    titleLabel.font = XMI_AD_PingFangFont(15);
    titleLabel.textColor = XMI_COLOR_RGB(0x609BEF);
    [titleLabel sizeToFit];
    titleLabel.xmi_centerY = remindView.xmi_height * 0.5f;
    UIImageView *iconView = [[UIImageView alloc] initWithImage:[XMICommonUtils imageNamed:@"web_top_tip_icon"]];
    [remindView addSubview:iconView];
    iconView.xmi_centerY = titleLabel.xmi_centerY;
    CGFloat interval = 4;
    iconView.xmi_left =( remindView.xmi_width - (titleLabel.xmi_width + interval + iconView.xmi_width)) * 0.5f;
    titleLabel.xmi_left = iconView.xmi_right + interval;
    if (showTime > 0.6f) {
        remindView.alpha = 0;
        [UIView animateWithDuration:0.3f animations:^{
                    remindView.alpha = 1.0f;
                } completion:^(BOOL finished) {
                    [UIView animateWithDuration:0.3f delay:showTime - 0.6f options:0 animations:^{
                        remindView.alpha = 0;
                    } completion:^(BOOL finished) {
                        [remindView removeFromSuperview];
                    }];
                }];
    }
}

+ (BOOL)adDataIsOperation:(XMIAdRelatedData *)adData { // 是否是声播广告
    // 这四种是运营广告
    if ([adData.adUserType isEqualToString:@"OPERATION"] || [adData.adUserType isEqualToString:@"MARKET"] || [adData.adUserType isEqualToString:@"OTHER"] || [adData.adUserType isEqualToString:@"PAY"]) {
        return YES;
    }
    return NO;
}

+ (NSString *)stringForSeconds2:(NSUInteger)seconds
{
    if (seconds == 0) {
        return [NSString stringWithFormat:@"00:00"];
    }
    else if (seconds < 60) {
        return [NSString stringWithFormat:@"00:%02ld", (unsigned long)seconds];
    }
    else if (seconds < 60 * 60) {
        return [NSString stringWithFormat:@"%02ld:%02ld", (unsigned long)seconds/60, (unsigned long)seconds%60];
    }
    else {
        NSInteger h = seconds/(60*60);
        NSInteger m = (seconds - h * 60 * 60)/60;
        if (h < 10)
            return [NSString stringWithFormat:@"%02ld:%02ld:%02ld", (signed long)h, (signed long)m, (unsigned long)seconds%60];
        else
            return [NSString stringWithFormat:@"%ld:%02ld:%02ld", (signed long)h, (signed long)m, (unsigned long)seconds%60];
    }
}

+ (void)apmLogMessage:(NSString *)message
{
    if ([[XMIAdManager sharedInstance].delegate respondsToSelector:@selector(managerApmLogMessage:)]) {
        [[XMIAdManager sharedInstance].delegate managerApmLogMessage:message];
    }
}

+ (BOOL)isSocialHome
{
    if ([[XMIAdManager sharedInstance].delegate respondsToSelector:@selector(isSocialHome)]) {
        return [[XMIAdManager sharedInstance].delegate isSocialHome];
    }
    return NO;
}

+ (NSString *)getTicketWithBusiness:(NSString *)business scene:(NSString *)scene
{
    if ([[XMIAdManager sharedInstance].delegate respondsToSelector:@selector(managerGetTicketWithBusiness:scene:)]) {
        return [[XMIAdManager sharedInstance].delegate managerGetTicketWithBusiness:business scene:scene];
    }
    return @"";
}

@end
