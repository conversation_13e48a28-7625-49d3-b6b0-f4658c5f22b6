//
//  XMITimeoutHelper.h
//  XMAd
//  超时工具，超时后执行某个方法，可取消
//  可取消版dispatch_after
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/23.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef long long XMIHandlerID;

@interface XMITimeoutHelper : NSObject

+ (instancetype)sharedInstance;

/**
 延迟执行
 @param ms :毫秒
 @param handler :block
 @return 标识该任务的唯一ID
 */
- (XMIHandlerID)delay:(long long)ms runHandler:(void (^)(void))handler;
/**
 延迟执行
 @param repeat :是否重复执行
 */
- (XMIHandlerID)delay:(long long)ms runHandler:(void (^)(void))handler repeat:(BOOL)repeat;
/**
 取消
 @param handlerID :任务ID
 */
- (BOOL)cancelHandler:(XMIHandlerID)handlerID;
/**
 是否已取消
 */
- (BOOL)isCancel:(XMIHandlerID)handlerID;

@end

NS_ASSUME_NONNULL_END
