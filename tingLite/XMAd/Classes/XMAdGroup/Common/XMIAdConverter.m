//
//  XMIAdConverter.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/9/2.
//

#import "XMIAdConverter.h"
#import "XMIAdDefines.h"
#import "NSData+XMIUtils.h"
#import "NSString+XMIUtils.h"
#import "XMIAdManager.h"
#import "XMICommonUtils.h"
#include <sys/sysctl.h>
#import <ifaddrs.h>
#import <arpa/inet.h>
#include <net/if.h>
#include <net/if_dl.h>
#import <CommonCrypto/CommonDigest.h>
#import <SystemConfiguration/CaptiveNetwork.h>
#import "XMIAdHeader.h"
#import "XMIAdConfigData.h"
#import "XMIAdSlot.h"

@implementation XMIAdConverter

+ (NSInteger)sdkTypeFromAdType:(NSInteger)adtype {
    XMIAdSource sdkType = XMIAdSourceXM;
    switch (adtype) {
        case XMIAdTypeXM:
            sdkType = XMIAdSourceXM;
            break;
        case XMIAdTypeBU:
        case XMIAdTypeBUExpress:
            sdkType = XMIAdSourceBU;
            break;
        case XMIAdTypeGDT:
        case XMIAdTypeGDTSplash:
            sdkType = XMIAdSourceGDT;
            break;
        case XMIAdTypeJAD:
            sdkType = XMIAdSourceJAD;
            break;
        case XMIAdTypeBAIDU:
            sdkType = XMIAdSourceBAIDU;
            break;
        default:
            break;
    }
    return sdkType;
}

+ (NSString *)providerFromAdType:(NSInteger)adType {
    NSString *provider = @"";
    switch (adType) {
        case XMIAdTypeXM:
            provider = XMIAdProviderXM;
            break;
        case XMIAdTypeBU:
        case XMIAdTypeBUExpress:
            provider = XMIAdProviderBU;
            break;
        case XMIAdTypeGDT:
        case XMIAdTypeGDTSplash:
            provider = XMIAdProviderGDT;
            break;
        case XMIAdTypeJAD:
            provider = XMIAdProviderJAD;
            break;
        case XMIAdTypeBAIDU:
            provider = XMIAdProviderBAIDU;
            break;
        default:
            break;
    }
    
    return provider;
}

+ (NSInteger)slotAdTypeFromAdType:(NSInteger)adType {
    XMIAdSlotAdType slotAdType = XMIAdSlotAdTypeUnknown;
    switch (adType) {
        case XMIAdTypeXM:
            slotAdType = XMIAdSlotAdTypeFeed_Custom;
            break;
        case XMIAdTypeBU:
            slotAdType = XMIAdSlotAdTypeFeed_Custom;
            break;
        case XMIAdTypeBUExpress:
            slotAdType = XMIAdSlotAdTypeFeed;
            break;
        case XMIAdTypeGDT:
            slotAdType = XMIAdSlotAdTypeFeed_Custom;
            break;
        case XMIAdTypeBAIDU:
            slotAdType = XMIAdSlotAdTypeFeed_Custom;
            break;
        default:
            break;
    }
    
    return slotAdType;
}

+ (int)showTypeFromRelatedData:(XMIAdRelatedData *)relatedData {
    XMIAdShowStyle showstyle = relatedData.showstyle;
    XMIAdShowType showType = XMIAdShowTypeImage;
    switch (showstyle) {
        case XMIAdStyleHomeLargeImage:
        case XMIAdStyleHomeShopWindow:
        case XMIAdStyleHomeBackgroundImage:
        case XMIAdStyleMyPageFeedSecondPic:
        case XMIAdStyleMyPageFeedBannerPic:
            showType = XMIAdShowTypeImage;
            break;
        case XMIAdStyleHomeVideo:
        case XMIAdStyleHomeDoubleRowVerticalVideo:
        case XMIAdStyleSearchEntryPageInformationFlowVideo:
        case XMIAdStyleMyPageFeedSecondVideo:
        case XMIAdStyleMyPageFeedBannerVideo:
            if (relatedData.videoUrl.length > 0) {
                showType = XMIAdShowTypeVideo;
            } else {
                showType = XMIAdShowTypeImage;
            }
            break;
        default:
            break;
    }
    
    return showType;
}

+ (NSString *)decodeAdToken:(NSString *)eToken {
    if (eToken == nil) {
        return eToken;
    }
    if (eToken.length < 1 || [eToken isEqualToString:XMI_REPORT_DEFAULT_TOKEN]) {
        return eToken;
    }
    
    NSString *privateKey = @"1tyt1zuKMloXu/prwDTm5Q==";
    NSData *keyData = [[NSData alloc] initWithBase64EncodedString:privateKey options:0];
    NSData *textData = [[NSData alloc] initWithBase64EncodedString:eToken options:0];
    NSData *decodeData = [NSData xmi_AES256DecryptWithKeyData:keyData DecryptData:textData];
    NSString *token = [[NSString alloc] initWithData:decodeData encoding:NSUTF8StringEncoding];
    return token;
}

+ (BOOL)isVirtualAD:(long long)adid {
    return adid > XMI_VIRTUAL_ADID_MIN && adid <= XMI_VIRTUAL_ADID_MAX;
}

+ (NSString *)urlStringByReplaceADParams:(NSString *)urlString {
    return [self urlStringByReplaceADParams:urlString withClickInfo:nil];
}

+ (NSString *)urlStringByReplaceADParams:(NSString *)urlString withClickInfo:(nullable NSDictionary *)clickInfo {
    if (!urlString) {
        return urlString;
    }
    
    NSString *ADUrlStr = urlString;
    NSArray *allKey = [self adParamKeys].allKeys;
    NSString *uppercaseStr = [ADUrlStr uppercaseString];
    for (NSString *key in allKey) {
        NSArray *adKeys = @[[NSString stringWithFormat:@"{%@}",key], [NSString stringWithFormat:@"__%@__",key], [NSString stringWithFormat:@"[%@]",key]];
        NSMutableArray<NSValue *> *ranges = [NSMutableArray array];
        for (NSString *adKey in adKeys) {
            NSString *scanString = uppercaseStr;
            NSScanner *scanner = [NSScanner scannerWithString:scanString];
            [ranges removeAllObjects];
            while (true) {
                NSString *availdStr = nil;
                [scanner scanString:adKey intoString:&availdStr];
                if (availdStr) {
                    [ranges addObject:[NSValue valueWithRange:NSMakeRange(scanner.scanLocation-adKey.length, adKey.length)]];
                }
                if (scanner.isAtEnd) {
                    break;
                }
                scanner.scanLocation++;
            }
            if(ranges.count > 0) {
                NSString *adMapKey = [self adParamKeys][key];
                NSInteger keyIndex = [adMapKey intValue];
                NSString *realValue = [[self getAdStaticDataByIndex:keyIndex clickInfo:clickInfo] xmi_URLEncodedString];
                if (!realValue || !realValue.length) {
                    continue;
                }
                NSInteger offset = 0;
                for (NSValue *obj in ranges) {
                    NSRange tempRange = [obj rangeValue];
                    ADUrlStr = [ADUrlStr stringByReplacingCharactersInRange:NSMakeRange(tempRange.location+offset, tempRange.length) withString:realValue];
                    uppercaseStr = [uppercaseStr stringByReplacingCharactersInRange:NSMakeRange(tempRange.location+offset, tempRange.length) withString:realValue];
                    offset += realValue.length-adKey.length;
                }
            }
        }
    }
    
    return ADUrlStr;
}

+ (NSDictionary *)adParamKeys {
    static NSDictionary *adParamKeys = nil;
    if (!adParamKeys) {
        NSDictionary *dic = @{
                              @"OS":@1,//:    OS    /    OS    操作系统
                              @"IMEI":@2,//:    IMEI    /    IMEI    //can not get for ios
                              @"MAC":@3,//    MAC    /    MAC    不带冒号，MD5加密 // can not get for ios7 later
                              @"MAC1":@4,//    MAC1        /    /
                              @"MAC2":@5,//    /    mac    /    带冒号，不加密
                              @"IDFA":@6,//    IDFA    /    IDFA    去杠                              //identifier for advertise
                              @"IDFA1":@7,//    /    idfa    /    带杠
                              @"IDFV":@8,//    /    idfv    /    599F9C00-92DC-4B5C-9464-7971F01F8370  //Identifier for Vendor
                              //@"AAID",    //AAID    /    AAID    Android Advertising
                              @"OpenUDID":@9,//    OPENUDID    /    OpenUDID    /
                              @"UDID":@10,//    /    /    /    MD5加密    //can not get for ios7 later
                              @"ODIN":@11,//    /    /    /    /         //Open Device Identification Number
                              //@"DUID",//    DUID    /    /    Windows phone
                              @"IP":@12,//    IP    clickip    IP    Ip地址 //only local ip
                              @"UA":@13,//    /    /    /    User agent
                              @"TS":@14,//    /    /    /    例子：1350726130108 // get time stmap since 1970s
                              @"CLICKTIME":@15,//    /    clicktime    /    例子：20140808110117 //
                              @"OSVS":@16,//    /    sysversion    OSVS    操作系统版本
                              @"TERM":@17,//    /    devicename    TERM    终端机型
                              @"APPID":@18,//    /    /    AKEY    媒体APP Key
                              @"APPNAME":@19,    ///    /    ANAME    媒体APP Name
                              @"APP":@20,
                              @"ANAME":@21,
                              @"FIRSTOPENTIME":@22,//    /    devicestarttime    /    应用首次打开时间,例子：20140808110117
                              @"BSSID":@23, // wifi bbsid
                              @"DOWN_X":@24, // 用户手指按下时的横坐标。
                              @"DOWN_Y":@25, // 用户手指按下时的纵坐标
                              @"UP_X":@26, // 用户手指离开设备屏幕时的横坐标。
                              @"UP_Y":@27, // wifi bbsid
//                              @"CAID":@28 // CAID
                              @"IPV6":@(29)
                              };
        adParamKeys = dic;
    }
    return adParamKeys;
}

+ (NSString *)getAdStaticDataByIndex:(NSInteger)index clickInfo:(NSDictionary *)clickInfo {
    
    NSString *value = nil;
    
    switch (index) {
        case 1://OS
            value = @"1";
            break;
        case 2://IMEI
            break;
        case 3://MAC
            value = @"";
            break;
        case 4://MAC1
            value = [self getMacAddress1];
            break;
        case 5://MAC2
            value = [self getMacAddress2];
            break;
        case 6://IDFA 服务端替换
//            value = [XMIAdHeader sharedInstance].idfa;
            break;
        case 7://IDFA1 原来是设备ID，现在用真实IDFA再做md5
            value = [XMIAdHeader sharedInstance].md5IDFA;
            break;
        case 8://IDFV
            value = [[[UIDevice currentDevice] identifierForVendor] UUIDString];
            break;
        case 9://OpenUDID
            value = [self getOpenUDID];
            break;
        case 10://UDID
            value = @""; // 不能获取了
            break;
        case 11://ODIN
            value = [self getODINString];
            break;
        case 12://IP
            value = [self getClientIp];
            break;
        case 13://UA
            value = [XMIAdHeader sharedInstance].systemUserAgent;
            break;
        case 14://TS
            value = [NSString stringWithFormat:@"%lld", [XMICommonUtils currentTimestamp]];
            break;
        case 15://CLICKTIME
            value = [self getClickTime];
            break;
        case 16://OSVS
            value = [XMICommonUtils systemVersion];
            break;
        case 17://TERM
            value = [XMICommonUtils deviceType];
            break;
        case 18://APPID
            value = [XMIAdManager appID];
            break;
        case 19://APPNAME
        case 20:
        case 21:
            value = [XMICommonUtils appName];
            break;
        case 22://FIRSTOPENTIME
            value = [self getFirstLaunchTime];
            break;
        case 23://BSSID
            value = [self getWifiBBSID];
            break;
        case 24://用户手指按下时的横坐标。
            value = clickInfo != nil ? (clickInfo[@"absX"] ?: @"") : @"";
            break;
        case 25://用户手指按下时的纵坐标
            value = clickInfo != nil ? (clickInfo[@"absY"] ?: @"") : @"";
            break;
        case 26://用户手指离开设备屏幕时的横坐标。
            value = clickInfo != nil ? (clickInfo[@"x"] ?: @"") : @"";
            break;
        case 27://用户手指离开设备屏幕时的纵坐标。
            value = clickInfo != nil ? (clickInfo[@"y"] ?: @"") : @"";
            break;
//        case 28://caid
//            value = [CAIDProvider cachedCAID] ?: @"";
//            break;
        case 29:
            value = [XMICommonUtils getIPV6String:YES];
            break;
            break;
        default:
            break;
    }
    
    return value;
}

+ (NSString *)getOpenUDID {
    // TODO:
    return @"";
}

+ (NSString *)getODINString {
    // Step 1: Get MAC address
    int                 mib[6];
    size_t              len;
    char                *buf;
    unsigned char       *ptr;
    struct if_msghdr    *ifm;
    struct sockaddr_dl  *sdl;
    
    mib[0] = CTL_NET;
    mib[1] = AF_ROUTE;
    mib[2] = 0;
    mib[3] = AF_LINK;
    mib[4] = NET_RT_IFLIST;
    
    if ((mib[5] = if_nametoindex("en0")) == 0) {
        //NSLog(@"ODIN-1.1: if_nametoindex error");
        return nil;
    }
    
    if (sysctl(mib, 6, NULL, &len, NULL, 0) < 0) {
        //NSLog(@"ODIN-1.1: sysctl 1 error");
        return nil;
    }
    
    if ((buf = malloc(len)) == NULL) {
        //NSLog(@"ODIN-1.1: malloc error");
        return nil;
    }
    
    if (sysctl(mib, 6, buf, &len, NULL, 0) < 0) {
        //NSLog(@"ODIN-1.1: sysctl 2 error");
        free(buf);
        return nil;
    }
    
    ifm = (struct if_msghdr *)buf;
    sdl = (struct sockaddr_dl *)(ifm + 1);
    ptr = (unsigned char *)LLADDR(sdl);
    
    //NSLog(@"MAC Address: %02X:%02X:%02X:%02X:%02X:%02X", *ptr, *(ptr+1), *(ptr+2), *(ptr+3), *(ptr+4), *(ptr+5));
    
    // Step 2: Take the SHA-1 of the MAC address
    
    CFDataRef data = CFDataCreate(NULL, (uint8_t*)ptr, 6);
    
    unsigned char messageDigest[CC_SHA1_DIGEST_LENGTH];
    
    CC_SHA1(CFDataGetBytePtr((CFDataRef)data),
            (CC_LONG)CFDataGetLength((CFDataRef)data),
            messageDigest);
    
    CFMutableStringRef string = CFStringCreateMutable(NULL, 40);
    for(int i = 0; i < CC_SHA1_DIGEST_LENGTH; i++) {
        CFStringAppendFormat(string,
                             NULL,
                             (CFStringRef)@"%02X",
                             messageDigest[i]);
    }
    
    CFStringLowercase(string, CFLocaleGetSystem());
    
    //NSLog(@"ODIN-1: %@", string);
    
    free(buf);
    
    NSString *odinstring = [[NSString alloc] initWithString:(__bridge NSString*)string];
    CFRelease(data);
    CFRelease(string);
    
    return odinstring;
}

+ (NSString*)getFirstLaunchTime {
    // TODO:
    return @"";
}

+ (NSString *)getWifiBBSID {
    static NSString *WifiBBSID = nil;
    if (!WifiBBSID) {
        NSString *temp = nil;
        NSString *macIp = @"";
        CFArrayRef myArray = CNCopySupportedInterfaces();
        if (myArray != nil) {
            CFDictionaryRef myDict = CNCopyCurrentNetworkInfo(CFArrayGetValueAtIndex(myArray, 0));
            if (myDict != nil) {
                NSDictionary *dict = (NSDictionary*)CFBridgingRelease(myDict);
                //            ssid = [dict valueForKey:@"SSID"];
                macIp = [dict valueForKey:@"BSSID"];
            }
            CFRelease(myArray);
        }
        temp = macIp;
        WifiBBSID = temp;
    }
    return WifiBBSID;
}

+ (NSString*)getMacAddress1 {
    static NSString *macAddress1 = nil;
    if (!macAddress1) {
        NSString *temp = [[XMICommonUtils macaddress] stringByReplacingOccurrencesOfString:@":" withString:@""];
        macAddress1 = temp;
    }
    return macAddress1;
}

+ (NSString*)getMacAddress2 {
    static NSString *macAddress2 = nil;
    if (!macAddress2) {
        NSString *temp = [XMICommonUtils macaddress];
        macAddress2 = temp;
    }
    return macAddress2;
}

+ (NSString *)getClientIp {
    NSString *clientIp = [XMIAdConfigData stringConfigForKey:XMI_CONFIG_CLIENT_IP];
    if (clientIp == nil || clientIp.length < 1) {
        clientIp = [self getLocalIp];
        if (clientIp != nil && clientIp.length > 0) {
            [XMIAdConfigData updateConfig:clientIp forKey:XMI_CONFIG_CLIENT_IP];
        } else {
            clientIp = @"127.0.0.1";
        }
    }
    
    return clientIp;
}
+ (NSString *)getLocalIp {
    NSString *address = nil;
    struct ifaddrs *addrs;
    const struct ifaddrs *cursor;
    int error;
    error = getifaddrs(&addrs);
    
    if (error) {
        return nil;
    }
    for (cursor = addrs; cursor; cursor = cursor->ifa_next) {
        if (cursor->ifa_addr->sa_family == AF_INET && (cursor->ifa_flags & IFF_LOOPBACK) == 0) {
            NSString *ifa_name = [NSString stringWithUTF8String:cursor->ifa_name];
            if( [@"en0" isEqualToString:ifa_name] ||
               [@"en1" isEqualToString:ifa_name]) {
                address = [NSString stringWithUTF8String:inet_ntoa(((struct sockaddr_in *)cursor->ifa_addr)->sin_addr)];
                break;
            }
        }
    }
    freeifaddrs(addrs);
    return address;
}

+ (NSString*)getClickTime
{
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    [dateFormatter setDateFormat:@"yyyyMMddHHmmss"];
    return [dateFormatter stringFromDate:[NSDate date]];
}

+ (NSString *)urlStringByReplaceJtParam:(NSString *)urlString withOtherParma:(NSDictionary *)param {
    if (urlString == nil) {
        return urlString;
    }
    NSString *toUrl = urlString;
    NSDictionary *dict = [NSString xmi_parametersFromUrl:toUrl];
    NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithDictionary:param];
    [dic addEntriesFromDictionary:dict];
    NSString *jtValue = [dict objectForKey:@"jt"];
    if (jtValue != nil) {
        jtValue = [self replaceItingScheme:jtValue];
        dic[@"jt"] = jtValue;
    }
    toUrl = [NSString xmi_encodedURLString:toUrl withParam:dic];
    
    return toUrl;
}

+ (NSString *)replaceItingScheme:(NSString *)originstr {
    NSString *schemeStr = originstr;
    if (![schemeStr hasPrefix:@"iting://"]) {
        return schemeStr;
    }
    NSString *scheme = [self bundleScheme];
    if (scheme == nil) {
        return schemeStr;
    }
    
    schemeStr = [schemeStr stringByReplacingOccurrencesOfString:@"iting" withString:scheme];
    return schemeStr;
}

+ (NSString *)bundleScheme
{
    NSString *scheme = nil;
    NSArray *array = [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleURLTypes"];
    if (array == nil) {
        return nil;
    }
    for (NSDictionary *schemedict in array) {
        NSString *schemename = [schemedict objectForKey:@"CFBundleURLName"];
        if ([schemename isEqualToString:@"bundleScheme"]) {
            NSArray *schemearray = [schemedict objectForKey:@"CFBundleURLSchemes"];
            if ([schemearray isKindOfClass:[NSArray class]] && schemearray.count > 0) {
                scheme = [schemearray objectAtIndex:0];
                break;
            }
        }
    }
    return scheme;
}

@end
