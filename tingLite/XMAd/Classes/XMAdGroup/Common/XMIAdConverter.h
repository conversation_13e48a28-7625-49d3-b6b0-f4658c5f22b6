//
//  XMIAdConverter.h
//  XMAd
//  各种类型互转工具
//
//  Created by <PERSON><PERSON><PERSON> on 2021/9/2.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class XMIAdRelatedData;

@interface XMIAdConverter : NSObject

/**
 从adtype获取sdktype
 */
+ (NSInteger)sdkTypeFromAdType:(NSInteger)adtype;
/**
 从adtype获取provider
 */
+ (NSString *)providerFromAdType:(NSInteger)adType;
/**
 从adtype获取slotAdType
 */
+ (NSInteger)slotAdTypeFromAdType:(NSInteger)adType;
/**
 showstyle转showtype
 */
+ (int)showTypeFromRelatedData:(XMIAdRelatedData *)relatedData;
/**
 上报token解码
 */
+ (NSString *)decodeAdToken:(NSString *)eToken;

/**
 是否虚拟广告
 */
+ (BOOL)isVirtualAD:(long long)adid;

/**
 上报url中参数填充替换
 */
+ (NSString *)urlStringByReplaceADParams:(NSString *)urlString;
+ (NSString *)urlStringByReplaceADParams:(NSString *)urlString withClickInfo:(nullable NSDictionary *)clickInfo;
+ (NSString *)urlStringByReplaceJtParam:(NSString *)urlString withOtherParma:(NSDictionary *)param;

@end

NS_ASSUME_NONNULL_END
