//
//  XMIBDFindNativeAdView.m
//  XMAd
//
//  Created by xmly on 2025/1/14.
//

#import "XMIBAIDUFindNativeAdView.h"
#import <BaiduMobAdSDK/BaiduMobAdNativeAdView.h>
#import <BaiduMobAdSDK/BaiduMobAdNativeVideoView.h>
#import <BaiduMobAdSDK/BaiduMobAdNative.h>
#import <BaiduMobAdSDK/BaiduMobAdNativeAdObject.h>
#import "XMIAdError.h"
#import "XMIAdNativeLogger.h"
#import "UIView+XMIUtils.h"
#import "XMIAdMacro.h"

@interface XMIBAIDUFindNativeAdView () <BaiduMobAdNativeInterationDelegate, BaiduMobAdNativeVideoViewDelegate>

@property (nonatomic, strong) BaiduMobAdNativeAdView *relatedView;
@property (nonatomic, strong) BaiduMobAdNativeAdObject *adData;
@property (nonatomic, strong) BaiduMobAdNativeVideoView *videoView;
@property (nonatomic, strong) UIView *logoView;
@property (nonatomic, weak) UIViewController *rootViewController;

@end

@implementation XMIBAIDUFindNativeAdView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        
    }
    NSLog(@"BAIDU VIDEO initview-%p",self);
    return self;
}

- (BaiduMobAdNativeAdView *)relatedView {
    if (!_relatedView) {
        _relatedView = [[BaiduMobAdNativeAdView alloc] init];
    }
    return _relatedView;
}

#pragma mark - AdView Delegate

- (void)updateRootViewController:(UIViewController *)rootViewController {
    self.rootViewController = rootViewController;
    [self.adData setPresentAdViewController:rootViewController];
}

- (void)customRenderWithAdData:(id)adData {
    if (!adData || ![adData isKindOfClass:[BaiduMobAdNativeAdObject class]]) {
        [self failRenderWithError:[XMIAdError emptyDataError]];
        return;
    }
    
    if (self.adData == adData) {
        return;
    }
    
    self.adData = adData;
    self.adData.interationDelegate = self;
    [self doCustomRender];
}

- (void)doCustomRender {
    if (self.videoView) {
        [self.videoView removeFromSuperview];
    }
    if (self.logoView) {
        [self.logoView removeFromSuperview];
    }
    
    [self addThirdLogo];
    [self updateThirdLogo];
    [self.adMarkButtonView updateFeedAdType:XMIAdMarkAdTypeBAIDU];
    // 处理视频广告
    if (self.adData.materialType == VIDEO) {
        [self loadVideo];
    } else {
        self.videoView = nil;
    }
}

- (void)addThirdLogo {
    [self.logoView removeFromSuperview];
    self.logoView = self.relatedView.adLogoImageView;
    [self.logoView removeConstraints:self.logoView.constraints];
    [self.contentView addSubview:self.logoView];
}

- (void)updateThirdLogo {
    
}

- (void)updateCloseAndMarkUI {
    [super updateCloseAndMarkUI];
    [self updateThirdLogo];
}

- (void)loadVideo {
    [self.videoView removeFromSuperview];
    self.videoView = [[BaiduMobAdNativeVideoView alloc] initWithFrame:self.coverImageView.bounds andObject:self.adData];
    self.videoView.userInteractionEnabled = NO;
    [self.videoView setVideoMute:YES];
    self.videoView.videoDelegate = self;
    [self.coverImageView insertSubview:self.videoView atIndex:0];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    [self updateThirdLogo];
    if (self.videoView.superview == self.coverImageView) {
        self.videoView.frame = self.coverImageView.bounds;
    } else {
        self.videoView.frame = self.coverImageView.frame;
    }
}

- (void)didExposeAdView
{
    [super didExposeAdView];
    [self.adData trackImpression:self];
}

- (void)clickAdView:(NSDictionary *)userInfo {
    [self.adData handleClick:self.contentView];
    [super clickAdView:userInfo];
}

#pragma mark - BaiduMobAdNativeInterationDelegate

- (void)nativeAdExposure:(UIView *)nativeAdView nativeAdDataObject:(BaiduMobAdNativeAdObject *)object {
    [self didExposeDspView];
}

- (void)nativeAdClicked:(UIView *)nativeAdView nativeAdDataObject:(BaiduMobAdNativeAdObject *)object {    
//    NSLog(@"BAIDU_VIDEO nativeAdDataObject");
}

#pragma mark - BaiduMobAdNativeVideoViewDelegate
- (void)nativeVideoAdDidStartPlaying:(BaiduMobAdNativeVideoView *)videoView {
//    NSLog(@"BAIDU_VIDEO nativeVideoAdDidStartPlaying");
}

- (void)nativeVideoAdDidPause:(BaiduMobAdNativeVideoView *)videoView  {
//    NSLog(@"BAIDU_VIDEO nativeVideoAdDidPause");
}

- (void)nativeVideoAdDidReplay:(BaiduMobAdNativeVideoView *)videoView  {
//    NSLog(@"BAIDU_VIDEO nativeVideoAdDidReplay");
}

- (void)nativeVideoAdDidComplete:(BaiduMobAdNativeVideoView *)videoView  {
//    NSLog(@"BAIDU_VIDEO nativeVideoAdDidComplete");
}

- (void)nativeVideoAdDidFailed:(BaiduMobAdNativeVideoView *)videoView  {
//    NSLog(@"BAIDU_VIDEO nativeVideoAdDidFailed");
}

- (void)nativeVideoAdDidReadyForDisplay:(BaiduMobAdNativeVideoView *)videoView  {
//    NSLog(@"BAIDU_VIDEO nativeVideoAdDidReadyForDisplay");
}

#pragma mark - Public Methods

+ (CGFloat)calAdHeight:(id)adData withAdWidth:(CGFloat)adWidth {
    return adWidth * 9 / 16.0 + kContentViewEdgeTop * 2;
}

- (void)dealloc {
    [self.videoView removeFromSuperview];
    self.videoView = nil;
    self.adData.interationDelegate = nil;
}

- (void)reloadUI {
//    [self doCustomRender];
}

- (BOOL)isVideoAdView {
    return self.videoView != nil;
}

@end
