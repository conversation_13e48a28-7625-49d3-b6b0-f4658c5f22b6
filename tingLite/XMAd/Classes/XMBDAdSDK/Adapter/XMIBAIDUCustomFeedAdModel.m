//
//  XMIBAIDUCustomFeedAdModel.m
//  XMAd
//
//  Created by assistant on 2024/1/9.
//

#import "XMIBAIDUCustomFeedAdModel.h"
#import "XMINativeBaiduLoader.h"
#import "XMIAdDefines.h"
#import "XMIAdSlot.h"
#import "XMIAdMacro.h"
#import "XMIAdHelper.h"
#import "XMIAdNativeLogger.h"
#import <BaiduMobAdSDK/BaiduMobAdNative.h>
#import <BaiduMobAdSDK/BaiduMobAdNativeAdObject.h>
#import <BaiduMobAdSDK/BaiduMobAdNativeVideoView.h>

@interface XMIBAIDUCustomFeedAdModel () <BaiduMobAdNativeAdDelegate, BaiduMobAdNativeInterationDelegate>

@property (nonatomic, strong) BaiduMobAdNative *nativeAd;

@property (nonatomic, strong) BaiduMobAdNativeAdObject *materialMeta;

@end

@implementation XMIBAIDUCustomFeedAdModel

- (void)dealloc {
    _nativeAd.adDelegate = nil;
    _nativeAd = nil;
    _materialMeta = nil;
}

- (BaiduMobAdNative *)nativeAd {
    if (!_nativeAd) {
        _nativeAd = [[BaiduMobAdNative alloc] init];
        _nativeAd.adDelegate = self;
        _nativeAd.publisherId = kXMIBaiduSDKAppId;
        _nativeAd.timeout = 10;
    }
    return _nativeAd;
}

#pragma mark - Public Methods

- (void)loadAdData {
    if (self.loadingStatus != XMIFeedAdModelLoadingStatusInit) {
        return;
    }
    self.loadingStatus = XMIFeedAdModelLoadingStatusLoading;
    [super loadAdData];
    self.nativeAd.adUnitTag = self.relatedData.dspPositionId;
    // 请求广告
    [self.nativeAd requestNativeAds];
}

#pragma mark - BaiduMobAdNativeAdDelegate

- (void)nativeAdObjectsSuccessLoad:(NSArray *)nativeAds nativeAd:(BaiduMobAdNative *)nativeAd {
    // 获取广告数据
    BaiduMobAdNativeAdObject *adObject = nativeAds.firstObject;
    self.materialMeta = adObject;
    self.materialMeta.interationDelegate = self;
    
    // 处理RTB价格
    if (self.relatedData.isMobileRtb) {
        float originPrice = [[adObject getECPMLevel] floatValue];
        self.relatedData.price = originPrice * 1.0 / 100;
        XMILogNativeInfo(@"AD_LOG_NATIVE_RTB",@"BAIDU 请求dsp原生广告成功 🎉🎉🎉 【%lld(%ld, %@)】price = %.6f 元 sdk返回原始价格 %f 分", self.relatedData.adid, self.relatedData.adtype, self.relatedData.dspPositionId, self.relatedData.price, originPrice);
    }
    [self loadSuccess];
}

- (void)nativeAdsFailLoadCode:(NSString *)errCode message:(NSString *)message nativeAd:(BaiduMobAdNative *)nativeAd adObject:(BaiduMobAdNativeAdObject *)adObject {
    self.nativeAd = nil;
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    [dict setValue:message forKey:@"msg"];
    NSError *error = [NSError errorWithDomain:@"" code:[errCode integerValue] userInfo:dict];
    [self loadFailed:error];
    [XMIAdHelper apmLogMessage:[NSString stringWithFormat:@"baidu error: position=%lld, code=%@,message =%@", self.relatedData.positionId, errCode, message]];
}

#pragma mark - Parse Methods

- (id)adData
{
    return self.materialMeta;
}

- (NSString *)adTitle
{
    return self.materialMeta.title;
}

- (NSString *)adDescription
{
    return self.materialMeta.text;
}

- (NSString *)adButtonText
{
    return self.materialMeta.actButtonString;
}

- (NSArray<NSString *> *)adImageURLs
{
    if (self.materialMeta.mainImageURLString) {
        return @[self.materialMeta.mainImageURLString];
    }
    return @[];
}

- (NSString *)adVideoURL
{
    return nil;
}

- (NSString *)iconUrl
{
    return self.materialMeta.iconImageURLString;
}

@end 
