//
//  XMIBAIDUFeedAdViewFactory.m
//  XMAd
//
//  Created by assistant on 2024/3/19.
//

#import "XMIBAIDUFeedAdViewFactory.h"
#import "XMIBAIDUFindNativeAdView.h"
#import "XMIBAIDUFindFeedNativeAdSocialView.h"
@implementation XMIBAIDUFeedAdViewFactory

+ (Class)feedAdViewClasslWithShowStyle:(XMIAdShowStyle)showstyle adType:(XMIAdType)adType subShowStyle:(XMIAdShowSubStyle)subShowStyle
{
    if (adType != XMIAdTypeBAIDU) {
        return nil;
    }
    switch (showstyle) {
        case XMIAdStyleHomeLargeImage:
            if (subShowStyle == XMIAdSubShowSubStyleSocial) {
                return [XMIBAIDUFindFeedNativeAdSocialView class];
            }
            return [XMIBAIDUFindNativeAdView class];
            break;
            
        default:
            break;
    }
    return nil;
}

@end 
