//
//  XMIBDFeedAdModelFactory.m
//  XMAd
//
//  Created by assistant on 2024/1/9.
//

#import "XMIBAIDUFeedAdModelFactory.h"
#import "XMIBAIDUCustomFeedAdModel.h"

@implementation XMIBAIDUFeedAdModelFactory

+ (XMIFeedAdModel *)adModelWithRelatedData:(XMIAdRelatedData *)relatedData
{
    // 目前仅接入百度自渲染
    XMIBAIDUCustomFeedAdModel *model = [[XMIBAIDUCustomFeedAdModel alloc] init];
    model.relatedData = relatedData;
    return model;
}

@end 
