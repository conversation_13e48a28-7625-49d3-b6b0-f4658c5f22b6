//
//  XMIAdMarkView.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/24.
//

#define kXMIAdMarkDefaultLimitHeight 12.0f

#import "XMIAdMarkView.h"
#import <XMWebImage/UIImageView+WebCache.h>

@implementation XMIAdMarkView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.limitedHeight = kXMIAdMarkDefaultLimitHeight;
    }
    return self;
}

- (void)sd_setImageWithURL:(NSURL *)url placeholderImage:(UIImage *)placeholder completed:(SDExternalCompletionBlock)completedBlock
{
    [super sd_setImageWithURL:url placeholderImage:placeholder completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL){
        if (!image || error) {
            self.image = placeholder;
        }
        if (self.limitedHeight > 0 && self.image
            && self.image.size.height > 0) {
            self.bounds = CGRectMake(0, 0, self.limitedHeight * self.image.size.width/self.image.size.height, self.limitedHeight);
        } else if (self.limitedHeight < 0 && self.image
                   && self.image.size.height > 0 && self.image.scale < 2) {
            CGFloat scale = 3.0f;
            if (scale > 0) {
                self.bounds = CGRectMake(0, 0, self.image.size.width * self.image.scale / scale, self.image.size.height * self.image.scale / scale);
            }
        } else {
            self.bounds = CGRectMake(0, 0, self.image.size.width, self.image.size.height);
        }
        if (completedBlock) {
            completedBlock(image, error, cacheType, imageURL);
        }
    }];
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
