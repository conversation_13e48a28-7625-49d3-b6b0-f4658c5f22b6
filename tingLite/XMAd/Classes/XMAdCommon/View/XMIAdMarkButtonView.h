//
//  XMIAdMarkButtonView.h
//  XMAd
//
//  Created by xiaodong2.zhang on 2023/11/13.
//

#import <UIKit/UIKit.h>
#import "XMIAdButton.h"

typedef NS_ENUM(NSInteger, XMIAdMarkAdType)
{
    XMIAdMarkAdTypeXimalaya = 0,
    XMIAdMarkAdTypeGDT = 4, // 广点通
    XMIAdMarkAdTypeBU = 10014, // 穿山甲
    XMIAdMarkAdTypeBAIDU = 10151, // 百度广告
    XMIAdMarkAdTypeJAD = 10074, // 京东广告
};

typedef NS_ENUM(NSInteger, XMIAdMarkUIMode)
{
    XMIAdMarkUIModeLight = 1,
    XMIAdMarkUIModeDark = 2,
    XMIAdMarkUIModeLightClear = 3,
    XMIAdMarkUIModeSocialHomePage = 4,
    XMIAdMarkUIModeSocialHomePageGuessYouLike = 5,
    XMIAdMarkUIModeWhiteHomePage = 6
};

typedef NS_ENUM(NSInteger, XMIAdMarkType)
{
    XMIAdMarkTypeNone = 0,
    XMIAdMarkTypeArrow = 1,
    XMIAdMarkTypeClose = 2,
};

NS_ASSUME_NONNULL_BEGIN

#define XMI_ADMARKBUTTONVIEW_WIDTH 38.f
#define XMI_ADMARKBUTTONVIEW_CLOSEGAP 4.f
#define XMI_ADMARKBUTTONVIEW_CLOSE_WIDTH XMI_ADMARKBUTTONVIEW_HEIGHT
#define XMI_ADMARKBUTTONVIEW_DSP_WIDTH 52.f
#define XMI_ADMARKBUTTONVIEW_HEIGHT 18.f
#define XMI_ADMARKBUTTONVIEW_HEIGHTGUESS 16.f

@interface XMIAdMarkButtonView : XMIAdButton

- (instancetype)initWithType:(XMIAdMarkType)type;

@property (nonatomic, assign) XMIAdMarkUIMode adMarkUIMode;
@property (nonatomic, strong) UIImageView *adMarkView;

// 点击事件
@property (nonatomic, copy) void(^clickAction)(void);

// 重新布局之后的事件
@property (nonatomic, copy) void(^sizeDidChangeAction)(void);

- (void)updateAdMarkURL:(NSURL *)adMarkURL adMarkDarkURL:(NSURL *)adMarkDarkURL placeholder:(UIImage *)placeholder;

- (void)updateAdMarkImage:(UIImage *)adMarkImage size:(CGSize)size;

- (void)updateAdMarkText:(NSString *)text;

- (void)updateFeedAdType:(XMIAdMarkAdType)feedAdType;

- (void)updateAdMarkType:(XMIAdMarkType)adMarkType;

@end

NS_ASSUME_NONNULL_END

