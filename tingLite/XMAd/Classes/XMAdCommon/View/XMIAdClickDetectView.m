//
//  XMIAdClickDetectView.m
//  XMAd
//
//  Created by cuiyuanz<PERSON> on 2022/9/13.
//

#import "XMIAdClickDetectView.h"

@implementation XMIAdClickDetectView

- (BOOL)pointInside:(CGPoint)point withEvent:(UIEvent *)event
{
    if (self.pointInsideAction) {
        self.pointInsideAction(self, point);
    }
    return [super pointInside:point withEvent:event];
}
/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
