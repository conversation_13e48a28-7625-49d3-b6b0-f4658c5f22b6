//
//  XMIAdAlertView.m
//  XMAd
//
//  Created by xmly on 2022/6/28.
//

#import "XMIAdAlertView.h"
#import "XMIAdMacro.h"
#import <Masonry/Masonry.h>
@interface XMIAdAlertView()
@property (nonatomic, strong) UIView *bgView;
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UIView *lineView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIButton *centerButton;
@property (nonatomic, strong) UIButton *cancelButton;
@property (nonatomic, strong) UIButton *certainButton;

@property (nonatomic, copy) void (^certainBlock)(void);
@end

@implementation XMIAdAlertView

#define kIAdAlertContentWidth 280

#define kIAdAlertContentLeftMargin 15

#define kIAdAlertTitleTopMargin 24
#define kIAdAlertTitleBottomMargin 24

#define kIAdAlertCenterButtonWidth 200
#define kIAdAlertCenterButtonHeight 40

#define kIAdAlertButtonHeight 45
#define kIAdAlertButtonBottomMargin 30


+ (instancetype)alertWithType:(XMIAdAlertViewType)type
                 contentWidth:(CGFloat)contentWidth
                  contentInfo:(NSString *)contentInfo
            cancelButtonTitle:(NSString *)cancelButtonTitle
           certainButtonTitle:(NSString *)certainButtonTitle
                 certainBlock:(void (^)(void))certainBlock {
    
    return [[XMIAdAlertView alloc] initWithType:type contentWidth:contentWidth contentInfo:contentInfo cancelButtonTitle:cancelButtonTitle certainButtonTitle:certainButtonTitle certainBlock:certainBlock];
}

- (instancetype)initWithType:(XMIAdAlertViewType)type
                 contentWidth:(CGFloat)contentWidth
                  contentInfo:(NSString *)contentInfo
            cancelButtonTitle:(NSString *)cancelButtonTitle
           certainButtonTitle:(NSString *)certainButtonTitle
                 certainBlock:(void (^)(void))certainBlock {
    self = [super initWithFrame:UIScreen.mainScreen.bounds];
    if (self) {
        self.certainBlock = certainBlock;
        if (contentWidth <= 0) {
            contentWidth = kIAdAlertContentWidth;
        }
        if (!certainButtonTitle.length) {
            certainButtonTitle = @"确认";
        }
        
        [self addSubview:self.bgView];
        [self.bgView addSubview:self.contentView];
        [self.contentView addSubview:self.titleLabel];
        [self.contentView addSubview:self.centerButton];
        [self.contentView addSubview:self.lineView];
        [self.contentView addSubview:self.cancelButton];
        [self.contentView addSubview:self.certainButton];
        
        
        
        self.titleLabel.text = contentInfo;
        [self.centerButton setTitle:certainButtonTitle forState:UIControlStateNormal];
        [self.cancelButton setTitle:cancelButtonTitle forState:UIControlStateNormal];
        [self.certainButton setTitle:certainButtonTitle forState:UIControlStateNormal];
        
        
        [self.bgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self);
        }];
        
        
        CGFloat titleWidth = contentWidth - kIAdAlertContentLeftMargin * 2;
        UIView *superView = self.contentView;
        [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(superView).mas_offset(kIAdAlertTitleTopMargin);
            make.width.mas_equalTo(titleWidth);
            make.centerX.equalTo(superView);
        }];
        
        if (type == XMIAdAlertViewTypeCertain) {
            self.centerButton.hidden = YES;
            
            [self.certainButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.titleLabel.mas_bottom).mas_offset(kIAdAlertTitleBottomMargin);
                make.height.mas_equalTo(kIAdAlertCenterButtonHeight);
                make.width.mas_equalTo(contentWidth * 0.5);
                make.right.equalTo(superView);
            }];
            
            [self.cancelButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.titleLabel.mas_bottom).mas_offset(kIAdAlertTitleBottomMargin);
                make.height.mas_equalTo(kIAdAlertCenterButtonHeight);
                make.width.mas_equalTo(contentWidth * 0.5);
                make.left.equalTo(superView);
            }];
            
            [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.right.equalTo(superView);
                make.height.mas_equalTo(0.5);
                make.bottom.equalTo(self.cancelButton.mas_top);
            }];
        } else {
            self.certainButton.hidden = self.cancelButton.hidden = self.lineView.hidden = YES;
            [self.centerButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.titleLabel.mas_bottom).mas_offset(kIAdAlertTitleBottomMargin);
                make.height.mas_equalTo(kIAdAlertCenterButtonHeight);
                make.width.mas_equalTo(kIAdAlertCenterButtonWidth);
                make.centerX.equalTo(superView);
            }];
        }
        
        
        [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(self);
            make.width.mas_equalTo(contentWidth);
            if (type == XMIAdAlertViewTypeCertain) {
                make.bottom.equalTo(self.cancelButton.mas_bottom);
            } else {
                make.bottom.equalTo(self.centerButton.mas_bottom).mas_offset(kIAdAlertButtonBottomMargin);
            }
        }];
        
    }
    return self;
}

- (void)show {
    [[UIApplication sharedApplication].keyWindow addSubview:self];
    
    self.contentView.transform = CGAffineTransformMakeScale(0, 0);
    
    [UIView animateWithDuration:0.25 animations:^{
        self.contentView.transform = CGAffineTransformIdentity;
    } completion:^(BOOL finished) {
        
    }];
    
}

- (UIView *)bgView {
    if (!_bgView) {
        _bgView = [[UIView alloc] init];
        _bgView.backgroundColor = XMI_COLOR_RGBA(0x000000, 0.5);
    }
    return _bgView;
}

- (UIView *)contentView {
    if (!_contentView) {
        _contentView = [[UIView alloc] init];
        _contentView.layer.cornerRadius = 10;
        _contentView.layer.masksToBounds = YES;
        _contentView.backgroundColor = XMI_COLOR_DynamicFromRGB(0xffffff, 0x121212);
    }
    return _contentView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.numberOfLines = 0;
        _titleLabel.font = XMI_AD_PingFangFont(16);
        _titleLabel.textColor = XMI_COLOR_DynamicFromRGB(0x333333,0xcfcfcf);
        _titleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _titleLabel;
}

- (UIView *)lineView {
    if (!_lineView) {
        _lineView = [[UIView alloc] init];
        _lineView.backgroundColor = XMI_COLOR_RGB(0xE8E8E8);
    }
    return _lineView;
}

- (UIButton *)centerButton {
    if (!_centerButton) {
        _centerButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _centerButton.layer.cornerRadius = kIAdAlertCenterButtonHeight * 0.5;
        _centerButton.titleLabel.font = XMI_AD_PingFangFont(16);
        [_centerButton setTitleColor:XMI_COLOR_RGB(0xFFFFFF) forState:UIControlStateNormal];
        [_centerButton setBackgroundColor:XMI_COLOR_RGB(0xFF6D4C)];
        [_centerButton addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _centerButton;
}

- (UIButton *)certainButton {
    if (!_certainButton) {
        _certainButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _certainButton.titleLabel.font = XMI_AD_PingFangFont(16);
        [_certainButton setTitleColor:XMI_COLOR_RGB(0xFFFFFF) forState:UIControlStateNormal];
        [_certainButton setBackgroundColor:XMI_COLOR_RGB(0xFF6D4C)];
        [_certainButton addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _certainButton;
}

- (UIButton *)cancelButton {
    if (!_cancelButton) {
        _cancelButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _cancelButton.titleLabel.font = XMI_AD_PingFangFont(16);
        [_cancelButton setTitleColor:XMI_COLOR_DynamicFromRGB(0x666666,0x888888) forState:UIControlStateNormal];
        [_cancelButton addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _cancelButton;
}

- (void)btnClick:(UIButton *)sender {
    if (sender == self.centerButton) {
        
    } else if (sender == self.cancelButton) {
        if (self.cancelBlock) {
            self.cancelBlock();
        }
    } else if (sender == self.certainButton) {
        if(self.certainBlock) {
            self.certainBlock();
        }
    }
    [self removeFromSuperview];
}

- (void)dismiss
{
    [self removeFromSuperview];
}

@end
