//
//  XMIAdSourceLabel.h
//  XMAd
//
//  Created by cu<PERSON>yuanz<PERSON> on 2022/2/24.
//

#import <UIKit/UIKit.h>
#import <XMAd/XMIAdDefines.h>

NS_ASSUME_NONNULL_BEGIN

@class XMIAdRelatedData;

@interface XMIAdSourceLabel : UIView

@property (copy, nonatomic) NSString *title;

@property (copy, nonatomic)  NSURL * _Nullable logoUrl;

@property (assign, nonatomic) UIEdgeInsets edgeInset;

- (void)updateWithInScreenSource:(XMIInScreenSource)inScreenSource materialProvideSource:(NSString *)materialProvideSource;

+ (instancetype)labelWithNormalStyle;

+ (instancetype)labelWithSoundPatchStyle;

+ (instancetype)labelWithSmallStyle;

@end

NS_ASSUME_NONNULL_END
