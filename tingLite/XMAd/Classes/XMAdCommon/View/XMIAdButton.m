//
//  XMIAdButton.m
//  Alamofire
//
//  Created by <PERSON><PERSON>yuanz<PERSON> on 2022/2/16.
//

#import "XMIAdButton.h"

@implementation XMIAdButton

#if DEBUG
{
    CALayer *_outsideLayer;
}

- (void)setHitTestEdgeOutsets:(UIEdgeInsets)hitTestEdgeOutsets
{
    _hitTestEdgeOutsets = hitTestEdgeOutsets;
    if (!_outsideLayer) {
        _outsideLayer = [CAShapeLayer layer];
        [self.layer addSublayer:_outsideLayer];
        _outsideLayer.zPosition = 10000;
        _outsideLayer.backgroundColor = [[UIColor blueColor] colorWithAlphaComponent:0.2f].CGColor;
    }
    _outsideLayer.frame = CGRectMake(-hitTestEdgeOutsets.left, -hitTestEdgeOutsets.top, self.bounds.size.width + hitTestEdgeOutsets.left + hitTestEdgeOutsets.right, self.bounds.size.height + hitTestEdgeOutsets.top + hitTestEdgeOutsets.bottom);
    self.superview.layer.masksToBounds = YES;
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    if (_outsideLayer) {
        _outsideLayer.frame = CGRectMake(-self.hitTestEdgeOutsets.left, -self.hitTestEdgeOutsets.top, self.bounds.size.width + self.hitTestEdgeOutsets.left + self.hitTestEdgeOutsets.right, self.bounds.size.height + self.hitTestEdgeOutsets.top + self.hitTestEdgeOutsets.bottom);
    }
}

#endif

- (BOOL)pointInside:(CGPoint)point withEvent:(UIEvent *)event{
    CGRect rectBig = self.bounds;
    rectBig.size.width += self.hitTestEdgeOutsets.left + self.hitTestEdgeOutsets.right;
    rectBig.size.height += self.hitTestEdgeOutsets.top + self.hitTestEdgeOutsets.bottom;
    rectBig.origin.x = -self.hitTestEdgeOutsets.left;
    rectBig.origin.y = -self.hitTestEdgeOutsets.top;
    return CGRectContainsPoint(rectBig, point);
}

+ (UIEdgeInsets)closeAreaPaddingWithDefaultPadding:(UIEdgeInsets)defaultPadding extraPadding:(UIEdgeInsets)extraPadding
{
    return UIEdgeInsetsMake(extraPadding.top + defaultPadding.top, extraPadding.left + defaultPadding.left, extraPadding.bottom + defaultPadding.bottom, extraPadding.right + defaultPadding.right);
}


@end
