//
//  XMIAdSourceLabel.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/24.
//

#import "XMIAdSourceLabel.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import <XMWebImage/UIView+WebCache.h>
#import "XMIAdMacro.h"
#import <XMCategories/XMMacro.h>
#import "XMIAdRelatedData.h"

@interface XMIAdSourceLabel()

{
    BOOL _updateUI;
}

@property (nonatomic, copy) NSString *defaultTitle;

@property (nonatomic, strong) UIImageView *logoImageView;

@property (nonatomic, strong) UILabel *titleLabel;

@end

@implementation XMIAdSourceLabel

- (void)updateUIIfNeeded {
    _updateUI = YES;
    [self setNeedsLayout];
}

- (void)setEdgeInset:(UIEdgeInsets)edgeInset {
    _edgeInset = edgeInset;
    [self updateUIIfNeeded];
}

- (void)setTitle:(NSString *)title {
    _title = title;
    [self updateUIIfNeeded];
}

- (void)setLogoUrl:(NSURL *)logoUrl {
    _logoUrl = logoUrl;
    [self updateUIIfNeeded];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    if (!_updateUI) {
        return;
    }
    
    _updateUI = NO;
    [self updateUIAndLightUpdate:NO];
}

- (UIImageView *)logoImageView
{
    if (!_logoImageView) {
        _logoImageView = [[UIImageView alloc] init];
        _logoImageView.alpha = 0.4f;
        [self addSubview:_logoImageView];
    }
    return _logoImageView;
}

- (UILabel *)titleLabel
{
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = [UIFont systemFontOfSize:8];
        _titleLabel.textColor = colorFromRGBA(0xffffff, 0.4);
        [self addSubview:_titleLabel];
    }
    return _titleLabel;
}

- (void)updateUIAndLightUpdate:(BOOL)lightUpdate {
    NSInteger titleLeft = 3;
    NSInteger logoHeight = 8;
    
    // logo
    @weakify(self);
    __block UIImage *img = nil;
    [self.logoImageView sd_setImageWithURL:self.logoUrl placeholderImage:nil completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
        @strongify(self);
        img = image;
        if (!lightUpdate) {
            [self updateUIAndLightUpdate:YES];
        }
    }];
   
    CGFloat logoWidth = 0;
    if (img
        && img.size.width > 1
        && img.size.height > 1) {
        logoWidth = img.size.width*8.0/img.size.height;
    }
    self.logoImageView.frame = CGRectMake(self.edgeInset.left, self.edgeInset.top, logoWidth, logoHeight);
    
    // title
    self.titleLabel.text = self.title;
    [self.titleLabel sizeToFit];
    if (self.logoImageView.bounds.size.width > 0.1) {
        self.titleLabel.frame = CGRectMake(CGRectGetMaxX(self.logoImageView.frame) + titleLeft, self.logoImageView.center.y - self.titleLabel.bounds.size.height * 0.5f, self.titleLabel.bounds.size.width, self.titleLabel.bounds.size.height);
    }
    else {
        self.titleLabel.frame = CGRectMake(self.edgeInset.left, self.edgeInset.top, self.titleLabel.bounds.size.width, self.titleLabel.bounds.size.height);
    }
    if(self.title.length <= 0) {
        self.titleLabel.frame = CGRectMake(self.titleLabel.frame.origin.x, self.titleLabel.frame.origin.y, CGRectGetMaxX(self.logoImageView.frame) - self.titleLabel.frame.origin.x, self.titleLabel.frame.size.height);
    }
    self.frame = CGRectMake(self.frame.origin.x, self.frame.origin.y, CGRectGetMaxX(self.titleLabel.frame) + self.edgeInset.right, MAX(CGRectGetMaxY(self.logoImageView.frame), CGRectGetMaxY(self.titleLabel.frame)) + self.edgeInset.bottom);
}

- (void)updateWithInScreenSource:(XMIInScreenSource)inScreenSource materialProvideSource:(NSString *)materialProvideSource {
    if (inScreenSource != 0) {
        self.hidden = NO;
        if (inScreenSource == XMIInScreenSourceLogo
            && (self.defaultTitle.length || materialProvideSource.length)) {
            self.title = self.defaultTitle;
            self.logoUrl = [NSURL URLWithString:materialProvideSource];
        }
        else if (inScreenSource == XMIInScreenSourceText
                 && materialProvideSource.length) {
            self.title = materialProvideSource;
            self.logoUrl = nil;
        }
        else {
            self.hidden = YES;
        }
    }
    else {
        self.hidden = YES;
    }
}

+ (instancetype)labelWithNormalStyle {
    XMIAdSourceLabel *l = [[XMIAdSourceLabel alloc]init];
    l.edgeInset = UIEdgeInsetsMake(2, 2, 2, 2);
    l.backgroundColor = colorFromRGBA(0x0, 0.4f);
    l.defaultTitle = @"广告";
    CGRect rect = l.frame;
    rect.size.height = 12;
    l.frame = rect;
    return l;
}

+ (instancetype)labelWithSoundPatchStyle {
    XMIAdSourceLabel *l = [[XMIAdSourceLabel alloc]init];
    l.edgeInset = UIEdgeInsetsMake(2, 3, 2, 3);
//    l.layer.skCornerLayer.keepOnBackground = YES;
//    l.layer.skCornerLayer.displayCornerAsynchronously = YES;
//    l.layer.skCorner
//    .sCornerBackgroundColor([UIColor clearColor])
//    .sCornerRadiusSpacific(XMMakeCornerRadius(0, 0, 0, 6))
//    .sContentColor(colorFromRGBA(0x0, 0.05));
    l.backgroundColor = colorFromRGBA(0x0, 0.15f);
    CGRect rect = l.frame;
    rect.size.height = 12;
    l.frame = rect;
    return l;
}

+ (instancetype)labelWithSmallStyle {
    XMIAdSourceLabel *l = [[XMIAdSourceLabel alloc]init];
    l.edgeInset = UIEdgeInsetsMake(1, 2, 1, 2);
//    l.layer.skCornerLayer.keepOnBackground = YES;
//    l.layer.skCornerLayer.displayCornerAsynchronously = YES;
//    l.layer.skCorner
//    .sCornerBackgroundColor([UIColor clearColor])
//    .sCornerRadiusSpacific(XMMakeCornerRadius(0, 0, 0, 6))
//    .sContentColor(colorFromRGBA(0x0, 0.05));
    l.backgroundColor = colorFromRGBA(0x0, 0.15f);
    CGRect rect = l.frame;
    rect.size.height = 8;
    l.frame = rect;
    l.titleLabel.font = [UIFont systemFontOfSize:6];
    return l;
}


@end
