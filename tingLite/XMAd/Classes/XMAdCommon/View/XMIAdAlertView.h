//
//  XMIAdAlertView.h
//  XMAd
//
//  Created by xmly on 2022/6/28.
//

#import <UIKit/UIKit.h>

typedef NS_ENUM(NSInteger, XMIAdAlertViewType) {
    XMIAdAlertViewTypeDefault = 0, // 默认显示文本
    XMIAdAlertViewTypeCertain = 1    // 显示两个按钮，一个取消，一个确认
};

NS_ASSUME_NONNULL_BEGIN

@interface XMIAdAlertView : UIView
+ (instancetype)alertWithType:(XMIAdAlertViewType)type
                 contentWidth:(CGFloat)contentWidth
                  contentInfo:(NSString *)contentInfo
            cancelButtonTitle:(NSString *)cancelButtonTitle
           certainButtonTitle:(NSString *)certainButtonTitle
                 certainBlock:(void (^)(void))certainBlock;
- (void)show;

- (void)dismiss;

@property (nonatomic, copy) void (^cancelBlock)(void);

@end

NS_ASSUME_NONNULL_END
