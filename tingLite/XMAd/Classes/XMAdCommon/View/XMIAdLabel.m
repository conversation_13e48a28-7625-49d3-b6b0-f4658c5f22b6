//
//  XMIAdLabel.m
//  XMAd
//
//  Created by xmly on 2022/11/16.
//

#import "XMIAdLabel.h"

@implementation XMIAdLabel

- (void)setTextEdgeInsets:(UIEdgeInsets)textEdgeInsets {
    _textEdgeInsets = textEdgeInsets;
    [self invalidateIntrinsicContentSize];
}

- (CGRect)textRectForBounds:(CGRect)bounds limitedToNumberOfLines:(NSInteger)numberOfLines {
    CGRect insetRect = UIEdgeInsetsInsetRect(bounds, self.textEdgeInsets);
    CGRect textRect = [super textRectForBounds:insetRect limitedToNumberOfLines:numberOfLines];
    UIEdgeInsets invertedInsets = UIEdgeInsetsMake(-self.textEdgeInsets.top, -self.textEdgeInsets.left, -self.textEdgeInsets.bottom, -self.textEdgeInsets.right);
    return UIEdgeInsetsInsetRect(textRect, invertedInsets);
}

- (void)drawRect:(CGRect)rect {
    [super drawRect:UIEdgeInsetsInsetRect(rect, self.textEdgeInsets)];
}

@end
