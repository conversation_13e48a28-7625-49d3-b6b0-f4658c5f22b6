//
//  XMIAdMarkButtonView.m
//  XMAd
//
//  Created by xiaodong2.zhang on 2023/11/13.
//

#import "XMIAdMarkButtonView.h"
#import <XMCategories/XMMacro.h>
#import <XMWebImage/UIImageView+WebCache.h>
#import "XMIAdMacro.h"
#import "XMICommonUtils.h"
#import "XMIAdManager.h"
    
#define kSocialHomePageCornerRadius [XMIAdManager findNativeSocialShowStyle] ? 2 : 4

@interface XMIAdMarkButtonView ()

@property (nonatomic, assign) XMIAdMarkType adMarkType;
@property (nonatomic, strong) UIImageView *thirdLogoView;
@property (nonatomic, strong) UILabel *adMarkLabel;
@property (nonatomic, strong) UIImageView *arrowView;
@property (nonatomic, strong) NSURL *adMarkURL;
@property (nonatomic, strong) NSURL *adMarkDarkURL;

@end

@implementation XMIAdMarkButtonView

- (instancetype)initWithType:(XMIAdMarkType)type {
    self = [super initWithFrame:CGRectMake(0, 0, XMIAdPic(XMI_ADMARKBUTTONVIEW_WIDTH), XMIAdPic(XMI_ADMARKBUTTONVIEW_HEIGHT))];
    if (self) {
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleTapGesture:)];
        [self addGestureRecognizer:tap];
        self.adMarkType = type;
        [self configViews];
    }
    return self;
}

- (void)configViews {
    self.layer.cornerRadius = 2;
    self.thirdLogoView.hidden = YES;
}

- (void)setAdMarkType:(XMIAdMarkType)adMarkType {
    if (_adMarkType != adMarkType) {
        _adMarkType = adMarkType;
        [self updateAdMarkType:adMarkType];
    }
}

- (void)updateAdMarkType:(XMIAdMarkType)adMarkType {
    switch (adMarkType) {
        case XMIAdMarkTypeNone:
            self.arrowView.hidden = YES;
            break;
        case XMIAdMarkTypeArrow:
            self.arrowView.hidden = NO;
            self.arrowView.image = [[XMICommonUtils imageNamed:@"admark_arrow"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
            self.arrowView.size = CGSizeMake(XMIAdPic(10), XMIAdPic(10));
            self.arrowView.centerY = self.height / 2;
            self.arrowView.right = self.width - xmUIHSpace(2);
            break;
        case XMIAdMarkTypeClose:
            self.arrowView.hidden = NO;
            self.arrowView.image = [[XMICommonUtils imageNamed:@"admark_close"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
            self.arrowView.size = CGSizeMake(XMIAdPic(12), XMIAdPic(12));
            self.arrowView.centerY = self.height / 2;
            self.arrowView.right = self.width - xmUIHSpace(2);
            break;
        default:
            break;
    }
}

- (void)setAdMarkUIMode:(XMIAdMarkUIMode)adMarkUIMode {
    if (_adMarkUIMode != adMarkUIMode) {
        _adMarkUIMode = adMarkUIMode;
        [self updateAdMarkUIMode:adMarkUIMode];
    }
}

- (void)updateAdMarkUIMode:(XMIAdMarkUIMode)adMarkUIMode {
    switch (adMarkUIMode) {
        case XMIAdMarkUIModeLight:
            self.backgroundColor = xmUICBColorA(grayBackgroundColor, 1, white, 0.06);
            self.arrowView.tintColor = xmUICBColorA(ThinTextColor,1,titleColor,0.3);
            self.adMarkLabel.textColor = xmUICBColorA(ThinTextColor,1,titleColor,0.3);
            self.thirdLogoView.tintColor = xmUICBColorA(ThinTextColor,1,titleColor,0.3);
            break;
        case XMIAdMarkUIModeLightClear:
            self.backgroundColor = [UIColor clearColor];
            self.arrowView.tintColor = xmUICBColorA(ThinTextColor,1,titleColor,0.3);
            self.adMarkLabel.textColor = xmUICBColorA(ThinTextColor,1,titleColor,0.3);
            self.thirdLogoView.tintColor = xmUICBColorA(ThinTextColor,1,titleColor,0.3);
            break;
        case XMIAdMarkUIModeDark:
            self.backgroundColor = xmNUIColorA(titleColor, 0.5);
            self.arrowView.tintColor = xmUICBColorA(white,0.7, white,0.7);
            self.adMarkLabel.textColor = xmUICBColorA(white,0.7, white,0.7);
            self.thirdLogoView.tintColor = xmUICBColorA(white,0.7, white,0.7);
            break;
        case XMIAdMarkUIModeSocialHomePage:
            self.backgroundColor = XMI_COLOR_RGBA(0x000000, 0.35f);
            self.arrowView.tintColor = xmUICBColorA(white,0.8, white,0.8);
            self.adMarkLabel.textColor = xmUICBColorA(white,0.8, white,0.8);
            self.thirdLogoView.tintColor = xmUICBColorA(white,0.8, white,0.8);
            break;
        case XMIAdMarkUIModeSocialHomePageGuessYouLike:
            self.backgroundColor = XMI_COLOR_DynamicFromRGBA(0x8D8D91, 0.08f, 0x8D8D91, 0.1f);
            self.arrowView.tintColor = XMI_COLOR_DynamicFromRGBA(0x8D8D91, 0.8f, 0x8D8D91, 0.6f);
            self.adMarkLabel.textColor = XMI_COLOR_DynamicFromRGBA(0x8D8D91, 0.8f, 0x8D8D91, 0.6f);
            self.thirdLogoView.tintColor = XMI_COLOR_DynamicFromRGBA(0x8D8D91, 0.8f, 0x8D8D91, 0.6f);
            self.adMarkView.tintColor = XMI_COLOR_DynamicFromRGBA(0x8D8D91, 0.8f, 0x8D8D91, 0.6f);
            break;
        case XMIAdMarkUIModeWhiteHomePage:
            self.backgroundColor = XMI_COLOR_DynamicFromRGBA(0xFFFFFF, 1, 0x212121, 1);
            self.arrowView.tintColor = XMI_COLOR_DynamicFromRGBA(0x2C2C3C, 0.3f, 0x8D8D91, 1);
            self.adMarkLabel.textColor = XMI_COLOR_DynamicFromRGBA(0x2C2C3C, 0.3f, 0x8D8D91, 1);
            self.thirdLogoView.tintColor = XMI_COLOR_DynamicFromRGBA(0x2C2C3C , 0.3f, 0x8D8D91, 1);
            self.adMarkView.tintColor = XMI_COLOR_DynamicFromRGBA(0x2C2C3C, 0.3f, 0x8D8D91, 1);
            break;
        default:
            break;
    }
}

#pragma - mark lazy load

- (UIImageView *)adMarkView {
    if (!_adMarkView) {
        _adMarkView = [[UIImageView alloc] init];
        _adMarkView.contentMode = UIViewContentModeScaleAspectFit;
        [self addSubview:_adMarkView];
    }
    return _adMarkView;
}

- (UIImageView *)thirdLogoView {
    if (!_thirdLogoView) {
        _thirdLogoView = [[UIImageView alloc] init];
        [self addSubview:_thirdLogoView];
    }
    return _thirdLogoView;
}

- (UILabel *)adMarkLabel
{
    if (!_adMarkLabel) {
        _adMarkLabel = [[UILabel alloc] init];
        [self addSubview:_adMarkLabel];
        _adMarkLabel.font = XMI_AD_PingFangMediumFont(XMIAdFont(11));
        _adMarkLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _adMarkLabel;
}


- (UIImageView *)arrowView {
    if (!_arrowView) {
        _arrowView = [[UIImageView alloc] init];
        [self addSubview:_arrowView];
    }
    return _arrowView;
}

#pragma - mark public methods

- (void)updateAdMarkURL:(NSURL *)adMarkURL adMarkDarkURL:(NSURL *)adMarkDarkURL placeholder:(UIImage *)placeholder {
    self.adMarkView.hidden = NO;
    self.adMarkLabel.hidden = YES;
    
    self.adMarkURL = adMarkURL;
    self.adMarkDarkURL = adMarkDarkURL;
    NSURL *url = adMarkURL;
    if (self.adMarkUIMode != XMIAdMarkUIModeSocialHomePageGuessYouLike) {
        if (@available(iOS 13.0, *)) {
            if (adMarkDarkURL && UIUserInterfaceStyleDark == self.traitCollection.userInterfaceStyle) {
                url = adMarkDarkURL;
            }
        }
    }
    @weakify(self)
    [self.adMarkView sd_setImageWithURL:url placeholderImage:placeholder completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
        @strongify(self)
        if (self.adMarkUIMode == XMIAdMarkUIModeSocialHomePageGuessYouLike || self.adMarkUIMode == XMIAdMarkUIModeWhiteHomePage) {
            self.adMarkView.image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        }
    }];
    if (self.adMarkUIMode == XMIAdMarkUIModeSocialHomePageGuessYouLike) {
        self.adMarkView.size = CGSizeMake(XMIAdPic(19), XMIAdPic(16));
    } else {
        self.adMarkView.size = CGSizeMake(XMIAdPic(22), XMIAdPic(XMI_ADMARKBUTTONVIEW_HEIGHT));
    }
   
    self.adMarkView.centerY = self.height / 2;
    self.adMarkView.left = self.thirdLogoView.hidden ? xmUIHSpace(4) : self.thirdLogoView.right + xmUIHSpace(2);
    [self updateLayouts];
}

- (void)updateAdMarkImage:(UIImage *)adMarkImage size:(CGSize)size {
    self.adMarkView.hidden = NO;
    self.adMarkLabel.hidden = YES;
    
    self.adMarkView.image = [adMarkImage imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    self.size = size;
    self.adMarkView.frame = self.bounds;
}

- (void)updateAdMarkText:(NSString *)text {
    self.adMarkView.hidden = YES;
    self.adMarkLabel.hidden = text.length > 0 ? NO : YES;
    self.adMarkLabel.text = text;
    self.adMarkLabel.size = CGSizeMake(XMIAdPic(24), XMIAdPic(XMI_ADMARKBUTTONVIEW_HEIGHT));
    self.adMarkLabel.centerY = self.height / 2;
    [self updateLayouts];
}

- (void)updateFeedAdType:(XMIAdMarkAdType)feedAdType {
    UIImage *image = nil;
    switch (feedAdType) {
        case XMIAdMarkAdTypeGDT:
            image = [XMICommonUtils imageNamed:@"admark_gdt"];
            break;
        case XMIAdMarkAdTypeBU:
            image = [XMICommonUtils imageNamed:@"admark_csj"];
            break;
        case XMIAdMarkAdTypeJAD:
            image = [XMICommonUtils imageNamed:@"admark_jd"];
            break;
        case XMIAdMarkAdTypeBAIDU:
            image = [XMICommonUtils imageNamed:@"admark_bd"];
            break;
        default:
            break;
    }
    if (image) {
        self.thirdLogoView.hidden = NO;
        self.thirdLogoView.image = [image imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        self.thirdLogoView.size = CGSizeMake(XMIAdPic(14), XMIAdPic(10));
        self.thirdLogoView.centerY = self.height / 2;
        self.thirdLogoView.left = xmUIHSpace(2);
        [self updateAdMarkText:@"广告"];
        [self updateLayouts];
    }
}

- (void)updateLayouts {
    CGFloat closeGap = self.adMarkType == XMIAdMarkTypeClose ? XMI_ADMARKBUTTONVIEW_CLOSEGAP : 0;
    if (self.adMarkUIMode == XMIAdMarkUIModeSocialHomePageGuessYouLike) {
        self.height = XMIAdPic(XMI_ADMARKBUTTONVIEW_HEIGHTGUESS);
    } else {
        self.height = XMIAdPic(XMI_ADMARKBUTTONVIEW_HEIGHT);
    }
    if (self.adMarkUIMode == XMIAdMarkUIModeSocialHomePage || self.adMarkUIMode == XMIAdMarkUIModeSocialHomePageGuessYouLike) {
        self.adMarkLabel.font = XMI_AD_PingFangMediumFont(XMIAdFont(10));
    }
    if (!self.thirdLogoView.hidden) {
        self.width = XMIAdPic(XMI_ADMARKBUTTONVIEW_DSP_WIDTH + closeGap);
        switch (self.adMarkUIMode) {
            case XMIAdMarkUIModeSocialHomePage:
                self.layer.cornerRadius = XMIAdPic(kSocialHomePageCornerRadius);
                break;
            default:
                self.layer.cornerRadius = XMIAdPic(2);
                break;
        }
        [self updateAdMarkType:self.adMarkType];
        [self updateAdMarkUIMode:self.adMarkUIMode];
        self.arrowView.right = self.width - xmUIHSpace(2);
    } else if (!self.adMarkLabel.hidden || !self.adMarkView.hidden) {
        self.width = XMIAdPic(XMI_ADMARKBUTTONVIEW_WIDTH + closeGap);
        switch (self.adMarkUIMode) {
            case XMIAdMarkUIModeSocialHomePage:
                self.layer.cornerRadius = XMIAdPic(kSocialHomePageCornerRadius);
                self.width -= XMIAdPic(2);
                break;
            case XMIAdMarkUIModeSocialHomePageGuessYouLike:
                self.layer.cornerRadius = XMIAdPic(2);
                self.width -= XMIAdPic(3);
                break;
            case XMIAdMarkUIModeWhiteHomePage:
                self.layer.cornerRadius = XMIAdPic(kSocialHomePageCornerRadius);
                break;
            default:
                self.layer.cornerRadius = XMIAdPic(2);
                break;
        }
        [self updateAdMarkType:self.adMarkType];
        [self updateAdMarkUIMode:self.adMarkUIMode];
        self.arrowView.right = self.width - xmUIHSpace(2);
    } else {
        self.width = self.height;
        if (self.adMarkUIMode == XMIAdMarkUIModeLight) {
            [self updateAdMarkUIMode:XMIAdMarkUIModeLightClear];
        } else {
            [self updateAdMarkUIMode:self.adMarkUIMode];
            self.layer.cornerRadius = XMIAdPic(self.height * 0.5f);
        }
        if (self.adMarkUIMode == XMIAdMarkUIModeSocialHomePage || self.adMarkUIMode == XMIAdMarkUIModeSocialHomePageGuessYouLike) {
            self.arrowView.right = self.width - xmUIHSpace(4);
        } else {
            [self updateAdMarkType:XMIAdMarkTypeClose];
            self.arrowView.right = self.width - xmUIHSpace(3);
        }
       
    }
    self.adMarkLabel.left = self.thirdLogoView.hidden ? xmUIHSpace(4) : self.thirdLogoView.right + xmUIHSpace(2);
    if (self.sizeDidChangeAction) {
        self.sizeDidChangeAction();
    }
}

- (void)handleTapGesture:(UIGestureRecognizer *)tap {
    if (self.clickAction) {
        self.clickAction();
    }
}

- (void)traitCollectionDidChange:(UITraitCollection *)previousTraitCollection {
    [super traitCollectionDidChange:previousTraitCollection];
    if (@available(iOS 13.0, *)) {
        if ([self.traitCollection hasDifferentColorAppearanceComparedToTraitCollection:previousTraitCollection]) {
            NSURL *url = self.adMarkURL;
            if (self.adMarkUIMode != XMIAdMarkUIModeSocialHomePageGuessYouLike && UIUserInterfaceStyleDark == self.traitCollection.userInterfaceStyle) {
                url = self.adMarkDarkURL;
            }
            @weakify(self)
            [self.adMarkView sd_setImageWithURL:url placeholderImage:self.adMarkView.image completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
                @strongify(self)
                if (self.adMarkUIMode == XMIAdMarkUIModeSocialHomePageGuessYouLike) {
                    self.adMarkView.image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
                }
            }];
        }
    }
}

@end
