//
//  XMIAdMarkViewNew.m
//  XMAd
//
//  Created by xiaodong2.zhang on 2023/11/28.
//

#import "XMIAdMarkViewNew.h"
#import "XMIAdMacro.h"
#import "XMICommonUtils.h"
#import <XMWebImage/UIImageView+WebCache.h>

@interface XMIAdMarkViewNew ()

@property (nonatomic, strong) UIImageView *thirdLogoView;
@property (nonatomic, strong) UILabel *adMarkLabel;

@end

@implementation XMIAdMarkViewNew

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self configViews];
    }
    return self;
}

- (void)setShowStyle:(XMIAdMarkViewNewShowStyle)showStyle
{
    _showStyle = showStyle;
    [self configViews];
}

- (void)configViews {
    switch (self.showStyle) {
        case XMIAdMarkViewNewShowStyleDefault:
        {
            self.layer.cornerRadius = 2;
            self.backgroundColor = xmNUIColorA(titleColor, 0.5);
            self.adMarkLabel.font = XMI_AD_PingFangMediumFont(XMIAdFont(11));
            self.adMarkLabel.textColor = xmUICBColorA(white,0.7, white,0.7);
            self.thirdLogoView.tintColor = xmUICBColorA(white,0.7, white,0.7);
        }
            break;
        case XMIAdMarkViewNewShowStyleWakeEnd:
        {
            self.layer.cornerRadius = 0;
            self.backgroundColor = [UIColor clearColor];
            self.adMarkLabel.font = XMI_AD_PingFangMediumFont(XMIAdFont(9));
            self.adMarkLabel.textColor = colorDynamicFromRGBA(0xAAAAAA, 1.0f, 0xFFFFFF, 0.3f);
            self.thirdLogoView.tintColor = colorDynamicFromRGBA(0xAAAAAA, 1.0f, 0xFFFFFF, 0.3f);
        }
            break;
        default:
            break;
    }
}

#pragma - mark lazy load

- (UIImageView *)thirdLogoView {
    if (!_thirdLogoView) {
        _thirdLogoView = [[UIImageView alloc] init];
        [self addSubview:_thirdLogoView];
    }
    return _thirdLogoView;
}

- (UILabel *)adMarkLabel {
    if (!_adMarkLabel) {
        _adMarkLabel = [[UILabel alloc] init];
        [self addSubview:_adMarkLabel];
        _adMarkLabel.font = XMI_AD_PingFangMediumFont(XMIAdFont(11));
        _adMarkLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _adMarkLabel;
}

#pragma - mark public methods

- (void)updateAdMarkText:(NSString *)text {
    self.adMarkLabel.hidden = text.length > 0 ? NO : YES;
    self.adMarkLabel.text = text;
    self.adMarkLabel.size = CGSizeMake(XMIAdPic(24), XMIAdPic(XMI_ADMARKBUTTONVIEW_HEIGHT));
    self.adMarkLabel.centerY = self.height / 2;
    if (_thirdLogoView && !_thirdLogoView.hidden) {
        self.adMarkLabel.left =  self.thirdLogoView.right + xmUIHSpace(2);
    } else {
        self.adMarkLabel.left = xmUIHSpace(2);
    }
    if (self.showStyle == XMIAdMarkViewNewShowStyleWakeEnd) {
        CGFloat right = self.right;
        self.width = self.adMarkLabel.right;
        self.right = right;
    }
}

- (void)updateFeedAdType:(XMIAdMarkAdType)feedAdType {
    UIImage *image = nil;
    switch (feedAdType) {
        case XMIAdMarkAdTypeGDT:
            image = [XMICommonUtils imageNamed:@"admark_gdt"];
            break;
        case XMIAdMarkAdTypeBU:
            image = [XMICommonUtils imageNamed:@"admark_csj"];
            break;
        case XMIAdMarkAdTypeJAD:
            image = [XMICommonUtils imageNamed:@"admark_jd"];
            break;
        case XMIAdMarkAdTypeBAIDU:
            image = [XMICommonUtils imageNamed:@"admark_bd"];
            break;
        default:
            break;
    }
    if (image) {
        self.thirdLogoView.hidden = NO;
        self.thirdLogoView.image = [image imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        self.thirdLogoView.size = CGSizeMake(XMIAdPic(14), XMIAdPic(10));
        self.thirdLogoView.centerY = self.height / 2;
        self.thirdLogoView.left = xmUIHSpace(2);
        [self updateAdMarkText:@"广告"];
    } else {
        _thirdLogoView.hidden = YES;
        [self updateAdMarkText:@"广告"];
    }
}


@end


@interface XMIAdMarkViewNewNext ()

@property (nonatomic, strong) UIImageView *adMarkImageView;

@end


@implementation XMIAdMarkViewNewNext

- (void)updateWithAdMarUrl:(NSString *)adMarkUrl
{
    
    _adMarkImageView.hidden = YES;
    self.adMarkLabel.alpha = 1.0f;
    if (adMarkUrl.length > 0) {
        @weakify(self)
        [self.adMarkImageView sd_setImageWithURL:[NSURL URLWithString:adMarkUrl] completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
            @strongify(self)
            if (!self) {
                return;
            }
            if (image && !error) {
                self.adMarkLabel.alpha = 0;
                self.adMarkImageView.hidden = NO;
                if (image.size.height > 0) {
                    CGFloat height = XMIAdPic(9);
                    self.adMarkImageView.size = CGSizeMake(height * image.size.width / image.size.height, height);
                    self.adMarkImageView.centerY = self.adMarkLabel.centerY;
                    self.adMarkImageView.left = self.adMarkLabel.left;
                    CGFloat right = self.right;
                    self.width = self.adMarkImageView.right;
                    self.right = right;
                }
             
            }
        }];
    }
}

- (UIImageView *)adMarkImageView
{
    if (!_adMarkImageView) {
        _adMarkImageView = [[UIImageView alloc] init];
        [self addSubview:_adMarkImageView];
    }
    return _adMarkImageView;
}

@end
