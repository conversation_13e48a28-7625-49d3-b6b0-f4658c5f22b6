//
//  NSObject+XMICache.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/3.
//

#import "NSObject+XMICache.h"
#import "XMIAdCache.h"

@implementation NSObject (XMICache)

- (BOOL)xmi_save {
    if (![self createTableIfNeed]) {
        return NO;
    }
    
    return [[XMIAdCache sharedInstance] saveRecord:[self toDictionary] toTable:NSStringFromClass([self class])];
}

- (BOOL)xmi_updateByCondition:(NSString *)condition {
    if (![self createTableIfNeed]) {
        return NO;
    }
    
    return [[XMIAdCache sharedInstance] updateRecord:[self toDictionary] inTable:NSStringFromClass([self class]) byCondition:condition];
}

+ (BOOL)xmi_deleteByCondition:(NSString *)condition {
    return [[XMIAdCache sharedInstance] deleteByCondition:condition inTable:NSStringFromClass([self class])];
}

+ (BOOL)xmi_deleteAll {
    return [[XMIAdCache sharedInstance] deleteAllInTable:NSStringFromClass([self class])];
}

+ (NSArray *)xmi_selectByCondition:(NSString *)condition {
    if (![[self class] respondsToSelector:@selector(xmi_cachePropertyMapper)]) {
        return nil;
    }
    
    NSDictionary *mapper = [[self class] xmi_cachePropertyMapper];
    NSArray *keyArray = [mapper allKeys];
    NSArray *resultArray = [[XMIAdCache sharedInstance] selectByCondition:condition inTable:NSStringFromClass([self class])];
    NSMutableArray *objArray = [NSMutableArray array];
    for (NSDictionary *dic in resultArray) {
        NSObject *obj = [[[self class] alloc] init];
        for (NSString *key in keyArray) {
            [obj setValue:[dic objectForKey:key] forKey:key];
        }
        [objArray addObject:obj];
    }
    
    return objArray;
}

+ (instancetype)xmi_selectOneByCondition:(NSString *)condition {
    if (![[self class] respondsToSelector:@selector(xmi_cachePropertyMapper)]) {
        return nil;
    }
    
    NSDictionary *dic = [[XMIAdCache sharedInstance] selectOneByCondition:condition inTable:NSStringFromClass([self class])];
    if (dic == nil) {
        return nil;
    }
    
    NSDictionary *mapper = [[self class] xmi_cachePropertyMapper];
    NSArray *keyArray = [mapper allKeys];
    NSObject *obj = [[[self class] alloc] init];
    for (NSString *key in keyArray) {
        [obj setValue:[dic objectForKey:key] forKey:key];
    }
    
    return obj;
}

/**
 按需建表
 */
- (BOOL)createTableIfNeed {
    if (![[self class] respondsToSelector:@selector(xmi_cachePropertyMapper)]) {
        return NO;
    }
    
    return [[XMIAdCache sharedInstance] createTable:NSStringFromClass([self class]) withColumn:[[self class] xmi_cachePropertyMapper]];
}

/**
 对象转字典
 */
- (NSDictionary *)toDictionary {
    if (![[self class] respondsToSelector:@selector(xmi_cachePropertyMapper)]) {
        return nil;
    }
    
    NSDictionary *mapper = [[self class] xmi_cachePropertyMapper];
    NSArray *keyArray = [mapper allKeys];
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    for (NSString *key in keyArray) {
        [dic setObject:[self valueForKey:key] forKeyedSubscript:key];
    }
    
    return dic;
}

@end
