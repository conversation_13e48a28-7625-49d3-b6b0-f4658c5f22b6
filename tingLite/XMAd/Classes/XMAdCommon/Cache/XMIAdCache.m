//
//  XMIAdCache.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/2.
//

#import "XMIAdCache.h"
#import <XMDataBase/FMDB.h>

@interface XMIAdCache ()

@property (nonatomic, strong) FMDatabaseQueue *queue;
@property (nonatomic, strong) NSMutableDictionary *tableMapper;

@end

@implementation XMIAdCache
NSString * const kAdDoc = @"iAdDoc";
NSString * const kAdDBName = @"xmaddb.sqlite";
NSString * const kPrimaryKeyName = @"pkid";

+ (instancetype)sharedInstance {
    static XMIAdCache *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        [self commonInit];
    }
    return self;
}

- (void)commonInit {
    NSString *docDir = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) firstObject];
    NSString *adDir = [docDir stringByAppendingPathComponent:kAdDoc];
    BOOL isDir = NO;
    BOOL exist = [[NSFileManager defaultManager] fileExistsAtPath:adDir isDirectory:&isDir];
    if (!exist || !isDir) {
        NSError *error = nil;
        [[NSFileManager defaultManager] createDirectoryAtPath:adDir withIntermediateDirectories:YES attributes:nil error:&error];
    }
    NSString *dbPath = [adDir stringByAppendingPathComponent:kAdDBName];
    self.queue = [FMDatabaseQueue databaseQueueWithPath:dbPath];
}

- (NSMutableDictionary *)tableMapper {
    if (_tableMapper == nil) {
        _tableMapper = [[NSMutableDictionary alloc] init];
    }
    return _tableMapper;
}

- (BOOL)createTable:(NSString *)tableName withColumn:(NSDictionary *)columnDic {
    if (!self.queue) {
        return NO;
    }
    
    // 每个app生命周期内仅创建一次
    if ([self.tableMapper objectForKey:tableName] != nil) {
        return YES;
    }
    
    __block BOOL ret = YES;
    [self.queue inTransaction:^(FMDatabase * _Nonnull db, BOOL * _Nonnull rollback) {
        NSString *sql = [NSString stringWithFormat:@"CREATE TABLE IF NOT EXISTS %@ (%@);", tableName, [self getColumnAndTypeString:columnDic]];
        if (![db executeUpdate:sql]) {
            ret = NO;
            *rollback = YES;
            return;
        }
        // 表结构更新
        NSMutableArray *columns = [NSMutableArray array];
        FMResultSet *rs = [db getTableSchema:tableName];
        while ([rs next]) {
            NSString *column = [rs stringForColumn:@"name"];
            [columns addObject:column];
        }
        NSArray *properties = [columnDic allKeys];
        NSPredicate *filterPredicate = [NSPredicate predicateWithFormat:@"NOT (SELF IN %@)",columns];
        NSArray *resultArray = [properties filteredArrayUsingPredicate:filterPredicate];
        if (resultArray.count > 0) {
            for (NSString *column in resultArray) {
                NSString *propType = (NSString *)[columnDic objectForKey:column];
                NSString *alterSql = [NSString stringWithFormat:@"ALTER TABLE %@ ADD COLUMN %@  %@;", tableName, column, propType];
                if (![db executeUpdate:alterSql]) {
                    ret = NO;
                    *rollback = YES;
                    return;
                }
            }
        }
        [self.tableMapper setObject:@(YES) forKeyedSubscript:tableName];
    }];
    
    return ret;
}

- (BOOL)saveRecord:(NSDictionary *)recordDic toTable:(NSString *)tableName {
    if (!self.queue) {
        return NO;
    }
    
    NSArray *propNames = [recordDic allKeys];
    NSMutableString *keyStr = [NSMutableString string];
    NSMutableString *valueStr = [NSMutableString string];
    for (NSString *key in propNames) {
        [keyStr appendFormat:@"%@,", key];
        [valueStr appendFormat:@":%@,", key];
    }
    if (keyStr.length > 1) {
        [keyStr deleteCharactersInRange:NSMakeRange(keyStr.length - 1, 1)];
    }
    if (valueStr.length > 1) {
        [valueStr deleteCharactersInRange:NSMakeRange(valueStr.length - 1, 1)];
    }
    
    __block BOOL ret = NO;
    [self.queue inDatabase:^(FMDatabase * _Nonnull db) {
        NSString *sql = [NSString stringWithFormat:@"INSERT INTO %@ (%@) VALUES (%@);", tableName, keyStr, valueStr];
        ret = [db executeUpdate:sql withParameterDictionary:recordDic];
    }];
    
    return ret;
}

- (BOOL)updateRecord:(NSDictionary *)recordDic inTable:(NSString *)tableName byCondition:(NSString *)condition {
    if (!self.queue) {
        return NO;
    }
    
    __block BOOL ret = NO;
    NSMutableString *setStr = [NSMutableString string];
    NSArray *propNames = [recordDic allKeys];
    for (NSString *key in propNames) {
        [setStr appendFormat:@"%@=:%@,", key, key];
    }
    if (setStr.length > 1) {
        [setStr deleteCharactersInRange:NSMakeRange(setStr.length - 1, 1)];
    }
    
    [self.queue inDatabase:^(FMDatabase * _Nonnull db) {
        NSString *sql = [NSString stringWithFormat:@"UPDATE %@ SET %@ WHERE %@;", tableName, setStr, condition];
        ret = [db executeUpdate:sql withParameterDictionary:recordDic];
    }];
    
    return ret;
}

- (NSArray *)selectByCondition:(NSString *)condition inTable:(NSString *)tableName {
    if (!self.queue) {
        return nil;
    }
    
    __block NSMutableArray *resultArray = [NSMutableArray array];
    [self.queue inDatabase:^(FMDatabase * _Nonnull db) {
        NSString *sql = [NSString stringWithFormat:@"SELECT * FROM %@ WHERE %@;", tableName, condition];
        FMResultSet *rs = [db executeQuery:sql];
        while ([rs next]) {
            NSMutableDictionary *dic = [NSMutableDictionary dictionary];
            for (int i = 0; i < rs.columnCount; i++) {
                NSString *columnName = [rs columnNameForIndex:i];
                id columnValue = [rs objectForColumnIndex:i];
                [dic setObject:columnValue forKeyedSubscript:columnName];
            }
            [resultArray addObject:dic];
        }
    }];
    
    return resultArray;
}

- (NSDictionary *)selectOneByCondition:(NSString *)condition inTable:(NSString *)tableName {
    if (!self.queue) {
        return nil;
    }
    
    __block NSDictionary *resultDic = nil;
    [self.queue inDatabase:^(FMDatabase * _Nonnull db) {
        NSString *sql = [NSString stringWithFormat:@"SELECT * FROM %@ WHERE %@ LIMIT 1;", tableName, condition];
        FMResultSet *rs = [db executeQuery:sql];
        if ([rs next]) {
            NSMutableDictionary *dic = [NSMutableDictionary dictionary];
            for (int i = 0; i < rs.columnCount; i++) {
                NSString *columnName = [rs columnNameForIndex:i];
                id columnValue = [rs objectForColumnIndex:i];
                [dic setObject:columnValue forKeyedSubscript:columnName];
            }
            resultDic = dic;
        }
    }];
    
    return resultDic;
}

- (BOOL)deleteByCondition:(NSString *)condition inTable:(NSString *)tableName {
    if (!self.queue) {
        return NO;
    }
    
    __block BOOL ret = NO;
    [self.queue inDatabase:^(FMDatabase * _Nonnull db) {
        NSString *sql = [NSString stringWithFormat:@"DELETE FROM %@ WHERE %@;", tableName, condition];
        ret = [db executeUpdate:sql];
    }];
    
    return ret;
}

- (BOOL)deleteAllInTable:(NSString *)tableName {
    if (!self.queue) {
        return NO;
    }
    
    __block BOOL ret = NO;
    [self.queue inDatabase:^(FMDatabase * _Nonnull db) {
        NSString *sql = [NSString stringWithFormat:@"DELETE FROM %@;", tableName];
        ret = [db executeUpdate:sql];
    }];
    
    return ret;
}

- (NSString *)getColumnAndTypeString:(NSDictionary *)columnDic {
    NSMutableString *str = [NSMutableString string];
    [str appendFormat:@"%@ INTEGER PRIMARY KEY AUTOINCREMENT,", kPrimaryKeyName];
    [columnDic enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, id  _Nonnull obj, BOOL * _Nonnull stop) {
        [str appendFormat:@"%@ %@,", key, obj];
    }];
    [str deleteCharactersInRange:NSMakeRange(str.length - 1, 1)];
    
    return str;
}

@end
