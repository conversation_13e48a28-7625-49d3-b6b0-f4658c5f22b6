//
//  XMIAdCache.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/2.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XMIAdCache : NSObject

+ (instancetype)sharedInstance;

/**
 创建表
 
 @param tableName :表名
 @param columnDic :列 key-字段名 value-字段类型
 @return YES-成功, NO-失败
 */
- (BOOL)createTable:(NSString *)tableName withColumn:(NSDictionary *)columnDic;
/**
 保存记录
 
 @param recordDic :记录 key-字段名 value-字段值
 @param tableName :表名
 @return 是否成功
 */
- (BOOL)saveRecord:(NSDictionary *)recordDic toTable:(NSString *)tableName;
/**
 更新记录
 
 @param recordDic :记录 key-字段名 value-字段值
 @param tableName :表名
 @param condition :条件 例如 a = 1234 and b = 'hello'
 */
- (BOOL)updateRecord:(NSDictionary *)recordDic inTable:(NSString *)tableName byCondition:(NSString *)condition;
/**
 查询记录
 
 @param condition :条件
 @param tableName :表名
 @return 结果数组，每一个元素都是dictionary key-字段名 value-字段值（对象NSString, NSNumber, NSData等）
 */
- (NSArray *)selectByCondition:(NSString *)condition inTable:(NSString *)tableName;
/**
 查询一条记录
 
 @param condition :条件
 @param tableName :表名
 @return 结果dictionary key-字段名 value-字段值（对象NSString, NSNumber, NSData等）
 */
- (NSDictionary *)selectOneByCondition:(NSString *)condition inTable:(NSString *)tableName;
/**
 删除记录
 
 @param condition :条件
 @param tableName :表名
 @return 是否成功
 */
- (BOOL)deleteByCondition:(NSString *)condition inTable:(NSString *)tableName;
/**
 删除所有记录
 
 @param tableName :表名
 @return 是否成功
 */
- (BOOL)deleteAllInTable:(NSString *)tableName;

@end

NS_ASSUME_NONNULL_END
