//
//  NSObject+XMICache.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/3.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

#define XMIC_STRING @"TEXT"
#define XMIC_INT    @"INTEGER"
#define XMIC_FLOAT  @"REAL"
#define XMIC_DATA   @"BLOB"

@protocol XMICache <NSObject>

@optional
+ (NSDictionary *)xmi_cachePropertyMapper;

@end

@interface NSObject (XMICache)

/**
 保存
 */
- (BOOL)xmi_save;
/**
 更新
 
 @param condition :条件 例如 a=1234 and b = 'hello'
 @return 是否成功
 */
- (BOOL)xmi_updateByCondition:(NSString *)condition;
/**
 删除
 
 @param condition :条件 例如 a=1234 and b = 'hello'
 @return 是否成功
 */
+ (BOOL)xmi_deleteByCondition:(NSString *)condition;
/**
 删除所有
 
 @return 是否成功
 */
+ (BOOL)xmi_deleteAll;
/**
 查询
 
 @param condition :条件 例如 a=1234 and b = 'hello'
 @return 对象数组
 */
+ (NSArray *)xmi_selectByCondition:(NSString *)condition;
/**
 查询一个
 
 @param condition :条件 例如 a=1234 and b = 'hello'
 @return 对象
 */
+ (instancetype)xmi_selectOneByCondition:(NSString *)condition;

@end

NS_ASSUME_NONNULL_END
