//
//  NSObject+XMIModel.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/5.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSObject (XMIModel)

+ (instancetype)xmi_modelWithJSON:(id)json;

- (NSString *)xmi_modelToJSONString;

- (id)xmi_modelToJSONObject;

- (NSData *)xmi_modelToJSONData;

@end

@interface NSArray (XMIModel)
+ (NSArray *)xmi_modelArrayWithClass:(Class)cls json:(id)json;
@end

NS_ASSUME_NONNULL_END
