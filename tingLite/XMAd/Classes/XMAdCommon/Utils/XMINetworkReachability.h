//
//  XMINetworkReachability.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/5.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, XMINetworkStatus) {
    XMINetworkStatusNotReachable = 0,
    XMINetworkStatusUnknown = 1,
    XMINetworkStatusWWAN2G = 2,
    XMINetworkStatusWWAN3G = 3,
    XMINetworkStatusWWAN4G = 4,
    XMINetworkStatusWWAN5G = 5,
    
    XMINetworkStatusWiFi = 9,
};

extern NSString *kXMINetworkReachabilityChangedNotification;

@interface XMINetworkReachability : NSObject

/*!
 * Use to check the reachability of a given host name.
 */
+ (instancetype)reachabilityWithHostName:(NSString *)hostName;

/*!
 * Use to check the reachability of a given IP address.
 */
+ (instancetype)reachabilityWithAddress:(const struct sockaddr *)hostAddress;

/*!
 * Checks whether the default route is available. Should be used by applications that do not connect to a particular host.
 */
+ (instancetype)reachabilityForInternetConnection;

- (BOOL)startNotifier;

- (void)stopNotifier;

- (XMINetworkStatus)currentReachabilityStatus;

@end

NS_ASSUME_NONNULL_END
