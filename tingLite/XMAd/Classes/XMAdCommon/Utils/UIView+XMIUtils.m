//
//  UIView+XMIUtils.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/9/8.
//

#import "UIView+XMIUtils.h"

@implementation UIView (XMIUtils)

//- (BOOL)xmi_isExposed {
//    // 还未添加到window上
//    if (!self.superview || !self.window) {
//        return NO;
//    }
//    // 在后台
//    if ([UIApplication sharedApplication].applicationState != UIApplicationStateActive) {
//        return NO;
//    }
//    // 宽或高为0
//    int w = (int)self.frame.size.width;
//    int h = (int)self.frame.size.height;
//    if (w == 0 || h == 0) {
//        return NO;
//    }
//    CGRect rect = [self.superview convertRect:self.frame toView:nil];
////    CGPoint center = CGPointMake(CGRectGetMidX(rect), CGRectGetMidY(rect));
//
//    CGPoint point0 = CGPointMake(CGRectGetMidX(rect), CGRectGetHeight(rect)*0.01);
//    CGPoint point1 = CGPointMake(CGRectGetMidX(rect), CGRectGetHeight(rect)*0.99);
//
//    BOOL isExposed0 = CGRectContainsPoint([UIScreen mainScreen].bounds, point0);
//    BOOL isExposed1 = CGRectContainsPoint([UIScreen mainScreen].bounds, point1);
//
////    BOOL isExposed = CGRectContainsPoint([UIScreen mainScreen].bounds, center);
//
//    return isExposed0 || isExposed1;
//}

- (BOOL)xmi_isExposed:(UIView *)toView
                radio:(CGFloat)radio
{
    // 还未添加到window上
    if (!self.superview) {
        return NO;
    }
    // 在后台
    if ([UIApplication sharedApplication].applicationState != UIApplicationStateActive) {
        return NO;
    }
    // 宽或高为0
    int w = (int)self.frame.size.width;
    int h = (int)self.frame.size.height;
    if (w == 0 || h == 0) {
        return NO;
    }
    CGRect rect = [self convertRect:self.bounds toView:toView];
    
    int toViewHeight = toView.frame.size.height;
    if (toViewHeight == 0) {
        return NO;
    }
    
    if (-rect.origin.y >= h*(1-radio)) {
        return NO;
    }
    
    if (rect.origin.y + h*radio >= toViewHeight) {
        return NO;
    }
    
    return YES;
}

- (CGFloat)xmi_left
{
    return self.frame.origin.x;
}

- (void)setXmi_left:(CGFloat)xmi_left
{
    CGRect frame = self.frame;
    frame.origin.x =  xmi_left;
    self.frame = frame;
}

- (CGFloat)xmi_top
{
    return self.frame.origin.y;
}

- (void)setXmi_top:(CGFloat)xmi_top
{
    CGRect frame = self.frame;
    frame.origin.y =  xmi_top;
    self.frame = frame;
}

- (CGFloat)xmi_right
{
    return CGRectGetMaxX(self.frame);
}

- (void)setXmi_right:(CGFloat)xmi_right
{
    CGRect frame = self.frame;
    frame.origin.x =  xmi_right - self.bounds.size.width;
    self.frame = frame;
}

- (CGFloat)xmi_bottom
{
    return CGRectGetMaxY(self.frame);
}

- (void)setXmi_bottom:(CGFloat)xmi_bottom
{
    CGRect frame = self.frame;
    frame.origin.y =  xmi_bottom - self.bounds.size.height;
    self.frame = frame;
}

- (CGFloat)xmi_width
{
    return self.frame.size.width;
}

- (void)setXmi_width:(CGFloat)xmi_width
{
    CGRect frame = self.frame;
    frame.size.width = xmi_width;
    self.frame = frame;
}

- (CGFloat)xmi_height
{
    return self.frame.size.height;
}

- (void)setXmi_height:(CGFloat)xmi_height
{
    CGRect frame = self.frame;
    frame.size.height = xmi_height;
    self.frame = frame;
}

- (CGSize)xmi_size
{
    return self.frame.size;
}

- (void)setXmi_size:(CGSize)xmi_size
{
    CGRect frame = self.frame;
    frame.size = xmi_size;
    self.frame = frame;
}

- (CGFloat)xmi_centerX
{
    return self.center.x;
}

- (void)setXmi_centerX:(CGFloat)xmi_centerX
{
    self.center = CGPointMake(xmi_centerX, self.center.y);
}

- (CGFloat)xmi_centerY
{
    return self.center.y;
}

- (void)setXmi_centerY:(CGFloat)xmi_centerY
{
    self.center = CGPointMake(self.center.x, xmi_centerY);
}

@end
