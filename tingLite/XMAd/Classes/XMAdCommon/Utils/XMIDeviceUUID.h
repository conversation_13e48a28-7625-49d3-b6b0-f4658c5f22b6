//
//  XMDeviceUUID.h
//  XMCommonUtil
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/26.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, XMQueryIDFAType) {
    XMQueryIDFATypeEmpty = 2,
    XMQueryIDFATypeAuthorized = 0,
    XMQueryIDFATypeCached = 1,
};

@interface XMIDeviceUUID : NSObject

/**
 设备唯一ID
 */
+ (NSString *)uniqueDeviceIdentifier;
/**
 获取IDFA
 */
+ (NSString *)advertisingIdentifier;

/**
 无权限时,返回keychain中缓存的idfa
 */
+ (nullable NSString *)cachedIDFA;
/**
 无权限时,返回keychain中缓存的idfa
 @param type :来源标志
 */
+ (nullable NSString *)cachedIDFAType:(nullable XMQueryIDFAType *)type;

@end

NS_ASSUME_NONNULL_END
