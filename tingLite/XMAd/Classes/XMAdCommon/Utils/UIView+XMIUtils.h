//
//  UIView+XMIUtils.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/9/8.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface UIView (XMIUtils)

/**
 是否曝光
 视图中心点在window可见范围视为曝光
 */
//- (BOOL)xmi_isExposed;

/// 视频是否曝光
- (BOOL)xmi_isExposed:(UIView *)toView
                radio:(CGFloat)radio;

@property (nonatomic, assign) CGFloat xmi_left;

@property (nonatomic, assign) CGFloat xmi_top;

@property (nonatomic, assign) CGFloat xmi_right;

@property (nonatomic, assign) CGFloat xmi_bottom;

@property (nonatomic, assign) CGFloat xmi_width;

@property (nonatomic, assign) CGFloat xmi_height;

@property (nonatomic, assign) CGSize xmi_size;

@property (nonatomic, assign) CGFloat xmi_centerX;

@property (nonatomic, assign) CGFloat xmi_centerY;
@end

NS_ASSUME_NONNULL_END
