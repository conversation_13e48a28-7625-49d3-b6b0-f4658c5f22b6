//
//  XMDeviceUUID.m
//  XMCommonUtil
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/26.
//

#import "XMIDeviceUUID.h"
#import <SAMKeychain/SAMKeychain.h>
#import <SAMKeychain/SAMKeychainQuery.h>
#import <AdSupport/AdSupport.h>
#import <AppTrackingTransparency/AppTrackingTransparency.h>

@interface SAMKeychain (xmi_NOiClound)

+ (BOOL)xmNoiCloud_setPassword:(NSString *)password forService:(NSString *)serviceName account:(NSString *)account error:(NSError *__autoreleasing *)error;
+ (NSString *)xmNoiCloud_passwordForService:(NSString *)serviceName account:(NSString *)account error:(NSError *__autoreleasing *)error;

@end

@implementation XMIDeviceUUID

static dispatch_queue_t _concurrentQueue;
/// iOS10.0打开限制广告跟踪后uuid返回全0
#define NULL_UUID @"********-0000-0000-0000-********0000"
#ifndef kAccountIdentifier
#define kAccountIdentifier                  @"iting"
#endif
#ifndef kDeviceUUIDName
#define kDeviceUUIDName                     @"itingUUID"
#endif

#define XMI_UD_OBJECT(key)         [[NSUserDefaults standardUserDefaults] objectForKey:key]
#define XMI_UD_SETOBJECT(obj, key) [[NSUserDefaults standardUserDefaults] setObject:obj forKey:key];[[NSUserDefaults standardUserDefaults] synchronize]
#define XMI_UD_BOOL(key)           [[NSUserDefaults standardUserDefaults] boolForKey:key]
#define XMI_UD_SETBOOL(value, key) [[NSUserDefaults standardUserDefaults] setBool:value forKey:key];[[NSUserDefaults standardUserDefaults] synchronize]
#define XMI_UD_STRING(key)         [[NSUserDefaults standardUserDefaults] stringForKey:key]

+ (void)initialize
{
    static BOOL initialized = NO;
    if(!initialized) {
        initialized = YES;
        _concurrentQueue = dispatch_queue_create("com.xmad.deviceUUID", DISPATCH_QUEUE_CONCURRENT);
    }
}

+ (NSString *)uniqueDeviceIdentifier
{
    // 从NSUserDefaults读缓存
    NSString *uuid = XMI_UD_OBJECT(kDeviceUUIDName);
    
    // 为空或者不是NSString类型，从keychain中获取
    if (!uuid || ![uuid isKindOfClass:[NSString class]]) {
        
        NSError *error = nil;
        uuid = [self uuidFromKeychainWithError:&error];
        
        // 存入NSUserDefaults，为下次使用缓存
        XMI_UD_SETOBJECT(uuid, kDeviceUUIDName);
        
        //如果读取失败，重新写入keychain
        if(error) {
            [self uuidIntoKeychain:uuid];
        }
    }
    
    return uuid;
}

+ (NSString *)advertisingIdentifier
{
    NSString *stringToHash = [[[ASIdentifierManager sharedManager] advertisingIdentifier] UUIDString];
    NSString *uniqueIdentifier = stringToHash;
    return uniqueIdentifier;
}

#pragma mark - Private
/**
 keychain中读取uuid 如果失败生成一个uuid
 
 @param err read error info
 @return uuid
 */
+ (NSString *)uuidFromKeychainWithError:(NSError **)err
{
    __block NSString *uuid = nil;
    __block NSError *error = nil;
    dispatch_sync(_concurrentQueue, ^{
        uuid = [SAMKeychain xmNoiCloud_passwordForService:kAccountIdentifier account:kDeviceUUIDName error:&error];
        if (uuid.length <= 0 || [uuid isEqualToString:NULL_UUID]) {
            uuid = [self advertisingIdentifier];
            
            // iOS10.0打开限制广告跟踪后uuid返回全0
            if (uuid.length<=0 || [uuid isEqualToString:NULL_UUID]) {
                uuid = [self createUUIDCustom];
            }
        }
    });
    
    *err = error;
    
    return uuid;
}

/**
 将uuid写入keychain中 如果失败搜集数据
 */
+ (void)uuidIntoKeychain:(NSString *)uuid
{
    dispatch_barrier_async(_concurrentQueue, ^{
        
        //save uuid in keychain
        NSError *error = nil;
        BOOL success = [SAMKeychain xmNoiCloud_setPassword:uuid forService:kAccountIdentifier account:kDeviceUUIDName error:&error];
        
        // 如果写入Keychain失败，进行xdcs统计
        if (!success) {
            NSDate *xdcsDate = XMI_UD_OBJECT(@"itingUUID_XDCS");
            NSDate *nowDate = [NSDate date];
            NSTimeInterval interval = 0;
            if (xdcsDate) {
                interval = [nowDate timeIntervalSinceDate:xdcsDate];
            }
            //五分钟收集一次，收集频率太短
            if (!xdcsDate || interval > 60 * 60 * 24) {
                XMI_UD_SETOBJECT(nowDate, @"itingUUID_XDCS");
            }
        }
        else {
//            LOGCA(@"%@ write uuid %@",[NSThread currentThread],uuid);
        }
    });
}

+ (NSString *)createUUIDCustom
{
    static NSString *cUUID = nil;
    static dispatch_once_t uuidOnce;
    dispatch_once(&uuidOnce,^{
        cUUID = [[NSUUID UUID] UUIDString];
    });
    
    return cUUID;
}

#ifndef kCachedIDFAKey
#define kCachedIDFAKey @"kXMCachedIDFAKey"
#endif
#ifndef kQueryKeychain
#define kQueryKeychain @"kXMQueryKeychain"
#endif

#ifndef kIDFAService
#define kIDFAService @"itingIDFAService"
#endif
#ifndef kDeviceIDFAName
#define kDeviceIDFAName @"itingIDFAAccount"
#endif

+ (NSString *)cachedIDFA {
    return [self cachedIDFAType:nil];
}

+ (nullable NSString *)cachedIDFAType:(XMQueryIDFAType *)type {
    BOOL isAuthorized = YES;
    if (@available(iOS 14.5, *)) {
        isAuthorized = [ATTrackingManager trackingAuthorizationStatus] == ATTrackingManagerAuthorizationStatusAuthorized;
    } else {
        isAuthorized = [ASIdentifierManager sharedManager].advertisingTrackingEnabled;
    }
    
    XMQueryIDFAType queryType = XMQueryIDFATypeEmpty;
    
    NSString *curIDFA = isAuthorized ? [self advertisingIdentifier] : nil;
    
    if (curIDFA.length > 0 && ![curIDFA isEqualToString:NULL_UUID]) {
        NSString *cachedIDFA = XMI_UD_STRING(kCachedIDFAKey);
        if (![cachedIDFA isEqualToString:curIDFA]) {
            XMI_UD_SETOBJECT(curIDFA, kCachedIDFAKey);
            [SAMKeychain xmNoiCloud_setPassword:curIDFA forService:kIDFAService account:kDeviceIDFAName error:nil];
        }
        queryType = XMQueryIDFATypeAuthorized;
    } else {
        curIDFA = XMI_UD_STRING(kCachedIDFAKey);
        if (curIDFA.length == 0) {
            BOOL queryKeychain = XMI_UD_BOOL(kQueryKeychain);
            if (!queryKeychain) {
                curIDFA = [SAMKeychain xmNoiCloud_passwordForService:kIDFAService account:kDeviceIDFAName error:nil];
                if (curIDFA.length > 0) {
                    XMI_UD_SETOBJECT(curIDFA, kCachedIDFAKey);
                }
                XMI_UD_SETBOOL(YES, kQueryKeychain);
            }
        }
        queryType = curIDFA.length > 0 ? XMQueryIDFATypeCached : XMQueryIDFATypeEmpty;
    }
    if (type != nil) {
        *type = queryType;
    }
    return curIDFA;
}

@end


@implementation  SAMKeychain (NOiClound)

+ (BOOL)xmNoiCloud_setPassword:(NSString *)password forService:(NSString *)serviceName account:(NSString *)account error:(NSError *__autoreleasing *)error {
    SAMKeychainQuery *query = [[SAMKeychainQuery alloc] init];
    query.synchronizationMode = SAMKeychainQuerySynchronizationModeNo;
    query.service = serviceName;
    query.account = account;
    query.password = password;
    return [query save:error];
}


+ (NSString *)xmNoiCloud_passwordForService:(NSString *)serviceName account:(NSString *)account error:(NSError *__autoreleasing *)error {
    SAMKeychainQuery *query = [[SAMKeychainQuery alloc] init];
    query.synchronizationMode = SAMKeychainQuerySynchronizationModeNo;
    query.service = serviceName;
    query.account = account;
    [query fetch:error];
    return query.password;
}

@end
