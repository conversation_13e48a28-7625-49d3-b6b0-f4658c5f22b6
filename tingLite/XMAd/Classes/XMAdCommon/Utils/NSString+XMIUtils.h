//
//  NSString+XMIUtils.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/19.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSString (XMIUtils)

/**
 URLEncoded
 */
- (NSString *)xmi_URLEncodedString;
/**
 url和参数拼接成新url
 原url已经有的参数会被param中的替换
 */
+ (NSString *)xmi_encodedURLString:(NSString *)originUrl withParam:(NSDictionary *)param;
/**
 url中的参数
 */
+ (NSDictionary *)xmi_parametersFromUrl:(NSString *)urlString;

/**
 md5 全小写
 */
- (NSString *)xmi_md5String;

@end

NS_ASSUME_NONNULL_END
