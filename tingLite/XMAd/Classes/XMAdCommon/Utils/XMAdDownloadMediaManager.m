//
//  XMAdDownloadMediaManager.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/12/7.
//

#import "XMAdDownloadMediaManager.h"
#import <XMWebImage/UIImageView+WebCache.h>
#import "NSURL+XMICommon.h"
#import "XMIAnimatedImageView.h"

@implementation XMAdDownloadMediaManager

- (void)setImageWithImageView:(XMIAnimatedImageView *)imageView
             placeholderImage:(UIImage * __nullable)placeholderImage
                     complete:(void(^)(XMAdDownloadMediaManager *))complete
{
    __weak typeof(self) weakSelf = self;
    [imageView setImageWithURL:self.url placeholderImage:placeholderImage loadSuccess:^(UIImage * image) {
        weakSelf.isSuccess = YES;
        weakSelf.image = image;
        if (complete) {
            complete(weakSelf);
        }
    } loadFailure:^(NSError *error) {
        weakSelf.isSuccess = NO;
        if (complete) {
            complete(weakSelf);
        }
    }];
}
@end
