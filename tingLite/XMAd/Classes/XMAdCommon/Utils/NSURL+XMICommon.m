//
//  NSURL+XMICommon.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/12/15.
//

#import "NSURL+XMICommon.h"

static NSString *kXMIOldHost = @"fdfs.xmcdn.com";
static NSString *kXMINewHost = @"imagev2.xmcdn.com";

static BOOL xmi_webpEnable = YES;

@implementation NSURL (XMICommon)

+ (void)xmi_enableWebp:(BOOL)enable
{
    xmi_webpEnable = enable;
}

- (NSURL *)xmi_webpURL
{
    return [self xmi_webpURLWithSize:CGSizeZero resize:NO];
}

- (NSURL *)xmi_webpURLWithSize:(CGSize)size
{
    return [self xmi_webpURLWithSize:size resize:YES];
}

- (NSURL *)xmi_webpURLWithSize:(CGSize)size resize:(BOOL)resize
{
    if (!xmi_webpEnable) {
        return self;
    }
    
    NSString *str = self.absoluteString;
    if (0 == str.length) {
        return self;
    }
    
    // 已经处理过了
    if (0 != [str rangeOfString:@"xmagick"].length) {
        return self;
    }
    
    // 只处理指定域名
    if (![self.host isEqualToString:kXMINewHost] && ![self.host isEqualToString:kXMIOldHost]) {
        return self;
    }
    
    // 业务方不允许改写
    if (0 != [str rangeOfString:@"url_rewrite=0"].length) {
        return self;
    }
    
    /** 有存在特殊的图片链接，不能使用ext来判断是不是jpg或png
    http://imagev2.xmcdn.com/group84/M06/8F/05/wKg5JF8buDDCfLBzAADGB4NNhBA848.jpg!op_type=15&upload_type=listenlist&device_type=ios&name=large&magick=png&border_weight=1&border_color=ccccccff&roundrect=10&image_info=80/group78/M09/D0/8A/wKgO4F6G4RiDhKSIAALQYeSHtxE279.jpg&image_info=70/group71/M01/F8/DC/wKgO2V6hUsKyJPXRAATCwgf6PsY880.jpg
    */
    
    NSMutableArray *pathList = [NSMutableArray arrayWithArray:[str componentsSeparatedByString:@"!"]];
    if (0 == pathList.count) {
        return self;
    }
    
    // 只处理特定格式的
    NSString *path = pathList[0];
    if (![path hasSuffix:@".jpg"] &&
        ![path hasSuffix:@".jpeg"] &&
        ![path hasSuffix:@".png"] &&
        ![path hasSuffix:@".gif"]) {
        return self;
    }
    
    // 做fdfs.xmcdn.com到imagev2.xmcdn.com的域名映射
    path = [path stringByReplacingOccurrencesOfString:kXMIOldHost withString:kXMINewHost];
    pathList[0] = path;
    
    // 会出现多个!分割符的情况
    str = [pathList componentsJoinedByString:@"!"];
    // 对于gif图片，只做域名替换
    if ([path hasSuffix:@".gif"]) {
        return [NSURL URLWithString:str];
    }
    
    if (1 == pathList.count) {
        str = [str stringByAppendingString:@"!"];
    }
    else {
        str = [str stringByAppendingString:@"&"];
    }
    str = [str stringByAppendingString:@"xmagick=webp"];
    
    if (resize) {
        if (size.width > 0.1) {
            str = [str stringByAppendingFormat:@"&xcolumns=%d", (int)(size.width)];
        }
        
        if (size.height > 0.1) {
            str = [str stringByAppendingFormat:@"&xrows=%d", (int)(size.height)];
        }
    }
    
    return [NSURL URLWithString:str];
}

@end
