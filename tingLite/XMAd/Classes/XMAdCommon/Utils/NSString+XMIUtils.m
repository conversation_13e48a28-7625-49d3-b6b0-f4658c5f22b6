//
//  NSString+XMIUtils.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/19.
//

#import "NSString+XMIUtils.h"
#import <CommonCrypto/CommonDigest.h>

@implementation NSString (XMIUtils)

- (NSString *)xmi_URLEncodedString {
    NSCharacterSet *set = [NSCharacterSet characterSetWithCharactersInString:@"?!@#$^&%*+,:;='\"`<>()[]{}/\\| "].invertedSet;
    return [self stringByAddingPercentEncodingWithAllowedCharacters:set];
}

- (NSString *)xmi_URLDecodedString {
    return [self stringByRemovingPercentEncoding];
}

- (NSString *)xmi_md5String {
    const char *cStr = [self UTF8String];
    if (cStr == NULL) {
        return nil;
    }
    unsigned char digest[CC_MD5_DIGEST_LENGTH];
    CC_MD5( cStr, (CC_LONG)self.length, digest );
    NSMutableString *result = [NSMutableString stringWithCapacity:CC_MD5_DIGEST_LENGTH * 2];
    for(int i = 0; i < CC_MD5_DIGEST_LENGTH; i++) {
        [result appendFormat:@"%02x", digest[i]];
    }
    return result;
}

+ (NSString *)xmi_encodedURLString:(NSString *)originUrl withParam:(NSDictionary *)param {
    if (originUrl == nil) {
        return originUrl;
    }
    if (param == nil) {
        return originUrl;
    }
    NSURL *url = [NSURL URLWithString:originUrl];
    if (url == nil) {
        return nil;
    }
    
    NSMutableDictionary *newParam = [[NSMutableDictionary alloc] init];
    NSDictionary *oldParam = [self xmi_parametersFromUrl:originUrl];
    if (oldParam != nil) {
        [newParam addEntriesFromDictionary:oldParam];
    }
    [newParam addEntriesFromDictionary:param];
    NSString *query = [self xmi_queryStringFromDictonary:newParam];
    
    return [self xmi_encodedURLByConponentScheme:url.scheme withHost:url.host withPort:[url.port integerValue] withUser:url.user withPassword:url.password withPath:url.path withParam:url.parameterString withQuery:query withFragment:url.fragment];
}

+ (NSDictionary *)xmi_parametersFromUrl:(NSString *)urlString {
    if (urlString == nil) {
        return nil;
    }
    NSURL *url = [NSURL URLWithString:urlString];
    if (url == nil) {
        return nil;
    }
    if (!url.query) {
        return nil;
    }
    
    NSString *delimiter = @"&";
    NSString *separator = @"=";
    NSArray *parameterPairs = [url.query componentsSeparatedByString:delimiter];
    NSMutableDictionary *parameters = [NSMutableDictionary dictionaryWithCapacity:[parameterPairs count]];
    for (NSString *currentPair in parameterPairs) {
        NSRange range = [currentPair rangeOfString:separator];
        if (range.location == NSNotFound) {
            continue;
        }
        NSString *key = [currentPair substringToIndex:range.location];
        NSString *value = [currentPair substringFromIndex:range.location + 1];
        value = [value xmi_URLDecodedString];
        if (!value) {
            value = @"";
        }
        if (key) {
            parameters[key] = value;
        }
    }
    return parameters;
}

+ (NSString *)xmi_queryStringFromDictonary:(NSDictionary *)params {
    NSMutableString *requeryStr = [NSMutableString string];
    NSInteger index = 0;
    for (id key in params){
        if(index != 0){
            [requeryStr appendString:@"&"];
        }
        id value = [params objectForKey:key];
        NSString *valueStr = @"";
        if ([value isKindOfClass:[NSNumber class]]) {
            valueStr = [value stringValue];
        }
        else if ([value isKindOfClass:[NSString class]]) {
            valueStr = value;
        }
        [requeryStr appendString:[NSString stringWithFormat:@"%@=%@", key, [valueStr xmi_URLEncodedString]]];
        index++;
    }
    return requeryStr;
}

+ (NSString *)xmi_encodedURLByConponentScheme:(NSString*)scheme
                                withHost:(NSString*)host
                                withPort:(NSInteger)port
                                withUser:(NSString*)user
                            withPassword:(NSString*)pass
                                withPath:(NSString*)path
                               withParam:(NSString*)param
                               withQuery:(NSString*)query
                            withFragment:(NSString*)fragment {

    NSMutableString *finalStr = nil;
    if (scheme) {
        finalStr = [NSMutableString stringWithFormat:@"%@://",scheme];
    } else {
        NSLog(@"URL Invalidate with no scheme (http,https or file or other) !!!");
        return nil;
    }
    
    NSString *pathStr = @"";
    
    if (user && pass) {
        [finalStr appendFormat:@"%@:%@@",[user xmi_URLEncodedString],[pass xmi_URLEncodedString]];
    } else if (user) {
        [finalStr appendFormat:@"%@@",[user xmi_URLEncodedString]];
    }
    
    if(host) {
        [finalStr appendFormat:@"%@",host];
    } else {
        NSLog(@"URL Invalidate with no host !!!");
        return nil;
    }
    
    if (port > 0) {
        [finalStr appendFormat:@":%ld",(long)port];
    }
    
    if (path) {
        NSMutableArray *finalPathArray = [NSMutableArray array];
        NSArray *pathArray = [path componentsSeparatedByString:@"/"];
        
        for (id item in pathArray) {
            if([item isEqualToString:@""]) {
                continue;
            }
            NSString *aItem = [item xmi_URLEncodedString];
            [finalPathArray addObject:aItem];
        }
        
        pathStr = [finalPathArray componentsJoinedByString:@"/"];
        [finalStr appendFormat:@"/%@",pathStr];
        
    }
    
    if (param && ![param isEqualToString:@""]) {
        [finalStr appendFormat:@";%@",param];
    }
    
    if (query && ![query isEqualToString:@""]) {
        [finalStr appendFormat:@"?%@",query];
    }
    
    if (fragment && ![fragment isEqualToString:@""]) {
       [finalStr appendFormat:@"#%@",fragment];
    }
    return finalStr;
}

@end
