//
//  NSObject+XMIModel.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/5.
//

#import "NSObject+XMIModel.h"
#import <YYModel/YYModel.h>

@implementation NSObject (XMIModel)

+ (instancetype)xmi_modelWithJSON:(id)json {
    return [self yy_modelWithJSON:json];
}

- (NSString *)xmi_modelToJSONString {
    return [self yy_modelToJSONString];
}

- (id)xmi_modelToJSONObject {
    return [self yy_modelToJSONObject];
}

- (NSData *)xmi_modelToJSONData {
    return [self yy_modelToJSONData];
}

@end


@implementation NSArray (XMIModel)
+ (NSArray *)xmi_modelArrayWithClass:(Class)cls json:(id)json {
    return [self yy_modelArrayWithClass:cls json:json];
}
@end
