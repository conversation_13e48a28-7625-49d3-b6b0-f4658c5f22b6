//
//  XMAdDownloadMediaManager.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/12/7.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
@class XMIAnimatedImageView;
@interface XMAdDownloadMediaManager : NSObject
@property(nonatomic, strong)NSString *identifier;
@property(nonatomic, strong)NSURL *url;
@property(nonatomic, assign)BOOL isSuccess;
@property(nonatomic, strong)UIImage *image;

/// 普通图
- (void)setImageWithImageView:(XMIAnimatedImageView *)imageView
             placeholderImage:(UIImage * __nullable)placeholderImage
                     complete:(void(^)(XMAdDownloadMediaManager *))complete;

@end

NS_ASSUME_NONNULL_END
