//
//  XMICommonUtils.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/16.
//

#import "XMICommonUtils.h"
#import <AdSupport/AdSupport.h>
#import <CoreTelephony/CTCarrier.h>
#import <CoreTelephony/CTTelephonyNetworkInfo.h>
#import <sys/utsname.h>
#include <sys/sysctl.h>
#include <sys/socket.h>
#include <net/if.h>
#include <net/if_dl.h>
#import "XMIDeviceUUID.h"
#import <XMWebImage/SDWebImageManager.h>
#import <XMCategories/NSString+XMCommon.h>
#import <ifaddrs.h>
#import <netinet/ip.h>
#import <arpa/inet.h>
#import <sys/stat.h>

#define kChinaMobile @"移动"
#define kChinaUnicom @"联通"
#define kChinaTelecom @"电信"
#define kChinaTietong @"铁通"

@implementation XMICommonUtils

+ (UIImage *)imageNamed:(NSString *)name {
    NSString *bundlePath = [[NSBundle bundleForClass:[self class]] pathForResource:@"XMAd" ofType:@"bundle"];
    NSBundle *adBundle = [NSBundle bundleWithPath:bundlePath];
    UIImage *image = [UIImage imageNamed:name inBundle:adBundle compatibleWithTraitCollection:nil];
    // 容错，主bundle找
    if (!image) {
        image = [UIImage imageNamed:name];
    }
    
    return image;
}

+ (UIImage *)imageFromCacheNamed:(NSString *)name drawing:(UIImage * _Nonnull (^)(void))drawImage {
    if (name.length <= 0) {
        return nil;
    }
    
    NSString *imageNameKey = [NSString stringWithFormat:@"xmi_create_image_%@", name];
    
    SDWebImageManager *manager = [SDWebImageManager sharedManager];
    UIImage *cachedImage = [manager.imageCache imageFromMemoryCacheForKey:imageNameKey];
    if (cachedImage) {
        return cachedImage;
    }
    
    UIImage *localImage = nil;
    if (drawImage) {
        localImage = drawImage();
    }
    if (!localImage) {
        return nil;
    }
    NSData *resultData = UIImagePNGRepresentation(localImage);
    if (!resultData) {
        return nil;
    }
    [manager.imageCache storeImage:localImage imageData:resultData forKey:imageNameKey toDisk:NO completion:nil];
    return localImage;
}


+ (long long)currentTimestamp {
    return (long long)([[NSDate date] timeIntervalSince1970] * 1000);
}

+ (long long)currentMS {
    return (long long)(CACurrentMediaTime() * 1000);
}

+ (NSString *)appVersion {
    NSDictionary *dic = [NSBundle mainBundle].infoDictionary;
    return (NSString *)[dic objectForKey:@"CFBundleShortVersionString"];
}

+ (NSString *)appName {
    NSDictionary *dic = [NSBundle mainBundle].infoDictionary;
    return (NSString *)[dic objectForKey:@"CFBundleDisplayName"];
}

+ (NSString *)deviceId {
    return [XMIDeviceUUID uniqueDeviceIdentifier];
}

+ (NSString *)idfa {
    return [XMIDeviceUUID advertisingIdentifier];
}

+ (NSString *)deviceModel {
    struct utsname systemInfo;
    uname(&systemInfo);
    NSString *modelName = [NSString stringWithCString:systemInfo.machine encoding:NSUTF8StringEncoding];
    return modelName;
}

+ (NSString *)deviceReadableModel {
    NSString *platform = [self deviceModel];
    if ([platform rangeOfString:@"iPhone"].length >0) {
        // iPhone
        if ([platform isEqualToString:@"iPhone1,1"]) return @"iPhone2G";
        if ([platform isEqualToString:@"iPhone1,2"]) return @"iPhone3G";
        if ([platform isEqualToString:@"iPhone2,1"]) return @"iPhone3GS";
        if ([platform isEqualToString:@"iPhone3,1"]) return @"iPhone4";
        if ([platform isEqualToString:@"iPhone3,2"]) return @"iPhone4";
        if ([platform isEqualToString:@"iPhone3,3"]) return @"iPhone4";
        if ([platform isEqualToString:@"iPhone4,1"]) return @"iPhone4s";
        if ([platform isEqualToString:@"iPhone5,1"]) return @"iPhone5";
        if ([platform isEqualToString:@"iPhone5,2"]) return @"iPhone5";
        if ([platform isEqualToString:@"iPhone5,3"]) return @"iPhone5c";
        if ([platform isEqualToString:@"iPhone5,4"]) return @"iPhone5c";
        if ([platform isEqualToString:@"iPhone6,1"]) return @"iPhone5s";
        if ([platform isEqualToString:@"iPhone6,2"]) return @"iPhone5s";
        if ([platform isEqualToString:@"iPhone7,2"]) return @"iPhone6";
        if ([platform isEqualToString:@"iPhone7,1"]) return @"iPhone6Plus";
        if ([platform isEqualToString:@"iPhone8,1"]) return @"iPhone6s";
        if ([platform isEqualToString:@"iPhone8,2"]) return @"iPhone6sPlus";
        if ([platform isEqualToString:@"iPhone8,3"]) return @"iPhoneSE";
        if ([platform isEqualToString:@"iPhone8,4"]) return @"iPhoneSE";
        if ([platform isEqualToString:@"iPhone9,1"]) return @"iPhone7";
        if ([platform isEqualToString:@"iPhone9,2"]) return @"iPhone7Plus";
        if ([platform isEqualToString:@"iPhone10,1"]) return @"iPhone8";
        if ([platform isEqualToString:@"iPhone10,4"]) return @"iPhone8";
        if ([platform isEqualToString:@"iPhone10,2"]) return @"iPhone8Plus";
        if ([platform isEqualToString:@"iPhone10,5"]) return @"iPhone8Plus";
        if ([platform isEqualToString:@"iPhone10,3"]) return @"iPhoneX";
        if ([platform isEqualToString:@"iPhone10,6"]) return @"iPhoneX";
        if ([platform isEqualToString:@"iPhone11,8"]) return @"iPhoneXR";
        if ([platform isEqualToString:@"iPhone11,2"]) return @"iPhoneXS";
        if ([platform isEqualToString:@"iPhone11,4"]) return @"iPhoneXS Max";
        if ([platform isEqualToString:@"iPhone11,6"]) return @"iPhoneXS Max";
        if ([platform isEqualToString:@"iPhone12,1"]) return @"iPhone 11";
        if ([platform isEqualToString:@"iPhone12,3"]) return @"iPhone 11 Pro";
        if ([platform isEqualToString:@"iPhone12,5"]) return @"iPhone 11 Pro Max";
        if ([platform isEqualToString:@"iPhone12,8"]) return @"iPhoneSE2"; //iPhone SE (2 Gen)
        if ([platform isEqualToString:@"iPhone13,1"]) return @"iPhone 12 mini";
        if ([platform isEqualToString:@"iPhone13,2"]) return @"iPhone 12";
        if ([platform isEqualToString:@"iPhone13,3"]) return @"iPhone 12 Pro";
        if ([platform isEqualToString:@"iPhone13,4"]) return @"iPhone 12 Pro Max";
        if ([platform isEqualToString:@"iPhone14,4"]) return @"iPhone 13 mini";
        if ([platform isEqualToString:@"iPhone14,5"]) return @"iPhone 13";
        if ([platform isEqualToString:@"iPhone14,2"]) return @"iPhone 13 Pro";
        if ([platform isEqualToString:@"iPhone14,3"]) return @"iPhone 13 Pro Max";
        if ([platform isEqualToString:@"iPhone14,6"]) return @"iPhone SE 3rd Gen";
        if ([platform isEqualToString:@"iPhone14,7"]) return @"iPhone 14";
        if ([platform isEqualToString:@"iPhone14,8"]) return @"iPhone 14 Plus";
        if ([platform isEqualToString:@"iPhone15,2"]) return @"iPhone 14 Pro";
        if ([platform isEqualToString:@"iPhone15,3"]) return @"iPhone 14 Pro Max";
    }
    else if ([platform rangeOfString:@"iPad"].length >0) {
        //iPad
        if ([platform isEqualToString:@"iPad1,1"])   return @"iPad";
        if ([platform isEqualToString:@"iPad2,1"])   return @"iPad2";
        if ([platform isEqualToString:@"iPad2,2"])   return @"iPad2";
        if ([platform isEqualToString:@"iPad2,3"])   return @"iPad2";
        if ([platform isEqualToString:@"iPad2,4"])   return @"iPad2";
        if ([platform isEqualToString:@"iPad3,1"])   return @"iPad3";
        if ([platform isEqualToString:@"iPad3,2"])   return @"iPad3";
        if ([platform isEqualToString:@"iPad3,3"])   return @"iPad3";
        if ([platform isEqualToString:@"iPad3,4"])   return @"iPad4";
        if ([platform isEqualToString:@"iPad3,5"])   return @"iPad4";
        if ([platform isEqualToString:@"iPad3,6"])   return @"iPad4";
        if ([platform isEqualToString:@"iPad6,11"])  return @"iPad5";
        if ([platform isEqualToString:@"iPad6,12"])  return @"iPad5";
        if ([platform isEqualToString:@"iPad7,5"])   return @"iPad6";
        if ([platform isEqualToString:@"iPad7,6"])   return @"iPad6";
        if ([platform isEqualToString:@"iPad7,11"])  return @"iPad7";
        if ([platform isEqualToString:@"iPad7,12"])  return @"iPad7";
        if ([platform isEqualToString:@"iPad11,6"])  return @"iPad8";
        if ([platform isEqualToString:@"iPad11,7"])  return @"iPad8";
        if ([platform isEqualToString:@"iPad12,1"])   return @"iPad9";
        if ([platform isEqualToString:@"iPad12,2"])   return @"iPad9";

        //iPad Air
        if ([platform isEqualToString:@"iPad4,1"])   return @"iPadAir";
        if ([platform isEqualToString:@"iPad4,2"])   return @"iPadAir";
        if ([platform isEqualToString:@"iPad4,3"])   return @"iPadAir";
        if ([platform isEqualToString:@"iPad5,3"])   return @"iPadAir2";
        if ([platform isEqualToString:@"iPad5,4"])   return @"iPadAir2";
        if ([platform isEqualToString:@"iPad11,3"])  return @"iPadAir3";
        if ([platform isEqualToString:@"iPad11,4"])  return @"iPadAir3";
        if ([platform isEqualToString:@"iPad13,1"])  return @"iPadAir4";
        if ([platform isEqualToString:@"iPad13,2"])  return @"iPadAir4";
        if ([platform isEqualToString:@"iPad13,16"])  return @"iPadAir5";
        if ([platform isEqualToString:@"iPad13,17"])  return @"iPadAir5(4G)";
        
        //iPad Pro
        if ([platform isEqualToString:@"iPad6,3"])   return @"iPadPro(9.7-inch)";
        if ([platform isEqualToString:@"iPad6,4"])   return @"iPadPro(9.7-inch)";
        if ([platform isEqualToString:@"iPad6,7"])   return @"iPadPro(12.9-inch)";
        if ([platform isEqualToString:@"iPad6,8"])   return @"iPadPro(12.9-inch)";
        if ([platform isEqualToString:@"iPad7,1"])   return @"iPadPro(12.9-inch)2";
        if ([platform isEqualToString:@"iPad7,2"])   return @"iPadPro(12.9-inch)2";
        if ([platform isEqualToString:@"iPad7,3"])   return @"iPadPro(10.5-inch)";
        if ([platform isEqualToString:@"iPad7,4"])   return @"iPadPro(10.5-inch)";
        if ([platform isEqualToString:@"iPad8,1"])   return @"iPadPro(11-inch)";
        if ([platform isEqualToString:@"iPad8,2"])   return @"iPadPro(11-inch)";
        if ([platform isEqualToString:@"iPad8,3"])   return @"iPadPro(11-inch)";
        if ([platform isEqualToString:@"iPad8,4"])   return @"iPadPro(11-inch)";
        if ([platform isEqualToString:@"iPad8,5"])   return @"iPadPro(12.9-inch)3";
        if ([platform isEqualToString:@"iPad8,6"])   return @"iPadPro(12.9-inch)3";
        if ([platform isEqualToString:@"iPad8,7"])   return @"iPadPro(12.9-inch)3";
        if ([platform isEqualToString:@"iPad8,8"])   return @"iPadPro(12.9-inch)3";
        
        if ([platform isEqualToString:@"iPad8,9"])   return @"iPadPro(11-inch)2";
        if ([platform isEqualToString:@"iPad8,10"])  return @"iPadPro(11-inch)2";
        if ([platform isEqualToString:@"iPad8,11"])  return @"iPadPro(12.9-inch)4";
        if ([platform isEqualToString:@"iPad8,12"])  return @"iPadPro(12.9-inch)4";
        
        if ([platform isEqualToString:@"iPad13,4"])   return @"iPadPro(11-inch)3";
        if ([platform isEqualToString:@"iPad13,5"])   return @"iPadPro(11-inch)3";
        if ([platform isEqualToString:@"iPad13,6"])   return @"iPadPro(11-inch)3";
        if ([platform isEqualToString:@"iPad13,7"])   return @"iPadPro(11-inch)3";
        
        if ([platform isEqualToString:@"iPad13,8"])   return @"iPadPro(12.9-inch)5";
        if ([platform isEqualToString:@"iPad13,9"])   return @"iPadPro(12.9-inch)5";
        if ([platform isEqualToString:@"iPad13,10"])  return @"iPadPro(12.9-inch)5";
        if ([platform isEqualToString:@"iPad13,11"])  return @"iPadPro(12.9-inch)5";

        //iPad mini
        if ([platform isEqualToString:@"iPad2,5"])   return @"iPadmini1G";
        if ([platform isEqualToString:@"iPad2,6"])   return @"iPadmini1G";
        if ([platform isEqualToString:@"iPad2,7"])   return @"iPadmini1G";
        if ([platform isEqualToString:@"iPad4,4"])   return @"iPadmini2";
        if ([platform isEqualToString:@"iPad4,5"])   return @"iPadmini2";
        if ([platform isEqualToString:@"iPad4,6"])   return @"iPadmini2";
        if ([platform isEqualToString:@"iPad4,7"])   return @"iPadmini3";
        if ([platform isEqualToString:@"iPad4,8"])   return @"iPadmini3";
        if ([platform isEqualToString:@"iPad4,9"])   return @"iPadmini3";
        if ([platform isEqualToString:@"iPad5,1"])   return @"iPadmini4";
        if ([platform isEqualToString:@"iPad5,2"])   return @"iPadmini4";
        if ([platform isEqualToString:@"iPad11,1"])  return @"iPadmini5";
        if ([platform isEqualToString:@"iPad11,2"])  return @"iPadmini5";
        if ([platform isEqualToString:@"iPad14,1"])   return @"iPadmini6";
        if ([platform isEqualToString:@"iPad14,2"])   return @"iPadmini6";
    }
    else if ([platform rangeOfString:@"iPod"].length >0) {
        //iPod Touch
        if ([platform isEqualToString:@"iPod1,1"])   return @"iPodTouch";
        if ([platform isEqualToString:@"iPod2,1"])   return @"iPodTouch2G";
        if ([platform isEqualToString:@"iPod3,1"])   return @"iPodTouch3G";
        if ([platform isEqualToString:@"iPod4,1"])   return @"iPodTouch4G";
        if ([platform isEqualToString:@"iPod5,1"])   return @"iPodTouch5G";
        if ([platform isEqualToString:@"iPod7,1"])   return @"iPodTouch6G";
        if ([platform isEqualToString:@"iPod9,1"])   return @"iPodTouch7G";
    }
    
    if ([platform isEqualToString:@"i386"])      return @"iPhoneSimulator";
    if ([platform isEqualToString:@"x86_64"])    return @"iPhoneSimulator";
    
    return platform;
}

+ (NSString *)deviceType {
    NSString *type = @"iPhone";
    NSString *model = [[UIDevice currentDevice] model];
    if ([[model lowercaseString] rangeOfString:@"ipad"].location != NSNotFound) {
        type = @"iPad";
    }
    return type;
}

+ (NSString *)deviceName {
    return [[UIDevice currentDevice] name];
}

+ (NSString *)deviceOperator {
    CTTelephonyNetworkInfo *telephonyInfo = [[CTTelephonyNetworkInfo alloc] init];
    CTCarrier *carrier = [telephonyInfo subscriberCellularProvider];
    NSString *currentCountryCode = [carrier mobileCountryCode];
    NSString *mobileNetWorkCode = [carrier mobileNetworkCode];
    
    if (![currentCountryCode isEqualToString:@"460"]) {
        return @"未知";
    }
    
    // 参考 https://en.wikipedia.org/wiki/Mobile_country_code
    if ([mobileNetWorkCode isEqualToString:@"00"] ||
        [mobileNetWorkCode isEqualToString:@"02"] ||
        [mobileNetWorkCode isEqualToString:@"07"]) {
        // 中国移动
        return kChinaMobile;
    }
    else if ([mobileNetWorkCode isEqualToString:@"01"] ||
             [mobileNetWorkCode isEqualToString:@"06"] ||
             [mobileNetWorkCode isEqualToString:@"09"]) {
        // 中国联通
        return kChinaUnicom;
    }
    else if ([mobileNetWorkCode isEqualToString:@"03"] ||
             [mobileNetWorkCode isEqualToString:@"05"] ||
             [mobileNetWorkCode isEqualToString:@"11"]) {
        // 中国电信
        return kChinaTelecom;
    }
    else if ([mobileNetWorkCode isEqualToString:@"20"]) {
        // 中国铁通
        return kChinaTietong;
    }
    
    return @"未知";
}

+ (CGSize)deviceResolution {
    CGRect rect = [UIScreen mainScreen].bounds;
    CGFloat scale = [UIScreen mainScreen].scale;
    return CGSizeMake(rect.size.width * scale, rect.size.height * scale);
}

+ (NSString *)bundleId {
    return [NSBundle mainBundle].bundleIdentifier;
}

+ (NSString *)systemVersion {
    return [[UIDevice currentDevice] systemVersion];
}

+ (NSString *)systemName {
    return [[UIDevice currentDevice] systemName];
}

+ (NSString *)channel {
    return @"ios-b1";
}

+ (BOOL)isJailBreak {
    // 三个路径 base64处理规避审核风险
    // L0xpYnJhcnkvTW9iaWxlU3Vic3RyYXRlL01vYmlsZVN1YnN0cmF0ZS5keWxpYg==
    // L0FwcGxpY2F0aW9ucy9DeWRpYS5hcHA=
    // L3Zhci9saWIvY3lkaWE=
    NSString *encodePath1 = @"L0xpYnJhcnkvTW9iaWxlU3Vic3RyYXRlL01vYmlsZVN1YnN0cmF0ZS5keWxpYg==";
    NSData *data1 = [[NSData alloc] initWithBase64EncodedString:encodePath1 options:0];
    NSString *path1 = [[NSString alloc] initWithData:data1 encoding:NSUTF8StringEncoding];
    NSString *encodePath2 = @"L0FwcGxpY2F0aW9ucy9DeWRpYS5hcHA=";
    NSData *data2 = [[NSData alloc] initWithBase64EncodedString:encodePath2 options:0];
    NSString *path2 = [[NSString alloc] initWithData:data2 encoding:NSUTF8StringEncoding];
    NSString *encodePath3 = @"L3Zhci9saWIvY3lkaWE=";
    NSData *data3 = [[NSData alloc] initWithBase64EncodedString:encodePath3 options:0];
    NSString *path3 = [[NSString alloc] initWithData:data3 encoding:NSUTF8StringEncoding];
    if ([[NSFileManager defaultManager] fileExistsAtPath:path1]
        || [[NSFileManager defaultManager] fileExistsAtPath:path2]
        || [[NSFileManager defaultManager] fileExistsAtPath:path3]
        ) {
        return YES;
    }
    
    return NO;
}

+ (NSString *)macaddress {
    int                 mib[6];
    size_t              len;
    char                *buf;
    unsigned char       *ptr;
    struct if_msghdr    *ifm;
    struct sockaddr_dl  *sdl;
    
    mib[0] = CTL_NET;
    mib[1] = AF_ROUTE;
    mib[2] = 0;
    mib[3] = AF_LINK;
    mib[4] = NET_RT_IFLIST;
    
    if ((mib[5] = if_nametoindex("en0")) == 0) {
        printf("Error: if_nametoindex error\n");
        return NULL;
    }
    
    if (sysctl(mib, 6, NULL, &len, NULL, 0) < 0) {
        printf("Error: sysctl, take 1\n");
        return NULL;
    }
    
    if ((buf = malloc(len)) == NULL) {
        printf("Could not allocate memory. error!\n");
        return NULL;
    }
    
    if (sysctl(mib, 6, buf, &len, NULL, 0) < 0) {
        printf("Error: sysctl, take 2");
        free(buf);
        return NULL;
    }
    
    ifm = (struct if_msghdr *)buf;
    sdl = (struct sockaddr_dl *)(ifm + 1);
    ptr = (unsigned char *)LLADDR(sdl);
    NSString *outstring = [NSString stringWithFormat:@"%02X:%02X:%02X:%02X:%02X:%02X",
                           *ptr, *(ptr+1), *(ptr+2), *(ptr+3), *(ptr+4), *(ptr+5)];
    free(buf);
    
    return outstring;
}

+ (NSString *)UUID
{
    CFUUIDRef puuid = CFUUIDCreate(nil);
    CFStringRef uuidString = CFUUIDCreateString(nil, puuid);
    NSString *result = (NSString *)CFBridgingRelease(CFStringCreateCopy(NULL, uuidString));
    if (puuid) {
        CFRelease(puuid);
    }
    if (uuidString) {
        CFRelease(uuidString);
    }
    return result;
}

+ (BOOL)isDeviceIPad
{
    return (UI_USER_INTERFACE_IDIOM()==UIUserInterfaceIdiomPad);
}

+ (NSString *)getIPV6String:(BOOL)shouldEncode
{
    NSDictionary *dic = [self getAllIPV6];
    if (!dic || dic.allKeys.count == 0) {
        return @"";
    }
    NSString *str = [[NSString alloc] initWithData:[NSJSONSerialization dataWithJSONObject:dic options:0 error:NULL] encoding:NSUTF8StringEncoding];
    if (shouldEncode) {
        str = [str base64EncodedString];
        str = [str stringWithURLEncoded];
    }
    return str;
}

+ (NSDictionary<NSString *, NSArray<NSString *> *> *)getAllIPV6 {
    struct ifaddrs *interfaceList = nil;
    if (getifaddrs(&interfaceList) != 0 || interfaceList == nil) {
    return nil;
    }
    NSSet<NSString *> *interfaceWhiteList = [NSSet setWithObjects:@"en0", @"pdp_ip0", @"pdp_ip1", nil];
    NSMutableDictionary *result = [NSMutableDictionary dictionary];
    struct ifaddrs *interface = interfaceList;
    char addrBuf[INET6_ADDRSTRLEN];
    do {
    if (!(interface->ifa_flags & IFF_UP)) {
    continue;
    }
    NSString *name = [NSString stringWithUTF8String:interface->ifa_name];
    if (![interfaceWhiteList containsObject:name]) {
    continue;
    }
    const struct sockaddr_in6 *addr = (const struct sockaddr_in6 *)interface->ifa_addr;
    if (!addr || addr->sin6_family != AF_INET6) {
    continue;
    }
    if (inet_ntop(AF_INET6, &addr->sin6_addr, addrBuf, INET6_ADDRSTRLEN) && strncmp("fe80::",
   addrBuf, 6) != 0) {
    NSMutableArray<NSString *> *ipList = [result objectForKey:name];
    if (ipList == nil) {
    ipList = [NSMutableArray array];
    [result setObject:ipList forKey:name];
    }
    [ipList addObject:[NSString stringWithUTF8String:addrBuf]];
    }
    } while((interface = interface->ifa_next));
    freeifaddrs(interfaceList);
    return result;
}


+ (NSString *)getOsUpdateTime {
    NSString *enCodePath = @"L3Zhci9tb2JpbGUvTGlicmFyeS9Vc2VyQ29uZmlndXJhdGlvblByb2ZpbGVzL1B1YmxpY0luZm8vTUNNZXRhLnBsaXN0";
    NSString *timeString = [self fileCreateTimeWithEncodePath:enCodePath];
    if (timeString == nil) {
        timeString = @"0.0";
    }
    return timeString ? timeString : @"";
}

+ (NSString *)fileCreateTimeWithEncodePath:(NSString *)encodeStr {
    NSString *result = nil;
    NSData *data = [[NSData alloc] initWithBase64EncodedString:encodeStr options:0];
    NSString *dataString = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    NSError *error = nil;
    NSDictionary *fileAttributes = [[NSFileManager defaultManager] attributesOfItemAtPath:dataString error:&error];
    if (fileAttributes) {
        id singleAttibute = [fileAttributes objectForKey:NSFileCreationDate];
        if ([singleAttibute isKindOfClass:[NSDate class]]) {
            NSDate *dataDate = singleAttibute;
            result = [NSString stringWithFormat:@"%f",[dataDate timeIntervalSince1970]];
        }
    }
    return result;
}

+ (NSBundle *)adBundle {
    NSString *bundlePath = [[NSBundle bundleForClass:[self class]] pathForResource:@"XMAd" ofType:@"bundle"];
    NSBundle *adBundle = [NSBundle bundleWithPath:bundlePath];
    return adBundle;
}

static NSString *getFileTime() {
    struct stat info;
    int result = stat("/var/mobile", &info);
    if (result != 0) {
        return @"";
    }
    struct timespec time = info.st_birthtimespec;
    return [NSString stringWithFormat:@"%ld.%09ld", time.tv_sec, time.tv_nsec];
}

+ (NSString *)deviceInitializationTime {
    return getFileTime();
}
@end
