//
//  UIColor+XMICommon.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/11/2.
//

#import "UIColor+XMICommon.h"

@implementation UIColor (XMICommon)

// 原始颜色方法
+ (UIColor *)xmi_colorWithRGBHex:(UInt32)hex andAlpha:(CGFloat)alpha
{
    int r = (hex >> 16) & 0xFF;
    int g = (hex >> 8) & 0xFF;
    int b = (hex) & 0xFF;
    
    return [UIColor colorWithRed:r / 255.0f
                           green:g / 255.0f
                            blue:b / 255.0f
                           alpha:alpha];
}

+(UIColor *)xmi_normalColor:(UInt32)normalHex
               normalAlpha:(CGFloat)normalAlpha
                 darkColor:(UInt32)darkHex
                 darkAlpha:(CGFloat)darkAlpha{
#ifdef __IPHONE_13_0
    if (@available(iOS 13.0, *)) {
        return [UIColor colorWithDynamicProvider:^UIColor * _Nonnull(UITraitCollection * _Nonnull trait) {
            if (trait.userInterfaceStyle == UIUserInterfaceStyleDark){
                return [UIColor xmi_colorWithRGBHex:darkHex andAlpha:darkAlpha];
            }
            else {
                return [UIColor xmi_colorWithRGBHex:normalHex andAlpha:normalAlpha];
            }
        }];
    }
    else {
        return [UIColor xmi_colorWithRGBHex:normalHex andAlpha:normalAlpha];
    }
#else
    return [UIColor xmi_colorWithRGBHex:normalHex andAlpha:normalAlpha];
#endif
}

+(UIColor *)xmi_normalColor:(UInt32)normalHex
                 darkColor:(UInt32)darkHex{
    return [UIColor xmi_normalColor:normalHex normalAlpha:1 darkColor:darkHex darkAlpha:1];
}

@end
