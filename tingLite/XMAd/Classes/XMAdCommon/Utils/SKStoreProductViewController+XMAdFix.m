//
//  SKStoreProductViewController+XMAdFix.m
//  XMAd
//
//  Created by cuiyuanzhe on 2022/7/12.
//

#import "SKStoreProductViewController+XMAdFix.h"
#import <objc/runtime.h>

@implementation SKStoreProductViewController (XMAdFix)

+ (void)load
{
    Class class = [self class];
    Method method = class_getInstanceMethod(class, @selector(sceneDisconnected:));
    if (method == NULL) {
        IMP imp = imp_implementationWithBlock(^{});
        class_addMethod(class, @selector(sceneDisconnected:), imp, "v@:");
    }
}

- (void)appWillTerminate
{
    
}

@end
