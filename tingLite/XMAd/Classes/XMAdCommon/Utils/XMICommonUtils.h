//
//  XMICommonUtils.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/16.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XMICommonUtils : NSObject

/**
 加载广告bundle中的图片
 */
+ (UIImage *)imageNamed:(NSString *)name;

/*
 绘制并缓存图片
 */
+ (UIImage *)imageFromCacheNamed:(NSString *)name drawing:(UIImage *(^)(void))drawImage;

/**
 时间戳
 */
+ (long long)currentTimestamp;

/**
 当前MediaTime，用于计算耗时差值
 */
+ (long long)currentMS;

/**
 app版本
 */
+ (NSString *)appVersion;
/**
 app名称
 */
+ (NSString *)appName;

// ------------- 设备信息相关 ---------------
/**
 设备唯一id
 */
+ (NSString *)deviceId;
/**
 idfa
 */
+ (NSString *)idfa;
/**
 设备型号
 形如 iPhone10,3
 */
+ (NSString *)deviceModel;
/**
 设备型号
 形如 iPhoneX、iPhone12 Max
 */
+ (NSString *)deviceReadableModel;
/**
 设备类型
 形如 iPhone、iPad
 */
+ (NSString *)deviceType;
/**
 设备名
 形如 我的iPhone
 */
+ (NSString *)deviceName;
/**
 运营商 移动/联调/电信/未知
 */
+ (NSString *)deviceOperator;
/**
 设备分辨率
 */
+ (CGSize)deviceResolution;
/**
 包名
 */
+ (NSString *)bundleId;
/**
 系统版本 如14.1
 */
+ (NSString *)systemVersion;
/**
 系统名称 如iOS
 */
+ (NSString *)systemName;
/**
 渠道
 */
+ (NSString *)channel;
/**
 是否越狱
 */
+ (BOOL)isJailBreak;
/**
 mac地址
 */
+ (NSString *)macaddress;

// -----------------------------------------

+ (NSString *)UUID;

+ (BOOL)isDeviceIPad;

+ (NSString *)getIPV6String:(BOOL)shouldEncode;

+ (NSString *)getOsUpdateTime;

+ (NSBundle *)adBundle;

+ (NSString *)deviceInitializationTime;
@end

NS_ASSUME_NONNULL_END
