//
//  NSData+XMIUtils.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/9/8.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSData (XMIUtils)

/**
 AES加解密
 */
+ (NSData *)xmi_AES256EncryptWithKey:(NSString *)key EncryptData:(NSData *)data;
+ (NSData *)xmi_AES256DecryptWithKey:(NSString *)key DecryptData:(NSData *)data;
+ (NSData *)xmi_AES256EncryptWithKeyData:(NSData *)keyData EncryptData:(NSData *)data;
+ (NSData *)xmi_AES256DecryptWithKeyData:(NSData *)keyData DecryptData:(NSData *)data;

+(NSData*)xmi_gzipData: (NSData*)pUncompressedData;

@end

NS_ASSUME_NONNULL_END
