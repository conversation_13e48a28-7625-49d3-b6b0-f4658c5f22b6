//
//  XMINetDataTask.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/2.
//

#import "XMINetDataTask.h"
#import <XMNetworkRequest/XMNRequest.h>
#import "XMIAdNativeLogger.h"

@interface XMINetDataTask ()

@end

@implementation XMINetDataTask

+ (instancetype)getTaskWithUrl:(NSString *)urlString
                    parameters:(nullable NSDictionary *)parameters
             completionHandler:(nullable void (^)(NSURLResponse *response, id _Nullable responseObject, NSError * _Nullable error))completionHandler {
    XMNRequest *xmRequest = [XMNRequest requestWithServerUrl:urlString path:nil parameters:parameters method:XMRequestMethodGet completionHandler:^(XMBaseRequest * _Nonnull request, BOOL success) {
        if (completionHandler == nil) {
            return;
        }
        completionHandler(request.sessonTask.response, request.httpResponseObject, success ? nil : request.error);
    }];
    xmRequest.hasCommonHeader = NO;
    xmRequest.errorHint = NO;
    XMINetDataTask *task = [[self alloc] init];
    task.innerRequest = (id<XMIRequestProtocol>)xmRequest;
    XMILogNativeNetworkInfo(@"getTaskInited, requestID:%@, url:%@, method:get, params:%@", xmRequest.requestID, urlString, parameters);
    return task;
}

+ (instancetype)postTaskWithUrl:(NSString *)urlString
                     parameters:(nullable NSDictionary *)parameters
              completionHandler:(nullable void (^)(NSURLResponse *response, id _Nullable responseObject, NSError * _Nullable error))completionHandler {
    XMNRequest *xmRequest = [XMNRequest requestWithServerUrl:urlString path:nil parameters:parameters method:XMRequestMethodPost completionHandler:^(XMBaseRequest * _Nonnull request, BOOL success) {
        if (completionHandler == nil) {
            return;
        }
        completionHandler(request.sessonTask.response, request.httpResponseObject, success ? nil : request.error);
    }];
    xmRequest.hasCommonHeader = NO;
    xmRequest.errorHint = NO;
    XMINetDataTask *task = [[self alloc] init];
    task.innerRequest = (id<XMIRequestProtocol>)xmRequest;
    XMILogNativeNetworkInfo(@"postTaskInited, requestID:%@, url:%@, method:post, params:%@", xmRequest.requestID, urlString, parameters);
    return task;
}

+ (instancetype)dataTaskWithRequest:(NSURLRequest *)request
                  completionHandler:(nullable void (^)(NSURLResponse *response, id _Nullable responseObject, NSError * _Nullable error))completionHandler {
    NSMutableURLRequest *urlRequest = [request mutableCopy];
    XMNRequest *xmRequest = [XMNRequest requestWithCustomURLRequest:urlRequest completionHandler:^(XMNRequest * _Nonnull request, BOOL success) {
        if (completionHandler == nil) {
            return;
        }
        completionHandler(request.sessonTask.response, request.httpResponseObject, success ? nil : request.error);
        if (success) {
            XMILogNativeNetworkInfo(@"DataTaskSuccess, requestID:%@, responsCode:%zd, responseBody:%@",request.requestID, [(NSHTTPURLResponse *)request.sessonTask.response statusCode], request.httpResponseRawString);
            if (!request.httpResponseObject) {
                XMILogNativeNetworkWarning(@"DataTaskSuccessWithEmptyResponseObject, requestID:%@, URL:%@, responsCode:%zd,",request.requestID, request.sessonTask.response.URL.absoluteString, [(NSHTTPURLResponse *)request.sessonTask.response statusCode]);
            }
        } else {
            XMILogNativeNetworkError(@"DataTaskFailed, requestID:%@, URL:%@, responsCode:%zd, responseBody:%@, errorCode:%zd, errorDesc:%@",request.requestID, request.sessonTask.response.URL.absoluteString,[(NSHTTPURLResponse *)request.sessonTask.response statusCode], request.httpResponseRawString, request.error.code, request.error.localizedDescription);
        }
    }];
    xmRequest.hasCommonHeader = NO;
    xmRequest.errorHint = NO;
    XMINetDataTask *task = [[self alloc] init];
    task.innerRequest = (id<XMIRequestProtocol>)xmRequest;
    XMILogNativeNetworkInfo(@"DataTaskInited, requestID:%@, url:%@, header:%@, method:%@, body:%@", xmRequest.requestID, request.URL, request.allHTTPHeaderFields ? [[NSString alloc] initWithData:[NSJSONSerialization dataWithJSONObject:request.allHTTPHeaderFields options:0 error:NULL] encoding:NSUTF8StringEncoding] : @"", request.HTTPMethod, [[NSString alloc] initWithData:request.HTTPBody encoding:NSUTF8StringEncoding]);
    return task;
}

- (void)start {
    XMILogNativeNetworkInfo(@"XMINetDataTaskStart, requestID:%@", self.innerRequest.requestID);
    [self.innerRequest start];
}

- (void)stop {
    XMILogNativeNetworkInfo(@"XMINetDataTaskStop, requestID:%@", self.innerRequest.requestID);
    [self.innerRequest stop];
}

@end
