//
//  XMINetManager.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/2.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class XMINetDataTask;
@class XMIUploadTask;
@class XMIDownloadTask;

@interface XMINetManager : NSObject

/**
 GET
 */
+ (XMINetDataTask *)getTaskWithUrl:(NSString *)urlString
                        parameters:(nullable NSDictionary *)parameters
                 completionHandler:(nullable void (^)(NSURLResponse *response, id _Nullable responseObject, NSError * _Nullable error))completionHandler;
/**
 POST
 */
+ (XMINetDataTask *)postTaskWithUrl:(NSString *)urlString
                         parameters:(nullable NSDictionary *)parameters
                  completionHandler:(nullable void (^)(NSURLResponse *response, id _Nullable responseObject, NSError * _Nullable error))completionHandler;
+ (XMINetDataTask *)dataTaskWithRequest:(NSURLRequest *)request
                      completionHandler:(nullable void (^)(NSURLResponse *response, id _Nullable responseObject, NSError * _Nullable error))completionHandler;

/**
 下载
 */
+ (XMIDownloadTask *)downloadTaskWithUrl:(NSString *)urlString
                                savePath:(nullable NSString *)path
                                progress:(nullable void (^)(CGFloat progress))progressBlock
                       completionHandler:(nullable void (^)(NSURLResponse *response, NSURL * _Nullable filePath, NSError * _Nullable error))completionHandler;

/**
 上传
 */
+ (XMIUploadTask *)uploadTaskWithUrl:(NSString *)urlString
                            fromFile:(NSURL *)fileURL
                            progress:(nullable void (^)(CGFloat progress))progressBlock
                   completionHandler:(nullable void (^)(NSURLResponse *response, id _Nullable responseObject, NSError * _Nullable error))completionHandler;
+ (XMIUploadTask *)uploadTaskWithUrl:(NSString *)urlString
                            fromData:(NSData *)fileData
                            progress:(nullable void (^)(CGFloat progress))progressBlock
                   completionHandler:(nullable void (^)(NSURLResponse *response, id _Nullable responseObject, NSError * _Nullable error))completionHandler;

/**
 创建post json request
 */
+ (NSMutableURLRequest *)createRequestWithUrl:(NSString *)url
                                   parameters:(nullable NSDictionary *)paramters
                                      headers:(nullable NSDictionary *)headers
                                        error:(NSError *_Nullable __autoreleasing*)error;

/**
 创建get请求
 */
+ (NSMutableURLRequest *)createGetRequestWithUrl:(NSString *)url
                                      parameters:(nullable NSDictionary *)paramters
                                         headers:(nullable NSDictionary *)headers
                                           error:(NSError *_Nullable __autoreleasing*)error;

@end

NS_ASSUME_NONNULL_END
