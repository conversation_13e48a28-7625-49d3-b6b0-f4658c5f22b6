//
//  XMINetManager.m
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/2.
//

#import "XMINetManager.h"
#import "XMINetDataTask.h"
#import <AFNetworking/AFURLRequestSerialization.h>
#import "NSData+XMIUtils.h"
#import "XMIAdDataCenter.h"

@implementation XMINetManager

+ (XMINetDataTask *)getTaskWithUrl:(NSString *)urlString parameters:(nullable NSDictionary *)parameters completionHandler:(nullable void (^)(NSURLResponse *response, id responseObject, NSError *error))completionHandler {
    return [XMINetDataTask getTaskWithUrl:urlString parameters:parameters completionHandler:completionHandler];
}

+ (XMINetDataTask *)postTaskWithUrl:(NSString *)urlString
                         parameters:(nullable NSDictionary *)parameters
                  completionHandler:(nullable void (^)(NSURLResponse *response, id _Nullable responseObject, NSError * _Nullable error))completionHandler {
    return [XMINetDataTask postTaskWithUrl:urlString parameters:parameters completionHandler:completionHandler];
}

+ (XMINetDataTask *)dataTaskWithRequest:(NSURLRequest *)request
                      completionHandler:(nullable void (^)(NSURLResponse *response, id _Nullable responseObject, NSError * _Nullable error))completionHandler {
    return [XMINetDataTask dataTaskWithRequest:request completionHandler:completionHandler];
}

+ (XMIDownloadTask *)downloadTaskWithUrl:(NSString *)urlString savePath:(NSString *)path progress:(void (^)(CGFloat))progressBlock completionHandler:(void (^)(NSURLResponse * _Nonnull, NSURL * _Nullable, NSError * _Nullable))completionHandler {
    return nil;
}

+ (XMIUploadTask *)uploadTaskWithUrl:(NSString *)urlString fromFile:(NSURL *)fileURL progress:(void (^)(CGFloat))progressBlock completionHandler:(void (^)(NSURLResponse * _Nonnull, id _Nullable, NSError * _Nullable))completionHandler {
    return nil;
}

+ (XMIUploadTask *)uploadTaskWithUrl:(NSString *)urlString fromData:(NSData *)fileData progress:(void (^)(CGFloat))progressBlock completionHandler:(void (^)(NSURLResponse * _Nonnull, id _Nullable, NSError * _Nullable))completionHandler {
    return nil;
}

+ (NSMutableURLRequest *)createRequestWithUrl:(NSString *)url parameters:(nullable NSDictionary *)paramters headers:(nullable NSDictionary *)headers error:(NSError * _Nullable __autoreleasing *)error {
    NSMutableURLRequest *request = nil;
    if ([self shouldCompressHeader:url]) {
        NSMutableDictionary *headersInBody = [NSMutableDictionary dictionary];
        NSMutableDictionary *headersCopy = [headers mutableCopy];
        NSArray *keys = @[@"xmTraceId", @"Cookie"];
        for (NSString *key in keys) {
            if ([headers objectForKey:key]) {
                [headersInBody setObject:[headers objectForKey:key] forKey:key];
            }
            [headersCopy removeObjectForKey:key];
        }
        headersCopy[@"Content-Encoding"] = @"gzip";
        headersCopy[@"Content-Type"] = @"application/json";
        headers = [headersCopy copy];
        NSMutableDictionary *mParamters = [NSMutableDictionary dictionary];
        if (paramters) {
            mParamters[@"body"] = paramters;
        }
        mParamters[@"headers"] = headersInBody;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:mParamters options:0 error:NULL];
        request = [[AFJSONRequestSerializer serializer] requestWithMethod:@"POST" URLString:url parameters:nil error:error];
        request.HTTPBody = [NSData xmi_gzipData:jsonData];
        request.HTTPShouldHandleCookies = NO;
    } else {
        request = [[AFJSONRequestSerializer serializer] requestWithMethod:@"POST" URLString:url parameters:paramters error:error];
    }
    
    if (headers.count > 0) {
        [headers enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, id  _Nonnull obj, BOOL * _Nonnull stop) {
            [request setValue:(NSString *)obj forHTTPHeaderField:(NSString *)key];
        }];
    }
    return request;
}

+ (NSMutableURLRequest *)createGetRequestWithUrl:(NSString *)url parameters:(nullable NSDictionary *)paramters headers:(nullable NSDictionary *)headers error:(NSError * _Nullable __autoreleasing *)error {
    NSMutableURLRequest *request = [[AFHTTPRequestSerializer serializer] requestWithMethod:@"GET" URLString:url parameters:paramters error:error];
    if (headers.count > 0) {
        [headers enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, id  _Nonnull obj, BOOL * _Nonnull stop) {
            [request setValue:(NSString *)obj forHTTPHeaderField:(NSString *)key];
        }];
    }
    return request;
}

+ (BOOL)shouldCompressHeader:(NSString *)url
{
    if (![XMIAdDataCenter shouldCompressHeader]) {
        return NO;
    }
    NSString *path = [NSURL URLWithString:url].path;
    return [path hasPrefix:@"/adx/ad"] || [path hasPrefix:@"/api/v1/adRealTime"];
}

@end
