//
//  XMINetTask.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/2.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger , XMISerializerType) {
    XMISerializerTypeHTTP = 0,
    XMISerializerTypeJSON = 1,
};

@protocol XMIRequestProtocol;

@interface XMINetTask : NSObject

@property (nonatomic, strong) id<XMIRequestProtocol> innerRequest;

- (void)start;

- (void)stop;

@end

@protocol XMIRequestProtocol <NSObject>

/**
 *  request timeout, default is 10s
 */
@property (nonatomic, assign) NSTimeInterval timeoutSeconds;
/**
 *  最大重试次数，默认为6次
 */
@property (nonatomic, assign) NSUInteger maxRetryCount;
/**
 默认json
 */
@property (nonatomic, assign) XMISerializerType requestSerializerType;
/**
 默认json
 */
@property (nonatomic, assign) XMISerializerType responseSerializerType;

- (NSString *)requestID;

- (void)start;

- (void)stop;

@end

NS_ASSUME_NONNULL_END
