//
//  XMINetDataTask.h
//  XMAd
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/2.
//

#import "XMINetTask.h"

NS_ASSUME_NONNULL_BEGIN

@interface XMINetDataTask : XMINetTask

/**
 GET请求
 */
+ (instancetype)getTaskWithUrl:(NSString *)urlString
                    parameters:(nullable NSDictionary *)parameters
             completionHandler:(nullable void (^)(NSURLResponse *response, id _Nullable responseObject, NSError * _Nullable error))completionHandler;

/**
 POST请求
 */
+ (instancetype)postTaskWithUrl:(NSString *)urlString
                         parameters:(nullable NSDictionary *)parameters
                  completionHandler:(nullable void (^)(NSURLResponse *response, id _Nullable responseObject, NSError * _Nullable error))completionHandler;

/**
 自定义请求，用于自定义header
 */
+ (instancetype)dataTaskWithRequest:(NSURLRequest *)request
                      completionHandler:(nullable void (^)(NSURLResponse *response, id _Nullable responseObject, NSError * _Nullable error))completionHandler;

@end

NS_ASSUME_NONNULL_END
