//
//  XMLAdRequestManager.swift
//  tingLite
//
//  Created by yangle<PERSON> on 2020/2/12.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import BaseModule

// ADX请求管理类
public class XMLAdRequestManager: NSObject {
    // 单例
    public static func shared() -> XMLAdRequestManager { return instance }
    // 唯一对象
    private static let instance = XMLAdRequestManager()
    // ADX请求池
    private var pools: [XMLAdRequest] = []
    
    // 创建并持有请求
    private func saftyRequest() -> XMLAdRequest {
        let request: XMLAdRequest = XMLAdRequest()
        self.pools.append(request)
        return request
    }
    
    // 移除指定请求
    private func remove(_ request: XMLAdRequest?) {
        guard let req = request else { return }
        self.pools.remove(object: req)
    }
    
    // 请求
    public class func requestAd(at position: XMLAdRequestPositionType,
                                preRAdIds  : String = "",
                                extraInfo  : [String: Any]? = nil,
                                completion : (([XMLAdTypeItemModel], Bool, TimeInterval) -> Void)? = nil) {
        let manager: XMLAdRequestManager = XMLAdRequestManager.shared()
        let request: XMLAdRequest = manager.saftyRequest()
        request.requestAd(at: position, preRAdIds: preRAdIds, extraInfo: extraInfo) { [weak manager, weak request] (items, isSuccess, reqTime) in
            manager?.remove(request)
            completion?(items, isSuccess, reqTime)
        }
    }

    // 初始化ADX配置请求
    public class func initAdx() {
        let manager: XMLAdRequestManager = XMLAdRequestManager.shared()
        let request: XMLAdRequest = manager.saftyRequest()
        request.initAdx { [weak manager, weak request] success in
            manager?.remove(request)
        }
    }
}
