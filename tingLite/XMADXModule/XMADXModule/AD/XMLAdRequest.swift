//
//  XMLAdRequest.swift
//  XMADXModule
//
//  Created by ya<PERSON><PERSON> on 2020/8/10.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import UIKit
import XMNetworkModule
import XMConfigModule
import XMBase
import XMBase.XMEnvironment
import SwiftyJSON
import RxSwift

// MARK: - ADX初始化配置请求类型
/// ADX初始化配置请求类型，参考XMLAdRequestPositionType的设计模式
public enum XMLAdxInitRequestType {
    case initConfig  // 初始化配置请求

    /// 请求域名
    var domain: String {
        switch self {
        case .initConfig:
            return XMLAdxInitRequestType.getAdServerBaseUrl() + "subapp/init"
        }
    }

    /// 请求参数
    var params: [String: Any] {
        return [
            "appid": "1463",
            "device": "iphone",
            "network": XMConfig.getStatisticNetwork(),
            "operator": XMConfig.getStatisticOperator(),
            "uid": XMSettings.shared().isLoggedIn ? (XMSettings.shared().userModel.user?.uid ?? 0) : -1,
            "userAgent": XMConfig.systemUserAgent(),
            "version": XMSYSTEM_BUNDLEVERSION,
            "xt": Int64(Date().timeIntervalSince1970 * 1000),
            "isDisplayedInScreen": XMLADXManager.shared().isEnterForeground ? 1 : 0
        ]
    }

    /// 是否使用GET方法
    var isGetMethod: Bool {
        switch self {
        case .initConfig: return false  // 使用POST方法
        }
    }

    /// 获取广告服务器基础URL
    private static func getAdServerBaseUrl() -> String {
        let env = XMEnvironment.xmEnvironment()
        var baseUrl: String

        switch env {
        case APPENV_TEST:
            baseUrl = "http://adse.test.ximalaya.com/"
        case APPENV_UAT:
            baseUrl = "http://adse.uat.ximalaya.com/"
        default:
            baseUrl = "http://adse.ximalaya.com/"
        }

        // 如果需要HTTPS，则替换协议
        if XMEnvironment.onlineEnvironment() {
            baseUrl = baseUrl.replacingOccurrences(of: "http://", with: "https://")
        }

        return baseUrl
    }
}

// ADX数据请求
public class XMLAdRequest: NSObject {

    private let networkManager: XMNetwork = XMNetwork().timeout(3)

    private let disposeBag: DisposeBag    = DisposeBag()

    private var isFinished: Bool          = false

    // 单例实例，用于全局配置初始化
    public static let shared = XMLAdRequest()
    
    /// Swift版本的initAdx方法，用于初始化ADX配置
    /// 参考OC版本的XMIAdDataCenter.initAdx实现，使用XMLAdxInitRequestType配置
    public func initAdx() {
        // 使用配置枚举发起网络请求
        requestInitAdxConfig(requestType: .initConfig) { [weak self] success, configData in
            if success, let configData = configData {
                // 更新ABTest配置
                if let abTestConfig = configData["abLabConfig"] as? [String: Any] {
                    XMIAdABTest.updateConfigCache(abTestConfig)
                }

                XMLAPMLogger.info("ADX初始化配置成功", moduler: .adx)
            } else {
                XMLAPMLogger.error("ADX初始化配置失败", moduler: .adx)
            }
        }
    }



    /// 请求初始化配置，参考requestAd方法的实现模式
    private func requestInitAdxConfig(requestType: XMLAdxInitRequestType, completion: @escaping (Bool, [String: Any]?) -> Void) {
        // 请求时间
        let currentTime: TimeInterval = Date().timeIntervalSince1970

        // 无网络时直接返回
        guard XMConfig.shared().networkAvailable else {
            XMLAPMLogger.error("设备无网络，ADX初始化配置失败", moduler: .adx)
            completion(false, nil)
            return
        }

        // 网络请求处理
        if networkManager.isExecuting() { networkManager.cancel() }
        self.isFinished = false

        let params = requestType.params

        // 网络处理回调，参考requestAd的实现
        let networkHandle: (((network: XMRequest & XMResponse, success: Bool)) -> Void)? = { [weak self] response in
            guard let wself = self, !wself.isFinished else { return }
            wself.isFinished = true

            XMLAPMLogger.info(response.network.serverUrl, moduler: .adx)
            XMLAPMLogger.info(response.network.params, moduler: .adx)
            XMLAPMLogger.info(response.network.json, moduler: .adx)

            if response.success, let json = response.network.json {
                let responseJSON = JSON(json)

                // 更新本地存储远程IP
                XMLADXUtils.updateClientIP(responseJSON["clientIp"].stringValue)

                // 解析配置数据
                if let configData = responseJSON["data"].dictionaryObject {
                    completion(true, configData)
                } else {
                    XMLAPMLogger.error("ADX初始化配置数据解析失败", moduler: .adx)
                    completion(false, nil)
                }
            } else {
                XMLAPMLogger.error("ADX初始化配置网络请求失败", moduler: .adx)
                completion(false, nil)
            }
        }

        // 根据请求类型选择GET或POST方法
        if requestType.isGetMethod {
            _ = networkManager.get(requestType.domain, parameters: params)
                .subscribe(onNext: networkHandle)
                .disposed(by: disposeBag)
        } else {
            _ = networkManager.serverUrl(requestType.domain)
                .params(params)
                .customHeader(["Content-Type": "application/json"])
                .requestSerializerType(.json)
                .errHint(false)
                .post()
                .subscribe(onNext: networkHandle)
                .disposed(by: disposeBag)
        }

        // 防止超时处理，参考requestAd的实现
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.2) { [weak self] in
            guard let wself = self, !wself.isFinished else { return }
            wself.isFinished = true
            XMLAPMLogger.error("ADX初始化配置请求超时", moduler: .adx)
            completion(false, nil)
        }
    }

    public static func initializeAdx() {
        XMLAdRequest.shared.initAdx()
    }

    public func requestAd(at position: XMLAdRequestPositionType,
                          preRAdIds  : String = "",
                          extraInfo  : [String: Any]? = nil,
                          completion : (([XMLAdTypeItemModel], Bool, TimeInterval) -> Void)? = nil) {
        // 请求时间
        let currentTime: TimeInterval = Date().timeIntervalSince1970
        
        // 无网络时直接返回
        guard XMConfig.shared().networkAvailable else {
            completion?([], false, currentTime)
            XMLAPMLogger.error("设备无网络 " + position.domain + " 代码位:" + position.name, moduler: .adx)
            XMLADXLoaderItem.uploadADXReqFailureStatisticInfo(5001, currentTime: currentTime, positionName: position.name)
            XMLAdReqTimeManager.shared().update(with: position.name, moment: currentTime)
            return
        }
        
        // 网络请求Part
        if networkManager.isExecuting() { networkManager.cancel() }
        self.isFinished = false
        var params: [String: Any] = position.params
        if let lastReqTime = XMLAdReqTimeManager.shared().time(with: position.name) { params["lastReqTime"] = "\(Int64(lastReqTime * 1000))" }
        if preRAdIds.isEmpty == false { params["preRequestAdIds"] = preRAdIds }
        // 竞价信息更新(需要切换到主线程)
        let bidTokenInfoHandle = {
            if let token = XMLADXRTBManager.convert(position.name, type: position.loaderType), token.count > 0 {
                params["AdSdkToken"] = token
            } else {
                params["AdSdkToken"] = ""
            }
        }
        if Thread.isMainThread {
            bidTokenInfoHandle()
        } else {
            DispatchQueue.main.sync { bidTokenInfoHandle() }
        }
        
        // 网络处理回调
        let networkHandle: (((network: XMRequest & XMResponse, success: Bool)) -> Void)? = { [weak self] response in
            if position == .splash { XMLADXReportManager.sendCoolSplashADXReqToAPM(Date().timeIntervalSince1970 - currentTime)
                #if DEBUG
                NSLog("--------- ad api load time: %f", Date().timeIntervalSince1970 - currentTime)
                #endif
            }
            guard let wself = self, !wself.isFinished else { return }
            wself.isFinished = true
            XMLAPMLogger.info(response.network.serverUrl, moduler: .adx)
            XMLAPMLogger.info(response.network.params, moduler: .adx)
            XMLAPMLogger.info(response.network.json, moduler: .adx)
            if response.success, let json = response.network.json {
                let responseJSON = JSON(json)
                // 更新本地存储远程IP
                XMLADXUtils.updateClientIP(responseJSON["clientIp"].stringValue)
                let responseId : String = responseJSON["responseId"].stringValue
                // 下发的实时竞价slotId列表
                var bidSlotList: [String: [String]]? = nil
                if var iterator = responseJSON["bidSlotList"].dictionary?.makeIterator() {
                    bidSlotList = [:]
                    var item = iterator.next()
                    while let model = item {
                        if model.key.count > 0 {
                            bidSlotList?[model.key] = model.value.arrayValue.map({ $0.stringValue })
                        }
                        item = iterator.next()
                    }
                }
                if let value = bidSlotList { XMLADXRTBManager.add(position.name, value: value) }
                let items: [XMLAdTypeItemModel] = responseJSON["data"].arrayValue.map { XMLAdTypeItemModel($0, responseId: responseId) }.filter { $0.xml_AdType().isValid && $0.xml_isSDKValid() }
                items.enumerated().forEach { (offset: Int, element: XMLAdTypeItemModel) in
                    element.xml_posiName  = position.name
                    element.xml_index     = offset
                    element.update(with: extraInfo)
                }
                completion?(items, true, currentTime)
                XMLAdReqTimeManager.shared().update(with: position.name, moment: currentTime)
            } else {
                completion?([], false, currentTime)
                XMLADXLoaderItem.uploadADXReqFailureStatisticInfo(5001, currentTime: currentTime, positionName: position.name)
                XMLAdReqTimeManager.shared().update(with: position.name, moment: currentTime)
            }
        }
        
        if position.isGetMethod {
            _ = networkManager.get(position.domain, parameters: params).subscribe(onNext: networkHandle).disposed(by: disposeBag)
        } else {
            _ = networkManager.serverUrl(position.domain).params(params).customHeader(["Content-Type": "application/json"]).requestSerializerType(.json).errHint(true)
                .post().subscribe(onNext: networkHandle).disposed(by: disposeBag)
        }
        
        // 防止上方超时3s不处理(XMNetwork)
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.2) { [weak self] in
            guard let wself = self, !wself.isFinished else { return }
            wself.isFinished = true
            completion?([], false, currentTime)
            XMLAPMLogger.error("ADX请求超时 " + position.domain + " 参数: \(params)", moduler: .adx)
            XMLADXLoaderItem.uploadADXReqFailureStatisticInfo(5001, currentTime: currentTime, positionName: position.name)
            XMLAdReqTimeManager.shared().update(with: position.name, moment: currentTime)
        }
    }
}
