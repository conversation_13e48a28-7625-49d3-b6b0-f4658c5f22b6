//
//  XMLADXManager.swift
//  tingLite
//
//  Created by yangle<PERSON> on 2020/4/10.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import UIKit
import RouterModule
import XMConfigModule
import BaseModule
import AVFoundation
import BUAdSDK
import GDTMobSDK
import BaiduMobAdSDK
import JADYun
import XMAPM

// MARK: - ADX数据请求管理器
@objcMembers
public class XMLADXManager: NSObject {
    
    private static let instance = XMLADXManager()
    /// 启动加载完成(展示广告或者没有广告)
    public static let kXMLaunchLoadFinished: Notification.Name = Notification.Name.init("kXMLaunchLoadFinishedNoti")
    
    public static func shared() -> XMLADXManager { return instance }
    
    // 贴片广告容器TAG
    static public let kPatchADContainTag: Int  = 888888
    
    private override init() { super.init() }
    
    // 是否正在展示任务中心的所有弹窗
    public var isShowingWelAlertAd : Bool = false
    // 是否正在前台
    public var isEnterForeground   : Bool = true
    // 是否进过后台
    public var hasBgFootprint      : Bool = false
    // 上次进入后台时间
    public var enterBgMoment       : TimeInterval = 0.0
    // 上次进入后台的时间(未触发广告弹窗)
    public var accumulateMoment    : TimeInterval = 0.0
    // 冷启动开屏开始展示时间点
    public var coolSplashShowMoment: TimeInterval? = nil
    
    public weak var lastWindow     : UIWindow?    = nil
    
    public weak var mediaDelegate  : XMLADXMediaProtocol? = nil
    
    // 是否在开屏广告逻辑中
    public var isInSplashADLogic   : Bool = false
    // 是否在视频贴片落地页
    public var isInVideoDetailPage : Bool = false
    // 是否在激励视频展示逻辑中
    public var isInRewardVideoLogic: Bool = false
    
    private var lastBUSplashViewController: UIViewController?        = nil
    private var lastGDTSplashViewTrigger  : XMLGDTSplashViewTrigger? = nil
    private var lastDRSplashViewController: UIViewController?        = nil
    private var lastBDSplashViewController: UIViewController?        = nil
    private var lastJDSplashViewController: UIViewController?        = nil

    private var lastBUVideoAdTrigger : XMLBURewardedVideoAdTrigger?  = nil
    private var lastGDTVideoAdTrigger: XMLGDTRewardedVideoAdTrigger? = nil
    private var lastBDVideoAdTrigger : XMLBDRewardedVideoAdTrigger?  = nil
    private var lastDRVideoAdTrigger : XMLDRRewardedVideoAdTrigger?  = nil

    private var lastBUPlaqueAdTrigger : XMLBUPlaqueAdTrigger?        = nil
    private var lastGDTPlaqueAdTrigger: XMLGDTPlaqueAdTrigger?       = nil
    private var lastDRNativePlaqueAdTrigger : XMLDRNativePlaqueAdTrigger?  = nil
    private var lastBUNativePlaqueAdTrigger : XMLBUNativePlaqueAdTrigger?  = nil
    private var lastGDTNativePlaqueAdTrigger: XMLGDTNativePlaqueAdTrigger? = nil
    
    // Key格式: PositionName
    public var waitRecoverAudioLogicKey: String? = nil
    // 冷启动开屏是否被处理
    public var isCoolLaunchHandle      : Bool    = false
    // 广告落地页承接层
    public static var viewController: UIViewController? {
        if let topVC = UIApplication.topViewController() {
            return topVC
        } else if let rootVC = UIApplication.shared.keyWindow?.rootViewController {
            return rootVC
        }
        return nil
    }
    
    // 穿山甲SDK初始化是否正常
    static public var isBUReady: Bool  = false
}

// MARK: - 环境注册 Method
public extension XMLADXManager {
    // 注册环境
    static func registerEnvironment() {
        // 穿山甲配置
        let configuration = BUAdSDKConfiguration.configuration();
        configuration.appID = kBUAdSDKAppId
#if DEBUG
        configuration.sdkdebug = true
#endif
//        BUAdSDKManager.setGDPR(1)
        BUAdSDKManager.setCustomIDFA(XMConfig.shared().idfa)
        BUAdSDKManager.start(syncCompletionHandler: { result, error in
            guard result, error == nil else {
                // 穿山甲SDK初始化异常统计
                XMEventLog.logEventWithId(43527, serviceId: "others", properties: ["sdkErrorMsg": error?.localizedDescription ?? "穿山甲初始化失败"])
                return
            }
            XMLADXManager.isBUReady = true
        })
        XMLADXUtils.consoleLog("ADX BUADSDK sdk-version \(BUAdSDKManager.sdkVersion ?? "")")
        // 广点通配置
        GDTSDKConfig.initWithAppId(kGDTAdSDKAppId)
        GDTSDKConfig.setChannel(14)
        GDTSDKConfig.enableDefaultAudioSessionSetting(false)
        GDTSDKConfig.start(completionHandler: { success, error in
            XMLAPMLogger.info("广点通启动, success=\(success), error=\(error?.localizedDescription ?? "")")
        })
        XMLADXUtils.consoleLog("ADX GDTSDK sdk-version \(GDTSDKConfig.sdkVersion() ?? "")")
        // 百度百青藤配置
        BaiduMobAdSetting.sharedInstance()?.setDebugLogEnable(false)
        BaiduMobAdNativeAdView.dealTapGesture(true)
        XMLADXUtils.consoleLog("ADX BAIDU sdk-version \(BaiduMobAdSetting.sharedInstance()?.getSDKVersion() ?? "")")
        // 京东云配置
        JADYunSDK.initWithAppID(kJDAdSDKAppId)
        JADYunSDK.canUseLocation(false)
        JADYunSDK.canUseIPAddress(false)
        XMLADXUtils.consoleLog("ADX JADYUNSDK sdk-version \(JADYunSDK.sdkVersion())")

        // 调试环境配置
        #if DEBUG
//        JADYunSDK.enableLog(true)
//        BUAdSDKManager.setLoglevel(.debug)
//        BaiduMobAdSetting.sharedInstance()?.setDebugLogEnable(true)
        #endif

        // 初始化ADX配置和ABTest功能
        XMLAdRequest.initializeAdx()
    }
    
    // 延时注册环境
    static func delayRegisterEnvironment() { }
}

// MARK: - 启动图 Method
extension XMLADXManager {
    // 加载启动图
    public func loadSplashVCIfNeed(_ isAsRootVC: Bool = true) {
        self.cleanSplashContent()
        self.isInSplashADLogic = true
        if !isAsRootVC { XMLADXWidgetManager.hiddenBlackWidgetsIfNeed() }
        #if DEBUG
        let date = Date()
        #endif
        let handleSplash: (Bool, XMLADXLoaderItem?) -> Void = { [weak self] (_, obj) in
            if Thread.isMainThread {
                #if DEBUG
                NSLog("-------- splash total rquest: %f", Date().timeIntervalSince(date))
                #endif
                self?.handleSplashLoaderItem(isAsRootVC, item: obj)
                return
            }
            DispatchQueue.main.async {
                #if DEBUG
                NSLog("-------- splash total rquest: %f", Date().timeIntervalSince(date))
                #endif
                self?.handleSplashLoaderItem(isAsRootVC, item: obj)
            }
        }
        
        if isAsRootVC { launchMaxLimitLoadingLogic() }
        XMLADXUtils.consoleLog("开屏广告-开始请求")
        XMLADXPreManager.preTreat(at: .splash, type: .splash, asyncHandle: { [weak self] (items, isSuccess, reqTime) in
           if items.count > 0 {
            let currentTime: TimeInterval = Date().timeIntervalSince1970
                XMLADXLoaderManager.shared().prepareLoader(.splash, items: items) { (obj, _) in
                    XMLADXReportManager.sendCoolSplashADXLoginToAPM(Date().timeIntervalSince1970 - currentTime)
                    #if DEBUG
                    NSLog("-------- splash SDK rquest: %f", Date().timeIntervalSince1970 - currentTime)
                    #endif
                    handleSplash(isAsRootVC, obj)
                }
            } else {
                self?.isCoolLaunchHandle = true
                DispatchQueue.main.async {
                    if isAsRootVC { self?.apmLaunchTick() }
                    self?.recoverTabBarContent(isAsRootVC)
                }
            }
        }) { (obj, _) in
            handleSplash(isAsRootVC, obj)
        }
    }
    
    // 渲染解析出的启动图广告XMLADXLoaderItem
    private func handleSplashLoaderItem(_ isDisplayAsRootVC: Bool, item: XMLADXLoaderItem?) {
        if isDisplayAsRootVC { apmLaunchTick() }
        guard let item = item else {
            XMLAPMLogger.error("开屏: 冷启动\(isDisplayAsRootVC), 没有物料可用", moduler: .adx)
            recoverTabBarContent(isDisplayAsRootVC)
            return
        }
        if isDisplayAsRootVC {
            self.isCoolLaunchHandle   = true
            self.coolSplashShowMoment = Date().timeIntervalSince1970
            if item.isBuAd() {
                let splashVC = XMLBUSplashViewController.controller(with: item, isAsRootVC: true)
                self.lastBUSplashViewController = splashVC
                UIApplication.shared.keyWindow?.rootViewController?.addChild(splashVC)
                UIApplication.shared.keyWindow?.rootViewController?.view.addSubview(splashVC.view)
            } else if item.isGDTAd() {
                let trigger = XMLGDTSplashViewTrigger.trigger(with: item, isAsRootVC: true)
                self.lastGDTSplashViewTrigger = trigger
                trigger.showAd()
            } else if item.isxmAd() {
                let splashVC = XMLDRSplashViewController.controller(with: item, isAsRootVC: true)
                self.lastDRSplashViewController = splashVC
                UIApplication.shared.keyWindow?.rootViewController?.addChild(splashVC)
                UIApplication.shared.keyWindow?.rootViewController?.view.addSubview(splashVC.view)
            } else if item.isBdAd() {
                let splashVC = XMLBDSplashViewController.controller(with: item, isAsRootVC: true)
                self.lastBDSplashViewController = splashVC
                UIApplication.shared.keyWindow?.rootViewController?.addChild(splashVC)
                UIApplication.shared.keyWindow?.rootViewController?.view.addSubview(splashVC.view)
            } else if item.isJDAd() {
                let splashVC = XMLJDSplashViewController.controller(with: item, isAsRootVC: true)
                self.lastJDSplashViewController = splashVC
                UIApplication.shared.keyWindow?.rootViewController?.addChild(splashVC)
                UIApplication.shared.keyWindow?.rootViewController?.view.addSubview(splashVC.view)
            } else {
                self.recoverTabBarContent(isDisplayAsRootVC)
            }
        } else {
            if item.isBuAd() {
                let view = UIApplication.shared.keyWindow?.subviews.first(where: { $0.tag == kSplashPageBgDefaultView_Tag })
                let splashVC = XMLBUSplashViewController.controller(with: item, isAsRootVC: false)
                self.lastBUSplashViewController = splashVC
                UIApplication.shared.keyWindow?.addSubview(splashVC.view)
                view?.removeFromSuperview()
            } else if item.isGDTAd() {
                let view = UIApplication.shared.keyWindow?.subviews.first(where: { $0.tag == kSplashPageBgDefaultView_Tag })
                let trigger = XMLGDTSplashViewTrigger.trigger(with: item, isAsRootVC: false)
                self.lastGDTSplashViewTrigger = trigger
                trigger.showAd()
                view?.removeFromSuperview()
            } else if item.isxmAd() {
                let view = UIApplication.shared.keyWindow?.subviews.first(where: { $0.tag == kSplashPageBgDefaultView_Tag })
                let splashVC = XMLDRSplashViewController.controller(with: item, isAsRootVC: false)
                self.lastDRSplashViewController = splashVC
                UIApplication.shared.keyWindow?.addSubview(splashVC.view)
                view?.removeFromSuperview()
            } else if item.isBdAd() {
                let view = UIApplication.shared.keyWindow?.subviews.first(where: { $0.tag == kSplashPageBgDefaultView_Tag })
                let splashVC = XMLBDSplashViewController.controller(with: item, isAsRootVC: false)
                self.lastBDSplashViewController = splashVC
                UIApplication.shared.keyWindow?.addSubview(splashVC.view)
                view?.removeFromSuperview()
            } else if item.isJDAd() {
                let view = UIApplication.shared.keyWindow?.subviews.first(where: { $0.tag == kSplashPageBgDefaultView_Tag })
                let splashVC = XMLJDSplashViewController.controller(with: item, isAsRootVC: false)
                self.lastJDSplashViewController = splashVC
                UIApplication.shared.keyWindow?.addSubview(splashVC.view)
                view?.removeFromSuperview()
            } else {
                self.recoverTabBarContent(isDisplayAsRootVC)
            }
        }
    }
    
    // 恢复至普通桌面
    private func recoverTabBarContent(_ isDisplayAsRootVC: Bool) {
        if isDisplayAsRootVC {
            if UIApplication.shared.keyWindow === self.lastWindow { } else { self.lastWindow?.makeKeyAndVisible() }
            UIApplication.shared.keyWindow?.rootViewController = (RouterBridge(nil).xmAppSceneControlElement(.tabBar) as? UIViewController) ?? UIViewController()
            NotificationCenter.default.post(name: NSNotification.Name(rawValue: "kXMAdDimssAdPresentNotificationName"), object: nil)
        } else {
            XMLADXWidgetManager.recoverBlackWidgetsIfNeed()
            if let view = UIApplication.shared.keyWindow?.subviews.first(where: { $0.tag == kSplashPageBgDefaultView_Tag }) { view.removeFromSuperview() }
            if UIApplication.shared.keyWindow === self.lastWindow { } else { self.lastWindow?.makeKeyAndVisible() }
            NotificationCenter.default.post(name: NSNotification.Name(rawValue: "applicationWillEnterForeground"), object: nil)
        }
        self.lastWindow = nil
        isInSplashADLogic = false
    }
    
    // 加载启动图
    public func setAppEnterBgMoment() {
        self.hasBgFootprint = true
        self.enterBgMoment  = Date().timeIntervalSince1970
    }
    
    // 检测热启动是否可以弹出闪屏
    public func checkPopSplashVCIfNeed(_ isAccumulateWhenLack: Bool = false) -> Bool {
        // 视频贴片落地页或激励视频环境中不做处理
        guard !isInVideoDetailPage, !isInRewardVideoLogic else { return false }
        // 顶层为苹果内部页则不加载广告
        if let topVC = UIApplication.topViewController(), NSStringFromClass(topVC.classForCoder) == "SKStoreProductViewController" {
            return false
        } else if let topVC = UIApplication.topViewController(),
                  (NSStringFromClass(topVC.classForCoder).contains("GDT") || NSStringFromClass(topVC.classForCoder).contains("BU") || NSStringFromClass(topVC.classForCoder).contains("JAD")) {
            return false
        }
        let limitMinTimeOffset: TimeInterval = XMLADXConfig.kAppLaunchLimitMinInterval()
        let nowMoment: TimeInterval = Date().timeIntervalSince1970
        let calcuBgMoment: TimeInterval = isAccumulateWhenLack && self.accumulateMoment != 0 ? self.accumulateMoment : self.enterBgMoment
        if calcuBgMoment > 0 && (nowMoment - calcuBgMoment) >= limitMinTimeOffset {
            if isAccumulateWhenLack { self.accumulateMoment = 0.0 }
            return true
        } else {
            if isAccumulateWhenLack { self.accumulateMoment = calcuBgMoment }
            #if DEBUG
            return false
            #else // 发布环境强制设置false(防止测试代码上线)
            return false
            #endif
        }
    }
    
    // 清理启动图数据
    public func cleanSplashContent() {
        self.lastBUSplashViewController = nil
        self.lastGDTSplashViewTrigger   = nil
        self.lastDRSplashViewController = nil
        self.lastBDSplashViewController = nil
        self.lastJDSplashViewController = nil
        self.isInSplashADLogic          = false
    }

    private func apmLaunchTick() {
        XMAPM.timestampForHiddenADView()
        NotificationCenter.default.post(name: XMLADXManager.kXMLaunchLoadFinished, object: nil)
    }
    
    // 启动闪屏最大时间间隔逻辑
    private func launchMaxLimitLoadingLogic() {
        DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 13.0) { [weak self] in
            XMLAPMLogger.error("开屏启动触发最大限制时间, Win:\(String(describing: self?.lastWindow)) Cool:\(String(describing: self?.isCoolLaunchHandle))", moduler: .adx)
            guard let wself = self, wself.lastWindow != nil, wself.isCoolLaunchHandle == false else {
                XMLAPMLogger.error("开屏发最大限制时间End(无需处理)", moduler: .adx)
                return
            }
            let currentWRootVC: UIViewController? = wself.lastWindow?.rootViewController
            let currentTabVC  : UIViewController? = (RouterBridge(nil).xmAppSceneControlElement(.tabBar) as? UIViewController)
            if currentWRootVC === currentTabVC {
                XMLAPMLogger.error("开屏最大限制时间End(一致)", moduler: .adx)
            } else {
                XMLAPMLogger.error("开屏最大限制时间End(恢复)", moduler: .adx)
                wself.recoverTabBarContent(true)
            }
        }
    }
}

// MARK: - 激励视频 Method
extension XMLADXManager {
    // 加载激励视频
    public func loadVideoAd(_ params: [String: Any], completion: @escaping (([String: Any]) -> Void)) {
        // PositionName特殊适配
        var params: [String: Any] = params
        let positionName: String = (params["positionName"] as? String) ?? ""
        if positionName == "sub_listentime_double_video", XMConfig.shared().isMinimalism {
            params["positionName"] = "sub_listentime_double_video_immerse"
        }
        self.mediaDelegate?.adxManager(self, willHandle: params)
        XMLAdPositionInfoManager.updatePositionInfoIfNeed(params)
        XMLADXManager.resetRewardVideoEnvironment()
        self.isInRewardVideoLogic = true
        let isIgnoreADX: Bool = (params["isIgnoreADX"] as? Bool) ?? false
        guard isIgnoreADX == false else { handleIgnoreADXVideoAd(params, completion: ({ [weak self] in self?.isInRewardVideoLogic = false;completion($0) })); return }
        let needLoading: Bool = (params["needLoading"] as? Bool) ?? true
        DispatchQueue.main.async { if needLoading { XMLGlobalLoadingManager.shared().show() } }
        XMLADXPreManager.preTreat(at: .customVideo(params), type: .video, extraInfo: params, asyncHandle: { [weak self] (items, isSuccess, reqTime) in
            if isSuccess == false {
                XMLADXLoaderManager.shared().prepareLoaderDirectlyFromCache(positionName, beginTime: reqTime) { [weak self] (obj, _) in self?.handleVideoLoaderItem(obj, params: params, isNeedDismiss: needLoading, completion: ({ [weak self] in self?.isInRewardVideoLogic = false;completion($0) })) }
            } else if items.count > 0 {
                XMLADXLoaderManager.shared().prepareLoader(.video, items: items) { [weak self] (obj, _) in
                    self?.handleVideoLoaderItem(obj, params: params, isNeedDismiss: needLoading, completion: ({ [weak self] in self?.isInRewardVideoLogic = false;completion($0) }))
                }
            } else {
                self?.handleVideoLoaderItem(nil, params: params, isNeedDismiss: needLoading, completion: ({ [weak self] in self?.isInRewardVideoLogic = false;completion($0) }))
            }
        }) { [weak self] (obj, _) in
            self?.handleVideoLoaderItem(obj, params: params, isNeedDismiss: needLoading, completion: ({ [weak self] in self?.isInRewardVideoLogic = false;completion($0) }))
        }
    }
    
    public func createCustomADXLoader() -> XMNativeRewardAdProtocol {
        return XMLADXCustomLoaderMgr()
    }
    
    // 忽略ADX直接使用本地解析出的视频XMLAdTypeItemModel
    func handleIgnoreADXVideoAd(_ params: [String: Any], completion: @escaping (([String: Any]) -> Void)) {
        let positionName: String = (params["positionName"] as? String) ?? ""
        let slotId: String = (params["slotId"] as? String) ?? ""
        let item = XMLAdTypeItemModel.ad(slotId, posiName: positionName, extraInfo: params)
        let needLoading: Bool = (params["needLoading"] as? Bool) ?? true
        DispatchQueue.main.async { if needLoading { XMLGlobalLoadingManager.shared().show() } }
        XMLADXLoaderManager.shared().prepareLoader(.video, items: [item], isIgnoreADX: true) { [weak self] (obj, _) in self?.handleVideoLoaderItem(obj, params: params, isNeedDismiss: needLoading, completion: completion) }
    }
    
    // 加载激励视频
    public func loadPaidUnlockVideoAd(_ params: [String: Any], scene: XMLADXBusiScene, completion: @escaping (([String: Any], XMLPaidUnlockAdTypeItemModel?) -> Void)) {
        var params: [String: Any] = params
        params["busiScene"] = scene.rawValue
        // PositionName特殊适配
        let positionName: String = (params["positionName"] as? String) ?? ""
        if positionName == "sub_listentime_double_video", XMConfig.shared().isMinimalism {
            params["positionName"] = "sub_listentime_double_video_immerse"
        }
        self.isInRewardVideoLogic = true
        self.mediaDelegate?.adxManager(self, willHandle: params)
        XMLAdPositionInfoManager.updatePositionInfoIfNeed(params)
        XMLADXManager.resetRewardVideoEnvironment()
        let needLoading : Bool   = (params["needLoading"] as? Bool) ?? true
        DispatchQueue.main.async { if needLoading { XMLGlobalLoadingManager.shared().show() } }
        XMLPulAdRequestManager.requestAd(at: positionName, extraInfo: params) { [weak self] (items, _, _) in
            if items.count > 0 {
                XMLADXPulLoaderManager.shared().prepareLoader(.video, items: items) { [weak self] (obj, _) in
                    self?.handleVideoLoaderItem(obj, params: params, isNeedDismiss: needLoading, isNeedDefault: false, completion: { [weak self] obj in
                        self?.isInRewardVideoLogic = false
                        completion(obj, items.first)
                    })
                }
            } else {
                DispatchQueue.main.async { if needLoading { XMLGlobalLoadingManager.shared().dismiss() } }
                self?.isInRewardVideoLogic = false
                completion([:], nil)
            }
        }
    }
    
    // 渲染解析出的激励视频广告XMLADXLoaderItem
    func handleVideoLoaderItem(_ item: XMLADXLoaderItem?,
                               _ adStatusCallback: XMNativeRewardAdDelegate? = nil,
                               params: [String: Any],
                               isNeedDismiss: Bool = true,
                               isNeedDefault : Bool = true,
                               completion: @escaping (([String: Any]) -> Void)) {
        DispatchQueue.main.async {
            if isNeedDismiss { XMLGlobalLoadingManager.shared().dismiss() }
            guard let item = item else {
                // 是否使用兜底视频
                guard isNeedDefault else { completion([:]); return }
                self.loadPropUpVideoAd(params, adStatusCallback, isNeedDismiss: isNeedDismiss, completion: completion)
                return
            }
            // 发送即将弹出视频类广告通知
            NotificationCenter.default.post(name: .kADXWillShowRewardedVideo, object: nil)
            if item.isBuAd() {
                let trigger = XMLBURewardedVideoAdTrigger.trigger(with: item, params: params)
                trigger.adLoaderStatus = adStatusCallback
                self.lastBUVideoAdTrigger = trigger
                DispatchQueue.main.asyncAfter(deadline: .now() + 1) { self.lastBUVideoAdTrigger?.showAd(completion) }
            } else if item.isGDTAd() {
                let trigger = XMLGDTRewardedVideoAdTrigger.trigger(with: item, params: params)
                trigger.adLoaderStatus = adStatusCallback
                self.lastGDTVideoAdTrigger = trigger
                DispatchQueue.main.asyncAfter(deadline: .now() + 1) { self.lastGDTVideoAdTrigger?.showAd(completion) }
            } else if item.isBdAd() {
                let trigger = XMLBDRewardedVideoAdTrigger.trigger(with: item, params: params)
                trigger.adLoaderStatus = adStatusCallback
                self.lastBDVideoAdTrigger = trigger
                DispatchQueue.main.asyncAfter(deadline: .now() + 1) { self.lastBDVideoAdTrigger?.showAd(completion) }
            } else if item.isxmAd() {
                let trigger = XMLDRRewardedVideoAdTrigger.trigger(with: item, params: params)
                trigger.adLoaderStatus = adStatusCallback
                self.lastDRVideoAdTrigger = trigger
                DispatchQueue.main.asyncAfter(deadline: .now() + 1) { self.lastDRVideoAdTrigger?.showAd(completion) }
            } else {
                completion([:])
            }
        }
    }
    
    // 加载兜底激励视频
    private func loadPropUpVideoAd(_ params: [String: Any],
                                   _ adStatusCallback: XMNativeRewardAdDelegate? = nil,
                                   isNeedDismiss: Bool = true,
                                   completion: @escaping (([String: Any]) -> Void)) {
        guard let isPropUp     = params["isPropUp"] as? Bool, isPropUp,
              let positionName = params["positionName"] as? String,
              let slotId       = params["slotId"] as? String else { completion([:]); return }
        let item = XMLAdTypeItemModel.ad(slotId, posiName: positionName, extraInfo: params)
        XMLADXLoaderManager.shared().prepareLoader(.video, items: [item]) { [weak self] (obj, _) in
            self?.handleVideoLoaderItem(obj, adStatusCallback, params: params, isNeedDismiss: isNeedDismiss, isNeedDefault: false, completion: completion)
        }
    }
    
    // 重置激励视频播放环境
    public class func resetRewardVideoEnvironment() {
        Self.shared().lastBUVideoAdTrigger  = nil
        Self.shared().lastGDTVideoAdTrigger = nil
        Self.shared().lastBDVideoAdTrigger  = nil
    }
}

// MARK: - 信息流 Method
extension XMLADXManager {
    // 恢复音频现场()
    public func recoverAudioLogicKeyIfNeed(_ positionName: String) {
        guard let key = self.waitRecoverAudioLogicKey, key == positionName else { return }
        self.waitRecoverAudioLogicKey = nil
        RouterBridge(nil).playerResume()
    }
    
    // 针对信息流视频
    public func recoverAudioControlIfNeed(_ isForce: Bool = false) {
        // 视频贴片落地页环境中不做处理
        guard !isInVideoDetailPage else { return }
        let audiosession = AVAudioSession.sharedInstance()
        if isForce || audiosession.category != .playback {
            var hasError: Bool = false
            do {
                if audiosession.category == .playback {
                    try audiosession.setActive(false, options: .notifyOthersOnDeactivation) // 取消其它打断
                }
            } catch let error {
                XMLogger.error(" - - Player Setting category to AVAudioSessionCategoryPlayback failed \(error) <<<", moduler: .media)
                hasError = true
            }
            do {
                try audiosession.setCategory(.playback, mode: .default)                 // 设置后台播放
                try audiosession.setActive(true)                                        // 激活当前配置
            } catch let error {
                hasError = true
                XMLogger.error(" - - Player Setting category to AVAudioSessionCategoryPlayback failed \(error) <<<", moduler: .media)
            }
            if hasError { return }
            UIApplication.shared.beginReceivingRemoteControlEvents()
        }
    }
    
    // 清理信息流视频当前播放环境
    public func cleanPatchVideoEnvironment(_ mediaView: UIView?) {
        if let gdtMediaView = mediaView as? GDTMediaView {
            gdtMediaView.stop()
            gdtMediaView.muteEnable(true)
        } else if let buMediaView = mediaView as? BUVideoAdView {
            buMediaView.pause()
            let selector: Selector = #selector(XMLADOCMethod.stop)
            if let player = buMediaView.value(forKey: "player") as? NSObject, player.responds(to: selector) {
                player.perform(selector)
            }
        }
    }
    
    // 加载播放页底部广告
    public func loadPlayPageAdInfo(_ trackId: Int64, params: [String: Any]? = nil, completion: @escaping ((Any?, XMLADXResponseStatus) -> Void)) {
        self.recoverAudioLogicKeyIfNeed(XMLAdRequestPositionType.playPageBottom(trackId).name)
        guard !XMLADXFreeManager.shared().isPatchFree else {
            // 免广告期间进行数据上报
            XMLADXFreeManager.shared().uploadADXItemsInFree(at: .playPageBottom(trackId), type: .flow, extraInfo: params)
            completion(nil, .empty)
            return
        }
        XMLADXPreManager.preTreat(at: .playPageBottom(trackId), type: .flow, extraInfo: params, asyncHandle: { (items, isSuccess, reqTime) in
            if items.count > 0 {
                XMLADXLoaderManager.shared().prepareLoader(.flow, items: items) { (obj, _) in completion(obj, .normal) }
            } else {
                completion(nil, .empty)
            }
        }) { (obj, _) in
            completion(obj, .normal)
        }
    }
    
    // 加载个人页底部广告
    public func loadMinePageAdInfo(_ params: [String: Any]? = nil, completion: @escaping ((Any?, XMLADXResponseStatus) -> Void)) {
        self.recoverAudioLogicKeyIfNeed(XMLAdRequestPositionType.personBottom.name)
        // 免广告期间进行数据上报
        guard !XMLADXFreeManager.shared().isPatchFree else {
            XMLADXFreeManager.shared().uploadADXItemsInFree(at: .personBottom, type: .flow, extraInfo: params)
            completion(nil, .empty)
            return
        }
        // 审核期间不返回个人页广告
        guard !XMRemote.appInReview else { completion(nil, .empty);return }
        XMLADXPreManager.preTreat(at: .personBottom, type: .flow, extraInfo: params, asyncHandle: { (items, isSuccess, reqTime) in
            if items.count > 0 {
                XMLADXLoaderManager.shared().prepareLoader(.flow, items: items) { (obj, _) in
                    completion(obj, .normal)
                }
            } else {
                completion(nil, .empty)
            }
        }) { (obj, _) in
            completion(obj, .normal)
        }
    }
    
    // 加载自定义弹窗广告
    public func loadCustomPopupAdInfo(_ params  : [String: Any],
                                      filter    : (([XMLAdTypeItemModel]) -> [XMLAdTypeItemModel])? = nil,
                                      completion: @escaping ((Any?) -> Void)) {
        XMLAdPositionInfoManager.updatePositionInfoIfNeed(params)
        let positionName: String = (params["positionName"] as? String) ?? ""
        let isIgnoreADX: Bool = (params["isIgnoreADX"] as? Bool) ?? false
        self.recoverAudioLogicKeyIfNeed(positionName)
        guard !XMLADXFreeManager.shared().isPatchFree else {
            // 免广告期间进行数据上报
            XMLADXFreeManager.shared().uploadADXItemsInFree(at: .customPopup(params), type: .flow, extraInfo: params)
            completion(nil)
            return
        }
        guard isIgnoreADX == false else { handleIgnoreADXFeedOrPopupAd(params, completion: completion); return }
        XMLADXPreManager.preTreat(at: .customPopup(params), type: .flow, extraInfo: params, filter: filter, asyncHandle: { (items, isSuccess, reqTime) in
            if items.count > 0 {
                XMLADXLoaderManager.shared().prepareLoader(.flow, items: items) { (obj, _) in
                    completion(obj)
                }
            } else {
                completion(nil)
            }
        }) { (obj, _) in
            completion(obj)
        }
    }
    
    // 加载自定义信息流广告
    public func loadCustomFeedAdInfo(_ params  : [String: Any],
                                     completion: @escaping ((Any?, XMLADXResponseStatus) -> Void)) {
        self.loadCustomFeedAdInfo(params, filter: nil, completion: completion)
    }
    
    // 加载自定义信息流广告
    public func loadCustomFeedAdInfo(_ params  : [String: Any],
                                     filter    : (([XMLAdTypeItemModel]) -> [XMLAdTypeItemModel])?,
                                     completion: @escaping ((Any?, XMLADXResponseStatus) -> Void)) {
        XMLAdPositionInfoManager.updatePositionInfoIfNeed(params)
        let positionName: String = (params["positionName"] as? String) ?? ""
        let isIgnoreADX: Bool = (params["isIgnoreADX"] as? Bool) ?? false
        self.recoverAudioLogicKeyIfNeed(positionName)
        guard !XMLADXFreeManager.shared().isPatchFree else {
            // 免广告期间进行数据上报
            XMLADXFreeManager.shared().uploadADXItemsInFree(at: .customFeed(params), type: .flow, extraInfo: params)
            completion(nil, .empty)
            return
        }
        guard isIgnoreADX == false else { handleIgnoreADXFeedOrPopupAd(params, completion: { completion($0, .normal) }); return }
        XMLADXPreManager.preTreat(at: .customFeed(params), type: .flow, extraInfo: params, filter: filter, asyncHandle: { (items, isSuccess, reqTime) in
            if items.count > 0 {
                XMLADXLoaderManager.shared().prepareLoader(.flow, items: items) { (obj, _) in
                    completion(obj, .normal)
                }
            } else {
                completion(nil, .empty)
            }
        }) { (obj, _) in
            completion(obj, .normal)
        }
    }
    
    // 忽略ADX直接使用本地解析出的信息流XMLAdTypeItemModel
    private func handleIgnoreADXFeedOrPopupAd(_ params: [String: Any], completion: @escaping ((Any?) -> Void)) {
        let positionName: String = (params["positionName"] as? String) ?? ""
        let slotId: String = (params["slotId"] as? String) ?? ""
        let isRenderTemplate: Bool = ((params["renderTemplate"] as? String) ?? "0") == "1"
        let item = XMLAdTypeItemModel.buAd(slotId, posiName: positionName, isNEAd: isRenderTemplate, extraInfo: params)
        XMLADXLoaderManager.shared().prepareLoader(.flow, items: [item], isIgnoreADX: true, completion: { (obj, _) in completion(obj) })
    }
    
    // 信息流广告数据与信息流视图绑定
    public func bindAd(_ item: XMLADXLoaderItem, presentVC: UIViewController, flowView: XMNativeAdFlowViewDelegate) {
        item.updateStatusIfNeed() // 更新数据状态
        if item.isBuAd() {
            bindBUAd(item, presentVC: presentVC, flowView: flowView, exposure: true)
        } else if item.isGDTAd() {
            bindGDTAd(item, presentVC: presentVC, flowView: flowView, exposure: true)
        } else if item.isxmAd() {
            bindDRAd(item, presentVC: presentVC, flowView: flowView, exposure: true)
        } else if item.isBdAd() {
            bindBDAd(item, presentVC: presentVC, flowView: flowView, exposure: true)
        } else if item.isJDAd() {
            bindJDAd(item, presentVC: presentVC, flowView: flowView, exposure: true)
        }
    }
    
    public func displayCustomRenderAd(_ item: XMLADXLoaderItem, presentVC: UIViewController, flowView: XMNativeAdFlowViewDelegate) {
        if item.isBuAd() {
            bindBUAd(item, presentVC: presentVC, flowView: flowView, exposure: false)
        } else if item.isGDTAd() {
            bindGDTAd(item, presentVC: presentVC, flowView: flowView, exposure: false)
        } else if item.isxmAd() {
            bindDRAd(item, presentVC: presentVC, flowView: flowView, exposure: false)
        } else if item.isBdAd() {
            bindBDAd(item, presentVC: presentVC, flowView: flowView, exposure: false)
        } else if item.isJDAd() {
            bindJDAd(item, presentVC: presentVC, flowView: flowView, exposure: false)
        }
    }
    
    private func bindBUAd(_ item: XMLADXLoaderItem, presentVC: UIViewController,
                          flowView: XMNativeAdFlowViewDelegate, exposure: Bool) {
        guard flowView is UIView else { return }
        
        if let model = item.f_buAd { // 非模版渲染
            model.rootViewController = presentVC
            
            let adSource: XMLNativeAdFlowDataSource = XMLNativeAdFlowDataSource()
            adSource.adContainer.tag = XMLADXManager.kPatchADContainTag
            // XMLBUNativeAdTrigger 内部弱持有 XMLADXLoaderItem, 故不会产生循环引用
            let trigger: XMLBUNativeAdTrigger = XMLBUNativeAdTrigger.trigger(with: item)
            trigger.flowView = flowView
            trigger.nativeAdView.videoAdView?.rootViewController = presentVC
            item.f_buTrigger = trigger
            
            if let imageURL = model.data?.imageAry?.first?.imageURL, let url = URL(string: imageURL) { adSource.imageURL = url }
            adSource.title        = model.data?.adTitle
            adSource.desc         = model.data?.adDescription
            adSource.logoView     = trigger.nativeAdView.logoADImageView
            adSource.logoSize     = CGSize(width: 38, height: 12.0923)
            adSource.mediaView    = model.xm_isFeedVideo ? trigger.nativeAdView.videoAdView : nil
            // 广告描述
            if let adDescription = model.data?.adDescription, !adDescription.isEmpty {
                adSource.adDescInfo = adDescription
            } else if let adTitle = model.data?.adTitle, !adTitle.isEmpty {
                adSource.adDescInfo = adTitle
            } else if item.advertiserInfo.count > 0 {
                adSource.adDescInfo = "由\(item.advertiserInfo)推荐"
            }
            // 广告主
            if let adTitle = model.data?.adTitle, !adTitle.isEmpty {
                adSource.adVertiserInfo = adTitle
            }
            if let isScatter = item.item?.xml_showType().isScatter, isScatter, let url = model.data?.icon?.imageURL, url.count > 0 {
                adSource.iconURLs      = [url]
            }
            flowView.updateFlowViewInfo(adSource)
            bindImageView(flowView.coverImageView(), adSource: adSource, item: item, statistic: exposure)
            if exposure {
                model.registerContainer(adSource.adContainer, withClickableViews: flowView.clickableViews())
                if model.xm_isFeedVideo { self.recoverAudioControlIfNeed() }
                item.f_isBind = true
            }
            
        } else if let model = item.f_buNEAd { // 模版渲染
            model.rootViewController = presentVC
            
            // XMLBUNativeAdTrigger 内部弱持有 XMLADXLoaderItem, 故不会产生循环引用
            let trigger: XMLBUNativeAdTrigger = XMLBUNativeAdTrigger.trigger(with: item)
            trigger.flowView = flowView
            item.f_buTrigger = trigger
            
            let adSource: XMLNativeAdFlowDataSource = XMLNativeAdFlowDataSource()
            adSource.adContainer.tag = XMLADXManager.kPatchADContainTag
            adSource.renderView  = model
            adSource.renderSize  = model.bounds.size
            adSource.adContainer.isUserInteractionEnabled = true
            
            flowView.updateFlowViewInfo(adSource)
            bindImageView(flowView.coverImageView(), adSource: adSource, item: item, statistic: exposure)
            item.f_isBind = true
        }
    }
    
    private func bindGDTAd(_ item: XMLADXLoaderItem, presentVC: UIViewController,
                           flowView: XMNativeAdFlowViewDelegate, exposure: Bool) {
        guard let model = item.f_gdtAd else { return }
        // XMLGDTNativeAdTrigger 内部弱持有 XMLADXLoaderItem, 故不会产生循环引用
        let trigger: XMLGDTNativeAdTrigger = XMLGDTNativeAdTrigger.trigger(with: item)
        trigger.flowView = flowView
        trigger.nativeAdView.viewController = presentVC
        trigger.nativeAdView.delegate = trigger
        trigger.nativeAdView.tag = XMLADXManager.kPatchADContainTag
        trigger.nativeAdView.clipsToBounds = false
        trigger.nativeAdView.layer.masksToBounds = false
        item.f_gdtTrigger = trigger
        if exposure {
            trigger.nativeAdView.registerDataObject(model, clickableViews: flowView.clickableViews())
            item.f_isBind = true
        }
        
        let adSource: XMLNativeAdFlowDataSource = XMLNativeAdFlowDataSource()
        adSource.title        = model.title
        adSource.desc         = model.desc
        adSource.logoView     = trigger.nativeAdView.logoView
        adSource.logoSize     = CGSize(width: 40, height: 12.1727791)
        adSource.adContainer  = trigger.nativeAdView
        adSource.mediaView    = model.isVideoAd ? trigger.nativeAdView.mediaView : nil
        // 广告描述
        if let adDescription = model.desc, !adDescription.isEmpty {
            adSource.adDescInfo = adDescription
        } else if let adTitle = model.title, !adTitle.isEmpty {
            adSource.adDescInfo = adTitle
        } else if item.advertiserInfo.count > 0 {
            adSource.adDescInfo = "由\(item.advertiserInfo)推荐"
        }
        // 广告主
        if let adTitle = model.title, !adTitle.isEmpty {
            adSource.adVertiserInfo = adTitle
        }
        if let isScatter = item.item?.xml_showType().isScatter, isScatter, let url = model.iconUrl, url.count > 0 {
            adSource.iconURLs      = [url]
        }
        flowView.updateFlowViewInfo(adSource)
        if let imageView = flowView.coverImageView() {
            let bundlePath : String   = Bundle(for: XMLRewardedVideoToolBar.self).path(forResource: "XMADXMedia", ofType: "bundle") ?? ""
            let placeholder: UIImage? = UIImage(named: "ad_flow_placeholder_bg", in: Bundle(path: bundlePath), compatibleWith: nil)
            model.bindImageViews([imageView], placeholder: placeholder ?? UIImage.imageFromColor(.white))
        }
        guard exposure else { return }
        if let showType = item.item?.xml_showType(), showType.isInterruptPatch { // 视频前插视频
            if model.isVideoAd { trigger.nativeAdView.mediaView.muteEnable(false) }
        } else {
            // 信息流视频关闭自动播放
            if model.isVideoAd, !XMLADXConfig.isFeedVideoADAutoPlay() { trigger.nativeAdView.mediaView.pause() }
        }
        if model.isVideoAd { self.recoverAudioControlIfNeed() }
    }
    
    private func bindDRAd(_ item: XMLADXLoaderItem, presentVC: UIViewController,
                          flowView: XMNativeAdFlowViewDelegate, exposure: Bool) {
        guard let model = item.f_xmAd else { return }
        // XMLDRNativeAdTrigger 内部弱持有 XMLADXLoaderItem, 故不会产生循环引用
        let trigger: XMLDRNativeAdTrigger = XMLDRNativeAdTrigger.trigger(with: item)
        trigger.flowView = flowView
        trigger.viewController = presentVC
        item.f_drTrigger = trigger
        trigger.nativeAd?.nativeAdView.registerClickableViews(flowView.clickableViews())
        trigger.nativeAd?.nativeAdView.flowImageView = flowView.coverImageView()
        
        let adSource: XMLNativeAdFlowDataSource = XMLNativeAdFlowDataSource()
        adSource.adContainer.tag = XMLADXManager.kPatchADContainTag
        adSource.image           = model.nativeAdView.videoPlayer.imageView.image
        adSource.imageURL        = model.imageURL
        adSource.title           = model.title
        adSource.desc            = model.desc
        adSource.logoView        = model.nativeAdReletedView.logoView()
        adSource.logoSize        = model.nativeAdReletedView.logoViewSize()
        adSource.sourceView      = model.nativeAdReletedView.sourceView()
        adSource.sourceSize      = model.nativeAdReletedView.sourceViewSize()
        adSource.mediaView       = model.nativeAdView.videoLocalURL.isEmpty ? nil : model.nativeAdView
        // 广告描述
        if model.desc.count > 0 {
            adSource.adDescInfo = model.desc
        } else if item.advertiserInfo.count > 0 {
            adSource.adDescInfo = "由\(item.advertiserInfo)推荐"
        }
        // 广告主
        if let name = item.item?.name, name.count > 0 {
            adSource.adVertiserInfo = name
        } else if item.advertiserInfo.count > 0 {
            adSource.adVertiserInfo = "由\(item.advertiserInfo)推荐"
        }
        if let isScatter = item.item?.xml_showType().isScatter, isScatter {
            adSource.iconIsDR = true
            if let urls = item.item?.morePics, urls.count > 0 {
                adSource.iconURLs      = urls
            } else if let path = Bundle(for: XMLPatchFlower.self).path(forResource: "XMADXMedia", ofType: "bundle"), path.count > 0,
                      let bundle = Bundle(path: path),
                      let placeholder = UIImage(named: "pic_sahua_moren", in: bundle, compatibleWith: nil) {
                    adSource.iconImages = [placeholder]
            }
        }
        flowView.updateFlowViewInfo(adSource)
        bindImageView(flowView.coverImageView(), adSource: adSource, item: item, statistic: exposure)
        if exposure, let view = item.f_xmAd?.nativeAdView {
            item.f_isBind = true
            trigger.mediaViewWillVisible(view)
        }
    }
    
    private func bindBDAd(_ item: XMLADXLoaderItem, presentVC: UIViewController,
                          flowView: XMNativeAdFlowViewDelegate, exposure: Bool) {
        guard let model = item.f_bdAd else { return }
        // XMLBDNativeAdTrigger 内部弱持有 XMLADXLoaderItem, 故不会产生循环引用
        let trigger: XMLBDNativeAdTrigger = XMLBDNativeAdTrigger.trigger(with: item)
        trigger.flowView = flowView
        item.f_reqBdAd?.presentAdViewController = presentVC
        item.f_bdTrigger = trigger
        trigger.registerClickableViews(flowView.clickableViews())
        
        let adSource: XMLNativeAdFlowDataSource = XMLNativeAdFlowDataSource()
        adSource.adContainer.tag = XMLADXManager.kPatchADContainTag
        if !model.mainImageURLString.isEmpty, let url = URL(string: model.mainImageURLString) { adSource.imageURL = url }
        adSource.title        = model.title
        adSource.desc         = model.text
        adSource.logoView     = trigger.nativeAdLogo
        adSource.logoSize     = CGSize(width: 38, height: 13)
        // 广告描述
        if !model.text.isEmpty {
            adSource.adDescInfo = model.text
        } else if !model.title.isEmpty {
            adSource.adDescInfo = model.title
        } else if item.advertiserInfo.count > 0 {
            adSource.adDescInfo = "由\(item.advertiserInfo)推荐"
        }
        // 广告主
        if !model.title.isEmpty {
            adSource.adVertiserInfo = model.title
        }
        if let isScatter = item.item?.xml_showType().isScatter, isScatter, !model.iconImageURLString.isEmpty {
            adSource.iconURLs = [model.iconImageURLString]
        }
        flowView.updateFlowViewInfo(adSource)
        bindImageView(flowView.coverImageView(), adSource: adSource, item: item, statistic: exposure)
        if exposure {
            item.f_isBind = true
            model.trackImpression(adSource.adContainer)
        }
    }
    
    private func bindJDAd(_ item: XMLADXLoaderItem, presentVC: UIViewController,
                          flowView: XMNativeAdFlowViewDelegate, exposure: Bool) {
        guard let model = item.f_jdAd else { return }
        // XMLJDNativeAdTrigger 内部弱持有 XMLADXLoaderItem, 故不会产生循环引用
        let trigger: XMLJDNativeAdTrigger = XMLJDNativeAdTrigger.trigger(with: item)
        trigger.flowView = flowView
        item.f_jdTrigger = trigger
        
        let adSource: XMLNativeAdFlowDataSource = XMLNativeAdFlowDataSource()
        adSource.adContainer.tag = XMLADXManager.kPatchADContainTag
        let adData = model.data?.first
        if let imgUrlStr = adData?.adImages.first {
            adSource.imageURL = URL(string: imgUrlStr)
        }
        adSource.title       = adData?.adTitle
        adSource.desc        = adData?.adDescription
        let jdNativeAdWidget = JADNativeAdWidget()
        adSource.logoView    = jdNativeAdWidget.logoAdWidget
        adSource.logoSize    = CGSize(width: 26, height: 10)
        flowView.updateFlowViewInfo(adSource)
        bindImageView(flowView.coverImageView(), adSource: adSource, item: item, statistic: exposure)
        let containerView = flowView as? UIView ?? UIView()
        item.f_isBind = true
        model.registerContainer(containerView, withClickableViews: flowView.clickableViews(), withClosableViews: [])
    }
    
    // 绑定封面信息
    private func bindImageView(_ imageView: UIImageView?, adSource: XMLNativeAdFlowDataSource,
                               item: XMLADXLoaderItem, statistic: Bool = true) {
        guard let `imageView` = imageView else { return }
        let bundlePath : String   = Bundle(for: XMLRewardedVideoToolBar.self).path(forResource: "XMADXMedia", ofType: "bundle") ?? ""
        let placeholder: UIImage? = UIImage(named: "ad_flow_placeholder_bg", in: Bundle(path: bundlePath), compatibleWith: nil)
        let isVideoAd  : Bool     = (adSource.mediaView != nil)
        if let image = adSource.image {
            imageView.image = image
            if isVideoAd == false, statistic { item.uploadVisibleStatisticInfo() }
        } else if let url = adSource.imageURL {
            imageView.kf.setImage(with: url, placeholder: placeholder, completionHandler: { [weak imageView] (result) in
                guard let wImageView = imageView else { return }
                switch result {
                case .success(let value):
                    wImageView.image = value.image
                    guard isVideoAd == false, statistic else { return }
                    if wImageView.image != nil {
                        item.uploadVisibleStatisticInfo()
                    } else {
                        item.uploadStatisticInfo(item.isInCache ? 1009 : 1001)
                    }
                case .failure:
                    guard isVideoAd == false, statistic else { return }
                    item.uploadStatisticInfo(item.isInCache ? 1009 : 1001)
                }
            })
        }
    }
}

// MARK: - 插屏 Method
extension XMLADXManager {
    // 加载插屏广告
    public func loadPlaqueAd(_ params: [String: Any], completion: @escaping (([String: Any]) -> Void)) {
        let positionName: String = (params["positionName"] as? String) ?? ""
        self.mediaDelegate?.adxManager(self, willHandle: params)
        XMLAdPositionInfoManager.updatePositionInfoIfNeed(params)
        XMLADXManager.resetPlaqueAdEnvironment()
        let isIgnoreADX: Bool = (params["isIgnoreADX"] as? Bool) ?? false
        guard isIgnoreADX == false else { handleIgnoreADXPlaqueAd(params, completion: completion); return }
        let needLoading: Bool = (params["needLoading"] as? Bool) ?? true
        DispatchQueue.main.async { if needLoading { XMLGlobalLoadingManager.shared().show() } }
        XMLADXPreManager.preTreat(at: .customPlaque(params), type: .plaque, extraInfo: params, asyncHandle: { [weak self] (items, isSuccess, reqTime) in
            if isSuccess == false {
                XMLADXLoaderManager.shared().prepareLoaderDirectlyFromCache(positionName, beginTime: reqTime) { [weak self] (obj, _) in self?.handlePlaqueLoaderItem(obj, params: params, isNeedDismiss: needLoading, completion: completion) }
            } else if items.count > 0 {
                XMLADXLoaderManager.shared().prepareLoader(.plaque, items: items) { [weak self] (obj, _) in
                    self?.handlePlaqueLoaderItem(obj, params: params, isNeedDismiss: needLoading, completion: completion)
                }
            } else {
                self?.handlePlaqueLoaderItem(nil, params: params, isNeedDismiss: needLoading, completion: completion)
            }
        }) { [weak self] (obj, _) in
            self?.handlePlaqueLoaderItem(obj, params: params, isNeedDismiss: needLoading, completion: completion)
        }
    }
    
    // 加载插屏广告
    public func preparePlaqueAd(_ params: [String: Any], completion: @escaping ((Any?) -> Void)) {
        let positionName: String = (params["positionName"] as? String) ?? ""
        XMLAdPositionInfoManager.updatePositionInfoIfNeed(params)
        XMLADXManager.resetPlaqueAdEnvironment()
        let isIgnoreADX: Bool = (params["isIgnoreADX"] as? Bool) ?? false
        guard isIgnoreADX == false else { preparePlaqueAdFromLocal(params, completion: completion); return }
        let needLoading: Bool = (params["needLoading"] as? Bool) ?? true
        DispatchQueue.main.async { if needLoading { XMLGlobalLoadingManager.shared().show() } }
        XMLADXPreManager.preTreat(at: .customPlaque(params), type: .plaque, extraInfo: params, asyncHandle: { (items, isSuccess, reqTime) in
            if isSuccess == false {
                XMLADXLoaderManager.shared().prepareLoaderDirectlyFromCache(positionName, beginTime: reqTime) { (obj, _) in completion(obj) }
            } else if items.count > 0 {
                XMLADXLoaderManager.shared().prepareLoader(.plaque, items: items) { (obj, _) in completion(obj) }
            } else {
                completion(nil)
            }
        }) { (obj, _) in
            completion(obj)
        }
    }
    
    // 加载插屏广告从本地数据
    func preparePlaqueAdFromLocal(_ params: [String: Any], completion: @escaping ((Any?) -> Void)) {
        let positionName: String = (params["positionName"] as? String) ?? ""
        let slotId: String = (params["slotId"] as? String) ?? ""
        let item = XMLAdTypeItemModel.ad(slotId, posiName: positionName, extraInfo: params)
        let needLoading: Bool = (params["needLoading"] as? Bool) ?? true
        DispatchQueue.main.async { if needLoading { XMLGlobalLoadingManager.shared().show() } }
        XMLADXLoaderManager.shared().prepareLoader(.plaque, items: [item], isIgnoreADX: true) { (obj, _) in completion(obj) }
    }
    
    // 忽略ADX直接使用本地解析出的插屏XMLAdTypeItemModel
    func handleIgnoreADXPlaqueAd(_ params: [String: Any], completion: @escaping (([String: Any]) -> Void)) {
        let positionName: String = (params["positionName"] as? String) ?? ""
        let slotId: String = (params["slotId"] as? String) ?? ""
        let item = XMLAdTypeItemModel.ad(slotId, posiName: positionName, extraInfo: params)
        let needLoading: Bool = (params["needLoading"] as? Bool) ?? true
        DispatchQueue.main.async { if needLoading { XMLGlobalLoadingManager.shared().show() } }
        XMLADXLoaderManager.shared().prepareLoader(.plaque, items: [item], isIgnoreADX: true) { [weak self] (obj, _) in self?.handlePlaqueLoaderItem(obj, params: params, isNeedDismiss: needLoading, completion: completion) }
    }
    
    // 渲染解析出的插屏广告XMLADXLoaderItem
    public func handlePlaqueLoaderItem(_ item       : XMLADXLoaderItem?,
                                       params       : [String: Any],
                                       isNeedDismiss: Bool = true,
                                       isNeedDefault: Bool = true,
                                       completion   : @escaping (([String: Any]) -> Void)) {
        DispatchQueue.main.async {
            if isNeedDismiss { XMLGlobalLoadingManager.shared().dismiss() }
            guard let item = item else {
                // 是否使用兜底视频
                guard isNeedDefault else { completion([:]); return }
                self.loadPropUpPlaqueAd(params, isNeedDismiss: isNeedDismiss, completion: completion)
                return
            }
            if item.isBuAd() && item.item?.xml_showType() == .lanPlaque {
                let trigger = XMLBUNativePlaqueAdTrigger.trigger(with: item, params: params)
                self.lastBUNativePlaqueAdTrigger = trigger
                self.lastBUNativePlaqueAdTrigger?.showAd(completion)
            } else if item.isGDTAd() && item.item?.xml_showType() == .lanPlaque {
                let trigger = XMLGDTNativePlaqueAdTrigger.trigger(with: item, params: params)
                self.lastGDTNativePlaqueAdTrigger = trigger
                self.lastGDTNativePlaqueAdTrigger?.showAd(completion)
            } else if item.isxmAd() {
               let trigger = XMLDRNativePlaqueAdTrigger.trigger(with: item, params: params)
               self.lastDRNativePlaqueAdTrigger = trigger
               self.lastDRNativePlaqueAdTrigger?.showAd(completion)
            } else if item.isBuAd() {
                let trigger = XMLBUPlaqueAdTrigger.trigger(with: item, params: params)
                self.lastBUPlaqueAdTrigger = trigger
                self.lastBUPlaqueAdTrigger?.showAd(completion)
            } else if item.isGDTAd() {
                let trigger = XMLGDTPlaqueAdTrigger.trigger(with: item, params: params)
                self.lastGDTPlaqueAdTrigger = trigger
                self.lastGDTPlaqueAdTrigger?.showAd(completion)
            } else {
                completion([:])
            }
        }
    }
    
    // 加载兜底插屏广告
    private func loadPropUpPlaqueAd(_ params: [String: Any],
                                    isNeedDismiss: Bool = true,
                                    completion: @escaping (([String: Any]) -> Void)) {
        guard let isPropUp     = params["isPropUp"] as? Bool, isPropUp,
              let positionName = params["positionName"] as? String,
              let slotId       = params["slotId"] as? String else { completion([:]); return }
        let item = XMLAdTypeItemModel.ad(slotId, posiName: positionName, extraInfo: params)
        XMLADXLoaderManager.shared().prepareLoader(.plaque, items: [item]) { [weak self] (obj, _) in
            self?.handlePlaqueLoaderItem(obj, params: params, isNeedDismiss: isNeedDismiss, isNeedDefault: false, completion: completion)
        }
    }
    
    // 重置插屏广告播放环境
    public class func resetPlaqueAdEnvironment() {
        Self.shared().lastBUPlaqueAdTrigger  = nil
        Self.shared().lastGDTPlaqueAdTrigger = nil
        Self.shared().lastDRNativePlaqueAdTrigger = nil
        Self.shared().lastBUNativePlaqueAdTrigger = nil
        Self.shared().lastGDTNativePlaqueAdTrigger = nil
    }
}
