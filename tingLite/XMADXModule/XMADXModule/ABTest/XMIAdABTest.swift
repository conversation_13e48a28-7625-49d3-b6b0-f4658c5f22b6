//
//  XMIAdABTest.swift
//  XMADXModule
//
//  Created by xiaodong2.zhang on 2025/7/1.
//  Copyright © 2025 ximalaya. All rights reserved.
//

import Foundation

// MARK: - XMIAdABTest Implementation
public class XMIAdABTest {

    // MARK: - Private Properties

    private static let kABTestConfigKey = "XMIAdABTestLabConfig"

    private static var _abLabConfigDic: [String: Any]?
    private static let configQueue = DispatchQueue(label: "com.ximalaya.ad.abtest.config", attributes: .concurrent)

    private static var abLabConfigDic: [String: Any]? {
        return configQueue.sync {
            if _abLabConfigDic == nil {
                _abLabConfigDic = loadConfigFromCache()
            }
            return _abLabConfigDic
        }
    }

    private static func loadConfigFromCache() -> [String: Any]? {
        if let data = UserDefaults.standard.data(forKey: kABTestConfigKey),
           let config = try? JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
            return config
        }

        return [:]
    }

    // MARK: - Public Methods

    /// 获取Bool类型的AB测试值
    /// - Parameters:
    ///   - key: AB测试的key
    ///   - defaultValue: 默认值
    /// - Returns: Bool值
    public static func getBoolValue(withKey key: String, defaultValue: Bool) -> Bool {
        guard let value = getValue(withKey: key) else {
            return defaultValue
        }

        if let boolValue = value as? Bool {
            return boolValue
        } else if let numberValue = value as? NSNumber {
            return numberValue.boolValue
        } else if let stringValue = value as? String {
            return stringValue.lowercased() == "true" || stringValue == "1"
        }

        return defaultValue
    }

    /// 获取Integer类型的AB测试值
    /// - Parameters:
    ///   - key: AB测试的key
    ///   - defaultValue: 默认值
    /// - Returns: Integer值
    public static func getIntegerValue(withKey key: String, defaultValue: Int) -> Int {
        guard let value = getValue(withKey: key) else {
            return defaultValue
        }

        if let intValue = value as? Int {
            return intValue
        } else if let numberValue = value as? NSNumber {
            return numberValue.intValue
        } else if let stringValue = value as? String {
            return Int(stringValue) ?? defaultValue
        }

        return defaultValue
    }

    /// 获取String类型的AB测试值
    /// - Parameters:
    ///   - key: AB测试的key
    ///   - defaultValue: 默认值
    /// - Returns: String值
    public static func getStrValue(withKey key: String, defaultValue: String) -> String {
        guard let value = getValue(withKey: key) else {
            return defaultValue
        }

        if let stringValue = value as? String {
            return stringValue
        } else {
            return String(describing: value)
        }
    }

    /// 获取Double类型的AB测试值
    /// - Parameters:
    ///   - key: AB测试的key
    ///   - defaultValue: 默认值
    /// - Returns: Double值
    public static func getDoubleValue(withKey key: String, defaultValue: Double) -> Double {
        guard let value = getValue(withKey: key) else {
            return defaultValue
        }

        if let doubleValue = value as? Double {
            return doubleValue
        } else if let numberValue = value as? NSNumber {
            return numberValue.doubleValue
        } else if let stringValue = value as? String {
            return Double(stringValue) ?? defaultValue
        }

        return defaultValue
    }

    /// 获取原始值
    /// - Parameter key: AB测试的key
    /// - Returns: 原始值，可能为nil
    public static func getValue(withKey key: String) -> Any? {
        guard let configDic = abLabConfigDic else {
            return nil
        }

        let result = configDic[key]

        // 过滤NSNull
        if result is NSNull {
            return nil
        }

        return result
    }

    /// 获取JSON对象
    /// - Parameters:
    ///   - key: AB测试的key
    ///   - defaultObject: 默认对象
    /// - Returns: 解析后的JSON对象
    public static func getJsonObject(withKey key: String, defaultObject: Any?) -> Any? {
        let stringValue = getStrValue(withKey: key, defaultValue: "")

        if stringValue.isEmpty {
            return defaultObject
        }

        guard let data = stringValue.data(using: .utf8) else {
            return defaultObject
        }

        do {
            let jsonObject = try JSONSerialization.jsonObject(with: data, options: [])
            return jsonObject
        } catch {
            return defaultObject
        }
    }

    // MARK: - Cache Management

    /// 手动更新配置缓存
    /// - Parameter config: 新的配置字典
    public static func updateConfigCache(_ config: [String: Any]?) {
        configQueue.async(flags: .barrier) {
            _abLabConfigDic = config

            // 更新 UserDefaults 缓存
            if let config = config {
                if let data = try? JSONSerialization.data(withJSONObject: config, options: []) {
                    UserDefaults.standard.set(data, forKey: kABTestConfigKey)
                }
            } else {
                UserDefaults.standard.removeObject(forKey: kABTestConfigKey)
            }
        }
    }
}
