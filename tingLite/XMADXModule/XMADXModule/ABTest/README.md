# XMIAdABTest Swift 实现说明

## 概述

XMIAdABTest 是广告 ABTest 功能的纯 Swift 实现，参考了 OC 版本的设计思路。该实现提供了线程安全的配置缓存和多种数据类型的获取方法。

## 主要功能

### 1. 配置获取方法

```swift
// 获取布尔值
let boolValue = XMIAdABTest.getBoolValue(withKey: "test_key", defaultValue: false)

// 获取整数值
let intValue = XMIAdABTest.getIntegerValue(withKey: "test_key", defaultValue: 0)

// 获取字符串值
let stringValue = XMIAdABTest.getStrValue(withKey: "test_key", defaultValue: "default")

// 获取双精度浮点数值
let doubleValue = XMIAdABTest.getDoubleValue(withKey: "test_key", defaultValue: 0.0)

// 获取任意类型值
let anyValue = XMIAdABTest.getValue(withKey: "test_key")

// 获取 JSON 对象
let jsonObject = XMIAdABTest.getJsonObject(withKey: "test_key", defaultObject: nil)
```

### 2. 配置管理

```swift
// 清除配置缓存
XMIAdABTest.clearConfigCache()

// 手动更新配置缓存
let newConfig = ["key1": "value1", "key2": 123]
XMIAdABTest.updateConfigCache(newConfig)
```

## 初始化配置

### 1. Swift 版本的 initAdx

```swift
// 初始化 ADX 配置
XMLAdRequest.initializeAdx()

// 或者使用实例方法
let adRequest = XMLAdRequest()
adRequest.initAdx()
```

### 2. 配置缓存机制

配置数据会被缓存到以下位置：
- **内存缓存**：`_abLabConfigDic` 静态变量，提供快速访问
- **持久化缓存**：UserDefaults 中的 `XMIAdConfig_abLabConfig` 键，支持应用重启后恢复

## 线程安全

所有配置操作都使用了并发队列 `configQueue` 来确保线程安全：

```swift
private static let configQueue = DispatchQueue(label: "com.ximalaya.ad.abtest.config", attributes: .concurrent)
```

- 读操作使用 `sync` 同步执行
- 写操作使用 `async(flags: .barrier)` 异步执行，确保写操作的独占性

## 错误处理

所有方法都包含了适当的错误处理：
- JSON 解析失败时返回默认值
- 类型转换失败时返回默认值
- 网络请求失败时记录错误日志

## 使用示例

```swift
// 在应用启动时初始化配置
XMLAdRequest.initializeAdx()

// 在需要的地方获取 ABTest 配置
class SomeViewController: UIViewController {
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 获取是否启用新功能的配置
        let isNewFeatureEnabled = XMIAdABTest.getBoolValue(withKey: "new_feature_enabled", defaultValue: false)
        
        if isNewFeatureEnabled {
            // 启用新功能
            enableNewFeature()
        }
        
        // 获取广告展示频率配置
        let adFrequency = XMIAdABTest.getIntegerValue(withKey: "ad_frequency", defaultValue: 5)
        configureAdFrequency(adFrequency)
    }
    
    private func enableNewFeature() {
        // 新功能实现
    }
    
    private func configureAdFrequency(_ frequency: Int) {
        // 配置广告频率
    }
}
```

## 注意事项

1. **配置更新**：当服务端配置更新时，需要调用 `clearConfigCache()` 清除缓存，然后重新调用 `initializeAdx()` 获取最新配置。

2. **默认值**：所有获取方法都需要提供默认值，确保在配置不存在或解析失败时有合理的回退值。

3. **性能考虑**：配置数据会被缓存在内存中，频繁调用获取方法不会产生性能问题。

4. **纯 Swift 实现**：该实现是纯 Swift 代码，不依赖 OC 桥接，具有更好的类型安全性和性能。
