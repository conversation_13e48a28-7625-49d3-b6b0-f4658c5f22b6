//
//  XMIAdABTestDemo.swift
//  XMADXModule
//
//  Created by xiaodong2.zhang on 2025/7/1.
//  Copyright © 2025 ximalaya. All rights reserved.
//

import Foundation

/// ABTest功能演示和测试类
public class XMIAdABTestDemo {
    
    /// 演示ABTest功能的基本使用
    public static func demonstrateABTestUsage() {
        print("=== XMIAdABTest 功能演示 ===")
        
        // 1. 首先初始化ADX配置
        print("1. 初始化ADX配置...")
        XMLAdRequest.initializeAdx()
        
        // 等待一段时间让网络请求完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            // 2. 演示各种数据类型的获取
            print("2. 演示ABTest配置获取:")
            
            // 布尔值测试
            let isNewFeatureEnabled = XMIAdABTest.getBoolValue(withKey: "new_feature_enabled", defaultValue: false)
            print("   - 新功能开关: \(isNewFeatureEnabled)")
            
            // 整数值测试
            let adFrequency = XMIAdABTest.getIntegerValue(withKey: "ad_frequency", defaultValue: 5)
            print("   - 广告频率: \(adFrequency)")
            
            // 字符串值测试
            let welcomeMessage = XMIAdABTest.getStrValue(withKey: "welcome_message", defaultValue: "欢迎使用")
            print("   - 欢迎消息: \(welcomeMessage)")
            
            // 双精度浮点数测试
            let adDisplayRatio = XMIAdABTest.getDoubleValue(withKey: "ad_display_ratio", defaultValue: 0.5)
            print("   - 广告展示比例: \(adDisplayRatio)")
            
            // 原始值测试
            let rawValue = XMIAdABTest.getValue(withKey: "raw_config")
            print("   - 原始配置值: \(rawValue ?? "nil")")
            
            // JSON对象测试
            let jsonConfig = XMIAdABTest.getJsonObject(withKey: "json_config", defaultObject: nil)
            print("   - JSON配置: \(jsonConfig ?? "nil")")
            
            print("3. ABTest功能演示完成!")
        }
    }
    
    /// 测试配置缓存功能
    public static func testConfigCache() {
        print("=== 测试配置缓存功能 ===")
        
        // 创建测试配置
        let testConfig: [String: Any] = [
            "test_bool": true,
            "test_int": 42,
            "test_string": "测试字符串",
            "test_double": 3.14,
            "test_json": "{\"key\":\"value\"}"
        ]
        
        // 1. 更新配置缓存
        print("1. 更新配置缓存...")
        XMIAdABTest.updateConfigCache(testConfig)
        
        // 2. 读取配置
        print("2. 读取配置:")
        let boolValue = XMIAdABTest.getBoolValue(withKey: "test_bool", defaultValue: false)
        let intValue = XMIAdABTest.getIntegerValue(withKey: "test_int", defaultValue: 0)
        let stringValue = XMIAdABTest.getStrValue(withKey: "test_string", defaultValue: "")
        let doubleValue = XMIAdABTest.getDoubleValue(withKey: "test_double", defaultValue: 0.0)
        let jsonValue = XMIAdABTest.getJsonObject(withKey: "test_json", defaultObject: nil)
        
        print("   - 布尔值: \(boolValue)")
        print("   - 整数值: \(intValue)")
        print("   - 字符串值: \(stringValue)")
        print("   - 双精度值: \(doubleValue)")
        print("   - JSON值: \(jsonValue ?? "nil")")
        
        // 3. 清除缓存
        print("3. 清除缓存...")
        XMIAdABTest.clearConfigCache()
        
        // 4. 再次读取（应该返回默认值）
        print("4. 清除缓存后读取:")
        let boolAfterClear = XMIAdABTest.getBoolValue(withKey: "test_bool", defaultValue: false)
        let intAfterClear = XMIAdABTest.getIntegerValue(withKey: "test_int", defaultValue: 0)
        
        print("   - 布尔值（应为默认值false）: \(boolAfterClear)")
        print("   - 整数值（应为默认值0）: \(intAfterClear)")
        
        print("配置缓存测试完成!")
    }
    
    /// 测试线程安全性
    public static func testThreadSafety() {
        print("=== 测试线程安全性 ===")
        
        let testConfig: [String: Any] = [
            "thread_test": "initial_value"
        ]
        
        XMIAdABTest.updateConfigCache(testConfig)
        
        let group = DispatchGroup()
        let queue = DispatchQueue.global(qos: .default)
        
        // 并发读取测试
        for i in 0..<10 {
            group.enter()
            queue.async {
                let value = XMIAdABTest.getStrValue(withKey: "thread_test", defaultValue: "default")
                print("线程 \(i) 读取到值: \(value)")
                group.leave()
            }
        }
        
        // 并发写入测试
        for i in 0..<5 {
            group.enter()
            queue.async {
                let newConfig = ["thread_test": "value_from_thread_\(i)"]
                XMIAdABTest.updateConfigCache(newConfig)
                print("线程 \(i) 更新了配置")
                group.leave()
            }
        }
        
        group.notify(queue: .main) {
            print("线程安全测试完成!")
            let finalValue = XMIAdABTest.getStrValue(withKey: "thread_test", defaultValue: "default")
            print("最终值: \(finalValue)")
        }
    }
    
    /// 运行所有测试
    public static func runAllTests() {
        print("🚀 开始运行 XMIAdABTest 所有测试...")
        
        // 基本功能演示
        demonstrateABTestUsage()
        
        // 延迟执行其他测试，避免网络请求干扰
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            testConfigCache()
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                testThreadSafety()
                
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                    print("✅ 所有测试完成!")
                }
            }
        }
    }
}
