//
//  XMModuleManager+Ad.swift
//  tingLite
//
//  Created by yangle<PERSON> on 2020/4/16.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import RouterModule
import UIKit
import AudioModule
import XMConfigModule
import XMADXModule
import XMAlertModule
import XMRNManager

extension XMModuleManager {
    
    func getAd(in type: XMLBusiAdType, params: [String : Any]?, completion: @escaping ((Any?) -> Void)) {
        if type == .welfareVideo {
            let params: [String: Any] = ["slotId": kBUAdSDK_WelfareVideo_SlotId, "positionName": type.positionName, "renderTemplate": "1", "isPropUp": true]
            XMLADXManager.shared().loadVideoAd(params, completion: completion)
        } else if type == .welfareSignVideo {
            let params: [String: Any] = ["slotId": kBUAdSDK_WelfareSignVideo_SlotId, "positionName": type.positionName, "renderTemplate": "1", "isPropUp": true]
            XMLADXManager.shared().loadVideoAd(params, completion: completion)
        } else if type == .welfareSignAlert {
            XMLADXManager.shared().loadCustomPopupAdInfo(type.adxParams) { (ad) in
                let amount      : Int = (params?["amount"] as? Int ) ?? 0
                let scoreSummary: Int = (params?["scoreSummary"] as? Int ) ?? 0
                let signInDay   : Int = (params?["signInDay"] as? Int ) ?? 0
                XMLWelfareSignAlertView().show(ad, signInDay: signInDay, amount: amount, coin: scoreSummary).didHideHandleAction = { isOpen in
                    completion(isOpen)
                }
            }
        } else if type == .welfareSignVideoAlert {
            XMLADXManager.shared().loadCustomPopupAdInfo(type.adxParams) { (ad) in
                let content: String = (params?["content"] as? String) ?? ""
                let coin: Int = (params?["coin"] as? Int ) ?? 0
                let awardCoin: Int = (params?["awardCoin"] as? Int ) ?? 0
                let adUiType: Int = XMRemote.intValue(group: "ximalaya_lite_ad", item: "PopupUI", defaultValue: 1)
                if adUiType == 2 {
                    XMLWelfareAwardCoinFAlertView().show(ad, content: content, coin: coin, awardCoin: awardCoin, adType: type).didHideHandleAction = { isOpen in
                        completion(isOpen)
                    }
                } else {
                    XMLWelfareAwardCoinAlertView().show(ad, content: content, coin: coin, awardCoin: awardCoin, adType: type).didHideHandleAction = { isOpen in
                        completion(isOpen)
                    }
                }
            }
        } else if type == .welfareBoxVideo {
            let params: [String: Any] = ["slotId": kBUAdSDK_WelfareBoxVideo_SlotId, "positionName": type.positionName, "renderTemplate": "1", "isIgnoreADX": true]
            XMLADXManager.shared().loadVideoAd(params, completion: completion)
        } else if type == .welfareBoxAlert {
            let param: [String: Any] = ["slotId": kBUAdSDK_WelfareBoxAlert_SlotId, "positionName": type.positionName, "isIgnoreADX": true]
            XMLADXManager.shared().loadCustomPopupAdInfo(param) { (ad) in
                let content: String = (params?["content"] as? String) ?? ""
                let coin: Int = (params?["coin"] as? Int ) ?? 0
                let awardCoin: Int = (params?["awardCoin"] as? Int ) ?? 0
                XMLWelfareAwardOperAlertView().show(ad, content: content, coin: coin, awardCoin: awardCoin, adType: type).didHideHandleAction = { isOpen in
                    completion(isOpen)
                }
            }
        } else if type == .welfareBoxVideoAlert {
            let param: [String: Any] = ["slotId": kBUAdSDK_WelfareBoxVAlert_SlotId, "positionName": type.positionName, "isIgnoreADX": true]
            XMLADXManager.shared().loadCustomPopupAdInfo(param) { (ad) in
                let content: String = (params?["content"] as? String) ?? ""
                let coin: Int = (params?["coin"] as? Int ) ?? 0
                let awardCoin: Int = (params?["awardCoin"] as? Int ) ?? 0
                let adUiType: Int = XMRemote.intValue(group: "ximalaya_lite_ad", item: "PopupUI", defaultValue: 1)
                if adUiType == 2 {
                    XMLWelfareAwardCoinFAlertView().show(ad, content: content, coin: coin, awardCoin: awardCoin, adType: type).didHideHandleAction = { isOpen in
                        completion(isOpen)
                    }
                } else {
                    XMLWelfareAwardCoinAlertView().show(ad, content: content, coin: coin, awardCoin: awardCoin, adType: type).didHideHandleAction = { isOpen in
                        completion(isOpen)
                    }
                }
            }
        } else if type == .homeFeedVideo {
            let params: [String: Any] = ["slotId": kBUAdSDK_HomeFeedInfoVideo_SlotId, "positionName": type.positionName, "renderTemplate": "1", "isPropUp": true]
            XMLADXManager.shared().loadVideoAd(params, completion: completion)
        } else if type == .homeFeedAlert {
            XMLADXManager.shared().loadCustomPopupAdInfo(type.adxParams) { (ad) in
                let content: String = (params?["content"] as? String) ?? ""
                let coin: Int = (params?["coin"] as? Int ) ?? 0
                let awardCoin: Int = (params?["awardCoin"] as? Int ) ?? 0
                XMLWelfareAwardOperAlertView().show(ad, content: content, coin: coin, awardCoin: awardCoin, adType: type).didHideHandleAction = { isOpen in
                    completion(isOpen)
                }
            }
        } else if type == .homeFeedVideoAlert {
            XMLADXManager.shared().loadCustomPopupAdInfo(type.adxParams) { (ad) in
                let content: String = (params?["content"] as? String) ?? ""
                let coin: Int = (params?["coin"] as? Int ) ?? 0
                let awardCoin: Int = (params?["awardCoin"] as? Int ) ?? 0
                let adUiType: Int = XMRemote.intValue(group: "ximalaya_lite_ad", item: "PopupUI", defaultValue: 1)
                if adUiType == 2 {
                    XMLWelfareAwardCoinFAlertView().show(ad, content: content, coin: coin, awardCoin: awardCoin, adType: type).didHideHandleAction = { isOpen in
                        completion(isOpen)
                    }
                } else {
                    XMLWelfareAwardCoinAlertView().show(ad, content: content, coin: coin, awardCoin: awardCoin, adType: type).didHideHandleAction = { isOpen in
                        completion(isOpen)
                    }
                }
            }
        } else if type == .welfareVideoAlert {
            XMLADXManager.shared().loadCustomPopupAdInfo(type.adxParams) { (ad) in
                let content: String = (params?["content"] as? String) ?? ""
                let coin: Int = (params?["coin"] as? Int ) ?? 0
                let awardCoin: Int = (params?["awardCoin"] as? Int ) ?? 0
                let adUiType: Int = XMRemote.intValue(group: "ximalaya_lite_ad", item: "PopupUI", defaultValue: 1)
                if adUiType == 2 {
                    XMLWelfareAwardCoinFAlertView().show(ad, content: content, coin: coin, awardCoin: awardCoin, adType: type).didHideHandleAction = { isOpen in
                        completion(isOpen)
                    }
                } else {
                    XMLWelfareAwardCoinAlertView().show(ad, content: content, coin: coin, awardCoin: awardCoin, adType: type).didHideHandleAction = { isOpen in
                        completion(isOpen)
                    }
                }
            }
        } else if case .customOperateAlert = type {
            let params: [String: Any] = params ?? [String: Any]()
            XMLADXManager.shared().loadCustomPopupAdInfo(params) { (ad) in
                XMLCoinOperateAlertView().show(ad, params: params, adType: type).didHideHandleAction = { completion($0) }
            }
        } else if case .customVideo = type {
            XMLADXManager.shared().loadVideoAd(params ?? [String: Any](), completion: completion)
        } else if case .customResultAlert = type {
            let params: [String: Any] = params ?? [String: Any]()
            XMLCoinResultAlertView().show(params, adType: type).didHideHandleAction = { completion($0) }
        } else if case .customRenderAlert = type {
            let params: [String: Any] = params ?? [String: Any]()
            let alertCls: String = (params["alertCls"] as? String) ?? ""
            if alertCls.isEmpty {
                XMLADXManager.shared().loadCustomPopupAdInfo(params, completion: completion)
            } else {
                XMLADXManager.shared().loadCustomPopupAdInfo(params) { (ad) in
                    if alertCls == "XMLWelfareSignAlertView" {
                        let amount      : Int = (params["amount"] as? Int ) ?? 0
                        let scoreSummary: Int = (params["scoreSummary"] as? Int ) ?? 0
                        let signInDay   : Int = (params["signInDay"] as? Int ) ?? 0
                        XMLWelfareSignAlertView().show(ad, signInDay: signInDay, amount: amount, coin: scoreSummary).didHideHandleAction = completion
                    } else if alertCls == "XMLWelfareAwardOperAlertView" {
                        let content  : String = (params["content"] as? String) ?? ""
                        let coin     : Int    = (params["coin"] as? Int) ?? 0
                        let awardCoin: Int    = (params["awardCoin"] as? Int) ?? 0
                        XMLWelfareAwardOperAlertView().show(ad, content: content, coin: coin, awardCoin: awardCoin, adType: type).didHideHandleAction = completion
                    } else if alertCls == "XMLWelfareAwardCoinAlertView" {
                        let content  : String = (params["content"] as? String) ?? ""
                        let coin     : Int    = (params["coin"] as? Int ) ?? 0
                        let awardCoin: Int    = (params["awardCoin"] as? Int ) ?? 0
                        XMLWelfareAwardCoinAlertView().show(ad, content: content, coin: coin, awardCoin: awardCoin, adType: type).didHideHandleAction = completion
                    } else if alertCls == "XMLCoinOperateAlertView" {
                        XMLCoinOperateAlertView().show(ad, params: params, adType: type).didHideHandleAction = completion
                    } else if alertCls == "XMLWelfareAwardCoinFAlertView" {
                        let content  : String = (params["content"] as? String) ?? ""
                        let coin     : Int    = (params["coin"] as? Int ) ?? 0
                        let awardCoin: Int    = (params["awardCoin"] as? Int ) ?? 0
                        XMLWelfareAwardCoinFAlertView().show(ad, content: content, coin: coin, awardCoin: awardCoin, adType: type).didHideHandleAction = completion
                    }
                }
            }
        } else if case .customPlaque = type {
            XMLADXManager.shared().loadPlaqueAd(params ?? [String: Any](), completion: completion)
        }
    }
    
    func bindAd(in type: XMLBusiAdType, adObj: AnyObject, presentVC: UIViewController, flowView: XMNativeAdFlowViewDelegate) {
        if let model = adObj as? XMLADXLoaderItem {
            XMLADXManager.shared().bindAd(model, presentVC: presentVC, flowView: flowView)
        }
    }
    
    /// 展示自定义渲染 的广告但内部不会和第三方广告真正绑定
    func displaycustomRenderAd(adObj: AnyObject, presentVC: UIViewController, flowView: XMNativeAdFlowViewDelegate) {
        if let model = adObj as? XMLADXLoaderItem {
            XMLADXManager.shared().displayCustomRenderAd(model, presentVC: presentVC, flowView: flowView)
        }
    }
    
    func analysisAdInfo(_ adObj: AnyObject?) -> [String: Any]? {
        if let model = adObj as? XMLADXLoaderItem {
            return ["positionName": model.positionName,
                    "slotId"    : model.slotId,
                    "vender"    : model.vender,
                    "isRender"  : model.isRender,
                    "renderSize": model.renderSize,
                    "adId"      : model.adId,
                    "sdkType"   : model.item?.xml_AdType().sdkType ?? 0]
        }
        return nil
    }
    
    func uploadAdFailureInfo(adObj: AnyObject, status: Int) {
        if let model = adObj as? XMLADXLoaderItem {
            let tStatus: Int = model.isInCache ? 1009 : status
            model.uploadStatisticInfo(tStatus)
        }
    }
    
    func uploadVisibleStatisticInfo(adObj: AnyObject) {
        if let model = adObj as? XMLADXLoaderItem {
            model.uploadVisibleStatisticInfo()
        }
    }
    
    // 是否正在显示全屏广告弹窗
    func isShowingWelAlertAd() -> Bool {
        return XMLADXManager.shared().isShowingWelAlertAd
    }
    
    // ADX数据三方链接上报
    func uploadADXItemThirdURLsReport(_ urls: [String]) {
        let urls: [String] = urls.filter { !$0.isEmpty }
        // 保证序列发送不为空
        guard urls.isEmpty == false else { return }
        // 异步发送事件
        DispatchQueue.global(qos: .background).async {
            for url in urls { XMLADXReportManager.sendThirdURLReport(url, item: nil) }
        }
    }
    
    // ADX数据展示上报
    func uploadADXItemTingShowReport(_ params: [String: Any], showTokenEnable: Bool) {
        XMLADXReportManager.sendTingShowReportInfo(params, showTokenEnable: showTokenEnable)
    }
    
    // ADX数据点击上报
    func uploadADXItemTingClickReport(_ url: String, params: [String: Any], clickTokenEnable: Bool) {
        // 保证上传链接有效性
        guard url.count > 0 else { return }
        XMLADXReportManager.sendTingClickReportInfo(url, params: params, clickTokenEnable: clickTokenEnable)
    }
    
    // 获取应用最近一次进入后台的时间
    func getADXEnterBgMoment() -> TimeInterval {
        return XMLADXManager.shared().enterBgMoment
    }
    
    // 是否在开屏广告逻辑中
    func isInSplashADLogic() -> Bool {
        return XMLADXManager.shared().isInSplashADLogic
    }
    
    // 直接处理喜马物料点击事件
    func handleXMAdLinkDirectly(_ realLink: String, dpRealLink: String, inApp: Bool) {
        // 清除空格
        let realLink  : String = realLink.replacingOccurrences(of: " ", with: "")
        let dpRealLink: String = dpRealLink.replacingOccurrences(of: " ", with: "")
        // iting:// -> uting:// 转换处理(仅处理realLink)
        let rLink: String = realLink.contains("iting://") ? realLink.replacingOccurrences(of: "iting://", with: "uting://") : realLink
        
        if inApp {
            if RouterScheme.canOpenURLString(rLink) {
                _ = RouterScheme.handleOpenURLString(rLink, fromView: nil)
            } else if dpRealLink.count > 0, let dpRealLinkURL = URL(string: dpRealLink) {
                dpRealLinkURL.handleInUniversalLinksWay(true) { isHandle in
                    guard isHandle == false else { return }
                    dpRealLinkURL.handleInUniversalLinksWay(false) { isHandle in
                        guard isHandle == false else { return }
                        if dpRealLink.lowercased().hasPrefix("http") {
                            let vc = RouterBridge(nil).webViewControllerWithURL(dpRealLinkURL)
                            vc.modalPresentationStyle = .fullScreen
                            UIApplication.topViewController()?.present(vc, animated: true, completion: nil)
                        } else if rLink.lowercased().hasPrefix("http"), let rLinkURL = URL(string: rLink) {
                            let vc = RouterBridge(nil).webViewControllerWithURL(rLinkURL)
                            vc.modalPresentationStyle = .fullScreen
                            UIApplication.topViewController()?.present(vc, animated: true, completion: nil)
                        }
                    }
                }
            } else if rLink.lowercased().hasPrefix("http"), let rLinkURL = URL(string: rLink) {
                let vc = RouterBridge(nil).webViewControllerWithURL(rLinkURL)
                vc.modalPresentationStyle = .fullScreen
                UIApplication.topViewController()?.present(vc, animated: true, completion: nil)
            }
        } else {
            if dpRealLink.count > 0, let dpRealLinkURL = URL(string: dpRealLink) {
                dpRealLinkURL.handleInUniversalLinksWay(true) { isHandle in
                    guard isHandle == false else { return }
                    dpRealLinkURL.handleInUniversalLinksWay(false) { isHandle in
                        guard isHandle == false else { return }
                        if rLink.lowercased().hasPrefix("http"), let rLinkURL = URL(string: rLink) {
                            rLinkURL.handleInUniversalLinksWay(true) { isHandle in
                                guard isHandle == false else { return }
                                rLinkURL.handleInUniversalLinksWay(false) { _ in }
                            }
                        }
                    }
                }
            } else if rLink.lowercased().hasPrefix("http"), let rLinkURL = URL(string: rLink) {
                rLinkURL.handleInUniversalLinksWay(true) { isHandle in
                    guard isHandle == false else { return }
                    rLinkURL.handleInUniversalLinksWay(false) { _ in }
                }
            }
        }
    }
    
    // AD URL广告化
    func stringByRepalceADParamsWithURL(_ url: String) -> String {
        return XMLADXUtils.stringByRepalceADParamsWithURL(url, item: nil)
    }
    
    // 增加标示用以移除iting UserAgent
    func stringByRemoveiTingUAWithURL(_ url: String) -> String {
        return url.removeiTingUA()
    }
    
    /// 获取 可以加载第三方激励视频广告的对象
    func getCustomAd() -> XMNativeRewardAdProtocol? {
        return XMLADXManager.shared().createCustomADXLoader()
    }
    
    func doRN() {
//        XMLRNManager.shared.startWithUrl("xmly://rncomponent?bundle=rn_credit_center_lite2&dev_host=************:8081", from: UIApplication.topViewController(), withParameters: nil) { _, _, _ in
//            // go back handler
//        }
        // 创建RN ViewController
        let rnVC = XMRNRouter.createRNViewController(
            withUrl: "xmly://rntest?bundle=rn_credit_center_lite2&dev_host=************:8081",
            props: nil,
            backHandler: { _, _, _ in
                // go back handler
            },
            loadWhenAppear: false
        )

        // 手动push
        if let topVC = UIApplication.topViewController() {
            topVC.navigationController?.pushViewController(rnVC, animated: true)
        }
    }
}
