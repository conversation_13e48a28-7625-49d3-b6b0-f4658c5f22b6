//
//  XMModuleManager.swift
//  tingLite
//
//  Created by bruce.zhou on 2019/7/12.
//  Copyright © 2019 ximalaya. All rights reserved.
//

import UIKit
import Foundation
import class MineModule.XMLMineViewController
import class MineModule.XMLPlazaViewController
import class MineModule.XMLWelfareViewController
import func MineModule.mineModuleRegist
import MyListenModule
import RouterModule
import BaseModule
import AudioModule
import class XMAccount.XMUserInstance
import XMBase.XMEnvironment
import XMConfigModule
import XMADXModule
import class XMUtilModule.XMPublished
import class XMUIKit.XMNPWindowKit
import XMTingModel.XMAlbum
import XMTingModel.XMTrack
import XMAlertModule
import class ImmerseModule.XMIHomeViewController

class XMAppCompactModel {
    var objectWillChange: (() -> Void)?
    @XMPublished var compactValue: Bool = false {
        didSet { objectWillChange?() }
    }
    var selectedBlock: ((UIViewController) -> Void)?
}

class XMModuleManager: NSObject, XMSceneInfoProtocol {
    var persistentIdentifier: String?
    // 首页(经典)
    private var _homeVC: XMNavigationController?
    var homeViewController: XMNavigationController {
        if _homeVC == nil {
            let homeVC: UIViewController? = homeRouterBridge(self.persistentIdentifier)?.getHomeViewController(XMProductForm.classic.rawValue)
            if let vc = homeVC as? XMBaseViewController {
                vc.persistentIdentifier = self.persistentIdentifier
            }
            if let vc = homeVC {
                _homeVC = XMNavigationController.controller(rootVC: vc)
            } else {
                _homeVC = XMNavigationController.controller(rootVC: XMBaseViewController())
            }
        }
        return _homeVC!
    }
    
    // 我听
    lazy var myListenViewController: XMNavigationController = {
        let listenVC: UIViewController? = myListenRouterBridge(self.persistentIdentifier)?.getMineTingController()
        if let vc = listenVC as? XMBaseViewController {
            vc.persistentIdentifier = self.persistentIdentifier
        }
        if let vc = listenVC {
            return XMNavigationController.controller(rootVC: vc)
        }
        return XMNavigationController.controller(rootVC: XMBaseViewController())
    }()
    
    /// 肚脐眼占位
    var navelViewController = UIViewController()
    
    /// 精选页面
    private var _channelVC: XMNavigationController?
    var channelViewController: XMNavigationController {
        if _channelVC == nil {
            let novelVC = bookRouterBridge(self.persistentIdentifier)?.getChannelViewController(0)
            if let vc = novelVC as? XMBaseViewController {
                vc.persistentIdentifier = self.persistentIdentifier
            }
            if let vc = novelVC {
                _channelVC = XMNavigationController.controller(rootVC: vc)
            } else {
                _channelVC = XMNavigationController.controller(rootVC: XMBaseViewController())
            }
        }
        return _channelVC!
    }
    
    /// 福利页
    lazy var welfareViewController: XMNavigationController = {
        // 根据ABTest配置决定使用哪个控制器
        let useNewWelfareVC = XMIAdABTest.getBoolValue(withKey: "lite_welfare_rn_page_ios", defaultValue: false)

        let mineVC: UIViewController
        if useNewWelfareVC {
            mineVC = XMLWelfareViewController()
        } else {
            mineVC = XMLPlazaViewController()
        }

        if let persistentVC = mineVC as? XMLPersistentIdentifierProtocol {
            persistentVC.persistentIdentifier = self.persistentIdentifier
        }

        return XMNavigationController.controller(rootVC: mineVC)
    }()
    
    ///账号页
    lazy var mineViewController : XMNavigationController = {
        let mineVC                  = XMLMineViewController()
        mineVC.persistentIdentifier = self.persistentIdentifier
        self.mineVC = mineVC
        return XMNavigationController.controller(rootVC: mineVC)
    }()
    
    // 电台(极简)
    lazy var miniRadioViewController: XMNavigationController = {
        return XMNavigationController.controller(rootVC: self.miniRadioVC)
    }()
    
    // 电台内容(极简)
    lazy var miniRadioVC: XMIHomeViewController = {
        let homeVC: XMIHomeViewController = XMIHomeViewController()
        homeVC.persistentIdentifier = self.persistentIdentifier
        return homeVC
    }()
    
    var mineVC: XMLMineViewController? = nil
//    var compact = XMAppCompactModel()
    
    var didPress: ((XMLTabItem) -> Void)!
    
    var windowkit: XMNPWindowKit?
    
    var lastPlayPageInfo: XMTrack?
    
    var lastAPMUploadTime: TimeInterval = .zero
    
    var mediaLogicManager: XMLMediaLogicManager?
    
    var playerLocationDashboard: Bool = false
    
    /// 播放页present 的Nav
    lazy var playerPresentVCs: NSHashTable<XMNavigationController> = {
        return NSHashTable.weakObjects()
    }()
    
    var tabBarController: XMLTabBarController? {
        var tabBarController: XMLTabBarController? = nil
        if let delegate = UIApplication.shared.delegate as? AppDelegate {
            tabBarController = delegate.tabBarController
        }
        return tabBarController
    }
    
    func loadData() {
        XMRouter.shared.registerBridge(self, identify: self.persistentIdentifier ?? "")
        self.didPress = { [weak self] tab in self?.didPressed(tab) }
    }
    // 在子线程执行，内部有UI操作的要自己回到主线程
    func registerModules(_ normalLaunch: Bool) {
        
        XMUserInstance.share().registerConfig(XMAccountConfig.self)
        XMUnreadManager.shared().update()
        audioModuleRegist()
        mineModuleRegist()
        myListenModuleRegist()
        alertModulRegist(normalLaunch)
        liveRouterBridge(self.persistentIdentifier)?.registModule?()
    }
    @discardableResult func didPressed(_ tab: XMLTabItem) -> XMNavigationController? {
        tabBarController?.switchToTab(tab.rawValue)
        switch tab {
        case .home:
            return homeViewController
        case .listen:
            return myListenViewController
        case .navel:
            return nil
        case .welfare:
            return welfareViewController
        case .mine:
            return mineViewController
        }
    }
    
    @discardableResult func didPressed(_ tab: XMLMiniTabItem) -> XMNavigationController? {
        tabBarController?.switchToTab(tab.rawValue)
        switch tab {
        case .channel:
            return channelViewController
        case .radio:
            return miniRadioViewController
        case .welfare:
            return welfareViewController
        case .mine:
            return mineViewController
        }
    }
}

extension XMModuleManager {
    func subscribeAlbumGuidePop(check: Bool, _ type: Int,
                                fireCallback: ((_ type: Int, _ id: UInt, _ time: Float) -> Bool)?) {
        self.mediaLogicManager?.subscribeGuidePop(check: check, type, fireCallback: fireCallback)
    }
    #if DEBUG
    func addAlbumListenTime(_ albumId: UInt, _ duration: Float) {
        self.mediaLogicManager?.addAlbumTime(albumId, duration)
    }
    func removeAlbumListenTime() {
        self.mediaLogicManager?.removeAllAlbumTime()
    }
    #endif
    
    // 显示用户引导弹窗 type 1 订阅成功引导 2 提现成功引导 3 活跃用户引导 4 异常用户引导
    func showUserGuideAlert(_ type: Int, dismissCallback: ((Bool) -> Void)?) -> Bool {
        if type == 1 {
            let alert = XMSubscrPraiseGuideAlert()
            alert.didHideHandleAction = dismissCallback
            return alert.show()
        } else if type == 2 {
            let alert = XMWithdrPraiseGuideAlert()
            alert.didHideHandleAction = dismissCallback
            return alert.show()
        } else if type == 3 {
            let alert = XMCutelyPraiseGuideAlert()
            alert.didHideHandleAction = dismissCallback
            return alert.show()
        } else if type == 4 {
            let alert = XMAbnormityGuideAlert()
            alert.didHideHandleAction = dismissCallback
            return alert.show()
        }
        return false
    }
    
    /// 显示tab小红点图标 不支持肚脐眼上显示角标
    /// - Parameters:
    ///   - tab: 要显示那个tab的红点
    ///   - count: 显示数量，当为0时为小红点
    func showTabTips(tab: Any, count: Int) {
        tabBarController?.tabBar.addUpdateTipLabelIfNeed(tab: tab, count)
    }
    
    /// 移除tab上的小红点 不支持肚脐眼上显示角标
    /// - Parameter tab: 指定的tab
    func removeTabTips(tab: Any) {
        tabBarController?.tabBar.removeTipIfNeeds(tab: tab)
    }
    /// 更新福利tab 右上角的拆红包文案，为nil 表示移除
    func updateWelfareTabTip(_ tip: String?) {
        tabBarController?.tabBar.showFuliTipLabelIfNeed(tip)
    }
    
    /// 更新指定tab 的tab文案。为nil 表示恢复默认
    func updateWelfareTabText(_ text: String?, _ countdown: NSNumber?) {
        tabBarController?.tabBar.modifyWelfareTabText(text, countdown?.intValue, false)
    }
    /// 改变tab皮肤 前提是处于minimalism 模式
    /// - Parameter isImmerse: 是否是沉浸式
    func changeTabBarStyle(_ isImmerse: Bool) {
        tabBarController?.tabBar.isImmerse = isImmerse
    }
}

// 专辑信息暂存管理器
class XMLAlbumDetailCacheManager {
    
    static let shared: XMLAlbumDetailCacheManager = XMLAlbumDetailCacheManager()

    private static let cache: NSCache<NSString, XMAlbumDetailModel> = {
        let cache = NSCache<NSString, XMAlbumDetailModel>()
        cache.totalCostLimit = 5
        cache.countLimit     = 5
        return cache
    }()

    private init() { }

    // Save data to cache
    func cache(album: XMAlbum?, preferredType: Int? = nil) {
        guard let `album` = album else { return }
        let detailAlbum: XMAlbumDetailModel = XMAlbumDetailModel()
        detailAlbum.albumId        = album.albumId
        detailAlbum.albumTitle     = album.albumTitle
        detailAlbum.playsCount     = album.playsCount
        detailAlbum.coverMiddle    = album.coverMiddle
        detailAlbum.coverLarge     = album.coverLarge
        detailAlbum.isVipFree      = album.isVipFree
        detailAlbum.vipFreeType    = album.vipFreeType
        detailAlbum.isFree         = album.isFree
        detailAlbum.isPaid         = album.isPaid
        detailAlbum.subscribeCount = album.subscribeCount
        detailAlbum.isFavorite     = album.isFavorite
        detailAlbum.preferredType  = NSNumber(value: preferredType ?? 0)
        Self.cache.setObject(detailAlbum, forKey: NSString(string: "\(album.albumId)"))
    }

    // Retrive data from cache
    func getFromCache(albumId: UInt) -> XMAlbumDetailModel? {
        return Self.cache.object(forKey: NSString(string: "\(albumId)"))
    }
    
    // Remove data from cache
    func removeCache(albumId: UInt) {
        Self.cache.removeObject(forKey: NSString(string: "\(albumId)"))
    }
}
