//
//  AppDelegate.swift
//  tingLite
//
//  Created by bruce.zhou on 2019/7/12.
//  Copyright © 2019 ximalaya. All rights reserved.
//

import UIKit
import XMBase
import XMConfigModule
import class BaseModule.XMLClockManager
import class MediaModule.XMDownloader
import class MediaModule.XMMediaPlayer
import class XMAPM.XMAPM
import XMADXModule
import XMKingfisherWebP
import Kingfisher
import class CommBusiness.XMLPaidUnlockManager
import class CommBusiness.XMPraiseGuideManager
import enum CommBusiness.XMLUserDefaultKey
import RouterModule
import class XMAlertModule.XMPrivacyAuthorizationViewController
import let XMAlertModule.kUserPrivacyAuthorizationKey
import AppTrackingTransparency
import AdSupport
import XMScriptLoader
import XMApoloEngine


@UIApplicationMain
class AppDelegate: UIResponder, UIApplicationDelegate, XMAudioFacMHPoloDelegate {
    
    var window          : UIWindow? = nil
    var tabBarController: XMLTabBarController!
    var moduleManager   : XMModuleManager!
    var userAuthoriseVC : XMPrivacyAuthorizationViewController? = nil
    var brightTimeStamp : TimeInterval = 0 // 亮屏时间
    
    func xmAudioFacMHPoloUserId() -> UInt {
        return XMSettings.shared().userModel.user?.uid ?? 0
    }
    
    func xmAudioFacMHPoloUserToken() -> String! {
        return XMSettings.shared().userModel.user?.token ?? ""
    }
    
    func apoloShowApmLogs(_ msg: String!) {
        
    }
    
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        // 产品要求此点需要放在最前方
        brightTimeStamp = CFAbsoluteTimeGetCurrent()
        handleVersionGlobalTaskFirstTime()
        // 启动XUID模块
        XMLAppContext.shared.xuidStart()
        #if DEBUG
        let date = CFAbsoluteTimeGetCurrent()
        #endif
        if UserDefaults.standard.bool(forKey: kUserPrivacyAuthorizationKey) { // 用户已同意隐私政策
            normalAppRun(launchOptions: launchOptions)
        } else {    // 用户未同意隐私政策
            self.window = UIWindow(frame: UIScreen.main.bounds)
            self.window?.backgroundColor = UIColor.white
            self.userAuthoriseVC = XMPrivacyAuthorizationViewController()
            self.userAuthoriseVC?.userActionCallBack = { [weak self] in
                guard let wself = self else { return }
                wself.normalAppRun(false, launchOptions: launchOptions)
            }
            self.window?.rootViewController = userAuthoriseVC
            self.window?.makeKeyAndVisible()
        }
        #if DEBUG
        NSLog("--------time:%f", CFAbsoluteTimeGetCurrent() - date)
        #endif
        return true
    }
    
    func applicationDidBecomeActive(_ application: UIApplication) {
        XMDeviceManager.authorizationHandleIfNeed()
    }
    
    func applicationWillResignActive(_ application: UIApplication) {
        guard UserDefaults.standard.bool(forKey: kUserPrivacyAuthorizationKey) else { return }
        NotificationCenter.default.post(name: NSNotification.Name.init(rawValue: "applicationWillResignActive"), object: nil)
        if XMMediaPlayer.shared().isMediaPlayerReady {
            XMLADXManager.shared().recoverAudioControlIfNeed()
        }
    }
    
    func applicationDidEnterBackground(_ application: UIApplication) {
        XMLADXManager.shared().isEnterForeground = false
        guard UserDefaults.standard.bool(forKey: kUserPrivacyAuthorizationKey) else { return }
        UIApplication.shared.sendAction(#selector(resignFirstResponder), to: nil, from: nil, for: nil)
        XMEventLog.logEventWithId(8897, serviceId: "navAppSleep", properties: ["duration": Int64(CFAbsoluteTimeGetCurrent() - brightTimeStamp)])
        // 超过5分钟切后台，请求一下配置中心
        XMRemote.updateRemoteConfig()
        // 进入后台记录一下时间
        if application.applicationState == .inactive || application.applicationState == .background {
            XMLADXManager.shared().setAppEnterBgMoment()
            XMLClockManager.shared.suspend()
        }
        // 清空串码
        UIPasteboard.cleanLastCommand()
        XMLDeviceManager.shared().uploadVolumeStatusIfNeed(XMMediaPlayer.shared().currentTrack?.trackId)
        XMLAPMLogger.shared().salvage(.enterBackground)
    }
    
    func applicationWillEnterForeground(_ application: UIApplication) {
        brightTimeStamp = CFAbsoluteTimeGetCurrent()
        // 更新本地活跃记录数据
        XMPraiseGuideManager.shared().updateLivelyRecordIfNeed()
        XMLADXManager.shared().isEnterForeground = true
        guard UserDefaults.standard.bool(forKey: kUserPrivacyAuthorizationKey) else { return }
        XMEventLog.logEventWithId(8896, serviceId: "navAppWakeUp", properties: nil)
        // 进入前台广告广告SplashView逻辑
        if self.popSplashVCWhenEnterForegroundIfNeed() == false {
            NotificationCenter.default.post(name: NSNotification.Name.init(rawValue: "applicationWillEnterForeground"), object: nil)
        }
        XMLDeviceManager.shared().syncServiceTimeData()
        XMCIInfoUploaderManager.shared().syncLocalListenTime(.enterForeground)
        XMReaderUploader.shared.syncLocalReadTime(.enterForeground)
        XMLAPMLogger.shared().salvage(.enterForeground)
        XMLActivateManager.shared().activate(.enterForeground)
        // 当前设备推送信息上报
        uploadAPNsTokenIfNeed()
        application.applicationIconBadgeNumber = 0  
        // 刷新配置付费解锁免费声音播放限制环境
        XMLPaidUnlockManager.shared().refreshFreeTrackLimitEnvironmentIfNeed()
    }
    
    func applicationWillTerminate(_ application: UIApplication) {
        guard UserDefaults.standard.bool(forKey: kUserPrivacyAuthorizationKey) else { return }
        XMConfig.shared().disableFisrtLaunchStatus()
        XMMediaPlayer.shared().saveCurrentPlayHistory()
    }
    
    // MARK: - 辅助方法
    private func normalAppRun(_ isNormal: Bool = true, launchOptions: [UIApplication.LaunchOptionsKey: Any]?) {
        // APM启动时间
        XMAPM.timestampForStartLaunch()
        #if DEBUG
        var date = Date()
        #endif
        /// 切换网络环境 网络必须放在启动的最前面
        self.setupEnvironment()
        
        XMAudioFacMHPolo.sharedManager().resetLaunchUpdateBlock = {[weak self] in
            _ = self?.normalAppRun(launchOptions: launchOptions)
        }

        XMAudioFacMHPolo.sharedManager().delegate = self
        if XMAudioFacMHPolo.sharedManager().checkNeedLaunchListenedInfo() {
            return
        }
        
        //因主线程和子线程都可能使用为了防止偶现死锁，这里提前初始化
        _ = XMLADXManager.shared()
        _ = XMConfig.shared()
        XMLAppContext.shared.xuidAgree()
        XMConfig.shared().globalSerialQueue.async {
            /// 配置密码环境
            self.registerWizardEnvironment()
            XMLADXManager.registerEnvironment()
            // 延时注册部分广告平台
            XMLADXManager.delayRegisterEnvironment()
            
            KingfisherManager.shared.defaultOptions += [.processor(WebPProcessor.default), .cacheSerializer(WebPSerializer.default)]
            
            XMEventLog.logEventWithId(8893, serviceId: "navAppOnCreate", properties: nil)
            XMEventLog.logEventWithId(39747, serviceId: "slipPage", properties: ["status": XMMediaPlayer.shared().currentTrack != nil ? "有内容" : "无内容"])
        }
        self.setupNetworkModule()
        self.setupSecurityModule()
        XMLDeviceManager.shared().activeDevice()
        // 加载数据
        loadProductFormConfig()
        #if DEBUG
        NSLog("------- env load: %f", Date().timeIntervalSince(date))
        #endif
        if isNormal {
            self.window = UIWindow(frame: UIScreen.main.bounds)
            self.window?.backgroundColor = UIColor.white
            moduleManager = XMModuleManager()
            moduleManager.loadData()
            tabBarController = XMLTabBarController()
            #if DEBUG
            NSLog("------- module load: %f", Date().timeIntervalSince(date))
            #endif
            // APM广告暂停时间
            XMAPM.timestampForLaunchADView()
            self.window?.rootViewController = UIStoryboard(name: "LaunchScreen", bundle: nil).instantiateInitialViewController()
            self.window?.makeKeyAndVisible()
            #if DEBUG
            NSLog("------- tabbar load: %f", Date().timeIntervalSince(date))
            #endif
            self.handleLaunchExceptionLogic { [weak self] ignore in
                if ignore {
                    self?.window?.rootViewController = self?.tabBarController
                    self?.window?.makeKeyAndVisible()
                } else {
                    UserDefaults.standard.setValue(true, forKey: "kXMLCoolLaunchExceptionKey")
                    UserDefaults.standard.synchronize()
                    XMLADXManager.shared().lastWindow = UIApplication.shared.keyWindow
                    // IMPORT:- 此步骤不能异步执行，3DTouch和跳转都需要判断isInSplashADLogic
                    XMLADXManager.shared().isInSplashADLogic = true
                    XMConfig.shared().globalSerialQueue.async {
                        XMLADXManager.shared().loadSplashVCIfNeed()
                    }
                }
            }
        } else {
            moduleManager = XMModuleManager()
            moduleManager.loadData()
            tabBarController = XMLTabBarController()
        }
        // 处理版本更新
        self.handleVersionUpgrade()
        
        XMConfig.shared().globalSerialQueue.async {
            ///开关,配置等获取
            self.getRemoteConfigs()
        }
        self.setupUBTModule()
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.setupAPMModule()
        }
        #if DEBUG
        NSLog("----- xmModule load : %f", Date().timeIntervalSince(date))
        date = Date()
        #endif
        self.setupFingerPrintModule()
        self.setup(isNormal)
        self.openAppFromLaunchOptions(launchOptions)
        self.SDKInit()
        // 启动相关打点
        self.normalLaunchEvent()
        #if DEBUG
        NSLog("------- sdk init: %f", Date().timeIntervalSince(date))
        #endif
        // 异步加载相关配置
        DispatchQueue.global().async { [weak self] in
            guard let wself = self else { return }
            wself.delayLaunch()
        }
    }
    
    private func setup(_ isNormal: Bool = true) {
        XMConfig.shared().globalSerialQueue.async {
            self.moduleManager.registerModules(isNormal)
        }
        // 第一次安装启动不予投放广告
        if !isNormal {
            self.window?.rootViewController = tabBarController
            if isNormal { self.window?.makeKeyAndVisible() }
            NotificationCenter.default.post(name: NSNotification.Name(rawValue: "kXMAdDimssAdPresentNotificationName"), object: nil)
        }
        //更新画像cookie
        XMConfig.shared().updateFacemaskPortal(false)
    }
    
    private func handleVersionGlobalTaskFirstTime() {
        if UserDefaults.xm_bool(forKey: "KGlobalTaskVersionCheckDate", defaultValue: false) == false {
            UserDefaults.xm_setBoolSynchronizing(true, forKey: "KGlobalTaskVersionCheckDate")
            
            UserDefaults.standard.setValue(Date().timeIntervalSince1970, forKey: "kGlobalListenTaskTime")
            UserDefaults.standard.synchronize()
        }
    }
    
    private func handleVersionUpgrade() {
        
        let last_version = XMSettings.shared().version
        let bundle_version = XMSYSTEM_BUNDLEVERSION
        
        XMConfig.shared().globalSerialQueue.async {
            let appLaunchCountKey = XMLUserDefaultKey.appLaunchCount.rawValue
            var launchCount: Double = UserDefaults.standard.double(forKey: appLaunchCountKey)
            launchCount += 1
            UserDefaults.standard.set(launchCount, forKey: appLaunchCountKey)
            UserDefaults.standard.synchronize()
        }
        
        if last_version == bundle_version {
            return
        }
        
        XMSettings.shared().lastVersion = last_version
        XMSettings.shared().version = bundle_version
        XMSettings.shared().firstLaunchForUpdate = true
        XMSettings.shared().trackPlayDuration = 0
        // 只有新安装的用户，升级上来的不展示
        if last_version == "" {
            UserDefaults.xm_setBoolSynchronizing(true, forKey: XMLUserDefaultKey.newUserPopPlayTipNeed.rawValue)
        }
    }
    
    private func getRemoteConfigs() {
        XMRemote.getRemoteConfigs()
        // 没确定之前后续上报，确定后立即上报
        if #available(iOS 14.5, *) {
            if ATTrackingManager.trackingAuthorizationStatus != .notDetermined {
                XMLDeviceManager.shared().uploadASAIfNeed()
            }
        } else {
            XMLDeviceManager.shared().uploadASAIfNeed()
        }
    }
    
    private func setupEnvironment() {
        #if DEBUG
        if let envRawValue = UserDefaults.standard.object(forKey: "kDebugEnvKey") as? UInt32 {
            let env = APPENV(rawValue: envRawValue)
            XMEnvironment.setEnvironment(env)
        } else {
            XMEnvironment.setEnvironment(APPENV_WWW)
        }
        #endif
    }
    
    private func normalLaunchEvent() {
        XMConfig.shared().globalSerialQueue.async {
            var status: Int = 0 // 已授权为1，未授权2，未触发0
            if #available(iOS 14.5, *) {
                if ATTrackingManager.trackingAuthorizationStatus == .notDetermined {
                    status = 0
                } else if ATTrackingManager.trackingAuthorizationStatus == .authorized {
                    status = 1
                } else {
                    status = 2
                }
            } else {
                status = ASIdentifierManager.shared().isAdvertisingTrackingEnabled ? 1 : 2
            }
            XMEventLog.logEventWithId(8894, serviceId: "navAppOpen", properties: ["status": status])
        }
        
        var isExistFullVersion: Bool = false
        if let fullVersionURL: URL = URL(string: "iting://"), fullVersionURL.canApplicationOpen() {
            isExistFullVersion = true
        }
        XMConfig.shared().globalSerialQueue.async {
            XMEventLog.logEventWithId(6032, serviceId: "openApp", properties: ["hasMainApp": isExistFullVersion])
        }
    }
}

extension AppDelegate {
    
    private func SDKInit() {
        // 1.0.11后音频控制延迟至用户主动播放音频，不抢占当前用户的在其它应用的声音
        // XMMediaPlayer.start()
        // UIApplication.shared.beginReceivingRemoteControlEvents()
        self.becomeFirstResponder()
        XMLAppContext.shared.setupSDKOnMainThread()
        XMConfig.shared().globalSerialQueue.async {
            XMLAppContext.shared.setupSDKonBackThread()
            self.XMPaySDKInit()
        }
    }
}

extension AppDelegate {
    // 延迟处理
    private func delayLaunch() {
        // 启动下载相关配置
        XMDownloader.start()
        
        XMLRNManager.shared.initWithAppId("9999", userId: nil, deviceId: "", bundleId: nil)
    }
    
    func popSplashVCWhenEnterForegroundIfNeed() -> Bool {
        if XMLADXManager.shared().checkPopSplashVCIfNeed(true), let launchVC = UIStoryboard.init(name: "LaunchScreen", bundle: nil).instantiateInitialViewController() {
            launchVC.hiddenGlobalPlayerWindow = true
            launchVC.hiddenGlobalTaskWindow   = true
            self.window?.addSubview(launchVC.view)
            launchVC.view.tag = kSplashPageBgDefaultView_Tag
            XMLADXManager.shared().lastWindow = UIApplication.shared.keyWindow
            XMLADXManager.shared().loadSplashVCIfNeed(false)
            return true
        } else {
            return false
        }
    }
    
    // 接入主站启动监测异常机制(上次异常本次直接给予用户提示)
    private func handleLaunchExceptionLogic(_ callback: @escaping (Bool) -> Void) {
        guard UserDefaults.standard.bool(forKey: "kXMLCoolLaunchExceptionKey") else { callback(false);return }
        XMConfig.shared().globalSerialQueue.async {
            XMEventLog.logEventWithId(33480, serviceId: "others", properties: nil)
        }
        callback(true)
    }
    
    /// 加载
    private func loadProductFormConfig() {
        
        XMConfig.shared().globalSerialQueue.async {
            let configInstance = XMConfig.shared()
            let oldProduct = configInstance.productForm
            configInstance.loadProductFormConfigIfNeeds(timeout: 2) { [weak configInstance] product in
                if product != oldProduct {
                    // 确保还没有初始化，否则下次生效
                    if let tabbar = self.tabBarController?.tabBar, !tabbar.hasInit {
                        configInstance?.productForm = product
                    } else if self.tabBarController == nil {
                        configInstance?.productForm = product
                    }
                }
            }
        }
    }
}

// MARK: 屏幕旋转
extension AppDelegate {
    
    func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        return [.portrait,.landscape]
    }
}
