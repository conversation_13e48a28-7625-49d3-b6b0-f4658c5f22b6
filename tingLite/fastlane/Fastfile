# Customise this file, documentation can be found here:
# https://github.com/fastlane/fastlane/tree/master/fastlane/docs
# All available actions: https://docs.fastlane.tools/actions
# can also be listed using the `fastlane actions` command

# Change the syntax highlighting to Ruby
# All lines starting with a # are ignored when running `fastlane`

# If you want to automatically update fastlane if a new version is available:
# update_fastlane

# This is the minimum version number required.
# Update this, if you use features of a newer version
# fastlane_version "2.212.2"

default_platform :ios


platform :ios do

  def deleteDirectory(dirPath, expect_name)
    if File.directory?(dirPath)
      Dir.foreach(dirPath) do |subFile|
        if subFile != '.' and subFile != '..' 
          deleteDirectory(File.join(dirPath, subFile), expect_name)
        end
      end     
    elsif File.exist?(dirPath)
        fileName = dirPath.split('/').last
        if !fileName.include?(expect_name)
          File.delete(dirPath)
          puts "delete File: #{dirPath}"        
        end
    end
  end

  before_all do
    # ENV["SLACK_URL"] = "https://hooks.slack.com/services/..."
     #cocoapods
  end
  # 二维码
  ipa_QRCode_url = ""
  #ipa_link_url
  ipa_link_url = ""

  desc "通用构建"
  lane :build do |options|

    # method值为下面的其中一个 app-store, package, ad-hoc, enterprise, development
    export_method = options[:export_method]   
    remove_modules = options[:optionalRemoveModules]

    # appstore 的包不能移除任何模块
    if export_method != "app-store" and remove_modules and remove_modules != ""
        sh "python3 ../increment_build/hotUpdateModule.py -removeModules #{remove_modules}"
    else
      remove_modules = ""
    end    
    sh "python3 ../increment_build/PodSourceChangeCheck.py -project '../'"
    sh "../build.sh"
    #用于邮件中使用
    time = Time.now.strftime("%Y-%m-%d %H:%M:%S")

    if options[:change_version]
      change_version = options[:change_version].strip
      if change_version != ""  
        modify_xcode_version(version: change_version)
      end
    end

    scheme = options[:scheme]
    configuration = options[:configuration] 

    app_version = get_version_number(xcodeproj: ENV['TING_LITE_XCODEPROJ'], # optional
                                          target: "#{scheme}")

    build_version = get_build_number(xcodeproj: ENV['TING_LITE_XCODEPROJ'])
    
    # 移除临时文件
    if File.exist?("../increment_build/temp.json")
      File.delete("../increment_build/temp.json")
    end


    parent_path = File.expand_path("..", Dir.pwd)
    #移除缓存
    moduleCache_path = "#{parent_path}/Build/ModuleCache.noindex"
    if File.directory?(moduleCache_path)
      sh "rm -rf #{moduleCache_path}"
    end
    SYMROOT = "#{parent_path}/Build"
    symroot_ipa_path = "#{SYMROOT}/#{configuration}-iphoneos"
    if File.directory?(symroot_ipa_path)
      sh "rm -rf #{symroot_ipa_path}"
    end    

    export_symbol = 0
    if options[:export_symbol]
        export_symbol = 1
    end    
    if options[:increment_build]
      sh "python3 ../increment_build/increment_build.py -config \"#{configuration}\" -device \"iphoneos\" -exportSymbol #{export_symbol} -removeModules #{remove_modules}"
    else
      sh "python3 ../increment_build/normal_build.py -exportSymbol #{export_symbol} -changeDynamic 1"
    end

    #增加构建时间
    if configuration == "Debug"
      plist_path = "../tingLite/Info.plist" #get_info_plist_path(xcodeproj: ENV['TING_LITE_XCODEPROJ'], target: "#{scheme}")
      if `grep \"tingLiteBuildTime\" #{plist_path}` != ""
        sh "/usr/libexec/PlistBuddy -c \"Delete :tingLiteBuildTime\" #{plist_path}"
      end
      sh "/usr/libexec/PlistBuddy -c \"Add :tingLiteBuildTime string #{time}\" #{plist_path}"
    end

     # 证书类型
    match_type = "development"
    codesigning_identity= ENV["DEVELOP_CODESIGNING_IDENTIFY"]
    scheme_name = ENV['APP_NAME'] + "_development"

    if export_method == "ad-hoc"
      match_type = "adhoc"
      codesigning_identity=ENV["RELEASE_CODESIGNING_IDENTIFY"]
      scheme_name = ENV['APP_NAME'] + "_adhoc"
    elsif export_method == "app-store"
      match_type = "appstore"
      codesigning_identity=ENV["RELEASE_CODESIGNING_IDENTIFY"]
      scheme_name = ENV['APP_NAME'] + "_appstore"
    elsif export_method == "enterprise"
      match_type = "enterprise"
      codesigning_identity=ENV["RELEASE_CODESIGNING_IDENTIFY"]
      scheme_name = ENV['APP_NAME'] + "_inhouse"
    end

    # 导出版本号
    sh "export FASTLANE_APP_VERSION=#{app_version}"

    # 证书所在分支
    match_branch = "master"
    case  scheme
    when ENV['SCHEME_INHOUSE']
      
    when  ENV['SCHEME_APPSTORE']
      matchProfile(type: "#{match_type}")
    else
      raise "scheme 参数错误"
    end

    timeDay = Time.now.strftime("%Y%m%d")
    timeHMS = Time.now.strftime("%H_%M_%S")
    output_directory = "fastlane/build/"

    archive_path = ""
    if options[:upload_archive]
      timeDay = Time.now.strftime("%Y%m%d")
      timeHMS = Time.now.strftime("%H_%M_%S")
      output_directory = "fastlane/build/archives/" + app_version
      archive_path = output_directory+'/'+scheme+app_version+'('+build_version+')'      
    end

    if options[:output_directory]
      output_directory = options[:output_directory]
    end

    output_name = "tingLite.ipa"
    if options[:output_name]
      output_name = options[:output_name]
    end

    message = changelog_from_git_commits(pretty: "- %an : %s", date_format: "short", commits_count: 5)

    if message == nil
      message = "暂无"
    else
      message = message.gsub(/[\n\r]/) { "<br />"  }  
    end
    # 清空环境变量
    sh "cat /dev/null > env.txt"

    include_bitcode = true
    if configuration == "Release"
      include_bitcode = false  	
    end

    xcargs = ""
    if options[:increment_build]
      xcargs = "SYMROOT=#{SYMROOT} MODULE_CACHE_DIR=#{moduleCache_path} "
    else
      xcargs = "MODULE_CACHE_DIR=#{moduleCache_path} "
    end
 
    # 设置 UseModernBuildSystem=NO  NO: Legacy Build System; YES: New Build System; 
    xcargs += "-UseModernBuildSystem=YES"

    if archive_path != ""
      gym(workspace: ENV['TING_LITE_WORKSPACE'],
        scheme: "#{scheme}",
        clean: true,
        silent: false,
        verbose: true,
        configuration: "#{configuration}",
        include_bitcode: include_bitcode,
        export_method: "#{export_method}",
        archive_path: "#{archive_path}",
        derived_data_path: "./DerivedData",
        export_options:{stripSwiftSymbols: true, signingCertificate: "#{codesigning_identity}"},
        codesigning_identity:"#{codesigning_identity}",
        output_directory: output_directory, # 打包后的 ipa 文件存放的目录
        output_name: "#{output_name}",  # ipa 文件名
        xcargs: "#{xcargs}"
      )      
    else
      gym(workspace: ENV['TING_LITE_WORKSPACE'],
        scheme: "#{scheme}",
        clean: true,
        silent: false,
        verbose: true,
        configuration: "#{configuration}",
        include_bitcode: include_bitcode,
        export_method: "#{export_method}",
        derived_data_path: "./DerivedData",
        export_options:{stripSwiftSymbols: true, signingCertificate: "#{codesigning_identity}"},
        codesigning_identity:"#{codesigning_identity}",
        output_directory: output_directory, # 打包后的 ipa 文件存放的目录
        output_name: "#{output_name}",  # ipa 文件名
        xcargs: "#{xcargs}"
      )      
    end
    
    # 删除多余的IPA包
    deleteDirectory(File.join(Dir.getwd, 'build/'+"#{match_type}"), output_name.split(".ipa").first)


    if options[:increment_build]
      # 缓存编译过的framework 到指定目录
      sh "python3 ../increment_build/binary_cache.py -config \"#{configuration}\" -device \"iphoneos\""
      # 移除临时文件
      sh "python3 ../increment_build/clean_temp.py"
    end

    #需要绝对路径
    ipa_path = "../#{output_directory}/#{output_name}"    

    # 上传蒲公英
    upload_pgyer = options[:upload_pgyer]
    if upload_pgyer
      upload_pgyer(ipa_path: "#{ipa_path}", message: "#{message}")
    end
    # 上传fir
    upload_fir = options[:upload_fir]
    if upload_fir
     upload_fir(ipa_path: "#{ipa_path}", message: "#{message}")
    end
    
    sh "echo IPA_QRCODE_URL='#{ipa_QRCode_url}' >> env.txt"
    sh "echo IPA_LINK_URL='#{ipa_link_url}' >> env.txt"
    sh "echo FASTLANE_APP_VERSION='#{app_version}' >> env.txt"
    sh "echo FASTLANE_APP_BUILDNUM='#{build_version}' >> env.txt"


    if options[:upload_archive]
      # puts "output_dir path: #{archive_path}"
      backup_dir = ENV["XM_ARCHIVE_BACKUP_DIR"]
      # sh("echo UGnoeNBS | sudo -S cp -R ./build/archives/#{app_version} #{backup_dir}") 
      # sh("echo UGnoeNBS | sudo -S chmod -R 777 #{backup_dir}/#{app_version}")
      sh("scp -r ./build/archives/#{app_version} ximalaya@***********:#{backup_dir}")           
    end
  end

  desc "Runs all the tests"
  lane :test do
    scan
  end

  desc "上传到蒲公英上"
  lane :upload_pgyer do |options|
      puts "++++++++++++++upload to pgyer +++++++++++++"
      ipa_path = options[:ipa_path]
      message = options[:message]
      APIKEY = ENV['PGYER_API_KEY']
      sh "echo $(curl -F 'file=@#{ipa_path}' -F '_api_key=#{APIKEY}'  -F 'updateDescription=#{message}' https://www.pgyer.com/apiv2/app/upload) > pgyerResponse.txt "
      
      json = ""
      File.open("pgyerResponse.txt","r") do |file|  
                  while line = file.gets   #标准输入流  
                     json += line.chomp 
                  end  
                end 

      response = JSON.parse(json)
      code = response['code']
      if code == 0
          resp = response['data']
          appKey = resp['buildKey']
          buildAPPURL = "https://www.pgyer.com/#{appKey}" 
          appIcon = resp['buildIcon']
          appShortcutUrl = "https://www.pgyer.com/#{resp['buildShortcutUrl']}" 
          appQRCodeURL = resp['buildQRCodeURL']
          appBuildVersion = resp['buildBuildVersion']

          ipa_QRCode_url = appQRCodeURL
          ipa_link_url = appShortcutUrl

      end
    File.delete("pgyerResponse.txt")
  end

  desc "上传到fir"
  lane :upload_fir do |options| 
    puts "++++++++++++++upload to fir +++++++++++++"
    ipa_path = options[:ipa_path]
    message = options[:message]
    token = ENV['FIR_TOKEN']
    shortCut = 45903 #rand(36 ** 4).to_s(36)
    ipa_link_url = "http://d.firim.top/#{shortCut}"
    sh "fir publish '#{ipa_path}' -s '#{shortCut}' -c '#{message}' -T '#{token}'" 
  end

def setup_logging
  # 创建日志目录
  FileUtils.mkdir_p("logs")
  # 生成时间戳
  timestamp = Time.now.strftime("%Y%m%d_%H%M%S")
  # 返回日志文件路径
  "logs/xcodebuild_#{timestamp}.log"
end

  lane :release_archive do |options|
    log_file = setup_logging
    output_name = "tingLite.ipa"
    build(scheme: ENV['SCHEME_APPSTORE'], 
      export_method: "app-store", 
      configuration: "Release", 
      increment_build: options[:increment_build], 
      export_symbol:true, 
      change_version: options[:change_version],
      output_name: "#{output_name}",
      upload_archive: true
    )
  end 

  desc "企业版"
  lane :inHouse do |options|
    debug = "Debug"
    if options[:configuration]
      debug = options[:configuration]
    end
    build(scheme: ENV['SCHEME_INHOUSE'], export_method: "enterprise", configuration: "#{debug}")
  end

  lane :development do |options|
    build(scheme: ENV['SCHEME_APPSTORE'], export_method: "development" , configuration: "Debug", increment_build: true, optionalRemoveModules:"XMLiveModule")
  end

  desc "ad-hoc"
  lane :adhoc do |options|
    build(scheme: ENV['SCHEME_APPSTORE'], export_method: "ad-hoc" , configuration: "Release", increment_build:false, optionalRemoveModules:options[:optionalRemoveModules])
  end

  desc "Deploy a new version to the App Store"
  lane :appstoreBundle do |options|
  # match(type: "appstore")
  # snapshot
  if options[:upload_archive]
    release_archive(increment_build: options[:increment_build], change_version: options[:change_version])
  else
    build(scheme: ENV['SCHEME_APPSTORE'], export_method: "app-store", configuration: "Release", increment_build: options[:increment_build], export_symbol:true, change_version: options[:change_version])  
  end
  # slack(message: "Successfully uploaded a new App Store build")
  # frameit
  end

  desc "testflight"
  lane :XMLTestflight do |options|
    release_archive(increment_build: options[:increment_build], change_version: options[:change_version])
    message = changelog_from_git_commits(pretty: "- %an : %s", date_format: "short", commits_count: 5)
    api_key = app_store_connect_api_key(
    key_id: "CUXQSTYVZ5",
    issuer_id: "93473c24-38c8-4099-acec-c3ad319968e3",
******************************************************************************************************************************************************************************************************************************************************************************************
    duration: 1200, # optional (maximum 1200)
    in_house: false # optional but may be required if using match/sigh
    )

    pilot(changelog: "#{message}",
      skip_waiting_for_build_processing: true,
      api_key: api_key)
  end
    # You can define as many lanes as you want


  """----------------------------------华丽的分割线---------------------------------"""
  desc "获取版本号"
  lane :get_xcode_version do |options|
    version = get_version_number(xcodeproj: ENV['TING_LITE_XCODEPROJ'], # optional
                                          target: ENV['SCHEME_APPSTORE'])
  end
  desc "修改版本号"
  lane :modify_xcode_version do |options|
    increment_version_number(xcodeproj: ENV['TING_LITE_XCODEPROJ'], version_number: options[:version])
  end

  desc "增长build Number"
  lane :incrementBuildNumber do |options|
    scheme = options[:scheme]
    if scheme == ENV['SCHEME_APPSTORE']
        increment_build_number(xcodeproj: ENV['TING_LITE_XCODEPROJ'])
    end
  end

  desc "修改Channel"
  lane :modify_channel do |options|
    update_plist(
      plist_path: options[:plist_path],
      block: proc do |plist|
        plist[:Channel] = options[:Channel]
      end
    )
  end

  desc "匹配证书"
  lane :matchProfile do |options|
    type = "appstore"
    if options[:type]
      type = options[:type]
    end

    match(type: "#{type}",
          readonly: true,
          verbose: true,
          app_identifier: [ "#{ENV['APP_IDENTIFIER']}", "#{ENV['APP_APNS_EXT_IDENTIFIER']}" ],
          git_branch: ENV['MATCH_APPSTORE_BRANCH'],
          clone_branch_directly: true,
          username: ENV['DEVELOP_ACCOUNT'])
  end

  desc "匹配所有证书"
  lane :matchAllProfile do 
    matchProfile(type: "adhoc")
    matchProfile(type: "appstore")
    matchProfile(type: "development")
    
  end

  desc "更新证书"
  lane :updateProfile do |options|
    lowLevelUpdateProfile(type: options[:type], app_identifier: [ options[:app_identifier] ], provisioning_name: options[:provisioning_name], branch: ENV['MATCH_APPSTORE_BRANCH'], username: ENV['DEVELOP_ACCOUNT'], team_id: ENV['TINGLITE_TEAMID'])
  end

  desc "更新指定的内容"
  lane :updateOptionalProfile do |options|
    if options[:development]
        updateProfile(type: "development", provisioning_name: "xmlyLite-Dev", app_identifier: ENV["APP_IDENTIFIER"])
        updateProfile(type: "development", provisioning_name: "xmlyLite-apnsExt-dev", app_identifier: ENV["APP_APNS_EXT_IDENTIFIER"])  
    end
    if options[:adhoc]
        updateProfile(type: "adhoc", provisioning_name: "xmlyLite-Adhoc", app_identifier: ENV["APP_IDENTIFIER"])
        updateProfile(type: "adhoc", provisioning_name: "xmlyLite-apnsExt-Adhoc", app_identifier: ENV["APP_APNS_EXT_IDENTIFIER"])  
    end
    if options[:appstore]
        updateProfile(type: "appstore", provisioning_name: "xmlyLite-Dis", app_identifier: ENV["APP_IDENTIFIER"])  
        updateProfile(type: "appstore", provisioning_name: "xmlyLite-apnsExt-Dis", app_identifier: ENV["APP_APNS_EXT_IDENTIFIER"])  
    end
  end

  desc "更新证书通用底层方法"
  lane :lowLevelUpdateProfile do |options|
  type = options[:type]
  provisioning_name = options[:provisioning_name]
  branch = options[:branch]
  username = options[:username]
  team_id = options[:team_id]
  match(type: "#{type}",
        verbose: true,
        force: true,
        app_identifier: options[:app_identifier],
        git_branch: "#{branch}",
        clone_branch_directly: true,
        force_for_new_devices: true,
        profile_name: "#{provisioning_name}",
        username: "#{username}",
        team_id: "#{team_id}")
  end

  #----------------------------------注册设备---------------------------------
  desc "注册设备" 
  lane :tingLite_register_device do |options|
    register_device(name: options[:name],
            udid: options[:udid],
            username: ENV['DEVELOP_ACCOUNT'],
            team_id: ENV['TINGLITE_TEAMID'])
  end

  desc "更新证书"
  lane :renew_cert do |options|
    type = options[:type]
    if !type
      type = "adhoc"
    end
    # match_nuke(type: type,
    #             app_identifier: ENV["APP_IDENTIFIER"],
    #             clone_branch_directly: true,
    #             generate_apple_certs: true,
    #             safe_remove_certs: true,
    #             platform: "ios",
    #             skip_set_partition_list: true,
    #             verbose: true,
    #             api_key_path: "fastlane/CUXQSTYVZ5.json")
    match(type: type,
          force: true,
          app_identifier: ENV["APP_IDENTIFIER"],
          clone_branch_directly: true,
          generate_apple_certs: true,
          skip_set_partition_list: true,
          api_key_path: "fastlane/CUXQSTYVZ5.json")
  end

  after_all do |lane|
    # This block is called, only if the executed lane was successful
    # slack(
    #   message: "Successfully deployed new App Update."
    # )
  end

  error do |lane, exception|
    # slack(
    #   message: exception.message,
    #   success: false
    # )
  end

end

# More information about multiple platforms in fastlane: https://github.com/fastlane/fastlane/blob/master/fastlane/docs/Platforms.md
# All available actions: https://docs.fastlane.tools/actions

# fastlane reports which actions are used. No personal data is recorded. 
# Learn more at https://github.com/fastlane/fastlane#metrics
