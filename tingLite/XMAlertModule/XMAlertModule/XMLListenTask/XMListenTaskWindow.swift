//
//  XMLListenTaskWindow.swift
//  XMAlertModule
//
//  Created by donglx on 2021/2/3.
//

import UIKit
import protocol BaseModule.XMAlertPriority
import enum BaseModule.XMPriority
import func BaseModule.kPingFangFont
import protocol BaseModule.XMGradientLayerProtocol
import RouterModule
import class RxSwift.DisposeBag
import class Lottie.AnimationView
import class BaseModule.XMLLottieManager
import XMConfigModule
import XMCategories
import XMUtilModule
import var RouterModule.RouterBridge

// MARK: - 悬浮收听红包控件
public class XMListenTaskWindow: XMPanableView {
    // 外部访问实例
    public static var instance: XMListenTaskWindow {
        if XMListenTaskWindow._instance == nil {
            let window = XMListenTaskWindow(frame: CGRect(x: 0, y: 0, width: 74, height: 63))
            XMListenTaskWindow._instance = window
        }
        return XMListenTaskWindow._instance!
    }
    
    public static var _instance  : XMListenTaskWindow?  = nil   // 悬浮控件实例
    internal      var packetSheet: XMListenPacketSheet? = nil   // 红包弹层
    internal      var viewModel  : XMLListenTaskVM      = XMLListenTaskVM.shared() // 收听红包业务管理类
    
    
    // MARK: - public funcs ---------------
    public func showIn(window: UIWindow, animation: Bool = false) {
        // 如果当前页面需要隐藏那么不显示
        if let page = self.currentPage, page.hiddenGlobalTaskWindow {
            return
        }
        guard !XMRemote.appInReview else { return }
        self.addToSuperView(window)
        if self.viewModel.taskState == .unlogin {
            redPacketView.layer.add(getShakeAnimation(), forKey: "shakeAnimation")
        } else {
            redPacketView.layer.removeAllAnimations()
        }
        if RouterBridge(nil).playerIsPlaying() {
            updateCircelAnimation(startOrStop: true)
        }
        exposureEvent()
        if animation {
            self.alpha = 0
            UIView.animate(withDuration: 0.3, animations: {
                self.alpha = 1
            }) { _ in
                self.alpha = 1
            }
        }
    }
    
    public func currentCenter() -> CGPoint {
        return self.center
    }
    
    // 悬浮控件曝光
    public func exposureEvent() {
        let isLogin = XMSettings.shared().isLoggedIn
        let btnText: String = listenButton.title(for: .normal) ?? ""
        XMEventLog.logEventWithId(28280, serviceId: "slipPage", properties: ["isLogin": isLogin, "text": btnText])
    }
    
    // 更新控件是否显示一半
    public func updateHidenHalf(_ hiden: Bool = false) {
        if hiden {
            let position = viewModel.packetPosition
            var x = position.x
            if position.x < screenWidth/2.0 {
                x = position.x - (self.frame.width/2.0) - 6
            }
            if position.x > screenWidth/2.0 {
                x = position.x + (self.frame.width/2.0) + 6
            }
            
            UIView.animate(withDuration: 0.1, animations: {
                self.center = CGPoint(x: x, y: position.y)
            })
        } else {
            UIView.animate(withDuration: 0.1, animations: {
                self.center = self.viewModel.packetPosition
            })
        }
    }
    
    // 主动隐藏红包弹层
    public static func dismissPacketAlert() {
        let window = XMListenTaskWindow._instance
        window?.packetSheet?.showing = false
        window?.packetSheet?.hidden(animation: false, complection: nil)
    }
    
    // 基础控件布局
    fileprivate func setupBaseUI() {
        self.tag         = XMLGobalTaskAlert.entrance.rawValue
        self.kHorPadding = 7
        self.layer.addSublayer(whiteCircelLayer)
        self.layer.addSublayer(yellowCircleLayer)
        self.layer.addSublayer(progressLayer)
        self.addSubview(redPacketView)
        self.addSubview(lottieRedPackDrop)
        self.addSubview(listenButton)
    }
    
    // 设置首次控件布局
    fileprivate func setupFirstWidgetLayout(_ frame: CGRect) {
        lottieRedPackDrop.frame = CGRect(x: 21/2.0, y: -26, width: 53, height: 53)
        yellowCircleLayer.frame = CGRect(x: (frame.width - 47) * 0.5, y: 3, width: 47, height: 47)
        
        let whiteCircleWidth: CGFloat = 53
        whiteCircelLayer.frame = CGRect(x: (frame.width - whiteCircleWidth) * 0.5, y: 0, width: whiteCircleWidth, height: whiteCircleWidth)
        whiteCircelLayer.cornerRadius = whiteCircleWidth * 0.5
        
        let progressW: CGFloat = 50
        progressLayer.path = UIBezierPath(roundedRect: CGRect(x: (frame.width - progressW) * 0.5, y: 1.5, width: progressW, height: progressW), cornerRadius: progressW * 0.5).cgPath
        
        redPacketView.image = UIImage.bundleWebPImage("listenTask_packet@3x", in: XMLModule.alert.rawValue)
        redPacketView.layer.anchorPoint = CGPoint(x: 0.5, y: 1)
        redPacketView.frame = CGRect(x: (frame.width - 53) * 0.5, y: 0, width: 53, height: 53)
        
        let height: CGFloat = 22
        listenButton.setTitle("登录领钱", for: .normal)
        listenButton.frame = CGRect(x: 0, y: (frame.height - height), width: frame.width, height: height)
        listenGradientLayer.frame = listenButton.bounds
        listenGradientLayer.cornerRadius = height * 0.5
        horProgressLayer.frame.size.width = listenGradientLayer.frame.width
        listenButton.layer.insertSublayer(listenGradientLayer, at: 0)
        self.center = viewModel.packetPosition
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        // 基础控件布局
        self.setupBaseUI()
        // 设置首次控件布局
        self.setupFirstWidgetLayout(frame)
        // 绑定控件业务逻辑
        self.bindWidgetActionHandle()
    }
    
    /// 跨天后重置收听赚钱
    func resetListenText() {
        if RouterBridge(nil).playerIsPlaying() {
            updateCircelAnimation(startOrStop: true)
        } else {
            CATransaction.begin()
            CATransaction.setDisableActions(true)
            horProgressLayer.frame.size.width = self.listenGradientLayer.frame.width
            CATransaction.commit()
            startListen = false
            listenButton.setTitle("收听赚钱", for: .normal)
        }
    }
    
    func reloadPacketView() {
        if RouterBridge(nil).playerIsPlaying() {
            updateCircelAnimation(startOrStop: true)
        }
        packetSheet?.reloadContent()
    }
    
    func taskTime() -> TimeInterval {
        let deniedVal: TimeInterval = UserDefaults.standard.value(forKey: "kGlobalListenTaskTime") as? TimeInterval ?? 0
        return deniedVal
    }
    
    func updateState() {
        let newState = viewModel.taskState
        self.listenButton.isHidden       = false
        self.listenButton.layer.isHidden = false
        self.redPacketView.isHidden      = false
        self.progressLayer.isHidden      = false
        self.horProgressLayer.isHidden   = false
        self.yellowCircleLayer.isHidden  = false
        self.whiteCircelLayer.isHidden   = false
        
        switch newState {
        case .login:
            var progress: CGFloat = viewModel.curStageProgress
            if let curStage = viewModel.curTaskStage {
                var displayCoin = "\(curStage.coinNum)金币"
                let duration = XMSettings.shared().checkInInfo.normalDuration ?? 0
                startListen = true
                // 未开始听时显示 收听赚钱
                if duration < 1 {
                    progress = 1
                    startListen = false
                    displayCoin = "收听赚钱"
                }
                if !self.cding {
                    listenButton.setTitle(displayCoin, for: .normal)
                }
            } else {
                if !self.cding {
                    listenButton.setTitle("--金币", for: .normal)
                }
            }
            CATransaction.begin()
            CATransaction.setDisableActions(true)
            progressLayer.strokeEnd    = 0
            horProgressLayer.frame.size.width = progress * self.listenGradientLayer.frame.width
            CATransaction.commit()
            // 如果已经处于可以领取的状态那么切换状态
            if let display = viewModel.displayFinishedTask {
                if _lottieFinishedView == nil || (_lottieFinishedView != nil && _lottieFinishedView!.isHidden) {
                    showFinishedAnimation()
                }
                updateFinishedCoin(display.coinNum, display.coinType)
                if _finishedAniTimer == nil, !self.cding {
                    // 开启定时器 30s 执行一次动画
                    self.finishedAniTimer.resume()
                }
            } else {
                _lottieFinishedView?.isHidden = true
                redPacketView.isHidden = false
            }
            redPacketView.layer.removeAllAnimations()
        case .unlogin:
            cding = false
            startListen = false
            _lottieFinishedView?.isHidden = true
            redPacketView.isHidden = false
            CATransaction.begin()
            CATransaction.setDisableActions(true)
            self.progressLayer.strokeEnd = 1
            horProgressLayer.frame.size.width = self.listenGradientLayer.frame.width
            CATransaction.commit()
            listenButton.setTitle("登录领钱", for: .normal)
            redPacketView.layer.removeAllAnimations()
            redPacketView.layer.add(getShakeAnimation(), forKey: "shakeAnimation")
            updateRedPacketDropAnimation(startOrStop: false)
        case .finished:
            cding = false
            CATransaction.begin()
            CATransaction.setDisableActions(true)
            self.progressLayer.strokeEnd = 1
            horProgressLayer.frame.size.width = self.listenGradientLayer.frame.width
            CATransaction.commit()
            coinFinishedTipLabel.text = nil
            redPacketView.layer.removeAllAnimations()
            listenButton.setTitle("赚更多", for: .normal)
            updateRedPacketDropAnimation(startOrStop: false)
        }
    }
    
    func updateCircelAnimation(startOrStop: Bool) {
        if startOrStop {
            if let curTask = viewModel.curTaskStage, viewModel.getableTask <= 0, viewModel.taskState == .login {
                self.progressLayer.removeAllAnimations()
                self.progressLayer.add(getCircleAnimation(Double(curTask.runTime)), forKey: "circleAnimation")
                self.updateRedPacketDropAnimation(startOrStop: startOrStop)
                return
            }
            self.progressLayer.removeAllAnimations()
            return
        }
        self.progressLayer.removeAllAnimations()
    }
    
    func updateRedPacketDropAnimation(startOrStop: Bool) {
        guard viewModel.taskState == .login else {
            lottieRedPackDrop.isHidden = false
            lottieRedPackDrop.stop()
            return
        }
        if startOrStop {
            lottieRedPackDrop.isHidden = false
            lottieRedPackDrop.play()
        } else {
            lottieRedPackDrop.stop()
            lottieRedPackDrop.isHidden = true
        }
    }
    
    /// 更新完成时的提示金币
    /// - Parameter coinNum: 金币数量
    func updateFinishedCoin(_ coinNum: Int, _ coinType: XMListenCoinType) {
        if viewModel.taskState == .login, viewModel.getableTask > 0 {
            if !self.cding {
                self.listenButton.setTitle("立即领取", for: .normal)
            }
            coinFinishedTipLabel.text = "+\(coinNum)"
            CATransaction.begin()
            CATransaction.setDisableActions(true)
            progressLayer.strokeEnd = 1
            horProgressLayer.frame.size.width = self.listenGradientLayer.frame.width
            CATransaction.commit()
        }
    }
    
    static func updateGlobalView(type: XMLGobalTaskAlert, hidden: Bool) {
        guard
            let window = UIApplication.shared.delegate?.window,
            let wind = window
        else {
            return
        }
        let view = wind.viewWithTag(type.rawValue)
        if view != nil {
            view?.isHidden = hidden
            return
        }
    }
    
    /// 跨天处理
    /// - Parameter refreshNet: 是否刷新下接口，从后台进入后跨天需要刷新接口
    func crossDayCheck(enterForeground: Bool) {
        if enterForeground {
            if RouterBridge(nil).playerIsPlaying() {
                self.updateCircelAnimation(startOrStop: true)
            }
            if let packet = packetSheet, packet.showing {
                packet.updateAnimation(stop: false)
            }
            return
        }
        guard !localDate.isSameToday(Date()) else { return }
        packetSheet?.hidden(animation: false, complection: nil)
        // 跨天后进度清除
        viewModel.resetTaskStateWhenCrossDay()
        localDate = Date()
    }
    //MARK: - private var ---------------
    private var localDate: Date = Date()
    /// 是否是在cd
    private var cding: Bool = false
    private var startListen: Bool = false
    private let disposeBag = DisposeBag()
    private var _finishedAniTimer: XMSTimer?
    private var finishedAniTimer: XMSTimer {
        if _finishedAniTimer == nil {
            let timer = XMSTimer(interval: .seconds(10), repeats: true) { [weak self] (_) in
                if let `self` = self, self.viewModel.getableTask > 0 {
                    self.showFinishedAnimation()
                }
            }
            _finishedAniTimer = timer
        }
        return _finishedAniTimer!
    }
    
    // 红包图片控件
    private lazy var redPacketView: UIImageView = {
        let imageView: UIImageView = UIImageView(frame: .zero)
        imageView.contentMode = .scaleAspectFill
        return imageView
    }()
    
    private lazy var listenButton: UIButton = {
        let btn                       = UIButton(type: .custom)
        btn.titleLabel?.font          = kPingFangFont(14, .semiBold)
        btn.titleLabel?.textAlignment = .center
        btn.layer.cornerRadius        = 11
        btn.layer.masksToBounds       = true
        btn.isEnabled = false
        btn.backgroundColor = UIColor(0xFFEBD4)
        btn.setTitleColor(UIColor(0xE83F46), for: .normal)
        return btn
    }()
    
    /// 完整的渐变进度条
    private lazy var listenGradientLayer: CAGradientLayer = {
        let layer = CAGradientLayer()
        layer.colors     = [UIColor(0xFFD44C).cgColor, UIColor(0xFFAB11).cgColor]
        layer.startPoint = CGPoint(x: 0.5, y: 0)
        layer.endPoint   = CGPoint(x: 0.5, y: 1)
        layer.mask = horProgressLayer
        return layer
    }()
    
    /// 圆环进度条
    private lazy var horProgressLayer: CALayer = {
        let layer         = CALayer()
        layer.frame = CGRect(x: 0, y: 0, width: 70, height: 22)
        layer.backgroundColor = UIColor(0xFF5B00).cgColor
        return layer
    }()
    
    /// 黄色的背景
    private lazy var yellowCircleLayer: CALayer = {
        let layer             = CALayer()
        layer.backgroundColor = UIColor(0xFFD59E).cgColor
        layer.cornerRadius    = 23.5
        return layer
    }()
    /// 圆环进度条
    private lazy var progressLayer: CAShapeLayer = {
        let layer         = CAShapeLayer()
        layer.strokeColor = UIColor(0xFF5B00).cgColor
        layer.strokeEnd   = 1
        layer.lineWidth   = 3.4
        layer.lineCap     = .round
        layer.fillColor   = UIColor.clear.cgColor
        return layer
    }()
    /// 浅白色的背景
    private lazy var whiteCircelLayer: CALayer = {
        let layer             = CAShapeLayer()
        layer.backgroundColor = UIColor(0xFFEACE).cgColor
        return layer
    }()
    
    private lazy var coinFinishedTipLabel: UILabel = {
        let label           = UILabel()
        label.font          = kPingFangFont(12, .semiBold)
        label.textColor     = UIColor(0xFFD979)
        label.textAlignment = .center
        label.alpha = 0
        return label
    }()
    
    /// lottie动画
    private lazy var lottieRedPackDrop: AnimationView = {
        var filePath: String = ""
        let bundlePath = Bundle.main.path(forResource: XMLModule.alert.rawValue, ofType: "bundle")
        if bundlePath != nil {
            let jsonBundle = Bundle(path: bundlePath!)
            filePath = jsonBundle?.path(forResource: "listenTask_redPacket_drop.json", ofType: nil) ?? ""
        }
        let animationView = XMLLottieManager.shared().animationView(filePath,
                                                                    imageBundle: nil,
                                                                    imagesSubPath: nil)
        animationView.isUserInteractionEnabled = false
        animationView.backgroundBehavior = .pauseAndRestore
        animationView.loopMode = .loop
        animationView.isHidden = true
        animationView.frame = CGRect(x: 21/2.0, y: 0, width: 53, height: 53)
        return animationView
    }()
    
    private var _lottieFinishedView: AnimationView?
    private var lottieFinishedView: AnimationView {
        if _lottieFinishedView == nil {
            var filePath: String = ""
            var imageBundle: Bundle?
            var imagesSubPath: String?
            let bundlePath = Bundle.main.path(forResource: XMLModule.alert.rawValue, ofType: "bundle")
            if bundlePath != nil {
                imageBundle = Bundle(path: bundlePath!)
                filePath = imageBundle?.path(forResource: "listen_task_finished_ani/data.json", ofType: nil) ?? ""
                imagesSubPath = "listen_task_finished_ani/images/"
            }
            let animationView = XMLLottieManager.shared().animationView(filePath,
                                                                        imageBundle: imageBundle,
                                                                        imagesSubPath: imagesSubPath)
            animationView.isUserInteractionEnabled = false
            animationView.loopMode = .playOnce
            animationView.animationSpeed = 0.5
            animationView.layer.anchorPoint = CGPoint(x: 0.5, y: 1)
            animationView.frame = CGRect(x: (self.frame.width - 53) * 0.5, y: 0, width: 53, height: 53)
            coinFinishedTipLabel.frame = CGRect(x: 0, y: 27, width: animationView.frame.width, height: 14)
            animationView.addSubview(coinFinishedTipLabel)
            insertSubview(animationView, belowSubview: listenButton)
            _lottieFinishedView = animationView
        }
        return _lottieFinishedView!
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    //MARK: - private func ---------------
    private func bindWidgetActionHandle() {
        // 设置CD结束回调事件
        self.viewModel.cdRemaindCallback = { [weak self] cdRemaind in
            self?.updateCdRemaindTime(cdRemaind)
        }
        // 拖拽结束回调事件
        self.panGestureEnd = { [weak self] (center) in
            self?.viewModel.savePacketLastPosition(pos: center)
        }
        
        let tap = UITapGestureRecognizer()
        _ = tap.rx.event.take(until: self.rx.deallocated).subscribe { [weak self] (gesture) in
            self?.clickAction()
        }
        self.addGestureRecognizer(tap)
        
        self.viewModel.$getableTask.bind { [weak self] (getTask) in
            guard let `self` = self else { return }
            if getTask <= 0 {
                self.updateState()
                self._finishedAniTimer?.cancel()
                self._finishedAniTimer = nil
                if RouterBridge(nil).playerIsPlaying() {
                    self.updateCircelAnimation(startOrStop: true)
                }
                return
            }
            self.updateCircelAnimation(startOrStop: false)
            self.showFinishedAnimation()
            if self.cding { return }
            // 开启定时器 30s 执行一次动画
            self.finishedAniTimer.resume()
        }.disposed(by: self.disposeBag)
        
        viewModel.$taskState.bind { [weak self] (state) in
            self?.updateState()
        }.disposed(by: self.disposeBag)
        
        viewModel.$curTaskStage.bind { [weak self] (stage) in
            guard let taskStage = stage else { return }
            // 如果正在显示sheet 那么刷新UI
            if let showing = self?.packetSheet?.showing, showing {
                self?.packetSheet?.updatePacketContent(refreshPacket: true)
            }
            guard let `self` = self, self.viewModel.getableTask <= 0, !self.cding else { return }
            self.listenButton.setTitle("\(taskStage.coinNum)金币", for: .normal)
        }.disposed(by: self.disposeBag)
        
        viewModel.$curStageProgress.bind { [weak self] (progress) in
            guard let `self` = self, self.viewModel.getableTask <= 0 else { return }
            if let stage = self.viewModel.curTaskStage, !self.startListen {
                self.startListen = true
                self.listenButton.setTitle("\(stage.coinNum)金币", for: .normal)
            }
            let curWidth = progress * self.listenGradientLayer.frame.width
            // 从1 变为 0 不使用动画
            if self.horProgressLayer.frame.width > curWidth {
                CATransaction.begin()
                CATransaction.setDisableActions(true)
                self.horProgressLayer.frame.size.width = curWidth
                CATransaction.commit()
                return
            }
            self.horProgressLayer.frame.size.width = curWidth
        }.disposed(by: self.disposeBag)
        
        let center = NotificationCenter.default
        _ = center.rx.notification(.XMLogoutSuccessNotification)
            .take(until: self.rx.deallocated).subscribe(onNext: { [weak self] (noti) in
                self?.viewModel.taskState = .unlogin
                self?.packetSheet?.hidden(animation: false, complection: nil)
                self?.packetSheet = nil
            })
        _ = center.rx.notification(.XMLoginSuccessNotification)
            .take(until: self.rx.deallocated).subscribe(onNext: { [weak self] (noti) in
                DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                    self?.isUserInteractionEnabled = false
                    self?.viewModel.getListenTaskConfig(complection: { (success, _) in
                        self?.isUserInteractionEnabled = true
                        // 拉取配置失败后隐藏视图
                        if success {
                            self?.updateState()
                            if RouterBridge(nil).playerIsPlaying() {
                                self?.updateCircelAnimation(startOrStop: true)
                            }
                            return
                        }
                        UIView.showInfoMessage("活动已下线")
                        self?.removeFromSuperview()
                        Self._instance = nil
                    })
                }
            })
    }
    
    public func clickAction(_ index: Int? = nil) {
        let isLogin = XMSettings.shared().isLoggedIn
        
        let btnText: String = listenButton.title(for: .normal) ?? ""
        XMEventLog.logEventWithId(28279, serviceId: "click", properties: ["isLogin": isLogin ? "true" : "false", "text": btnText])
        if !isLogin {
            RouterBridge(nil).login(nil, half: true)
            return
        }
        globalListenTaskTime()
        guard self.viewModel.taskState != .unlogin else { return }
        if packetSheet == nil {
            let sheet = XMListenPacketSheet(frame: UIScreen.main.bounds, vm: self.viewModel)
            packetSheet = sheet
        } else {
            packetSheet?.updatePacketContent(refreshPacket: true)
        }
        packetSheet?.showing = true
        if let index = index {
            packetSheet?.menuIndex = index
        }
        packetSheet?.showIn(view: nil, animation: true, complection: nil)
        XMEventLog.logEventWithId(28283, serviceId: "slipPage", properties: nil)
        // 切换到下一个红包
        packetSheet?.receivedPacketCallback = { [weak viewModel, weak packetSheet, weak self] in
            if let displayTask = viewModel?.displayFinishedTask {
                self?.updateFinishedCoin(displayTask.coinNum, displayTask.coinType)
            }
            // 更新显示cd时间
            if viewModel?.curPacketCdRemaindSec != nil {
                packetSheet?.updateCdDisplayTitle(viewModel?.curPacketCdRemaindSec)
                viewModel?.cdRemaindCallback = { [weak self]  (cdRemaind) in
                    self?.updateCdRemaindTime(cdRemaind)
                    self?.packetSheet?.updateCdDisplayTitle(cdRemaind)
                }
            }
        }
        // 更新显示cd时间
        if viewModel.curPacketCdRemaindSec != nil {
            packetSheet?.updateCdDisplayTitle(viewModel.curPacketCdRemaindSec)
            viewModel.cdRemaindCallback = { [weak self] (cdRemaind) in
                self?.packetSheet?.updateCdDisplayTitle(cdRemaind)
                self?.updateCdRemaindTime(cdRemaind)
            }
        }
    }
    
    func globalListenTaskTime() {
        UserDefaults.standard.setValue(Date().timeIntervalSince1970, forKey: "kGlobalListenTaskTime")
        UserDefaults.standard.synchronize()
    }
    
    private func showFinishedAnimation() {
        if !self.cding {
            self.lottieFinishedView.play { [weak self] (_) in
                guard let `self` = self else { return }
                let shakeAniKey = "shakeAnimation"
                self._lottieFinishedView?.layer.removeAnimation(forKey: shakeAniKey)
                let shakeAni = self.getFinishShakeAnimation()
                shakeAni.repeatCount = 2
                self._lottieFinishedView?.layer.add(shakeAni, forKey: shakeAniKey)
            }
        }
        self._lottieFinishedView?.isHidden = false
        coinFinishedTipLabel.alpha         = 0
        redPacketView.isHidden             = true
        UIView.animate(withDuration: 0.3) {
            self.coinFinishedTipLabel.alpha = 1
        } completion: { (_) in
            self.coinFinishedTipLabel.alpha = 1
        }
    }
    
    /// 更新cd提醒时间
    private func updateCdRemaindTime(_ cdTime: Int?) {
        if let cd = cdTime, cd > 0 {
            cding = true
            var displayTitle = ""
            if cd >= 3600 {
                let hour = cd / 3600
                let min = (cd - hour * 3600) / 60
                let sec = cd % 60
                displayTitle = String(format: "%02d:%02d:%02d", hour, min, sec)
            } else {
                let min = cd / 60
                let sec = cd % 60
                displayTitle = String(format: "%02d:%02d", min, sec)
            }
            listenButton.setTitle(displayTitle, for: .normal)
            redPacketView.layer.removeAllAnimations()
            self._finishedAniTimer?.cancel()
            self._finishedAniTimer = nil
            return
        }
        cding = false
        updateState()
        if self.viewModel.getableTask > 0 {
            // 开启定时器 30s 执行一次动画
            self.finishedAniTimer.resume()
        }
    }
    
    //MARK: - 晃动动画
    private func getFinishShakeAnimation() -> CAKeyframeAnimation {
        let shake         = CAKeyframeAnimation(keyPath: "transform.rotation")
        shake.keyTimes    = [0, 0.25, 0.5, 0.75, 1]
        let angle         = 1 / 18 * CGFloat.pi
        shake.values      = [0, angle, 0, -angle, 0]
        shake.duration    = 0.64
        shake.repeatCount = Float.greatestFiniteMagnitude
        return shake
    }
    
    private func getShakeAnimation() -> CAKeyframeAnimation {
        let shake         = CAKeyframeAnimation(keyPath: "transform.rotation")
        shake.keyTimes    = [0, 0.19, 0.38, 0.57, 0.76, 1]
        let angle         = 1 / 18 * CGFloat.pi
        shake.values      = [0, angle, 0, -angle, 0, 0]
        shake.duration    = 0.84
        shake.repeatCount = Float.greatestFiniteMagnitude
        return shake
    }
    
    private func getCircleAnimation(_ duration: Double) -> CABasicAnimation {
        let circleAni         = CABasicAnimation(keyPath: "strokeEnd")
        circleAni.fromValue   = 0
        circleAni.toValue     = 1
        circleAni.timingFunction = CAMediaTimingFunction(name: .linear)
        circleAni.duration    = duration
        circleAni.delegate = self
        return circleAni
    }
}

// MARK: - CAAnimationDelegate Method
extension XMListenTaskWindow: CAAnimationDelegate {
    // 动画结束回调
    public func animationDidStop(_ anim: CAAnimation, finished flag: Bool) {
        if flag, let curTask = viewModel.curTaskStage {
            let animationKey = "circleAnimationKey"
            self.progressLayer.removeAnimation(forKey: animationKey)
            self.progressLayer.add(getCircleAnimation(Double(curTask.runTime)), forKey: animationKey)
        }
    }
}

// MARK: - XMAlertPriority(弹窗层级) Method
extension XMListenTaskWindow: XMAlertPriority {
    public var priority: XMPriority {
        return .custom(XMPriority.middle.rawValue - 10)
    }
}
