// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		225C8FB326DDCA1F0077C851 /* XMLDriverModeAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225C8FB226DDCA1F0077C851 /* XMLDriverModeAlertView.swift */; };
		225C8FB526DDF3FC0077C851 /* XMLModeSwtichSuccessAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225C8FB426DDF3FC0077C851 /* XMLModeSwtichSuccessAlert.swift */; };
		874E22275C14559BDD052238 /* Pods_XMAlertModuleTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C71EE8063868D814E319F4E3 /* Pods_XMAlertModuleTests.framework */; };
		92918B145AE81963190DB1A1 /* Pods_XMAlertModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7F79EEBC50EC56449FBACDFD /* Pods_XMAlertModule.framework */; };
		9509B14026441DD0002C7C53 /* XMLPlayerVIPActionSheet.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9509B13F26441DD0002C7C53 /* XMLPlayerVIPActionSheet.swift */; };
		9509B14226441DEC002C7C53 /* XMLPlayerTingActionSheet.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9509B14126441DEC002C7C53 /* XMLPlayerTingActionSheet.swift */; };
		9509B14726443BDF002C7C53 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 9509B14326443BDE002C7C53 /* <EMAIL> */; };
		9509B14826443BDF002C7C53 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 9509B14426443BDE002C7C53 /* <EMAIL> */; };
		9509B14A26443BDF002C7C53 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 9509B14626443BDF002C7C53 /* <EMAIL> */; };
		9548D35926455DB800F41BA3 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 9548D35826455DB700F41BA3 /* <EMAIL> */; };
		95491B08265B97E900970B83 /* XMWithdrPraiseGuideAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95491B07265B97E900970B83 /* XMWithdrPraiseGuideAlert.swift */; };
		95491B0A265B981600970B83 /* XMCutelyPraiseGuideAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95491B09265B981600970B83 /* XMCutelyPraiseGuideAlert.swift */; };
		95491B0E265BAFD900970B83 /* XMSubscrPraiseGuideAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95491B0D265BAFD900970B83 /* XMSubscrPraiseGuideAlert.swift */; };
		95491B12265BC0DC00970B83 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 95491B0F265BC0DB00970B83 /* <EMAIL> */; };
		95491B13265BC0DC00970B83 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 95491B10265BC0DC00970B83 /* <EMAIL> */; };
		95491B14265BC0DC00970B83 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 95491B11265BC0DC00970B83 /* <EMAIL> */; };
		95491B18265CA1D700970B83 /* XMAbnormityGuideAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95491B17265CA1D700970B83 /* XMAbnormityGuideAlert.swift */; };
		9596F7EC26141EDD002D96F5 /* XMLPUPlanAResultToast.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9596F7E926141EDD002D96F5 /* XMLPUPlanAResultToast.swift */; };
		9596F7ED26141EDD002D96F5 /* XMLPUPlanAExhaActionSheet.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9596F7EA26141EDD002D96F5 /* XMLPUPlanAExhaActionSheet.swift */; };
		9596F7EE26141EDD002D96F5 /* XMLPUPlanAOperActionSheet.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9596F7EB26141EDD002D96F5 /* XMLPUPlanAOperActionSheet.swift */; };
		95A196EE26F2333200B3DA0C /* XMLSubscribeAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95A196ED26F2333200B3DA0C /* XMLSubscribeAlertView.swift */; };
		95A77FB226DE3C780049299B /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 95A77FB026DE3C770049299B /* <EMAIL> */; };
		95A77FB326DE3C780049299B /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 95A77FB126DE3C780049299B /* <EMAIL> */; };
		95A77FBB26DE58150049299B /* XMLAppTransitionWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95A77FBA26DE58150049299B /* XMLAppTransitionWidget.swift */; };
		95A88D5426A01C1C001881D0 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 95A88D5326A01C1C001881D0 /* <EMAIL> */; };
		95DDF029269D7A2E00682E1A /* XMLPUPlanBOperActionSheet.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DDF028269D7A2E00682E1A /* XMLPUPlanBOperActionSheet.swift */; };
		95DDF02B269D95AB00682E1A /* XMLPUPlanBUnlockingAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DDF02A269D95AB00682E1A /* XMLPUPlanBUnlockingAlertView.swift */; };
		95DDF02D269D95BB00682E1A /* XMLPUPlanBVIPUnlockAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DDF02C269D95BB00682E1A /* XMLPUPlanBVIPUnlockAlertView.swift */; };
		95DDF02F269D95CB00682E1A /* XMLPUPlanBUnlockFailAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DDF02E269D95CB00682E1A /* XMLPUPlanBUnlockFailAlertView.swift */; };
		95DDF031269DB11600682E1A /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 95DDF030269DB11600682E1A /* <EMAIL> */; };
		95F6ACDE269EC36300CEAE6B /* XMLPUPlanBOperItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95F6ACDD269EC36300CEAE6B /* XMLPUPlanBOperItem.swift */; };
		95F6ACE0269EDDFC00CEAE6B /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 95F6ACDF269EDDFC00CEAE6B /* <EMAIL> */; };
		B608A547263507C200731404 /* XMImageGuideAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = B608A546263507C200731404 /* XMImageGuideAlert.swift */; };
		B60EEA3D25CAA26600803513 /* XMListenTaskWindow.swift in Sources */ = {isa = PBXBuildFile; fileRef = B60EEA3C25CAA26600803513 /* XMListenTaskWindow.swift */; };
		B60EEA3F25CAA90500803513 /* XMListenTaskVM.swift in Sources */ = {isa = PBXBuildFile; fileRef = B60EEA3E25CAA90500803513 /* XMListenTaskVM.swift */; };
		B60EEA4125CB983B00803513 /* XMListenPacketSheet.swift in Sources */ = {isa = PBXBuildFile; fileRef = B60EEA4025CB983B00803513 /* XMListenPacketSheet.swift */; };
		B61DB186279A62BC00D6BE07 /* XMWebModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B61DB185279A62BC00D6BE07 /* XMWebModule.framework */; };
		B62C0FB725E8965B00CF54C2 /* XMListenTaskVM+Audio.swift in Sources */ = {isa = PBXBuildFile; fileRef = B62C0FB625E8965B00CF54C2 /* XMListenTaskVM+Audio.swift */; };
		B62C0FB925E8A2EF00CF54C2 /* XMListenTaskModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B62C0FB825E8A2EF00CF54C2 /* XMListenTaskModel.swift */; };
		B6315D87269D79380082AF37 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B6315D86269D79380082AF37 /* <EMAIL> */; };
		B63789EE262D244B0025321C /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B63789ED262D244B0025321C /* <EMAIL> */; };
		B63789F2262D83560025321C /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B63789F1262D83560025321C /* <EMAIL> */; };
		B64304B8262AB3B700B2D50F /* XMNewAwardViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B64304B7262AB3B700B2D50F /* XMNewAwardViewController.swift */; };
		B64304BE262ADEA900B2D50F /* XMLNewUserAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B64304BD262ADEA900B2D50F /* XMLNewUserAlertView.swift */; };
		B6487C6A259AD65E00885398 /* CommBusiness.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B6487C69259AD65E00885398 /* CommBusiness.framework */; };
		B64EB566269DB98D007A5264 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B64EB565269DB98D007A5264 /* <EMAIL> */; };
		B64EB578269E82B5007A5264 /* listen_task_alert in Resources */ = {isa = PBXBuildFile; fileRef = B64EB577269E82B5007A5264 /* listen_task_alert */; };
		B64EB580269EB49A007A5264 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B64EB57E269EB49A007A5264 /* <EMAIL> */; };
		B64EB587269ED423007A5264 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B64EB586269ED423007A5264 /* <EMAIL> */; };
		B64EB589269EDF3A007A5264 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B64EB588269EDF39007A5264 /* <EMAIL> */; };
		B64EB58D269EEBA3007A5264 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B64EB58C269EEBA3007A5264 /* <EMAIL> */; };
		B6517B062799560C00A31547 /* listenTask_redPacket_drop.json in Resources */ = {isa = PBXBuildFile; fileRef = B6517B052799560C00A31547 /* listenTask_redPacket_drop.json */; };
		B651E5AC2599D6950038CC1E /* AlertMedia.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = B651E5AB2599D6950038CC1E /* AlertMedia.xcassets */; };
		B651E5AD2599DBFF0038CC1E /* XMAlertModuleMedia.bundle in Resources */ = {isa = PBXBuildFile; fileRef = B651E5A22599BF880038CC1E /* XMAlertModuleMedia.bundle */; };
		B657D97C26CE302A002038EE /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B657D97B26CE302A002038EE /* <EMAIL> */; };
		B657D98326CE3C5D002038EE /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B657D98226CE3C5C002038EE /* <EMAIL> */; };
		B65DD1DE26156DB1008E0333 /* MediaModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9596F7EF26141F67002D96F5 /* MediaModule.framework */; };
		B6656DD82637E7F500DFFE14 /* LoginModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B6656DD72637E7F500DFFE14 /* LoginModule.framework */; };
		B666BBCC2728FDDE00579168 /* XMYesterdayCoinAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = B666BBCB2728FDDE00579168 /* XMYesterdayCoinAlert.swift */; };
		B666BBCE272926C000579168 /* XMYesterdayCoinVM.swift in Sources */ = {isa = PBXBuildFile; fileRef = B666BBCD272926C000579168 /* XMYesterdayCoinVM.swift */; };
		B669847B25ECCA110075CF9C /* listen_task_finished_ani in Resources */ = {isa = PBXBuildFile; fileRef = B669847A25ECCA110075CF9C /* listen_task_finished_ani */; };
		B66A93C4259472F80027C34D /* XMAlertModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B66A93BA259472F80027C34D /* XMAlertModule.framework */; };
		B66A93C9259472F80027C34D /* XMAlertModuleTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = B66A93C8259472F80027C34D /* XMAlertModuleTests.swift */; };
		B66A93CB259472F80027C34D /* XMAlertModule.h in Headers */ = {isa = PBXBuildFile; fileRef = B66A93BD259472F80027C34D /* XMAlertModule.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B66A93F4259474650027C34D /* XMLVipPriorityListenAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B66A93E9259474640027C34D /* XMLVipPriorityListenAlertView.swift */; };
		B66A93F5259474650027C34D /* XMLWelfareAwardCoinFAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B66A93EA259474640027C34D /* XMLWelfareAwardCoinFAlertView.swift */; };
		B66A93F6259474650027C34D /* XMLWelfareAwardOperAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B66A93EB259474640027C34D /* XMLWelfareAwardOperAlertView.swift */; };
		B66A93F8259474650027C34D /* XMLWelfareSignAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B66A93ED259474640027C34D /* XMLWelfareSignAlertView.swift */; };
		B66A93F9259474650027C34D /* XMLWelfareAwardCoinAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B66A93EE259474640027C34D /* XMLWelfareAwardCoinAlertView.swift */; };
		B66A93FA259474650027C34D /* XMLCoinOperateAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B66A93EF259474640027C34D /* XMLCoinOperateAlertView.swift */; };
		B66A93FB259474650027C34D /* XMLCoinResultAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B66A93F0259474640027C34D /* XMLCoinResultAlertView.swift */; };
		B66A93FC259474650027C34D /* XMPrivacyAuthorizationViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B66A93F1259474640027C34D /* XMPrivacyAuthorizationViewController.swift */; };
		B66A93FE259474650027C34D /* XMLNotificationAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B66A93F3259474650027C34D /* XMLNotificationAlertView.swift */; };
		B66A9406259474720027C34D /* XMLAwardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B66A9401259474720027C34D /* XMLAwardViewModel.swift */; };
		B66A9407259474720027C34D /* XMLListenAward.swift in Sources */ = {isa = PBXBuildFile; fileRef = B66A9403259474720027C34D /* XMLListenAward.swift */; };
		B66A9408259474720027C34D /* XMLCoinAwardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B66A9405259474720027C34D /* XMLCoinAwardView.swift */; };
		B66A940F259475B70027C34D /* RouterModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B66A940E259475B70027C34D /* RouterModule.framework */; };
		B66A9413259475BF0027C34D /* XMADXModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B66A9412259475BF0027C34D /* XMADXModule.framework */; };
		B66A9417259475D10027C34D /* XMUtilModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B66A9416259475D10027C34D /* XMUtilModule.framework */; };
		B66A941B259475DB0027C34D /* XMConfigModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B66A941A259475DB0027C34D /* XMConfigModule.framework */; };
		B66A941F25947D750027C34D /* BaseModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B66A941E25947D750027C34D /* BaseModule.framework */; };
		B67D956626A01C3700949B41 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B64EB582269EB6C5007A5264 /* <EMAIL> */; };
		B67D956726A01C3B00949B41 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B64EB57F269EB49A007A5264 /* <EMAIL> */; };
		B683961125F5C70A0016405E /* XMListenTaskAwardAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = B683961025F5C70A0016405E /* XMListenTaskAwardAlert.swift */; };
		B683961325F5CE000016405E /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B683961225F5CE000016405E /* <EMAIL> */; };
		B687C14E269C34760071B6B1 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B687C14D269C34760071B6B1 /* <EMAIL> */; };
		B687C150269C5BA80071B6B1 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B687C14F269C5BA80071B6B1 /* <EMAIL> */; };
		B6894E1C262DB26F001CB298 /* XMPanableView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6894E1B262DB26F001CB298 /* XMPanableView.swift */; };
		B694B7DF279E8ACC00FBB659 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B694B7DE279E8ACC00FBB659 /* <EMAIL> */; };
		B6B5F2E726B3A72300775BE5 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B6B5F2E626B3A72300775BE5 /* <EMAIL> */; };
		B6B5F2EB26B3F5B200775BE5 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B6B5F2EA26B3F5B200775BE5 /* <EMAIL> */; };
		B6B5F2ED26B3F6B400775BE5 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B6B5F2EC26B3F6B400775BE5 /* <EMAIL> */; };
		B6D03E8726CE5DDA00B632BB /* kayouPacket_finished.json in Resources */ = {isa = PBXBuildFile; fileRef = B6D03E8626CE5DDA00B632BB /* kayouPacket_finished.json */; };
		B6E7A4FC259C57C0009D9B64 /* XMNetworkModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B6E7A4FB259C57C0009D9B64 /* XMNetworkModule.framework */; };
		B6F0587F25E7428A00C614D5 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B6F0587E25E7428A00C614D5 /* <EMAIL> */; };
		B6F0588125E776D500C614D5 /* AlertModuleEntry.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6F0588025E776D500C614D5 /* AlertModuleEntry.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		B651E5AE2599DC070038CC1E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B66A93B1259472F80027C34D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B651E5A12599BF880038CC1E;
			remoteInfo = XMAlertModuleMedia;
		};
		B66A93C5259472F80027C34D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B66A93B1259472F80027C34D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B66A93B9259472F80027C34D;
			remoteInfo = XMAlertModule;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		225C8FB226DDCA1F0077C851 /* XMLDriverModeAlertView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLDriverModeAlertView.swift; sourceTree = "<group>"; };
		225C8FB426DDF3FC0077C851 /* XMLModeSwtichSuccessAlert.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLModeSwtichSuccessAlert.swift; sourceTree = "<group>"; };
		70384E3B66EB592824B9972D /* Pods-XMAlertModule.alpha.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XMAlertModule.alpha.xcconfig"; path = "Target Support Files/Pods-XMAlertModule/Pods-XMAlertModule.alpha.xcconfig"; sourceTree = "<group>"; };
		7F1CC649D03708E0A064CCFE /* Pods-XMAlertModule.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XMAlertModule.release.xcconfig"; path = "Target Support Files/Pods-XMAlertModule/Pods-XMAlertModule.release.xcconfig"; sourceTree = "<group>"; };
		7F79EEBC50EC56449FBACDFD /* Pods_XMAlertModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_XMAlertModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		869939F0A0410A9D85D7320A /* Pods-XMAlertModuleTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XMAlertModuleTests.release.xcconfig"; path = "Target Support Files/Pods-XMAlertModuleTests/Pods-XMAlertModuleTests.release.xcconfig"; sourceTree = "<group>"; };
		9509B13F26441DD0002C7C53 /* XMLPlayerVIPActionSheet.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLPlayerVIPActionSheet.swift; sourceTree = "<group>"; };
		9509B14126441DEC002C7C53 /* XMLPlayerTingActionSheet.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLPlayerTingActionSheet.swift; sourceTree = "<group>"; };
		9509B14326443BDE002C7C53 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		9509B14426443BDE002C7C53 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		9509B14626443BDF002C7C53 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		9548D35826455DB700F41BA3 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		95491B07265B97E900970B83 /* XMWithdrPraiseGuideAlert.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMWithdrPraiseGuideAlert.swift; sourceTree = "<group>"; };
		95491B09265B981600970B83 /* XMCutelyPraiseGuideAlert.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMCutelyPraiseGuideAlert.swift; sourceTree = "<group>"; };
		95491B0D265BAFD900970B83 /* XMSubscrPraiseGuideAlert.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMSubscrPraiseGuideAlert.swift; sourceTree = "<group>"; };
		95491B0F265BC0DB00970B83 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		95491B10265BC0DC00970B83 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		95491B11265BC0DC00970B83 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		95491B17265CA1D700970B83 /* XMAbnormityGuideAlert.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMAbnormityGuideAlert.swift; sourceTree = "<group>"; };
		9596F7E926141EDD002D96F5 /* XMLPUPlanAResultToast.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLPUPlanAResultToast.swift; sourceTree = "<group>"; };
		9596F7EA26141EDD002D96F5 /* XMLPUPlanAExhaActionSheet.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLPUPlanAExhaActionSheet.swift; sourceTree = "<group>"; };
		9596F7EB26141EDD002D96F5 /* XMLPUPlanAOperActionSheet.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLPUPlanAOperActionSheet.swift; sourceTree = "<group>"; };
		9596F7EF26141F67002D96F5 /* MediaModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = MediaModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		95A196ED26F2333200B3DA0C /* XMLSubscribeAlertView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; name = XMLSubscribeAlertView.swift; path = XMLPlayer/XMLSubscribeAlertView.swift; sourceTree = "<group>"; };
		95A77FB026DE3C770049299B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		95A77FB126DE3C780049299B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		95A77FBA26DE58150049299B /* XMLAppTransitionWidget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLAppTransitionWidget.swift; sourceTree = "<group>"; };
		95A88D5326A01C1C001881D0 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		95DDF028269D7A2E00682E1A /* XMLPUPlanBOperActionSheet.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLPUPlanBOperActionSheet.swift; sourceTree = "<group>"; };
		95DDF02A269D95AB00682E1A /* XMLPUPlanBUnlockingAlertView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLPUPlanBUnlockingAlertView.swift; sourceTree = "<group>"; };
		95DDF02C269D95BB00682E1A /* XMLPUPlanBVIPUnlockAlertView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLPUPlanBVIPUnlockAlertView.swift; sourceTree = "<group>"; };
		95DDF02E269D95CB00682E1A /* XMLPUPlanBUnlockFailAlertView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLPUPlanBUnlockFailAlertView.swift; sourceTree = "<group>"; };
		95DDF030269DB11600682E1A /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		95F6ACDD269EC36300CEAE6B /* XMLPUPlanBOperItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLPUPlanBOperItem.swift; sourceTree = "<group>"; };
		95F6ACDF269EDDFC00CEAE6B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		B608A546263507C200731404 /* XMImageGuideAlert.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMImageGuideAlert.swift; sourceTree = "<group>"; };
		B60EEA3C25CAA26600803513 /* XMListenTaskWindow.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMListenTaskWindow.swift; sourceTree = "<group>"; };
		B60EEA3E25CAA90500803513 /* XMListenTaskVM.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMListenTaskVM.swift; sourceTree = "<group>"; };
		B60EEA4025CB983B00803513 /* XMListenPacketSheet.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMListenPacketSheet.swift; sourceTree = "<group>"; };
		B61DB185279A62BC00D6BE07 /* XMWebModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMWebModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B62C0FB625E8965B00CF54C2 /* XMListenTaskVM+Audio.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMListenTaskVM+Audio.swift"; sourceTree = "<group>"; };
		B62C0FB825E8A2EF00CF54C2 /* XMListenTaskModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMListenTaskModel.swift; sourceTree = "<group>"; };
		B6315D86269D79380082AF37 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		B63789ED262D244B0025321C /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		B63789F1262D83560025321C /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		B64304B7262AB3B700B2D50F /* XMNewAwardViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMNewAwardViewController.swift; sourceTree = "<group>"; };
		B64304BD262ADEA900B2D50F /* XMLNewUserAlertView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLNewUserAlertView.swift; sourceTree = "<group>"; };
		B6487C69259AD65E00885398 /* CommBusiness.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = CommBusiness.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B64EB565269DB98D007A5264 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		B64EB577269E82B5007A5264 /* listen_task_alert */ = {isa = PBXFileReference; lastKnownFileType = folder; path = listen_task_alert; sourceTree = "<group>"; };
		B64EB57E269EB49A007A5264 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		B64EB57F269EB49A007A5264 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		B64EB582269EB6C5007A5264 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		B64EB586269ED423007A5264 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		B64EB588269EDF39007A5264 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		B64EB58C269EEBA3007A5264 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		B6517B052799560C00A31547 /* listenTask_redPacket_drop.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = listenTask_redPacket_drop.json; sourceTree = "<group>"; };
		B651E5A22599BF880038CC1E /* XMAlertModuleMedia.bundle */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = XMAlertModuleMedia.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		B651E5A42599BF880038CC1E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		B651E5AB2599D6950038CC1E /* AlertMedia.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = AlertMedia.xcassets; sourceTree = "<group>"; };
		B657D97B26CE302A002038EE /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		B657D98226CE3C5C002038EE /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		B6656DD72637E7F500DFFE14 /* LoginModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = LoginModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B666BBCB2728FDDE00579168 /* XMYesterdayCoinAlert.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMYesterdayCoinAlert.swift; sourceTree = "<group>"; };
		B666BBCD272926C000579168 /* XMYesterdayCoinVM.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMYesterdayCoinVM.swift; sourceTree = "<group>"; };
		B669847A25ECCA110075CF9C /* listen_task_finished_ani */ = {isa = PBXFileReference; lastKnownFileType = folder; path = listen_task_finished_ani; sourceTree = "<group>"; };
		B66A93BA259472F80027C34D /* XMAlertModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = XMAlertModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B66A93BD259472F80027C34D /* XMAlertModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XMAlertModule.h; sourceTree = "<group>"; };
		B66A93BE259472F80027C34D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		B66A93C3259472F80027C34D /* XMAlertModuleTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = XMAlertModuleTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		B66A93C8259472F80027C34D /* XMAlertModuleTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMAlertModuleTests.swift; sourceTree = "<group>"; };
		B66A93CA259472F80027C34D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		B66A93E9259474640027C34D /* XMLVipPriorityListenAlertView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLVipPriorityListenAlertView.swift; sourceTree = "<group>"; };
		B66A93EA259474640027C34D /* XMLWelfareAwardCoinFAlertView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLWelfareAwardCoinFAlertView.swift; sourceTree = "<group>"; };
		B66A93EB259474640027C34D /* XMLWelfareAwardOperAlertView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLWelfareAwardOperAlertView.swift; sourceTree = "<group>"; };
		B66A93ED259474640027C34D /* XMLWelfareSignAlertView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLWelfareSignAlertView.swift; sourceTree = "<group>"; };
		B66A93EE259474640027C34D /* XMLWelfareAwardCoinAlertView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLWelfareAwardCoinAlertView.swift; sourceTree = "<group>"; };
		B66A93EF259474640027C34D /* XMLCoinOperateAlertView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLCoinOperateAlertView.swift; sourceTree = "<group>"; };
		B66A93F0259474640027C34D /* XMLCoinResultAlertView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLCoinResultAlertView.swift; sourceTree = "<group>"; };
		B66A93F1259474640027C34D /* XMPrivacyAuthorizationViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMPrivacyAuthorizationViewController.swift; sourceTree = "<group>"; };
		B66A93F3259474650027C34D /* XMLNotificationAlertView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLNotificationAlertView.swift; sourceTree = "<group>"; };
		B66A9401259474720027C34D /* XMLAwardViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLAwardViewModel.swift; sourceTree = "<group>"; };
		B66A9403259474720027C34D /* XMLListenAward.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLListenAward.swift; sourceTree = "<group>"; };
		B66A9405259474720027C34D /* XMLCoinAwardView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLCoinAwardView.swift; sourceTree = "<group>"; };
		B66A940E259475B70027C34D /* RouterModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = RouterModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B66A9412259475BF0027C34D /* XMADXModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMADXModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B66A9416259475D10027C34D /* XMUtilModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMUtilModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B66A941A259475DB0027C34D /* XMConfigModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMConfigModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B66A941E25947D750027C34D /* BaseModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = BaseModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B683961025F5C70A0016405E /* XMListenTaskAwardAlert.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMListenTaskAwardAlert.swift; sourceTree = "<group>"; };
		B683961225F5CE000016405E /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		B687C14D269C34760071B6B1 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		B687C14F269C5BA80071B6B1 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		B6894E1B262DB26F001CB298 /* XMPanableView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMPanableView.swift; sourceTree = "<group>"; };
		B694B7DE279E8ACC00FBB659 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		B6B5F2E626B3A72300775BE5 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		B6B5F2EA26B3F5B200775BE5 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		B6B5F2EC26B3F6B400775BE5 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		B6D03E8626CE5DDA00B632BB /* kayouPacket_finished.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = kayouPacket_finished.json; sourceTree = "<group>"; };
		B6E7A4FB259C57C0009D9B64 /* XMNetworkModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMNetworkModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B6F0587E25E7428A00C614D5 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		B6F0588025E776D500C614D5 /* AlertModuleEntry.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AlertModuleEntry.swift; sourceTree = "<group>"; };
		BBF09D4EC6D509ED59C9BBDD /* Pods-XMAlertModuleTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XMAlertModuleTests.debug.xcconfig"; path = "Target Support Files/Pods-XMAlertModuleTests/Pods-XMAlertModuleTests.debug.xcconfig"; sourceTree = "<group>"; };
		BDC556EEC9A919C3A4780AEF /* Pods-XMAlertModule.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XMAlertModule.debug.xcconfig"; path = "Target Support Files/Pods-XMAlertModule/Pods-XMAlertModule.debug.xcconfig"; sourceTree = "<group>"; };
		C71EE8063868D814E319F4E3 /* Pods_XMAlertModuleTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_XMAlertModuleTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		D07438A4A3CDA4DADA2CBBBE /* Pods-XMAlertModuleTests.alpha.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XMAlertModuleTests.alpha.xcconfig"; path = "Target Support Files/Pods-XMAlertModuleTests/Pods-XMAlertModuleTests.alpha.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		B651E59F2599BF880038CC1E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B66A93B7259472F80027C34D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B66A9417259475D10027C34D /* XMUtilModule.framework in Frameworks */,
				B6E7A4FC259C57C0009D9B64 /* XMNetworkModule.framework in Frameworks */,
				B61DB186279A62BC00D6BE07 /* XMWebModule.framework in Frameworks */,
				B66A941B259475DB0027C34D /* XMConfigModule.framework in Frameworks */,
				B66A941F25947D750027C34D /* BaseModule.framework in Frameworks */,
				B66A940F259475B70027C34D /* RouterModule.framework in Frameworks */,
				B6656DD82637E7F500DFFE14 /* LoginModule.framework in Frameworks */,
				B65DD1DE26156DB1008E0333 /* MediaModule.framework in Frameworks */,
				B6487C6A259AD65E00885398 /* CommBusiness.framework in Frameworks */,
				B66A9413259475BF0027C34D /* XMADXModule.framework in Frameworks */,
				92918B145AE81963190DB1A1 /* Pods_XMAlertModule.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B66A93C0259472F80027C34D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B66A93C4259472F80027C34D /* XMAlertModule.framework in Frameworks */,
				874E22275C14559BDD052238 /* Pods_XMAlertModuleTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		3D88779C228D98DC2448B653 /* Pods */ = {
			isa = PBXGroup;
			children = (
				BDC556EEC9A919C3A4780AEF /* Pods-XMAlertModule.debug.xcconfig */,
				70384E3B66EB592824B9972D /* Pods-XMAlertModule.alpha.xcconfig */,
				7F1CC649D03708E0A064CCFE /* Pods-XMAlertModule.release.xcconfig */,
				BBF09D4EC6D509ED59C9BBDD /* Pods-XMAlertModuleTests.debug.xcconfig */,
				D07438A4A3CDA4DADA2CBBBE /* Pods-XMAlertModuleTests.alpha.xcconfig */,
				869939F0A0410A9D85D7320A /* Pods-XMAlertModuleTests.release.xcconfig */,
			);
			name = Pods;
			path = ../Pods;
			sourceTree = "<group>";
		};
		9509B13E26441D97002C7C53 /* XMLPlayer */ = {
			isa = PBXGroup;
			children = (
				9509B13F26441DD0002C7C53 /* XMLPlayerVIPActionSheet.swift */,
				9509B14126441DEC002C7C53 /* XMLPlayerTingActionSheet.swift */,
			);
			path = XMLPlayer;
			sourceTree = "<group>";
		};
		95491B03265B966100970B83 /* XMLAbnormity */ = {
			isa = PBXGroup;
			children = (
				95491B17265CA1D700970B83 /* XMAbnormityGuideAlert.swift */,
			);
			path = XMLAbnormity;
			sourceTree = "<group>";
		};
		95491B04265B96AA00970B83 /* XMLPraiseGuide */ = {
			isa = PBXGroup;
			children = (
				95491B0D265BAFD900970B83 /* XMSubscrPraiseGuideAlert.swift */,
				95491B07265B97E900970B83 /* XMWithdrPraiseGuideAlert.swift */,
				95491B09265B981600970B83 /* XMCutelyPraiseGuideAlert.swift */,
			);
			path = XMLPraiseGuide;
			sourceTree = "<group>";
		};
		9596F7E726141E9D002D96F5 /* XMLPaidUnlock */ = {
			isa = PBXGroup;
			children = (
				95DDF027269D690C00682E1A /* PlanB */,
				9596F7E826141EC5002D96F5 /* PlanA */,
			);
			path = XMLPaidUnlock;
			sourceTree = "<group>";
		};
		9596F7E826141EC5002D96F5 /* PlanA */ = {
			isa = PBXGroup;
			children = (
				9596F7EB26141EDD002D96F5 /* XMLPUPlanAOperActionSheet.swift */,
				9596F7EA26141EDD002D96F5 /* XMLPUPlanAExhaActionSheet.swift */,
				9596F7E926141EDD002D96F5 /* XMLPUPlanAResultToast.swift */,
			);
			path = PlanA;
			sourceTree = "<group>";
		};
		95DDF027269D690C00682E1A /* PlanB */ = {
			isa = PBXGroup;
			children = (
				95DDF028269D7A2E00682E1A /* XMLPUPlanBOperActionSheet.swift */,
				95F6ACDD269EC36300CEAE6B /* XMLPUPlanBOperItem.swift */,
				95DDF02A269D95AB00682E1A /* XMLPUPlanBUnlockingAlertView.swift */,
				95DDF02C269D95BB00682E1A /* XMLPUPlanBVIPUnlockAlertView.swift */,
				95DDF02E269D95CB00682E1A /* XMLPUPlanBUnlockFailAlertView.swift */,
			);
			path = PlanB;
			sourceTree = "<group>";
		};
		B608A5452635074500731404 /* XMLNewListen */ = {
			isa = PBXGroup;
			children = (
				B608A546263507C200731404 /* XMImageGuideAlert.swift */,
			);
			path = XMLNewListen;
			sourceTree = "<group>";
		};
		B60EEA3B25CAA23E00803513 /* XMLListenTask */ = {
			isa = PBXGroup;
			children = (
				B6894E1B262DB26F001CB298 /* XMPanableView.swift */,
				B60EEA3C25CAA26600803513 /* XMListenTaskWindow.swift */,
				B60EEA3E25CAA90500803513 /* XMListenTaskVM.swift */,
				B62C0FB625E8965B00CF54C2 /* XMListenTaskVM+Audio.swift */,
				B60EEA4025CB983B00803513 /* XMListenPacketSheet.swift */,
				B62C0FB825E8A2EF00CF54C2 /* XMListenTaskModel.swift */,
				B683961025F5C70A0016405E /* XMListenTaskAwardAlert.swift */,
			);
			path = XMLListenTask;
			sourceTree = "<group>";
		};
		B64304B6262AB2CB00B2D50F /* XMLNewUserGift */ = {
			isa = PBXGroup;
			children = (
				B64304BD262ADEA900B2D50F /* XMLNewUserAlertView.swift */,
				B64304B7262AB3B700B2D50F /* XMNewAwardViewController.swift */,
			);
			path = XMLNewUserGift;
			sourceTree = "<group>";
		};
		B651E5A32599BF880038CC1E /* XMAlertModuleMedia */ = {
			isa = PBXGroup;
			children = (
				B651E5AB2599D6950038CC1E /* AlertMedia.xcassets */,
				95A77FB026DE3C770049299B /* <EMAIL> */,
				B6517B052799560C00A31547 /* listenTask_redPacket_drop.json */,
				B694B7DE279E8ACC00FBB659 /* <EMAIL> */,
				95A77FB126DE3C780049299B /* <EMAIL> */,
				B6B5F2EC26B3F6B400775BE5 /* <EMAIL> */,
				B6B5F2EA26B3F5B200775BE5 /* <EMAIL> */,
				B6B5F2E626B3A72300775BE5 /* <EMAIL> */,
				95A88D5326A01C1C001881D0 /* <EMAIL> */,
				95DDF030269DB11600682E1A /* <EMAIL> */,
				95F6ACDF269EDDFC00CEAE6B /* <EMAIL> */,
				95491B10265BC0DC00970B83 /* <EMAIL> */,
				95491B0F265BC0DB00970B83 /* <EMAIL> */,
				95491B11265BC0DC00970B83 /* <EMAIL> */,
				9509B14326443BDE002C7C53 /* <EMAIL> */,
				9509B14626443BDF002C7C53 /* <EMAIL> */,
				9509B14426443BDE002C7C53 /* <EMAIL> */,
				9548D35826455DB700F41BA3 /* <EMAIL> */,
				B6F0587D25E7428A00C614D5 /* listenTask */,
				B6E7A4F4259B350C009D9B64 /* NewSign */,
				B651E5A42599BF880038CC1E /* Info.plist */,
			);
			path = XMAlertModuleMedia;
			sourceTree = "<group>";
		};
		B666BBCA2728FDDE00579168 /* XMYesterdayCoinAlert */ = {
			isa = PBXGroup;
			children = (
				B666BBCB2728FDDE00579168 /* XMYesterdayCoinAlert.swift */,
				B666BBCD272926C000579168 /* XMYesterdayCoinVM.swift */,
			);
			path = XMYesterdayCoinAlert;
			sourceTree = "<group>";
		};
		B66A93B0259472F80027C34D = {
			isa = PBXGroup;
			children = (
				B66A93BC259472F80027C34D /* XMAlertModule */,
				B66A93C7259472F80027C34D /* XMAlertModuleTests */,
				B651E5A32599BF880038CC1E /* XMAlertModuleMedia */,
				B66A93BB259472F80027C34D /* Products */,
				B66A940D259475B70027C34D /* Frameworks */,
				3D88779C228D98DC2448B653 /* Pods */,
			);
			sourceTree = "<group>";
		};
		B66A93BB259472F80027C34D /* Products */ = {
			isa = PBXGroup;
			children = (
				B66A93BA259472F80027C34D /* XMAlertModule.framework */,
				B66A93C3259472F80027C34D /* XMAlertModuleTests.xctest */,
				B651E5A22599BF880038CC1E /* XMAlertModuleMedia.bundle */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		B66A93BC259472F80027C34D /* XMAlertModule */ = {
			isa = PBXGroup;
			children = (
				B666BBCA2728FDDE00579168 /* XMYesterdayCoinAlert */,
				95491B04265B96AA00970B83 /* XMLPraiseGuide */,
				95491B03265B966100970B83 /* XMLAbnormity */,
				9509B13E26441D97002C7C53 /* XMLPlayer */,
				B608A5452635074500731404 /* XMLNewListen */,
				B64304B6262AB2CB00B2D50F /* XMLNewUserGift */,
				9596F7E726141E9D002D96F5 /* XMLPaidUnlock */,
				B60EEA3B25CAA23E00803513 /* XMLListenTask */,
				B66A93FF259474720027C34D /* XMLNewUserPacket */,
				B66A93EF259474640027C34D /* XMLCoinOperateAlertView.swift */,
				225C8FB226DDCA1F0077C851 /* XMLDriverModeAlertView.swift */,
				225C8FB426DDF3FC0077C851 /* XMLModeSwtichSuccessAlert.swift */,
				B66A93F0259474640027C34D /* XMLCoinResultAlertView.swift */,
				B66A93F3259474650027C34D /* XMLNotificationAlertView.swift */,
				B66A93E9259474640027C34D /* XMLVipPriorityListenAlertView.swift */,
				B66A93EE259474640027C34D /* XMLWelfareAwardCoinAlertView.swift */,
				B66A93EA259474640027C34D /* XMLWelfareAwardCoinFAlertView.swift */,
				B66A93EB259474640027C34D /* XMLWelfareAwardOperAlertView.swift */,
				B66A93ED259474640027C34D /* XMLWelfareSignAlertView.swift */,
				95A77FBA26DE58150049299B /* XMLAppTransitionWidget.swift */,
				95A196ED26F2333200B3DA0C /* XMLSubscribeAlertView.swift */,
				B66A93F1259474640027C34D /* XMPrivacyAuthorizationViewController.swift */,
				B66A93BD259472F80027C34D /* XMAlertModule.h */,
				B66A93BE259472F80027C34D /* Info.plist */,
				B6F0588025E776D500C614D5 /* AlertModuleEntry.swift */,
			);
			path = XMAlertModule;
			sourceTree = "<group>";
		};
		B66A93C7259472F80027C34D /* XMAlertModuleTests */ = {
			isa = PBXGroup;
			children = (
				B66A93C8259472F80027C34D /* XMAlertModuleTests.swift */,
				B66A93CA259472F80027C34D /* Info.plist */,
			);
			path = XMAlertModuleTests;
			sourceTree = "<group>";
		};
		B66A93FF259474720027C34D /* XMLNewUserPacket */ = {
			isa = PBXGroup;
			children = (
				B66A9400259474720027C34D /* ViewModel */,
				B66A9402259474720027C34D /* Model */,
				B66A9404259474720027C34D /* View */,
			);
			path = XMLNewUserPacket;
			sourceTree = "<group>";
		};
		B66A9400259474720027C34D /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				B66A9401259474720027C34D /* XMLAwardViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		B66A9402259474720027C34D /* Model */ = {
			isa = PBXGroup;
			children = (
				B66A9403259474720027C34D /* XMLListenAward.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		B66A9404259474720027C34D /* View */ = {
			isa = PBXGroup;
			children = (
				B66A9405259474720027C34D /* XMLCoinAwardView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		B66A940D259475B70027C34D /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				B61DB185279A62BC00D6BE07 /* XMWebModule.framework */,
				B6656DD72637E7F500DFFE14 /* LoginModule.framework */,
				9596F7EF26141F67002D96F5 /* MediaModule.framework */,
				B6E7A4FB259C57C0009D9B64 /* XMNetworkModule.framework */,
				B6487C69259AD65E00885398 /* CommBusiness.framework */,
				B66A941E25947D750027C34D /* BaseModule.framework */,
				B66A941A259475DB0027C34D /* XMConfigModule.framework */,
				B66A9416259475D10027C34D /* XMUtilModule.framework */,
				B66A9412259475BF0027C34D /* XMADXModule.framework */,
				B66A940E259475B70027C34D /* RouterModule.framework */,
				7F79EEBC50EC56449FBACDFD /* Pods_XMAlertModule.framework */,
				C71EE8063868D814E319F4E3 /* Pods_XMAlertModuleTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		B6E7A4F4259B350C009D9B64 /* NewSign */ = {
			isa = PBXGroup;
			children = (
				B64EB58C269EEBA3007A5264 /* <EMAIL> */,
				B64EB588269EDF39007A5264 /* <EMAIL> */,
				B64EB586269ED423007A5264 /* <EMAIL> */,
				B64EB582269EB6C5007A5264 /* <EMAIL> */,
				B64EB57F269EB49A007A5264 /* <EMAIL> */,
				B64EB57E269EB49A007A5264 /* <EMAIL> */,
				B63789F1262D83560025321C /* <EMAIL> */,
				B63789ED262D244B0025321C /* <EMAIL> */,
			);
			path = NewSign;
			sourceTree = "<group>";
		};
		B6F0587D25E7428A00C614D5 /* listenTask */ = {
			isa = PBXGroup;
			children = (
				B64EB577269E82B5007A5264 /* listen_task_alert */,
				B64EB565269DB98D007A5264 /* <EMAIL> */,
				B6D03E8626CE5DDA00B632BB /* kayouPacket_finished.json */,
				B657D98226CE3C5C002038EE /* <EMAIL> */,
				B6315D86269D79380082AF37 /* <EMAIL> */,
				B687C14F269C5BA80071B6B1 /* <EMAIL> */,
				B657D97B26CE302A002038EE /* <EMAIL> */,
				B687C14D269C34760071B6B1 /* <EMAIL> */,
				B683961225F5CE000016405E /* <EMAIL> */,
				B669847A25ECCA110075CF9C /* listen_task_finished_ani */,
				B6F0587E25E7428A00C614D5 /* <EMAIL> */,
			);
			path = listenTask;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		B66A93B5259472F80027C34D /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B66A93CB259472F80027C34D /* XMAlertModule.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		B651E5A12599BF880038CC1E /* XMAlertModuleMedia */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B651E5A52599BF880038CC1E /* Build configuration list for PBXNativeTarget "XMAlertModuleMedia" */;
			buildPhases = (
				B651E59E2599BF880038CC1E /* Sources */,
				B651E59F2599BF880038CC1E /* Frameworks */,
				B651E5A02599BF880038CC1E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = XMAlertModuleMedia;
			productName = XMAlertModuleMedia;
			productReference = B651E5A22599BF880038CC1E /* XMAlertModuleMedia.bundle */;
			productType = "com.apple.product-type.bundle";
		};
		B66A93B9259472F80027C34D /* XMAlertModule */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B66A93CE259472F80027C34D /* Build configuration list for PBXNativeTarget "XMAlertModule" */;
			buildPhases = (
				483959DA3D9E9CBA99AE05BC /* [CP] Check Pods Manifest.lock */,
				B66A93B5259472F80027C34D /* Headers */,
				B66A93B6259472F80027C34D /* Sources */,
				B66A93B7259472F80027C34D /* Frameworks */,
				B66A93B8259472F80027C34D /* Resources */,
				157D7F3FF319F654DFFF856C /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				B651E5AF2599DC070038CC1E /* PBXTargetDependency */,
			);
			name = XMAlertModule;
			productName = XMAlertModule;
			productReference = B66A93BA259472F80027C34D /* XMAlertModule.framework */;
			productType = "com.apple.product-type.framework";
		};
		B66A93C2259472F80027C34D /* XMAlertModuleTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B66A93D1259472F80027C34D /* Build configuration list for PBXNativeTarget "XMAlertModuleTests" */;
			buildPhases = (
				036839422F37F61DA5BEF414 /* [CP] Check Pods Manifest.lock */,
				B66A93BF259472F80027C34D /* Sources */,
				B66A93C0259472F80027C34D /* Frameworks */,
				B66A93C1259472F80027C34D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				B66A93C6259472F80027C34D /* PBXTargetDependency */,
			);
			name = XMAlertModuleTests;
			productName = XMAlertModuleTests;
			productReference = B66A93C3259472F80027C34D /* XMAlertModuleTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		B66A93B1259472F80027C34D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				DefaultBuildSystemTypeForWorkspace = Original;
				LastSwiftUpdateCheck = 1200;
				LastUpgradeCheck = 1200;
				TargetAttributes = {
					B651E5A12599BF880038CC1E = {
						CreatedOnToolsVersion = 12.0.1;
					};
					B66A93B9259472F80027C34D = {
						CreatedOnToolsVersion = 12.0.1;
						LastSwiftMigration = 1200;
					};
					B66A93C2259472F80027C34D = {
						CreatedOnToolsVersion = 12.0.1;
					};
				};
			};
			buildConfigurationList = B66A93B4259472F80027C34D /* Build configuration list for PBXProject "XMAlertModule" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = B66A93B0259472F80027C34D;
			productRefGroup = B66A93BB259472F80027C34D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				B66A93B9259472F80027C34D /* XMAlertModule */,
				B66A93C2259472F80027C34D /* XMAlertModuleTests */,
				B651E5A12599BF880038CC1E /* XMAlertModuleMedia */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		B651E5A02599BF880038CC1E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B694B7DF279E8ACC00FBB659 /* <EMAIL> in Resources */,
				95491B12265BC0DC00970B83 /* <EMAIL> in Resources */,
				9509B14726443BDF002C7C53 /* <EMAIL> in Resources */,
				B6B5F2ED26B3F6B400775BE5 /* <EMAIL> in Resources */,
				B6B5F2E726B3A72300775BE5 /* <EMAIL> in Resources */,
				95F6ACE0269EDDFC00CEAE6B /* <EMAIL> in Resources */,
				B64EB589269EDF3A007A5264 /* <EMAIL> in Resources */,
				B657D98326CE3C5D002038EE /* <EMAIL> in Resources */,
				B6B5F2EB26B3F5B200775BE5 /* <EMAIL> in Resources */,
				95A77FB226DE3C780049299B /* <EMAIL> in Resources */,
				95491B14265BC0DC00970B83 /* <EMAIL> in Resources */,
				B63789F2262D83560025321C /* <EMAIL> in Resources */,
				B64EB566269DB98D007A5264 /* <EMAIL> in Resources */,
				95A77FB326DE3C780049299B /* <EMAIL> in Resources */,
				B669847B25ECCA110075CF9C /* listen_task_finished_ani in Resources */,
				B6517B062799560C00A31547 /* listenTask_redPacket_drop.json in Resources */,
				95A88D5426A01C1C001881D0 /* <EMAIL> in Resources */,
				B67D956626A01C3700949B41 /* <EMAIL> in Resources */,
				B64EB587269ED423007A5264 /* <EMAIL> in Resources */,
				95491B13265BC0DC00970B83 /* <EMAIL> in Resources */,
				B687C150269C5BA80071B6B1 /* <EMAIL> in Resources */,
				B6F0587F25E7428A00C614D5 /* <EMAIL> in Resources */,
				B687C14E269C34760071B6B1 /* <EMAIL> in Resources */,
				B651E5AC2599D6950038CC1E /* AlertMedia.xcassets in Resources */,
				B6315D87269D79380082AF37 /* <EMAIL> in Resources */,
				9509B14A26443BDF002C7C53 /* <EMAIL> in Resources */,
				B64EB580269EB49A007A5264 /* <EMAIL> in Resources */,
				B64EB58D269EEBA3007A5264 /* <EMAIL> in Resources */,
				B683961325F5CE000016405E /* <EMAIL> in Resources */,
				B63789EE262D244B0025321C /* <EMAIL> in Resources */,
				B6D03E8726CE5DDA00B632BB /* kayouPacket_finished.json in Resources */,
				9509B14826443BDF002C7C53 /* <EMAIL> in Resources */,
				95DDF031269DB11600682E1A /* <EMAIL> in Resources */,
				B657D97C26CE302A002038EE /* <EMAIL> in Resources */,
				B64EB578269E82B5007A5264 /* listen_task_alert in Resources */,
				9548D35926455DB800F41BA3 /* <EMAIL> in Resources */,
				B67D956726A01C3B00949B41 /* <EMAIL> in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B66A93B8259472F80027C34D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B651E5AD2599DBFF0038CC1E /* XMAlertModuleMedia.bundle in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B66A93C1259472F80027C34D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		036839422F37F61DA5BEF414 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-XMAlertModuleTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		157D7F3FF319F654DFFF856C /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-XMAlertModule/Pods-XMAlertModule-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-XMAlertModule/Pods-XMAlertModule-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-XMAlertModule/Pods-XMAlertModule-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		483959DA3D9E9CBA99AE05BC /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-XMAlertModule-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		B651E59E2599BF880038CC1E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B66A93B6259472F80027C34D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9596F7EC26141EDD002D96F5 /* XMLPUPlanAResultToast.swift in Sources */,
				B60EEA3D25CAA26600803513 /* XMListenTaskWindow.swift in Sources */,
				95A196EE26F2333200B3DA0C /* XMLSubscribeAlertView.swift in Sources */,
				B666BBCC2728FDDE00579168 /* XMYesterdayCoinAlert.swift in Sources */,
				95DDF029269D7A2E00682E1A /* XMLPUPlanBOperActionSheet.swift in Sources */,
				B66A93F6259474650027C34D /* XMLWelfareAwardOperAlertView.swift in Sources */,
				95DDF02F269D95CB00682E1A /* XMLPUPlanBUnlockFailAlertView.swift in Sources */,
				9596F7ED26141EDD002D96F5 /* XMLPUPlanAExhaActionSheet.swift in Sources */,
				95491B18265CA1D700970B83 /* XMAbnormityGuideAlert.swift in Sources */,
				9596F7EE26141EDD002D96F5 /* XMLPUPlanAOperActionSheet.swift in Sources */,
				B6F0588125E776D500C614D5 /* AlertModuleEntry.swift in Sources */,
				95491B0A265B981600970B83 /* XMCutelyPraiseGuideAlert.swift in Sources */,
				B64304B8262AB3B700B2D50F /* XMNewAwardViewController.swift in Sources */,
				B66A93F5259474650027C34D /* XMLWelfareAwardCoinFAlertView.swift in Sources */,
				B66A93FB259474650027C34D /* XMLCoinResultAlertView.swift in Sources */,
				B66A9408259474720027C34D /* XMLCoinAwardView.swift in Sources */,
				95DDF02D269D95BB00682E1A /* XMLPUPlanBVIPUnlockAlertView.swift in Sources */,
				B66A93FA259474650027C34D /* XMLCoinOperateAlertView.swift in Sources */,
				B66A93F8259474650027C34D /* XMLWelfareSignAlertView.swift in Sources */,
				9509B14226441DEC002C7C53 /* XMLPlayerTingActionSheet.swift in Sources */,
				B60EEA3F25CAA90500803513 /* XMListenTaskVM.swift in Sources */,
				95DDF02B269D95AB00682E1A /* XMLPUPlanBUnlockingAlertView.swift in Sources */,
				9509B14026441DD0002C7C53 /* XMLPlayerVIPActionSheet.swift in Sources */,
				95F6ACDE269EC36300CEAE6B /* XMLPUPlanBOperItem.swift in Sources */,
				B683961125F5C70A0016405E /* XMListenTaskAwardAlert.swift in Sources */,
				B66A9407259474720027C34D /* XMLListenAward.swift in Sources */,
				225C8FB326DDCA1F0077C851 /* XMLDriverModeAlertView.swift in Sources */,
				95491B08265B97E900970B83 /* XMWithdrPraiseGuideAlert.swift in Sources */,
				225C8FB526DDF3FC0077C851 /* XMLModeSwtichSuccessAlert.swift in Sources */,
				B64304BE262ADEA900B2D50F /* XMLNewUserAlertView.swift in Sources */,
				B666BBCE272926C000579168 /* XMYesterdayCoinVM.swift in Sources */,
				B608A547263507C200731404 /* XMImageGuideAlert.swift in Sources */,
				B60EEA4125CB983B00803513 /* XMListenPacketSheet.swift in Sources */,
				B62C0FB725E8965B00CF54C2 /* XMListenTaskVM+Audio.swift in Sources */,
				B66A93FC259474650027C34D /* XMPrivacyAuthorizationViewController.swift in Sources */,
				B66A93F4259474650027C34D /* XMLVipPriorityListenAlertView.swift in Sources */,
				95491B0E265BAFD900970B83 /* XMSubscrPraiseGuideAlert.swift in Sources */,
				B62C0FB925E8A2EF00CF54C2 /* XMListenTaskModel.swift in Sources */,
				B66A9406259474720027C34D /* XMLAwardViewModel.swift in Sources */,
				95A77FBB26DE58150049299B /* XMLAppTransitionWidget.swift in Sources */,
				B6894E1C262DB26F001CB298 /* XMPanableView.swift in Sources */,
				B66A93FE259474650027C34D /* XMLNotificationAlertView.swift in Sources */,
				B66A93F9259474650027C34D /* XMLWelfareAwardCoinAlertView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B66A93BF259472F80027C34D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B66A93C9259472F80027C34D /* XMAlertModuleTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		B651E5AF2599DC070038CC1E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B651E5A12599BF880038CC1E /* XMAlertModuleMedia */;
			targetProxy = B651E5AE2599DC070038CC1E /* PBXContainerItemProxy */;
		};
		B66A93C6259472F80027C34D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B66A93B9259472F80027C34D /* XMAlertModule */;
			targetProxy = B66A93C5259472F80027C34D /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		B651E5A62599BF880038CC1E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = NO;
				INFOPLIST_FILE = XMAlertModuleMedia/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.XMAlertModuleMedia;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		B651E5A72599BF880038CC1E /* Alpha */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = NO;
				INFOPLIST_FILE = XMAlertModuleMedia/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.XMAlertModuleMedia;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "";
				WRAPPER_EXTENSION = bundle;
			};
			name = Alpha;
		};
		B651E5A82599BF880038CC1E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = NO;
				INFOPLIST_FILE = XMAlertModuleMedia/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.XMAlertModuleMedia;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		B66A93CC259472F80027C34D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		B66A93CD259472F80027C34D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		B66A93CF259472F80027C34D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BDC556EEC9A919C3A4780AEF /* Pods-XMAlertModule.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FDFullscreenPopGesture\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MJRefresh\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMBase\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMCategories\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMKingfisherWebP\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMPKHUD\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMTingModel\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/iCarousel\"",
					"\"${PODS_ROOT}/BaiduMobAdSDK/Frameworks\"",
					"\"${PODS_ROOT}/Bytedance-UnionAD/Frameworks\"",
					"\"${PODS_ROOT}/GDTMobSDK/Frameworks\"",
					"\"${PODS_ROOT}/PhoneNetSDK/frameworks\"",
					"\"${PODS_ROOT}/TencentOpenAPI/TencentOpenAPI/v3.3.9\"",
					"\"${PODS_ROOT}/XM3rdParty/XM3rdParty/Products/iOS\"",
					"\"${PODS_ROOT}/XMDataBase/XMDataBase/Products\"",
					"\"${PODS_ROOT}/XMThirdParty/XMThirdParty/Classes/WebP\"",
					"\"$(SRCROOT)/../Products\"",
				);
				INFOPLIST_FILE = XMAlertModule/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-l\"WeChatSDK\"",
					"-l\"WeiboSDK\"",
					"-l\"c++\"",
					"-l\"iconv\"",
					"-l\"resolv\"",
					"-l\"sqlite3\"",
					"-l\"xml2\"",
					"-l\"z\"",
					"-framework",
					"\"AVFoundation\"",
					"-framework",
					"\"Accelerate\"",
					"-framework",
					"\"AdSupport\"",
					"-framework",
					"\"BUAdSDK\"",
					"-framework",
					"\"BUFoundation\"",
					"-framework",
					"\"BaiduMobAdSDK\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreLocation\"",
					"-framework",
					"\"CoreMedia\"",
					"-framework",
					"\"CoreMotion\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"CoreText\"",
					"-framework",
					"\"CryptoSwift\"",
					"-framework",
					"\"FDFullscreenPopGesture\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"GDTMobSDK\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"KeychainAccess\"",
					"-framework",
					"\"Kingfisher\"",
					"-framework",
					"\"KingfisherSwiftUI\"",
					"-framework",
					"\"Lottie\"",
					"-framework",
					"\"MJRefresh\"",
					"-framework",
					"\"MapKit\"",
					"-framework",
					"\"MediaPlayer\"",
					"-framework",
					"\"MessageUI\"",
					"-framework",
					"\"MobileCoreServices\"",
					"-framework",
					"\"PhoneNetSDK\"",
					"-framework",
					"\"Photos\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"RxBlocking\"",
					"-framework",
					"\"RxCocoa\"",
					"-framework",
					"\"RxRelay\"",
					"-framework",
					"\"RxSwift\"",
					"-framework",
					"\"SafariServices\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"SnapKit\"",
					"-framework",
					"\"StoreKit\"",
					"-framework",
					"\"SwiftyJSON\"",
					"-framework",
					"\"SwiftyUserDefaults\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"TencentOpenAPI\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"WebKit\"",
					"-framework",
					"\"WebP\"",
					"-framework",
					"\"XMBase\"",
					"-framework",
					"\"XMCategories\"",
					"-framework",
					"\"XMDataBase\"",
					"-framework",
					"\"XMKingfisherWebP\"",
					"-framework",
					"\"XMPKHUD\"",
					"-framework",
					"\"XMTingModel\"",
					"-framework",
					"\"YYCache\"",
					"-framework",
					"\"iCarousel\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.XMAlertModule;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		B66A93D0259472F80027C34D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7F1CC649D03708E0A064CCFE /* Pods-XMAlertModule.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FDFullscreenPopGesture\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MJRefresh\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMBase\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMCategories\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMKingfisherWebP\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMPKHUD\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMTingModel\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/iCarousel\"",
					"\"${PODS_ROOT}/BaiduMobAdSDK/Frameworks\"",
					"\"${PODS_ROOT}/Bytedance-UnionAD/Frameworks\"",
					"\"${PODS_ROOT}/GDTMobSDK/Frameworks\"",
					"\"${PODS_ROOT}/PhoneNetSDK/frameworks\"",
					"\"${PODS_ROOT}/TencentOpenAPI/TencentOpenAPI/v3.3.9\"",
					"\"${PODS_ROOT}/XM3rdParty/XM3rdParty/Products/iOS\"",
					"\"${PODS_ROOT}/XMDataBase/XMDataBase/Products\"",
					"\"${PODS_ROOT}/XMThirdParty/XMThirdParty/Classes/WebP\"",
					"\"$(SRCROOT)/../Products\"",
				);
				INFOPLIST_FILE = XMAlertModule/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-l\"WeChatSDK\"",
					"-l\"WeiboSDK\"",
					"-l\"c++\"",
					"-l\"iconv\"",
					"-l\"resolv\"",
					"-l\"sqlite3\"",
					"-l\"xml2\"",
					"-l\"z\"",
					"-framework",
					"\"AVFoundation\"",
					"-framework",
					"\"Accelerate\"",
					"-framework",
					"\"AdSupport\"",
					"-framework",
					"\"BUAdSDK\"",
					"-framework",
					"\"BUFoundation\"",
					"-framework",
					"\"BaiduMobAdSDK\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreLocation\"",
					"-framework",
					"\"CoreMedia\"",
					"-framework",
					"\"CoreMotion\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"CoreText\"",
					"-framework",
					"\"CryptoSwift\"",
					"-framework",
					"\"FDFullscreenPopGesture\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"GDTMobSDK\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"KeychainAccess\"",
					"-framework",
					"\"Kingfisher\"",
					"-framework",
					"\"KingfisherSwiftUI\"",
					"-framework",
					"\"Lottie\"",
					"-framework",
					"\"MJRefresh\"",
					"-framework",
					"\"MapKit\"",
					"-framework",
					"\"MediaPlayer\"",
					"-framework",
					"\"MessageUI\"",
					"-framework",
					"\"MobileCoreServices\"",
					"-framework",
					"\"PhoneNetSDK\"",
					"-framework",
					"\"Photos\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"RxBlocking\"",
					"-framework",
					"\"RxCocoa\"",
					"-framework",
					"\"RxRelay\"",
					"-framework",
					"\"RxSwift\"",
					"-framework",
					"\"SafariServices\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"SnapKit\"",
					"-framework",
					"\"StoreKit\"",
					"-framework",
					"\"SwiftyJSON\"",
					"-framework",
					"\"SwiftyUserDefaults\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"TencentOpenAPI\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"WebKit\"",
					"-framework",
					"\"WebP\"",
					"-framework",
					"\"XMBase\"",
					"-framework",
					"\"XMCategories\"",
					"-framework",
					"\"XMDataBase\"",
					"-framework",
					"\"XMKingfisherWebP\"",
					"-framework",
					"\"XMPKHUD\"",
					"-framework",
					"\"XMTingModel\"",
					"-framework",
					"\"YYCache\"",
					"-framework",
					"\"iCarousel\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.XMAlertModule;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		B66A93D2259472F80027C34D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BBF09D4EC6D509ED59C9BBDD /* Pods-XMAlertModuleTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = XMAlertModuleTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.XMAlertModule.XMAlertModuleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		B66A93D3259472F80027C34D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 869939F0A0410A9D85D7320A /* Pods-XMAlertModuleTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = XMAlertModuleTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.XMAlertModule.XMAlertModuleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		B66A93E62594734F0027C34D /* Alpha */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Alpha;
		};
		B66A93E72594734F0027C34D /* Alpha */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 70384E3B66EB592824B9972D /* Pods-XMAlertModule.alpha.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FDFullscreenPopGesture\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MJRefresh\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMBase\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMCategories\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMKingfisherWebP\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMPKHUD\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMTingModel\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/iCarousel\"",
					"\"${PODS_ROOT}/BaiduMobAdSDK/Frameworks\"",
					"\"${PODS_ROOT}/Bytedance-UnionAD/Frameworks\"",
					"\"${PODS_ROOT}/GDTMobSDK/Frameworks\"",
					"\"${PODS_ROOT}/PhoneNetSDK/frameworks\"",
					"\"${PODS_ROOT}/TencentOpenAPI/TencentOpenAPI/v3.3.9\"",
					"\"${PODS_ROOT}/XM3rdParty/XM3rdParty/Products/iOS\"",
					"\"${PODS_ROOT}/XMDataBase/XMDataBase/Products\"",
					"\"${PODS_ROOT}/XMThirdParty/XMThirdParty/Classes/WebP\"",
					"\"$(SRCROOT)/../Products\"",
				);
				INFOPLIST_FILE = XMAlertModule/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-l\"WeChatSDK\"",
					"-l\"WeiboSDK\"",
					"-l\"c++\"",
					"-l\"iconv\"",
					"-l\"resolv\"",
					"-l\"sqlite3\"",
					"-l\"xml2\"",
					"-l\"z\"",
					"-framework",
					"\"AVFoundation\"",
					"-framework",
					"\"Accelerate\"",
					"-framework",
					"\"AdSupport\"",
					"-framework",
					"\"BUAdSDK\"",
					"-framework",
					"\"BUFoundation\"",
					"-framework",
					"\"BaiduMobAdSDK\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreLocation\"",
					"-framework",
					"\"CoreMedia\"",
					"-framework",
					"\"CoreMotion\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"CoreText\"",
					"-framework",
					"\"CryptoSwift\"",
					"-framework",
					"\"FDFullscreenPopGesture\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"GDTMobSDK\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"KeychainAccess\"",
					"-framework",
					"\"Kingfisher\"",
					"-framework",
					"\"KingfisherSwiftUI\"",
					"-framework",
					"\"Lottie\"",
					"-framework",
					"\"MJRefresh\"",
					"-framework",
					"\"MapKit\"",
					"-framework",
					"\"MediaPlayer\"",
					"-framework",
					"\"MessageUI\"",
					"-framework",
					"\"MobileCoreServices\"",
					"-framework",
					"\"PhoneNetSDK\"",
					"-framework",
					"\"Photos\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"RxBlocking\"",
					"-framework",
					"\"RxCocoa\"",
					"-framework",
					"\"RxRelay\"",
					"-framework",
					"\"RxSwift\"",
					"-framework",
					"\"SafariServices\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"SnapKit\"",
					"-framework",
					"\"StoreKit\"",
					"-framework",
					"\"SwiftyJSON\"",
					"-framework",
					"\"SwiftyUserDefaults\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"TencentOpenAPI\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"WebKit\"",
					"-framework",
					"\"WebP\"",
					"-framework",
					"\"XMBase\"",
					"-framework",
					"\"XMCategories\"",
					"-framework",
					"\"XMDataBase\"",
					"-framework",
					"\"XMKingfisherWebP\"",
					"-framework",
					"\"XMPKHUD\"",
					"-framework",
					"\"XMTingModel\"",
					"-framework",
					"\"YYCache\"",
					"-framework",
					"\"iCarousel\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.XMAlertModule;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = ALPHA;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Alpha;
		};
		B66A93E82594734F0027C34D /* Alpha */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D07438A4A3CDA4DADA2CBBBE /* Pods-XMAlertModuleTests.alpha.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = XMAlertModuleTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.XMAlertModule.XMAlertModuleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Alpha;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		B651E5A52599BF880038CC1E /* Build configuration list for PBXNativeTarget "XMAlertModuleMedia" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B651E5A62599BF880038CC1E /* Debug */,
				B651E5A72599BF880038CC1E /* Alpha */,
				B651E5A82599BF880038CC1E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B66A93B4259472F80027C34D /* Build configuration list for PBXProject "XMAlertModule" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B66A93CC259472F80027C34D /* Debug */,
				B66A93E62594734F0027C34D /* Alpha */,
				B66A93CD259472F80027C34D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B66A93CE259472F80027C34D /* Build configuration list for PBXNativeTarget "XMAlertModule" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B66A93CF259472F80027C34D /* Debug */,
				B66A93E72594734F0027C34D /* Alpha */,
				B66A93D0259472F80027C34D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B66A93D1259472F80027C34D /* Build configuration list for PBXNativeTarget "XMAlertModuleTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B66A93D2259472F80027C34D /* Debug */,
				B66A93E82594734F0027C34D /* Alpha */,
				B66A93D3259472F80027C34D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = B66A93B1259472F80027C34D /* Project object */;
}
