//
//  XMRouter+Ad.swift
//  RouterModule
//
//  Created by yangle<PERSON> on 2020/4/16.
//  Copyright © 2020 roy wang. All rights reserved.
//

import Foundation
import UIKit
import XMConfigModule

// 业务广告类型
public enum XMLBusiAdType {
    case playPageBottom                    // 播放器底部
    case minePageBottom                    // 个人中心底部
    case welfareVideo                      // 福利页激励视频
    case welfareVideoAlert                 // 福利页激励视频后弹窗
    case welfareSignAlert                  // 福利页签到弹窗
    case welfareSignVideo                  // 福利页签到激励视频
    case welfareSignVideoAlert             // 福利页签到激励视频后弹窗
    case welfareBoxAlert                   // 福利页宝箱弹窗
    case welfareBoxVideo                   // 福利页宝箱激励视频
    case welfareBoxVideoAlert              // 福利页宝箱激励视频后弹窗
    case homeFeedAlert                     // 首页信息流弹窗
    case homeFeedVideo                     // 首页信息流激励视频
    case homeFeedVideoAlert                // 首页信息流激励视频后弹窗
    case customOperateAlert([String: Any]) // 通用可操作弹窗
    case customResultAlert([String: Any])  // 通用奖励弹窗
    case customVideo([String: Any])        // 通用激励视频
    case customRenderAlert([String: Any])  // 通用获取广告后自己渲染
    case customPlaque([String: Any])       // 通用插屏广告

    public var positionName: String {
        switch self {
        case .playPageBottom                : return "sub_play_convention_large"           // 播放页底部
        case .minePageBottom                : return XMConfig.shared().isMinimalism ? "sub_personal_center_large_immerse" : "sub_personal_center_large" // 个人页底部
        case .welfareVideo                  : return "sub_task_inspire_video"
        case .welfareVideoAlert             : return XMConfig.shared().isMinimalism ? "sub_task_popup_immerse" : "sub_task_popup"
        case .welfareSignAlert              : return "sub_sign_popup_large"                // 福利页签到弹窗
        case .welfareSignVideo              : return "sub_sign_inspire_video"
        case .welfareSignVideoAlert         : return "sub_sign_popup_large"                // 福利页签到双倍弹窗
        case .welfareBoxAlert               : return "toutiao_welfare_treasure_popup"      // 福利页宝箱弹窗
        case .welfareBoxVideo               : return "toutiao_welfare_treasure_award_video"
        case .welfareBoxVideoAlert          : return "toutiao_welfare_treasure_popup"      // 福利页宝箱双倍弹窗
        case .homeFeedAlert                 : return XMConfig.shared().isMinimalism ? "sub_flow_large_immerse" : "sub_flow_large" // 首页信息流弹窗
        case .homeFeedVideo                 : return XMConfig.shared().isMinimalism ? "sub_flow_inspire_video_immerse" : "sub_flow_inspire_video"
        case .homeFeedVideoAlert            : return XMConfig.shared().isMinimalism ? "sub_flow_large_immerse" : "sub_flow_large" // 首页信息流双倍弹窗
        case .customOperateAlert(let params): return (params["positionName"] as? String) ?? ""
        case .customResultAlert (let params): return (params["positionName"] as? String) ?? ""
        case .customVideo       (let params): return (params["positionName"] as? String) ?? ""
        case .customRenderAlert (let params): return (params["positionName"] as? String) ?? ""
        case .customPlaque      (let params): return (params["positionName"] as? String) ?? ""
        }
    }
    
    public var adxParams: [String: String] {
        return ["positionName": positionName]
    }
    
    public static func ==(lhs: XMLBusiAdType, rhs: XMLBusiAdType) -> Bool {
        switch (lhs, rhs) {
        case (.playPageBottom        , .playPageBottom        ) : return true
        case (.minePageBottom        , .minePageBottom        ) : return true
        case (.welfareVideo          , .welfareVideo          ) : return true
        case (.welfareVideoAlert     , .welfareVideoAlert     ) : return true
        case (.welfareSignAlert      , .welfareSignAlert      ) : return true
        case (.welfareSignVideo      , .welfareSignVideo      ) : return true
        case (.welfareSignVideoAlert , .welfareSignVideoAlert ) : return true
        case (.welfareBoxAlert       , .welfareBoxAlert       ) : return true
        case (.welfareBoxVideo       , .welfareBoxVideo       ) : return true
        case (.welfareBoxVideoAlert  , .welfareBoxVideoAlert  ) : return true
        case (.homeFeedAlert         , .homeFeedAlert         ) : return true
        case (.homeFeedVideo         , .homeFeedVideo         ) : return true
        case (.homeFeedVideoAlert    , .homeFeedVideoAlert    ) : return true
        case (.customOperateAlert    , .customOperateAlert    ) : return true
        case (.customResultAlert     , .customResultAlert     ) : return true
        case (.customVideo           , .customVideo           ) : return true
        case (.customRenderAlert     , .customRenderAlert     ) : return true
        case (.customPlaque          , .customPlaque          ) : return true
        default: return false
        }
    }
}
@objcMembers
public class XMLNativeAdFlowDataSource: NSObject {
    public var imageURL    : URL?     = nil
    public var image       : UIImage? = nil
    public var title       : String?  = nil
    public var desc        : String?  = nil
    public var logoView    : UIView?  = nil
    public var logoSize    : CGSize?  = nil
    public var sourceView  : UIView?  = nil
    public var sourceSize  : CGSize?  = nil
    public var mediaView   : UIView?  = nil
    
    public var iconURLs     : [String]?  = nil
    public var iconImages   : [UIImage]? = nil
    public var iconIsDR     : Bool       = false
    
    public var renderView  : UIView?  = nil
    public var renderSize  : CGSize?  = nil
    public var adContainer : UIView   = UIView(frame: .zero)
    
    public var adDescInfo       : String?  = nil
    public var adVertiserInfo   : String?  = nil
    public var logoSizeOC  : CGSize {
        get {
            if let size = logoSize { return size }
            return CGSize.zero
        }
    }
    public var sourceSizeOC  : CGSize {
        get {
            if let size = sourceSize { return size }
            return CGSize(width: 15, height: 15)
        }
    }
}

// 路由器广告分支
public protocol XMRouterAd {
    //  获取广告数据
    func getAd(in type: XMLBusiAdType, params: [String : Any]?, completion: @escaping ((Any?) -> Void))
    
    //  将广告数据与视图进行绑定
    func bindAd(in type: XMLBusiAdType, adObj: AnyObject, presentVC: UIViewController, flowView: XMNativeAdFlowViewDelegate)
    
    /// 展示自定义渲染 的广告但内部不会和第三方广告真正绑定
    func displaycustomRenderAd(adObj: AnyObject, presentVC: UIViewController, flowView: XMNativeAdFlowViewDelegate)

    //  解析广告数据
    func analysisAdInfo(_ adObj: AnyObject?) -> [String: Any]?
    
    // 上报广告源数据解析失败
    func uploadAdFailureInfo(adObj: AnyObject, status: Int)
    
    // 上报广告源数据加载成功
    func uploadVisibleStatisticInfo(adObj: AnyObject)
    
    // 是否正在显示全屏广告弹窗
    func isShowingWelAlertAd() -> Bool
    
    // ADX数据三方链接上报
    func uploadADXItemThirdURLsReport(_ urls: [String])
    
    // ADX数据展示上报
    func uploadADXItemTingShowReport(_ params: [String: Any], showTokenEnable: Bool)
    
    // ADX数据点击上报
    func uploadADXItemTingClickReport(_ url: String, params: [String: Any], clickTokenEnable: Bool)
    
    // 获取应用最近一次进入后台的时间
    func getADXEnterBgMoment() -> TimeInterval
    
    // 直接处理喜马物料点击事件
    func handleXMAdLinkDirectly(_ realLink: String, dpRealLink: String, inApp: Bool)
    
    // AD URL广告化
    func stringByRepalceADParamsWithURL(_ url: String) -> String
    
    // 增加标示用以移除iting UserAgent
    func stringByRemoveiTingUAWithURL(_ url: String) -> String
    
    /// 获取 可以加载第三方激励视频广告的对象
    func getCustomAd() -> XMNativeRewardAdProtocol?
    
    func doRN()
    
    // 是否在开屏广告逻辑中
    func isInSplashADLogic() -> Bool
}

// 路由器信息流广告协议
@objc public protocol XMNativeAdFlowViewDelegate {
    // 广告可点击区域
    func clickableViews() -> [UIView]
    // 展示大图
    func coverImageView() -> UIImageView?
    // 根据广告信息更新内容
    func updateFlowViewInfo(_ info: XMLNativeAdFlowDataSource?)
    func isHandleExtraCallBack() -> Bool
    func nativeAdDidCloseOtherController()
    func nativeAdDidClick()
    func nativeAdDidBecomeVisible()
    // 信息流视频贴片播放状态
    @objc optional func nativeAdViewStatusChanged(_ status: Int)
}

public protocol XMNativeRewardAdProtocol {
    
    /// 激励视频的状态回调
    var rewardAdDelegate: XMNativeRewardAdDelegate? { get set }
    
    /// 加载广告 目前只支持激励视频的加载，不过后续可以扩展为信息流广告
    /// - Parameter params: 参数列表
    func loadADXAd(_ params: [String: Any])
    /// 将广告视频渲染在vc上
    /// - Parameter vc: 要展示广告的vc
    func showVideoAd(from viewController: UIViewController)
}

@objc public protocol XMNativeRewardAdDelegate: NSObjectProtocol {
    
    /// 加载成功
    func nativeVideoAdDidLoad()
    /// 加载失败
    /// - Parameter error: 错误原因
    func nativeVideoAdDidFailure(error: Error?)
    /// 视频曝光成功
    func nativeVideoAdDidVisible()
    /// 视频广告关闭
    func nativeVideoAdDidClose()
    /// 视频广告点击
    func nativeVideoAdClick()
    /// 视频播放完成
    /// - Parameter error: 是否存在播放失败
    func nativeVideoAdDidPlayFinish(withError error: Error?)
    /// 是否满足奖励条件
    /// - Parameter verify: 是否满足条件
    func nativeVideoAdFinishVerify(verify: Bool)
}
